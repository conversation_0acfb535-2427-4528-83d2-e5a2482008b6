import StringHelper from "./string";

export default class CookiesHelper {
  static set = (
    name: string,
    value: string,
    expiry: number = 365,
    domain?: string
  ): string | undefined => {
    try {
      const _domain = domain
        ? domain
        : StringHelper.getDomainFromUrl({
            url: window.location.origin,
            includeSubdomain: true,
            includeProtocol: false,
          });

      const date = new Date();
      date.setTime(date.getTime() + expiry * 24 * 60 * 60 * 1000);

      document.cookie = `${name}=${value}; expires=${date.toUTCString()}; domain=${_domain}; path=/; SameSite=None; Secure;`;

      try {
        localStorage?.setItem(name, value);
      } catch (error) {}

      return value;
    } catch (error) {
      console.error(error);
    }
  };

  static get = (name: string): string | null | undefined => {
    try {
      const _name = name + "=";
      const split = document.cookie.split(";");

      for (let j = 0; j < split.length; j++) {
        let char = split[j];

        while (char.charAt(0) == " ") {
          char = char.substring(1);
        }

        if (char.indexOf(_name) == 0) {
          return char.substring(_name.length, char.length);
        }
      }

      try {
        return localStorage?.getItem(name);
      } catch (error) {}
    } catch (error) {
      console.error(error);
    }
  };
}
