export default class StringHelper {
  static getDomainFromUrl(params: {
    url: string;
    includeSubdomain: boolean;
    includeProtocol: boolean;
  }) {
    const { url, includeSubdomain, includeProtocol } = params;

    const includePort = true;

    let domain = "";
    let protocol = "";

    // Extract the protocol if required
    if (includeProtocol) {
      const matchProtocol = url.match(/^(https?:\/\/)/i);
      protocol = matchProtocol ? matchProtocol[1] : "";
    }

    // Remove the protocol if not required
    if (!includeProtocol) {
      domain = url.replace(/^(https?:\/\/)?/, "");
    }

    // Get the domain and port from the URL
    const matches = url.match(
      /^(?:https?:\/\/)?(?:www\.)?([^:/\n?]+)(?::(\d+))?/i
    );
    const port = matches && matches[2] ? `:${matches[2]}` : "";
    domain = matches ? matches[1] : "";

    // Remove subdomain if not required
    if (!includeSubdomain && domain !== null) {
      const domainParts = domain.split(".");
      if (domainParts.length > 2) {
        return (
          protocol + domainParts.slice(1).join(".") + (includePort ? port : "")
        );
      }
    }

    return protocol + domain + (includeProtocol && port ? port : "");
  }
}
