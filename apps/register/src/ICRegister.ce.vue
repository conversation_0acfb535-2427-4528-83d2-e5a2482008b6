<script setup lang="ts">
import { ref, computed } from "vue";
import CookiesHelper from "./cookies";

// Reactive data
const email = ref("");
const isLoading = ref(false);
const errorMsg = ref("");
const creationStep = ref(0);
const creationSteps = [
  "Creating your account...",
  "Sending your password via email...",
  "Creating your app...",
  "Connecting you to your app...",
];

// Computed data
const isValidEmail = computed(() => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email.value);
});

const handleUtm = () => {
  try {
    const appUtm = CookiesHelper.get("app_utm");

    return appUtm ? JSON.parse(decodeURIComponent(appUtm)) : {};
  } catch (error) {
    console.error(error);

    return {};
  }
};

const handleFullQueryString = () => {
  try {
    const appFullQueryString = CookiesHelper.get("app_full_qs");

    return appFullQueryString
      ? JSON.parse(decodeURIComponent(appFullQueryString))
      : {};
  } catch (error) {
    console.error(error);

    return {};
  }
};

const handleSubmit = async (event: Event) => {
  event.preventDefault();

  if (isLoading.value) {
    return;
  }

  if (!isValidEmail.value) {
    event.preventDefault();
    alert("Please enter a valid email address.");
    return;
  }

  errorMsg.value = "";
  isLoading.value = true;
  creationStep.value = 0;

  const utm = handleUtm();
  const fullQueryString = handleFullQueryString();

  // Start the API call immediately
  const apiCall = fetch("https://api.insertchat.com/v1/auth/access", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      email: email.value,
      utm,
      qs: fullQueryString,
      gclid: utm?.gclid,
    }),
  });

  // Show fake steps
  for (let i = 0; i < creationSteps.length; i++) {
    creationStep.value = i;
    await new Promise((resolve) => setTimeout(resolve, 2000));
  }

  try {
    const response = await apiCall;

    if (response.ok) {
      const data = await response.json();

      // Redirect the user here
      window.location.href =
        "https://app.insertchat.com/auth/access?token=" + data?.token;

      return;
    } else {
      const data = await response.json();

      console.error("Error 1:", data);

      errorMsg.value = data?.message;
    }
  } catch (error) {
    console.error("Error 2:", error);
  } finally {
    isLoading.value = false;
    creationStep.value = 0;
  }
};
</script>

<template>
  <form @submit="handleSubmit" method="GET" class="register-form">
    <div class="input-group">
      <input
        v-model="email"
        type="email"
        name="email"
        class="input"
        :class="{ 'is-invalid': email && !isValidEmail }"
        placeholder="Enter your business email"
        required
        :disabled="isLoading"
      />

      <button class="btn" type="submit" :disabled="isLoading || !isValidEmail">
        <span v-if="!isLoading">Sign Up Free</span>
        <span v-else class="spinner"></span>
      </button>
    </div>

    <p v-if="isLoading" class="loading-message text-center">
      {{ creationSteps[creationStep] }}
    </p>
    <p v-if="errorMsg" class="error-message text-center">
      {{ errorMsg }}
    </p>
  </form>
</template>

<style>
@import url("https://fonts.googleapis.com/css2?family=Outfit:wght@400;600&display=swap");

:host {
  --text-color: #0e111b;
  --bg-normal: #5e44ff;
  --border-color: #eeeeee;
  --border-radius: 24px;
  --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  --width: 570px;
  font-family: "Outfit", sans-serif;
}

.input-group .input,
.input-group .btn,
.loading-message,
.error-message {
  font-family: "Outfit", sans-serif;
}

.register-form {
  display: inline-block;
  width: var(--width);
  min-width: var(--width);
}

.input-group {
  display: flex;
}

.input-group .input {
  box-sizing: border-box;
  flex: 1;
  padding: 12px 22px;
  height: 50px;
  font-size: 16px;
  color: var(--text-color);
  background-color: #fff;
  border: 1px solid var(--border-color);
  transition: all 0.2s ease-in-out;
  border-radius: var(--border-radius) 0 0 var(--border-radius);
}

.input-group .input:focus {
  outline: 0;
  background-color: #ffffff;
}

.input-group .btn {
  padding: 12px 24px;
  height: 50px;
  font-size: 16px;
  font-weight: 600;
  color: #fff;
  background-color: var(--bg-normal);
  border: none;
  border-radius: 0 var(--border-radius) var(--border-radius) 0;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
}

.input-group .btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.loading-message {
  margin: 12px 0 0 0;
  font-size: 14px;
  color: var(--text-color);
  min-height: 1.5em;
}

.error-message {
  margin: 12px 0 0 0;
  font-size: 14px;
  color: #f82a5e;
  min-height: 1.5em;
}

@media (max-width: 768px) {
  .register-form {
    display: block;
    width: 100%;
    min-width: 100%;
    max-width: 100%;
  }

  .input-group {
    flex-direction: column;
  }

  .input-group .input,
  .input-group .btn {
    width: 100%;
    border-radius: var(--border-radius);
  }

  .input-group .input {
    margin-bottom: 1rem;
  }
}

.text-center {
  text-align: center;
}

.spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid #fff;
  border-radius: 50%;
  border-top-color: var(--bg-normal);
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
</style>
