import { defineConfig } from "vite";
import vue from "@vitejs/plugin-vue";
import icons from "unplugin-icons/vite";
import IconsResolver from "unplugin-icons/resolver";
import components from "unplugin-vue-components/vite";
import dts from "vite-plugin-dts";

export default defineConfig({
  define: {
    __VUE_OPTIONS_API__: "false",
    __VUE_PROD_DEVTOOLS__: "false",
    "process.env": {
      NODE_ENV: "production",
      TURNSTILE_SITE_KEY: "0x4AAAAAAAO6EBpoTUytxQdg",
    },
  },
  resolve: {
    alias: {
      vue: "vue/dist/vue.esm-bundler.js",
    },
  },
  build: {
    lib: {
      entry: "src/index.ts",
      formats: ["es"],
      fileName: "ic-register",
    },
    rollupOptions: {
      // Externalize deps that shouldn't be bundled into the library.
      // external: ["vue"],
    },
    sourcemap: false,
    // Reduce bloat from legacy polyfills.
    target: "esnext",
    // Leave minification up to applications.
    minify: true,
  },
  plugins: [
    dts(),
    components({
      resolvers: [IconsResolver({ componentPrefix: "" })],
    }),
    icons({
      autoInstall: true,
    }),
    vue(),
  ],
});
