{"name": "register", "version": "0.1.0", "private": true, "scripts": {"dev": "vite build --watch", "check": "vue-tsc --noEmit", "build": "vite build", "prepublishOnly": "npm run check && npm run build"}, "files": ["dist", "src"], "type": "module", "main": "dist/index.js", "module": "dist/index.js", "types": "dist/src/index.d.ts", "exports": {".": "./dist/index.js", "./package.json": "./package.json"}, "dependencies": {"vue": "^3.5.13"}, "devDependencies": {"@types/node": "^22.10.1", "@vitejs/plugin-vue": "5.2.1", "tslib": "^2.8.1", "typescript": "^5.7.2", "unplugin-icons": "0.21.0", "unplugin-vue-components": "0.27.5", "vite": "^6.0.3", "vite-plugin-dts": "^4.3.0", "vue": "^3.5.13", "vue-tsc": "^2.1.10"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/vue3-essential", "eslint:recommended"], "parserOptions": {"parser": "babel-es<PERSON>"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}