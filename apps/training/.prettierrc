{"trailingComma": "es5", "semi": false, "singleQuote": true, "useTabs": false, "quoteProps": "consistent", "bracketSpacing": true, "arrowParens": "always", "printWidth": 100, "plugins": ["@trivago/prettier-plugin-sort-imports", "prettier-plugin-edgejs"], "importOrder": ["^@adonisjs/(.*)$", "^#interfaces", "^#controllers", "^#modules/(.*)/(.*)_model$", "^#modules/(.*)/(.*)_service$", "^#modules/(.*)/(.*)_controllers$", "^#modules/(.*)/(.*)_validator$", "^#modules/(.*)$", "^[./]"], "importOrderParserPlugins": ["typescript", "jsx", "decorators"], "importOrderSeparation": false, "importOrderSortSpecifiers": true, "importOrderGroupNamespaceSpecifiers": true, "importOrderCaseInsensitive": true}