/*
|--------------------------------------------------------------------------
| Routes file
|--------------------------------------------------------------------------
|
| The routes file is used for defining the HTTP routes.
|
*/
import router from '@adonisjs/core/services/router'

const UrlsController = () => import('#modules/urls/urls_controller')
const YoutubeController = () => import('#modules/youtube/youtube_controller')
const DocumentsController = () => import('#modules/documents/documents_controller')
const MediasController = () => import('#modules/medias/medias_controller')
const CrawlersController = () => import('#modules/crawlers/crawlers_controller')
const SearchsController = () => import('#modules/search/searchs_controller')

router.post('/ingest/url', [UrlsController, 'handle'])
router.post('/ingest/youtube', [YoutubeController, 'handle'])
router.post('/ingest/media', [MediasController, 'handle'])
router.post('/ingest/document', [DocumentsController, 'handle'])
router.post('/crawler/website', [CrawlersController, 'handleWebsites'])
router.post('/crawler/sitemap', [CrawlersController, 'handleSitemaps'])
router.post('/search/google', [SearchsController, 'googleSearch'])
