/*
|--------------------------------------------------------------------------
| Environment variables service
|--------------------------------------------------------------------------
|
| The `Env.create` method creates an instance of the Env service. The
| service validates the environment variables and also cast values
| to JavaScript data types.
|
*/
import { Env } from '@adonisjs/core/env'

export default await Env.create(new URL('../', import.meta.url), {
  NODE_ENV: Env.schema.enum(['development', 'production', 'test'] as const),
  PORT: Env.schema.number(),
  HOST: Env.schema.string(),
  LOG_LEVEL: Env.schema.enum(['fatal', 'error', 'warn', 'info', 'debug', 'trace']),
  TIMEOUT: Env.schema.number(),
  // App
  APP_KEY: Env.schema.string(),
  // Database
  DB_HOST: Env.schema.string(),
  DB_PORT: Env.schema.number(),
  DB_USER: Env.schema.string(),
  DB_PASSWORD: Env.schema.string.optional(),
  DB_DATABASE: Env.schema.string(),
  // LiteLLM
  LITE_LLM_API_KEY: Env.schema.string(),
  LITE_LLM_BASE_URL: Env.schema.string(),
  LITE_LLM_MODEL_NAME: Env.schema.string(),
  // Zyte
  ZYTE_API_KEY: Env.schema.string(),
  // GROQ
  GROQ_API_KEY: Env.schema.string(),
  GROQ_MODEL_NAME: Env.schema.string(),
  // Iframely
  IFRAMELY_API_URL: Env.schema.string(),
  // Chunking
  CHUNK_SIZE: Env.schema.number(),
  CHUNK_OVERLAP: Env.schema.number(),
  // DataImpulse
  DATA_IMPULSE_LOGIN: Env.schema.string(),
  DATA_IMPULSE_PASSWORD: Env.schema.string(),
  DATA_IMPULSE_HOST: Env.schema.string(),
  DATA_IMPULSE_PORT: Env.schema.number(),
  // GeoNode
  GEONODE_USERNAME: Env.schema.string(),
  GEONODE_PASSWORD: Env.schema.string(),
  GEONODE_DNS: Env.schema.string(),
  GEONODE_PORT: Env.schema.number(),
  // Drive
  DRIVE_DISK: Env.schema.enum(['fs'] as const),
  // Redis
  REDIS_HOST: Env.schema.string({ format: 'host' }),
  REDIS_PORT: Env.schema.number(),
  REDIS_PASSWORD: Env.schema.string.optional(),
  // Serper
  SERPER_API_KEY: Env.schema.string(),
})
