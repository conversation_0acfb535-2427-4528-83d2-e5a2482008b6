import axios from 'axios'
import axiosRateLimit from 'axios-rate-limit'
import axiosRetry from 'axios-retry'
import type { ApplicationService } from '@adonisjs/core/types'

export default class AxioProvider {
  constructor(protected app: ApplicationService) {}

  /**
   * Register bindings to the container
   */
  register() {
    this.app.container.singleton('axios', () => {
      const axiosInstance = axios.create()
      // const retryAxios = axiosRetry(axiosInstance, { retries: 3 })

      // @ts-ignore
      return axiosRateLimit(axiosInstance, {
        maxRequests: 499,
        perMilliseconds: 60 * 1000,
      })
    })
  }

  /**
   * The container bindings have booted
   */
  async boot() {}

  /**
   * The application has been booted
   */
  async start() {}

  /**
   * The process has been started
   */
  async ready() {}

  /**
   * Preparing to shutdown the app
   */
  async shutdown() {}
}
