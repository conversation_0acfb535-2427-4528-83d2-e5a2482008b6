import fs from 'node:fs'
import Application from '@adonisjs/core/services/app'
import type { ApplicationService } from '@adonisjs/core/types'

export default class AppProvider {
  constructor(protected app: ApplicationService) {}

  /**
   * Register bindings to the container
   */
  register() {}

  /**
   * The container bindings have booted
   */
  async boot() {
    const tmpPath = Application.tmpPath()

    if (!fs.existsSync(tmpPath)) {
      await fs.promises.mkdir(tmpPath)
    }
  }

  /**
   * The application has been booted
   */
  async start() {}

  /**
   * The process has been started
   */
  async ready() {}

  /**
   * Preparing to shutdown the app
   */
  async shutdown() {}
}
