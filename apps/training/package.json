{"name": "training_v2", "version": "0.0.0", "private": true, "type": "module", "license": "UNLICENSED", "scripts": {"start": "node bin/server.js", "build": "node ace build", "dev": "node ace serve --hmr", "test": "node ace test", "lint": "eslint .", "format": "prettier --write .", "typecheck": "tsc --noEmit", "pm2:start": "pm2 start ecosystem.config.cjs --env production && pm2 ls", "pm2:restart": "pm2 restart ecosystem.config.cjs --env production && pm2 ls", "pm2:reload": "pm2 reload ecosystem.config.cjs --env production && pm2 ls", "pm2:stop": "pm2 delete training", "pm2:delete": "pm2 delete training", "pm2:remove": "pm2 delete training", "pm2:save": "pm2 save"}, "imports": {"#modules/*": "./app/modules/*.js", "#modules/*/*": "./app/modules/*/*.js", "#resources/*": "./app/resources/*", "#helpers/*": "./app/helpers/*.js", "#controllers/*": "./app/controllers/*.js", "#exceptions/*": "./app/exceptions/*.js", "#models/*": "./app/models/*.js", "#mails/*": "./app/mails/*.js", "#services/*": "./app/services/*.js", "#listeners/*": "./app/listeners/*.js", "#events/*": "./app/events/*.js", "#middleware/*": "./app/middleware/*.js", "#validators/*": "./app/validators/*.js", "#providers/*": "./providers/*.js", "#policies/*": "./app/policies/*.js", "#abilities/*": "./app/abilities/*.js", "#database/*": "./database/*.js", "#start/*": "./start/*.js", "#tests/*": "./tests/*.js", "#config/*": "./config/*.js"}, "devDependencies": {"@adonisjs/assembler": "^7.8.2", "@adonisjs/eslint-config": "^2.0.0-beta.6", "@adonisjs/prettier-config": "^1.4.0", "@adonisjs/tsconfig": "^1.4.0", "@swc/core": "1.10.7", "@trivago/prettier-plugin-sort-imports": "^5.2.1", "@types/luxon": "^3.4.2", "@types/node": "^22.10.7", "eslint": "^9.18.0", "hot-hook": "^0.4.0", "pino-pretty": "^13.0.0", "prettier": "^3.4.2", "prettier-plugin-edgejs": "^1.0.0", "ts-node-maintained": "^10.9.4", "typescript": "~5.7.3"}, "dependencies": {"@adonisjs/auth": "^9.3.0", "@adonisjs/core": "^6.17.1", "@adonisjs/cors": "^2.2.1", "@adonisjs/drive": "^3.2.0", "@adonisjs/lucid": "^21.6.0", "@adonisjs/redis": "^9.1.0", "@aws-sdk/client-s3": "^3.731.1", "@aws-sdk/lib-storage": "^3.731.1", "@aws-sdk/s3-request-presigner": "^3.731.0", "@langchain/textsplitters": "^0.1.0", "@mozilla/readability": "^0.5.0", "@vinejs/vine": "^3.0.0", "axios": "^1.7.9", "axios-rate-limit": "^1.4.0", "axios-retry": "^4.5.0", "cheerio": "^1.0.0", "content-type-parser": "^1.0.2", "csvtojson": "^2.0.10", "dotenv": "^16.4.7", "epub2": "^3.0.2", "fastify": "^5.1.0", "geonode-scraper-api": "^1.0.3", "gpt-tokenizer": "^2.8.1", "groq-sdk": "^0.12.0", "html-encoding-sniffer": "^4.0.0", "html-to-text": "^9.0.5", "jsdom": "^26.0.0", "luxon": "^3.5.0", "mammoth": "^1.8.0", "md5-file": "^5.0.0", "nodejs-file-downloader": "^4.13.0", "officeparser": "^5.1.1", "openai": "^4.78.1", "pg": "^8.13.1", "reflect-metadata": "^0.2.2", "scribe.js-ocr": "^0.7.0", "srt-parser-2": "^1.2.3", "vtt-to-json": "^0.1.1", "whatwg-encoding": "^3.1.1", "xml2js": "^0.6.2"}, "hotHook": {"boundaries": ["./app/controllers/**/*.ts", "./app/middleware/*.ts"]}}