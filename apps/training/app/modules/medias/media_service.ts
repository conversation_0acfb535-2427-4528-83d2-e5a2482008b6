import env from '#start/env'
import fs from 'fs'
import Groq from 'groq-sdk'
import { inject } from '@adonisjs/core'
import { HttpContext } from '@adonisjs/core/http'

@inject()
export default class MediaService {
  private _ctx: HttpContext

  constructor(ctx: HttpContext) {
    this._ctx = ctx as HttpContext
  }

  // mp3, mp4, mpeg, mpga, m4a, wav, webm, opus
  async extractContentFromAudio(params: { localPath: string; langIso_639_1: string }) {
    const { local_path, lang_iso_639_1 } = params

    const groq = new Groq({ apiKey: env.get('GROQ_API_KEY') })

    const transcription: { text: string } = await groq.audio.transcriptions.create({
      file: fs.createReadStream(local_path),
      model: env.get('GROQ_MODEL_NAME'),
      prompt:
        'Transcribe the audio accurately. Pay attention to context, tone, and any specialized terminology.',
      language: lang_iso_639_1,
      temperature: 0,
      response_format: 'json',
    })

    return transcription?.text?.trim()
  }
}
