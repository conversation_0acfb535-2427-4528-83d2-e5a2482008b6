import TrainingException from '#exceptions/training_exception'
import { StorageHelper } from '#helpers/drive_helper'
import LangchainHelper from '#helpers/langchain_helper'
import ResponseValidator from '#validators/response_validator'
import { Infer } from '@vinejs/vine/types'
import fs from 'fs'
import { inject } from '@adonisjs/core/container'
import { HttpContext } from '@adonisjs/core/http'
import MediaService from '#modules/medias/media_service'
import MediasValidator from '#modules/medias/media_validator'

@inject()
export default class MediasController {
  constructor(private mediaService: MediaService) {}

  async handle({ request, response }: HttpContext) {
    const { storage_path, lang_iso_639_1 } = await request.validateUsing(
      MediasValidator.handle_validator
    )

    const { local_path, extname } = await StorageHelper.downloadFile({
      storage_path,
      visibility: 'private',
    })

    let content: string | null = null

    if (['mp3', 'mp4', 'mpeg', 'mpga', 'm4a', 'wav', 'webm', 'opus'].includes(extname)) {
      content = await this.mediaService.extractContentFromAudio({
        local_path,
        lang_iso_639_1,
      })
    } else {
      throw new TrainingException({
        message: 'training_error_unsupported_file_type',
      })
    }

    if (!content) {
      throw new TrainingException({
        message: 'training_error_no_content',
      })
    }

    try {
      await fs.promises.unlink(local_path)
    } catch (error) {}

    const chunks = await LangchainHelper.chunkText({ type: 'markdown', text: content })

    if (!chunks || chunks?.length === 0) {
      throw new TrainingException({
        message: 'training_error_no_content',
      })
    }

    try {
      await fs.promises.unlink(local_path)
    } catch (error) {}

    const output: Infer<typeof ResponseValidator.process_validator> = {
      chunks,
      content_original: content,
      content_enhanced: null,
      content_summary: null,
      metadata: null,
    }

    return response.status(200).json(output)
  }
}
