import vine from '@vinejs/vine'

const handleSchema = vine.object({
  lang_iso_639_1: vine.string().optional().nullable(),
  lang_iso_639_2: vine.string().optional().nullable(),
  improve: vine.boolean(),
  storage_path: vine.string(),
  credentials: vine.object({
    access_key: vine.string().trim(),
    secret_key: vine.string().trim(),
    bucket: vine.string().trim(),
    region: vine.string().trim(),
    endpoint: vine.string().trim(),
  }),
})

const mediasHandleValidator = vine.compile(handleSchema)

export { mediasHandleValidator }
