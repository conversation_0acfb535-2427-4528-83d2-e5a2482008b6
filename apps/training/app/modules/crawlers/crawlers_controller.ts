import { responseCrawlerValidator } from '#validators/response_validator'
import { Infer } from '@vinejs/vine/types'
import { cuid } from '@adonisjs/core/helpers'
import { HttpContext } from '@adonisjs/core/http'
import { crawlerValidator } from '#modules/crawlers/crawler_validator'
import CrawlerHelper from '#modules/crawlers/crawler_helper'

export default class CrawlersController {
  async handleWebsites({ request, response }: HttpContext) {
    const { url } = await request.validateUsing(crawlerValidator)
    const { crawl_id, urls } = await CrawlerHelper.crawl(url)

    const output: Infer<typeof ResponseValidator.crawler_validator> = {
      crawl_id,
      urls,
    }

    return response.status(200).json(output)
  }

  async handleSitemaps({ request, response }: HttpContext) {
    const { url } = await request.validateUsing(CrawlerValidator.handle_validator)

    const output: Infer<typeof ResponseValidator.crawler_validator> = {
      crawl_id: cuid(),
      urls: await <PERSON>rawler<PERSON>elper.getSitemapUrls(url),
    }

    return response.status(200).json(output)
  }
}
