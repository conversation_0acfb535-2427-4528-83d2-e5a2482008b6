import Html<PERSON>elper from '#helpers/html_helper'
import <PERSON><PERSON><PERSON><PERSON><PERSON> from '#helpers/zyte_helper'
import axios from 'axios'
import { URL } from 'url'
import xml2js from 'xml2js'
import { cuid } from '@adonisjs/core/helpers'
import Application from '@adonisjs/core/services/app'

export default class CrawlerHelper {
  private static readonly CONCURRENT_REQUESTS = 3
  private static readonly MAX_PAGES_DEFAULT = 1000

  static async crawl(
    startUrl: string,
    maxPages: number = this.MAX_PAGES_DEFAULT
  ): Promise<{ crawl_id: string; urls: string[] }> {
    const crawl_id = cuid()
    const visited = new Set<string>()
    const queue: string[] = [startUrl]
    const origin = new URL(startUrl).origin
    const redis = await Application.container.make('redis')
    const visited_set = `crawler:${crawl_id}:visited`

    while (queue.length > 0 && visited.size < maxPages) {
      const batch = queue.splice(0, this.CONCURRENT_REQUESTS)

      const promises = batch.map(async (currentUrl) => {
        const is_visited = await redis.sismember(visited_set, currentUrl)

        if (!is_visited) {
          await redis.sadd(visited_set, currentUrl)

          visited.add(currentUrl)

          try {
            const { html } = await ZyteHelper.getHtml({ url: currentUrl, javascript: false })

            const urls = this.extractUrls(html, origin)

            for (const url of urls) {
              const already_visited =
                (await redis.sismember(visited_set, url)) || queue.includes(url)

              if (!already_visited) {
                const is_html = await HtmlHelper.isHtmlPage(url)

                if (is_html) {
                  queue.push(url)
                }
              }
            }
          } catch (error) {
            console.error(`Error crawling ${currentUrl}:`, error)
          }
        }
      })

      await Promise.all(promises)
    }

    await redis.del(visited_set)

    return { crawl_id, urls: Array.from(visited) }
  }

  private static extractUrls(html: string | null, origin: string): string[] {
    if (!html) {
      return []
    }

    // Extract body content only
    const bodyMatch = /<body[^>]*>([\s\S]*)<\/body>/i.exec(html)
    const body = bodyMatch ? bodyMatch[1] : ''

    const urlRegex = /href=["']?(https?:\/\/[^"'\s>]+)["']?/gi
    const urls: string[] = []
    let match

    while ((match = urlRegex.exec(body)) !== null) {
      try {
        const url = new URL(match[1])

        // same origin only - no subdomains
        if (url.origin === origin) {
          const is_html = HtmlHelper.isHtmlPage(url.href)

          if (is_html) {
            const clean_url = this.normalizeUrl(url.href)

            if (clean_url) {
              urls.push(clean_url)
            }
          }
        }
      } catch {
        // Ignore invalid URLs
      }
    }
    return urls
  }

  static normalizeUrl = (url: string): string | null => {
    try {
      const parsedUrl = new URL(url)
      parsedUrl.hash = '' // Remove fragment

      // Remove default port
      if (
        (parsedUrl.protocol === 'http:' && parsedUrl.port === '80') ||
        (parsedUrl.protocol === 'https:' && parsedUrl.port === '443')
      ) {
        parsedUrl.port = ''
      }

      // Remove trailing slash
      let normalized = parsedUrl.toString().toLowerCase()
      if (normalized.endsWith('/')) {
        normalized = normalized.slice(0, -1)
      }

      return normalized
    } catch (error) {
      // If invalid URL, return null
      console.warn(`Invalid URL encountered during normalization: ${url}`)
      return null
    }
  }

  static getSitemapUrls = async (url: string): Promise<string[]> => {
    const visited_sitemaps = new Set<string>()
    const urls: string[] = []

    const processSitemap = async (url: string) => {
      if (visited_sitemaps.has(url)) {
        return
      }

      visited_sitemaps.add(url)

      try {
        const response = await axios.get(url)
        const xml = response.data as string

        // Parse XML
        const parser = new xml2js.Parser()
        const result = await parser.parseStringPromise(xml)

        if (result?.urlset && result?.urlset?.url) {
          // Sitemap with URLs
          for (const urlEntry of result?.urlset?.url) {
            if (urlEntry?.loc && urlEntry?.loc[0]) {
              const normalized = CrawlerHelper.normalizeUrl(urlEntry.loc[0])

              if (normalized && HtmlHelper.isHtmlPage(normalized)) {
                urls.push(normalized)
              }
            }
          }
        } else if (result.sitemapindex && result.sitemapindex.sitemap) {
          // Sitemap index with nested sitemaps
          for (const sitemapEntry of result.sitemapindex.sitemap) {
            if (sitemapEntry.loc && sitemapEntry.loc[0]) {
              await processSitemap(sitemapEntry.loc[0])
            }
          }
        }
      } catch (error) {
        console.warn(`Failed to fetch or parse sitemap '${url}': ${error.message}`)
      }
    }

    await processSitemap(url)

    return urls
  }
}
