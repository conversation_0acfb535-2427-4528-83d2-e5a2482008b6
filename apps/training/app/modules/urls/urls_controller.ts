import TrainingException from '#exceptions/training_exception'
import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> from '#helpers/langchain_helper'
import <PERSON><PERSON><PERSON>el<PERSON> from '#helpers/zyte_helper'
import ResponseValidator from '#validators/response_validator'
import { Infer } from '@vinejs/vine/types'
import { HttpContext } from '@adonisjs/core/http'
import UrlsValidator from '#modules/urls/url_validator'

export default class UrlsController {
  async handle({ request, response }: HttpContext) {
    const { url, improve } = await request.validateUsing(UrlsValidator.handle_validator)
    const { content } = await ZyteHelper.scrape({ url })

    if (!content) {
      throw new TrainingException({
        message: 'training_error_no_content',
      })
    }

    const chunks = await LangchainHelper.chunkText({ type: 'markdown', text: content })

    if (!chunks || chunks?.length === 0) {
      throw new TrainingException({
        message: 'training_error_no_content',
      })
    }

    const output: Infer<typeof ResponseValidator.process_validator> = {
      chunks,
      content_original: content,
      content_enhanced: null,
      content_summary: null,
      metadata: null,
    }

    return response.status(200).json(output)
  }
}
