import TrainingException from '#exceptions/training_exception'
import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> from '#helpers/langchain_helper'
import ResponseValidator from '#validators/response_validator'
import { Infer } from '@vinejs/vine/types'
import { inject } from '@adonisjs/core'
import { HttpContext } from '@adonisjs/core/http'
import YoutubeService from '#modules/youtube/youtube_service'
import YoutubeValidator from '#modules/youtube/youtube_validator'

@inject()
export default class YoutubeController {
  constructor(private youtubeService: YoutubeService) {}

  async handle({ response, request }: HttpContext) {
    const { url, improve, lang_iso_639_1 } = await request.validateUsing(
      YoutubeValidator.handle_validator
    )

    const content = await this.youtubeService.extractSubtitle({
      url,
      lang_iso_639_1,
    })

    if (!content) {
      throw new TrainingException({
        message: 'training_error_no_content',
      })
    }

    const chunks = await LangchainHelper.chunkText({ type: 'markdown', text: content })

    if (!chunks || chunks?.length === 0) {
      throw new TrainingException({
        message: 'training_error_no_content',
      })
    }

    const output: Infer<typeof ResponseValidator.process_validator> = {
      chunks,
      content_original: content,
      content_enhanced: null,
      content_summary: null,
      metadata: null,
    }

    return response.status(200).json(output)
  }
}
