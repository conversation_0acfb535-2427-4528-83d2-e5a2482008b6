import vine from '@vinejs/vine'

const handleSchema = vine.object({
  lang_iso_639_1: vine.string().optional().nullable(),
  lang_iso_639_2: vine.string().optional().nullable(),
  improve: vine.boolean(),
  url: vine
    .string()
    .trim()
    .url({ require_protocol: true, protocols: ['http', 'https'] }),
})

const youtubeHandleValidator = vine.compile(handleSchema)

export { youtubeHandleValidator }
