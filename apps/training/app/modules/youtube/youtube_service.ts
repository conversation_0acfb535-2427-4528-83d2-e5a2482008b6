import TrainingException from '#exceptions/training_exception'
import env from '#start/env'
import { exec as execCallback } from 'child_process'
import fs from 'fs'
import util from 'util'
import vttToJson from 'vtt-to-json'
import { inject } from '@adonisjs/core'
import { HttpContext } from '@adonisjs/core/http'
import Application from '@adonisjs/core/services/app'
import YoutubeHelper from '#modules/youtube/youtube_helper'

@inject()
export default class YoutubeService {
  private _ctx: HttpContext
  private EXEC = util.promisify(execCallback)
  private MAX_RETRIES = 3
  private EXTENSION = 'vtt'
  private TMP_PATH = Application.tmpPath()
  private PROXIES = [
    `${env.get('DATA_IMPULSE_LOGIN')}__cr.us:${env.get('DATA_IMPULSE_PASSWORD')}@${env.get('DATA_IMPULSE_HOST')}:${env.get('DATA_IMPULSE_PORT')}`,
    `${env.get('DATA_IMPULSE_LOGIN')}__cr.ca:${env.get('DATA_IMPULSE_PASSWORD')}@${env.get('DATA_IMPULSE_HOST')}:${env.get('DATA_IMPULSE_PORT')}`,
    `${env.get('DATA_IMPULSE_LOGIN')}__cr.fr:${env.get('DATA_IMPULSE_PASSWORD')}@${env.get('DATA_IMPULSE_HOST')}:${env.get('DATA_IMPULSE_PORT')}`,
  ]

  constructor(ctx: HttpContext) {
    this._ctx = ctx as HttpContext
  }

  async extractSubtitle(params: { url: string; langIso_639_1: string }) {
    const { url, lang_iso_639_1 } = params

    const video_id = YoutubeHelper.getYoutudeId(url)
    const local_path = `${this.TMP_PATH}/${video_id}.${lang_iso_639_1}.${this.EXTENSION}`

    for (let attempt = 1; attempt <= this.MAX_RETRIES; attempt++) {
      try {
        const commands = this.PROXIES.map((proxy) => {
          return `cd ${this.TMP_PATH} && /Users/<USER>/Downloads/youtube-dl --proxy '${proxy}' --socket-timeout 30 --no-check-certificate --no-call-home --write-sub --sub-lang ${lang_iso_639_1} --sub-format ${this.EXTENSION} --write-auto-sub -o "${this.TMP_PATH}/${video_id}" --no-playlist --skip-download '${url}'`
        })

        const execPromises = commands.map((command) => this.EXEC(command))

        await Promise.any(execPromises)

        if (fs.existsSync(local_path)) {
          const file_content = await fs.promises.readFile(local_path, 'utf8')
          const json = await vttToJson(file_content)
          const deduplicatedParts = this.deduplicateParts(json)

          try {
            await fs.promises.unlink(local_path)
          } catch (error) {}

          return deduplicatedParts.join('\n') + '\n'?.trim()
        } else {
          continue
        }
      } catch (error) {
        if (error.message.includes('subtitles not available')) {
          throw new TrainingException({
            message: 'training_error_transcribe_not_available',
          })
        }

        if (error.message.includes('has no subtitles')) {
          throw new TrainingException({
            message: 'training_error_transcribe_inexsitant',
          })
        }

        if (error.message.includes('removed by the uploader')) {
          throw new TrainingException({
            message: 'training_error_video_deleted',
          })
        }

        if (attempt === this.MAX_RETRIES) {
          throw new TrainingException({ message: 'training_error_max_retries' })
        }
      }
    }
  }

  private deduplicateParts(parts: { part: string }[]): string[] {
    const partsText = parts.map((p) => p.part.trim()).filter((p) => p)

    for (let i = 0; i < partsText.length; i++) {
      const currentPart = partsText[i]?.trim()

      for (let j = i + 1; j < partsText.length; j++) {
        if (i !== j && partsText[j].includes(currentPart)) {
          partsText[j] = partsText[j].replace(currentPart, '')?.trim()
          break
        }
      }
    }

    return partsText.filter((p) => p)
  }
}
