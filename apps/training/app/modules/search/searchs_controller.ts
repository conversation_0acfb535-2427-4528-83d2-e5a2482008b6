import ResponseValidator from '#validators/response_validator'
import { Infer } from '@vinejs/vine/types'
import { inject } from '@adonisjs/core'
import { HttpContext } from '@adonisjs/core/http'
import SearchService from '#modules/search/search_service'
import SearchValidator from '#modules/search/search_validator'

@inject()
export default class SearchsController {
  constructor(private searchService: SearchService) {}

  async search({ request, response }: HttpContext) {
    const { q, gl, hl, tbs, autocorrect, num, page } = await request.validateUsing(
      SearchValidator.web_search_validator
    )

    const { search_id, data } = await this.searchService.googleSearch({
      q,
      gl,
      hl,
      tbs,
      autocorrect,
      num,
      page,
    })

    const output: Infer<typeof ResponseValidator.search_validator> = {
      search_id,
      data,
    }

    return response.status(200).json(output)
  }
}
