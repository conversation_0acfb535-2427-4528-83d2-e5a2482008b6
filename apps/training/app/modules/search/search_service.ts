import { Infer } from '@vinejs/vine/types'
import axios from 'axios'
import { inject } from '@adonisjs/core'
import { cuid } from '@adonisjs/core/helpers'
import { HttpContext } from '@adonisjs/core/http'
import SearchValidator from '#modules/search/search_validator'

@inject()
export default class CrawlerService {
  private _ctx: HttpContext

  constructor(ctx: HttpContext) {
    this._ctx = ctx as HttpContext
  }

  async googleSearch(payload: Infer<typeof SearchValidator.web_search_validator>) {
    const { data } = await axios.post('https://google.serper.dev/search', payload)

    return { search_id: cuid(), data }
  }
}
