import countries from '#app/resources/countries.json'
import languages from '#app/resources/languages.json'
import vine from '@vinejs/vine'

const webSearchSchema = vine.object({
  q: vine.string().trim(),
  gl: vine.enum((countries as Array<{ value: string }>).map((i: { value: string }) => i.value)),
  hl: vine.enum((languages as Array<{ value: string }>).map((i: { value: string }) => i.value)),
  tbs: vine.enum(['qdr:h', 'qdr:d', 'qdr:w', 'qdr:m', 'qdr:y']),
  autocorrect: vine.boolean(),
  num: vine.enum([10, 20, 30, 40, 50, 100]),
  page: vine.number().min(1),
})

const perplexitySearchSchema = vine.object({
  q: vine.string().trim(),
})

const searchWebSearchValidator = vine.compile(webSearchSchema)
const searchPerplexitySearchValidator = vine.compile(perplexitySearchSchema)

export { searchWebSearchValidator, searchPerplexitySearchValidator }
