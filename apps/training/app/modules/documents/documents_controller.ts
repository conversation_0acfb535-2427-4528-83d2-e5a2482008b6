import TrainingException from '#exceptions/training_exception'
import { StorageHelper } from '#helpers/drive_helper'
import LangchainHelper from '#helpers/langchain_helper'
import fs from 'fs'
import { inject } from '@adonisjs/core'
import { HttpContext } from '@adonisjs/core/http'
import DocumentService from '#modules/documents/document_service'
import DocumentsValidator from '#modules/documents/document_validator'

@inject()
export default class DocumentsController {
  constructor(private documentService: DocumentService) {}

  async handle({ request, response }: HttpContext) {
    const { credentials, storage_path, improve, lang_iso_639_2 } = await request.validateUsing(
      DocumentsValidator.handle_validator
    )

    let content: string | null = null

    const { local_path, extname } = await StorageHelper.downloadFile({
      credentials,
      storage_path,
    })

    // html
    if (['html'].includes(extname)) {
      content = await this.documentService.extractContentFromHtml({ local_path })
    }
    // txt, json, md, xml
    else if (['txt', 'md', 'json', 'xml'].includes(extname)) {
      content = await this.documentService.extractContentFromText({ local_path })
    }
    // csv, xlsx, xls, ods
    else if (['csv', 'xlsx', 'xls', 'ods'].includes(extname)) {
      content = await this.documentService.extractContentFromCsv({ local_path })
    }
    // docx
    else if (['docx'].includes(extname)) {
      content = await this.documentService.extractContentFromDocx({ local_path })
    }
    // pptx, odt, odp
    else if (['pptx', 'odt', 'odp'].includes(extname)) {
      content = await this.documentService.extractContentFromPptx({ local_path })
    }
    // srt
    else if (['srt'].includes(extname)) {
      content = await this.documentService.extractContentFromSrt({ local_path })
    }
    // epub
    else if (['epub'].includes(extname)) {
      content = await this.documentService.extractContentFromEpub({ local_path })
    }
    // pdf
    else if (['pdf'].includes(extname)) {
      content = await this.documentService.extractContentFromPdf({
        local_path,
        lang_iso_639_2,
      })
    } else {
      throw new TrainingException({
        message: 'training_error_unsupported_file_type',
      })
    }

    if (!content) {
      throw new TrainingException({
        message: 'training_error_no_content',
      })
    }

    const chunks = await LangchainHelper.chunkText({ type: 'markdown', text: content })

    if (!chunks || chunks?.length === 0) {
      throw new TrainingException({
        message: 'training_error_no_content',
      })
    }

    try {
      await fs.promises.unlink(local_path)
    } catch (error) {}

    return response.status(200).json({
      chunks,
      content_original: content,
      content_enhanced: null,
      content_summary: null,
      metadata: null,
    })
  }
}
