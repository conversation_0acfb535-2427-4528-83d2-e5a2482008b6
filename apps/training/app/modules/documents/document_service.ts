import Html<PERSON>elper from '#helpers/html_helper'
import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> from '#helpers/scribe_helper'
import csvto<PERSON>son from 'csvtojson/v2/index.js'
import { EPub } from 'epub2'
import fs from 'fs'
import mammoth from 'mammoth'
import officeParser from 'officeparser'
import srtParser2 from 'srt-parser-2'
import { inject } from '@adonisjs/core'
import { HttpContext } from '@adonisjs/core/http'

@inject()
export default class DocumentService {
  private _ctx: HttpContext

  constructor(ctx: HttpContext) {
    this._ctx = ctx as HttpContext
  }

  // html
  async extractContentFromHtml(params: { localPath: string }) {
    const { localPath } = params

    const html = await fs.promises.readFile(localPath, 'utf8')

    return HtmlHelper.convertHtmlToText({ html, url: '' })
  }

  // txt, json, md, xml
  async extractContentFromText(params: { localPath: string }) {
    const { localPath } = params

    return await fs.promises.readFile(localPath, 'utf8')
  }

  // csv, xlsx, xls, ods
  async extractContentFromCsv(params: { localPath: string }) {
    const { localPath } = params

    try {
      const data = await fs.promises.readFile(localPath, 'utf8')

      // Sanitize data by removing control characters
      const sanitizedData = data.replace(/[^\x00-\x08\x0B\x0C\x0E-\x1F\x7F-\x9F]/g, '')

      const jsonArray = await csvtojson({
        noheader: false,
        trim: true,
      }).fromString(sanitizedData)

      return JSON.stringify(jsonArray)
    } catch (error) {
      throw new Error('training_error_csv_parse')
    }
  }

  async extractContentFromDocx(params: { localPath: string }) {
    const { localPath } = params
    const mammothResponse = await mammoth.convertToHtml({ path: localPath })

    if (!mammothResponse || !mammothResponse?.value) {
      throw new Error('training_error_no_content')
    }

    return HtmlHelper.convertHtmlToText({ html: mammothResponse?.value, url: '' })
  }

  // pptx, odt, odp
  async extractContentFromPptx(params: { localPath: string }) {
    const { localPath } = params

    return await officeParser.parseOfficeAsync(localPath)
  }

  // srt
  async extractContentFromSrt(params: { localPath: string }) {
    const { localPath } = params

    const srtParser = new srtParser2()
    const srtString = await fs.promises.readFile(localPath, 'utf8')
    const srtArray = srtParser.fromSrt(srtString)

    return JSON.stringify(srtArray)
  }

  // epub
  async extractContentFromEpub(params: { localPath: string }) {
    const { localPath } = params

    const epub = await EPub.createAsync(localPath)

    const chapters = await Promise.all(
      epub.flow.map(async (chapter: any) => {
        if (!chapter.id) {
          return null
        }

        const html = await epub.getChapterRawAsync(chapter.id)

        if (!html) {
          return null
        }

        const originalContent = HtmlHelper.convertHtmlToText({ html, url: '' })

        return chapter.title ? `Chapter: ${chapter.title}\n\n${originalContent}` : originalContent
      })
    )

    return chapters?.join('\n\n')
  }

  // pdf
  async extractContentFromPdf(params: { localPath: string; langIso639_2: string }) {
    const { localPath, langIso639_2 } = params

    return await ScribeHelper.extractText({
      localPath,
      langIso639_2,
    })
  }
}
