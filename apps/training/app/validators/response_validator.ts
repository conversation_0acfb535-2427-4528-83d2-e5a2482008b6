import vine from '@vinejs/vine'

const processSchema = vine.object({
  chunks: vine.array(vine.string()),
  content_original: vine.string().nullable(),
  content_enhanced: vine.string().nullable(),
  content_summary: vine.string().nullable(),
  metadata: vine.object({}).allowUnknownProperties().nullable(),
})

const crawlerSchema = vine.object({
  crawl_id: vine.string(),
  urls: vine.array(vine.string()),
})

const searchSchema = vine.object({
  search_id: vine.string(),
  data: vine.any(),
})

const responseProcessValidator = vine.compile(processSchema)
const responseCrawlerValidator = vine.compile(crawlerSchema)
const responseSearchValidator = vine.compile(searchSchema)

export { responseProcessValidator, responseCrawlerValidator, responseSearchValidator }
