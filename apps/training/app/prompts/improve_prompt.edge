You are required to refine content within
<TEXT>
  tags, enhancing clarity, coherence, and discoverability.

Strict Instructions:
1. Restructure the Content: Improve logical flow, enrich detail, and retain all original context, information, data without loss, do not over simplify or omit anything!
2. Divide Content by Themes or Logical Breaks: Separate into self-contained sections based on themes or logical divisions, with each section standing independently, do not over simplify or omit anything!
3. Optimize Length for Readability: For lengthy content, split into smaller, readable sections without shortening the information, do not over simplify or omit anything!
4. Match Original Language Tone: Ensure that all refined language matches the tone and style of the original.
5. Maintain Distinct Sections: Avoid merging unrelated topics. Focus sections and keep them logically separated, do not over simplify or omit anything!
6. Use Markdown Formatting to Enhance Clarity: Structure content with Markdown for better readability (e.g., headings, lists, tables, icons, images, charts, links).
7. Integrate Complementary Media: Supplement sections with tables, images, graphics, URLs, media, code snippets, or other supportive material.
8. Preserve Detail and Expand Clarity: Refine each section to be as comprehensive as the original, preserving all specific details and enhancing clarity without shortening, do not over simplify or omit anything!
9. Do not over simplify or omit anything, the output text need to have more details then the original.
10. Do not include any additional text other then the JSON schema.
11. Only answer with a valid JSON schema.

Keys Explanation:
•	section_title: Primary title of the section.
•	section_subtitle: Additional context as a subtitle.
•	section_refined: Full refined content; must be an expanded, improved, but equally detailed version of the original.
•	section_succinct: A brief contextual note placing this section within the document.
•	section_summary: Concise summary of key points within the section.
•	section_urls: URLs or resources directly relevant to the section.
•	section_media: List of media items (e.g., charts, code snippets) relevant to the content.
•	section_keywords: Keywords encapsulating primary topics of the section.
•	other_related_sections: Titles of related sections to provide a broader context.
•	potential_user_questions: Questions users might ask concerning the section content.

JSON Schema:
[
  {
    "section_title": "",
    "section_subtitle": "",
    "section_refined": "",
    "section_succinct": "",
    "section_summary": "",
    "section_urls": [{ "url": "", "title": "" }],
    "section_media": [{ "url": "", "alt": "" }],
    "section_keywords": [""],
    "other_related_sections": [""],
    "potential_user_questions": [""]
  }
]
