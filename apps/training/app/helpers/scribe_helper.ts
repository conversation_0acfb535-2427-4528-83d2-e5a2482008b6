import TrainingException from '#exceptions/training_exception'
import scribe from 'scribe.js-ocr'

export default class ScribeHelper {
  static async extractText(params: { localPath: string; langIso639_2: string }): Promise<string> {
    const { localPath, langIso639_2 } = params

    try {
      await scribe.init()

      return await scribe.extractText([localPath], [langIso639_2])
    } catch (error) {
      if (error.message.includes('no objects found')) {
        throw new TrainingException({ message: 'training_error_damaged_file' })
      }

      throw error
    } finally {
      try {
        await scribe.clear()
        await scribe.terminate()
      } catch (error) {
        console.error(error)
      }
    }
  }
}
