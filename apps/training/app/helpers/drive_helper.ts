import FileHelper from '#helpers/file_helper'
import fs from 'node:fs'
import { cuid } from '@adonisjs/core/helpers'
import Application from '@adonisjs/core/services/app'
import drive from '@adonisjs/drive/services/main'

export class StorageHelper {
  static async downloadFile(params: {
    credentials: {
      accessKey: string
      secretKey: string
      bucket: string
      region: string
      endpoint: string
    }
    storagePath: string
  }): Promise<{ localPath: string; extname: string }> {
    const { credentials, storagePath } = params

    const tmpPath = Application.tmpPath()

    const disk = drive.use()

    const metadata = (await disk.getMetaData(storagePath)) || {}

    // @ts-ignore
    const extname = metadata?.extname || FileHelper.getFileExtension(storagePath)

    const fileBytes = await disk.getBytes(storagePath)

    const localPath = `${tmpPath}/${cuid()}.${extname}`

    await fs.promises.writeFile(localPath, fileBytes)

    return { localPath, extname }
  }
}
