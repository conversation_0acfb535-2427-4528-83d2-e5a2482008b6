import FileHelper from '#helpers/file_helper'
import fs from 'node:fs'
import { cuid } from '@adonisjs/core/helpers'
import Application from '@adonisjs/core/services/app'
import drive from '@adonisjs/drive/services/main'

export class StorageHelper {
  static async downloadFile(params: {
    credentials: {
      access_key: string
      secret_key: string
      bucket: string
      region: string
      endpoint: string
    }
    storagePath: string
  }): Promise<{ local_path: string; extname: string }> {
    const { credentials, storage_path } = params

    const tmp_path = Application.tmpPath()

    drive.use()

    const metadata = (await disk.getMetaData(storage_path)) || {}

    // @ts-ignore
    const extname = metadata?.extname || FileHelper.getFileExtension(storage_path)

    const file_bytes = await disk.getBytes(storage_path)

    const local_path = `${tmp_path}/${cuid()}.${extname}`

    await fs.promises.writeFile(local_path, file_bytes)

    return { local_path, extname }
  }
}
