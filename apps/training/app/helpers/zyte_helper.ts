import TrainingException from '#exceptions/training_exception'
import env from '#start/env'
import contentTypeParser from 'content-type-parser'
import htmlEncodingSniffer from 'html-encoding-sniffer'
import whatwgEncoding from 'whatwg-encoding'
import Application from '@adonisjs/core/services/app'
import HtmlHelper from './html_helper.js'

export default class ZyteHelper {
  private static async isJavaScriptRequired(params: { url: string }): Promise<boolean> {
    const { url } = params

    const origin = new URL(url).origin
    const redis = await Application.container.make('redis')
    const redis_js_status = await redis.get(origin)

    if (redis_js_status) {
      return Boolean(redis_js_status === '1' ? true : false)
    }

    const [{ content: contentWithoutJS }, { content: contentWithJS }] = await Promise.all([
      ZyteHelper.getHtml({
        url,
        javascript: false,
      }),
      ZyteHelper.getHtml({
        url,
        javascript: true,
      }),
    ])

    let js_status: boolean
    const contentWithoutJSLength = contentWithoutJS?.length || 0
    const contentWithJSLength = contentWithJS?.length || 0

    if (
      contentWithoutJS?.toLowerCase()?.includes('javascript is disabled') ||
      contentWithoutJS?.toLowerCase()?.includes('this requires javascript') ||
      contentWithoutJS?.toLowerCase()?.includes('enable javascript and then reload the page')
    ) {
      js_status = true
    } else if (!contentWithoutJS && contentWithJS) {
      js_status = true
    } else if (contentWithJSLength / contentWithoutJSLength > 1.2) {
      js_status = true
    } else {
      js_status = false
    }

    await redis.set(origin, js_status ? '1' : '0')

    return js_status
  }

  static async getHtml(params: { url: string; javascript: boolean }): Promise<{
    html: string | null
    content: string | null
  }> {
    const { url, javascript } = params
    const redis = await Application.container.make('redis')
    const redis_cache_key = `html_cache:${url}:${javascript}`
    const redis_cache_ttl = 3600

    // Check cache
    const cached_data = await redis.get(redis_cache_key)

    if (cached_data) {
      return JSON.parse(cached_data)
    }

    if (!HtmlHelper.isHtmlPage(url)) {
      throw new TrainingException({ message: 'training_error_unsupported_url' })
    }

    const axios = await Application.container.make('axios')
    const extractConfig = {
      url,
      httpResponseBody: !javascript,
      httpResponseHeaders: !javascript,
      browserHtml: javascript,
      echoData: url,
      actions: javascript ? [{ action: 'scrollBottom' }] : undefined,
      javascript,
    }

    while (true) {
      try {
        const zyteResponse = await axios.post('https://api.zyte.com/v1/extract', extractConfig, {
          auth: { username: env.get('ZYTE_API_KEY') || '', password: '' },
          timeout: 60 * 1000,
        })

        const { httpResponseHeaders, httpResponseBody, browserHtml } = zyteResponse?.data ?? {}

        let html: string

        if (browserHtml) {
          html = browserHtml
        } else {
          let contentType, contentTypeCharset

          if (httpResponseHeaders) {
            for (const item of httpResponseHeaders) {
              if (item.name.toLowerCase() === 'content-type') {
                const contentParser = contentTypeParser(item.value)
                contentType = contentParser?.type
                contentTypeCharset = contentParser?.get('charset') || 'UTF-8'
                break
              }
            }
          }

          if (!contentType || contentType !== 'text') {
            throw new TrainingException({ message: 'training_error_unsupported_url' })
          }

          const body: Buffer = Buffer.from(httpResponseBody, 'base64')
          const encoding = htmlEncodingSniffer(body, {
            transportLayerEncodingLabel: contentTypeCharset,
          })

          html = whatwgEncoding.decode(body, encoding)
        }

        html = HtmlHelper.convertRelativeUrls({ html, url })

        const content = HtmlHelper.convertHtmlToText({ html, url }) || null

        const response = {
          html,
          content,
        }

        await redis.set(redis_cache_key, JSON.stringify(response), 'EX', redis_cache_ttl)

        return response
      } catch (error) {
        console.error(error)

        const error_type = error.response?.data?.type
        const error_status = error.response?.status

        if (['/download/domain-forbidden'].includes(error_type)) {
          throw new TrainingException({ message: 'training_error_access_forbidden' })
        } else if (['/download/temporary-error'].includes(error_type)) {
          throw new TrainingException({ message: 'training_error_access_refused' })
        } else if (
          error?.message.includes('Maximum call stack size exceeded') ||
          [429, 500, 503, 520, 521].includes(error_status)
        ) {
          const retry_after_header = error.response?.headers?.['retry-after']

          if (retry_after_header) {
            const retry_after = parseInt(retry_after_header, 10) * 1000

            console.warn(
              `Received ${error_status} error. Retrying in ${retry_after / 1000} seconds...`
            )

            await new Promise((resolve) => setTimeout(resolve, retry_after))

            continue
          }
        } else {
          throw new TrainingException({ message: 'training_retry_later' })
        }
      }
    }
  }

  static async scrape(params: { url: string }) {
    const { url } = params

    const javascript = await ZyteHelper.isJavaScriptRequired({ url })

    return await ZyteHelper.getHtml({ url, javascript })
  }
}
