import env from '#start/env'
import { RecursiveCharacterTextSplitter } from '@langchain/textsplitters'

export default class LangchainHelper {
  static async chunkText(params: { type?: 'markdown' | 'html'; text: string }) {
    const { type, text } = params

    try {
      const splitter = RecursiveCharacterTextSplitter.fromLanguage(type || 'markdown', {
        chunkSize: env.get('CHUNK_SIZE'),
        chunkOverlap: env.get('CHUNK_OVERLAP'),
        keepSeparator: true,
      })

      return (await splitter.splitText(text)) || null
    } catch (error) {
      console.error(error)

      throw new Error('Failed to chunk text.')
    }
  }
}
