import * as cheerio from 'cheerio'
import { Readability } from '@mozilla/readability'
import { convert } from 'html-to-text'
import { JSDOM } from 'jsdom'

export default class HtmlHelper {
  static improveHtmlReadability(html: string): string {
    const document = new JSDOM(html)
    const readability = new Readability(document.window.document, { debug: false })
    const article = readability.parse()

    return article?.content?.trim() || html
  }

  static stripNavigationElements(html: string): string {
    return HtmlHelper.stripHtmlElements(html, [
      'script',
      'style',
      'link',
      'meta',
      'head',
      'header',
      'nav',
      'navbar',
      'menu',
      'aside',
      'footer',
      '#header',
      '#nav',
      '#navbar',
      '#navigation',
      '#menu',
      '#aside',
      '#footer',
    ])
  }

  static convertHtmlToText(params: { html: string; url: string }): string {
    const { html, url } = params

    let _html = HtmlHelper.convertRelativeUrls({ html, url })
    _html = HtmlHelper.improveHtmlReadability(_html)
    _html = HtmlHelper.stripNavigationElements(_html)

    const text: string = convert(_html, {
      decodeEntities: true,
      preserveNewlines: false,
      baseUrl: url,
    })

    return text
  }

  static stripHtmlElements(html: string, selectors: string[]): string {
    const $ = cheerio.load(html)

    selectors.forEach((selector) => {
      $(selector).remove()
    })

    return $.html()
  }

  static convertRelativeUrls(params: { html: string; url: string }): string {
    const { html, url } = params

    return html.replace(/(href|src)="([^"]*)"/g, (match, attr, p1) => {
      try {
        return `${attr}="${new URL(p1, new URL(url)).href}"`
      } catch (error) {
        return match
      }
    });
  }

  static isHtmlPage(url: string): boolean {
    if (url.includes('/wp-json/') || url.includes('xmlrpc.php')) {
      return false
    }

    return !url.match(
      /\.(jpg|jpeg|png|gif|svg|pdf|docx?|xlsx?|pptx?|zip|rar|exe|mp3|mp4|opus|avi|mov|wmv|css|js|json|xml|txt|kml)$/i
    );
  }
}
