import md5File from 'md5-file'
import path from 'node:path'

export default class FileHelper {
  static getFileExtension(path: string) {
    if (!path) {
      return ''
    }

    let pathName = path

    try {
      const url = new URL(path)

      pathName = url.pathname
    } catch (error) {}

    const extension = path.extname(pathName).toLowerCase()

    if (extension === '' || extension === pathName) {
      return ''
    }

    return extension.startsWith('.') ? extension.slice(1) : extension
  }

  static getMD5(path: string): string | undefined {
    return md5File.sync(path)
  }
}
