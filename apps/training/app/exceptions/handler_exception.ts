import axios, { AxiosError } from 'axios'
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, HttpContext } from '@adonisjs/core/http'
import app from '@adonisjs/core/services/app'

export default class HttpExceptionHandler extends ExceptionHandler {
  /**
   * In debug mode, the exception handler will display verbose errors
   * with pretty printed stack traces.
   */
  protected debug = !app.inProduction

  /**
   * The method is used for handling errors and returning
   * response to the client
   */
  async handle(error: unknown, ctx: HttpContext) {
    // Custom error handling logic
    let _message = 'Internal Server Error'
    let _status = 500
    let _code = 'base/error'

    if (error instanceof Error) {
      _message = error.message
    }

    // Axios error handling
    if (axios.isAxiosError(error)) {
      const { data, status } = error.response || {}
      if (typeof data === 'string' && data.length > 0) {
        _message = data
      } else {
        _message =
          data?.message || data?.error || data?.detail || data?.error_description || _message
      }
      _status = status || 500
    } else if (typeof error === 'object' && error !== null) {
      // If error has message/status/code properties
      // @ts-ignore
      if (error.message) _message = error.message
      // @ts-ignore
      if (error.status) _status = error.status
      // @ts-ignore
      if (error.code) _code = error.code
    }

    ctx.response.status(_status).send({ message: _message, status: _status, code: _code })
  }

  /**
   * The method is used to report error to the logging service or
   * the third party error monitoring service.
   *
   * @note You should not attempt to send a response from this method.
   */
  async report(error: unknown, ctx: HttpContext) {
    ctx.logger.error({ err: error }, error instanceof Error ? error.message : String(error))
  }
}
