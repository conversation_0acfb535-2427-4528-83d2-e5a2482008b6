import { Exception } from '@adonisjs/core/exceptions'
import { HttpContext } from '@adonisjs/core/http'
import adonisApp from '@adonisjs/core/services/app'

export default class TrainingException extends Exception {
  protected debug = !adonisApp.inProduction

  constructor(params: {
    message:
      | 'training_error_no_content'
      | 'training_error_unsupported_file_type'
      | 'training_error_unsupported_url'
      | 'training_error_access_forbidden'
      | 'training_error_access_refused'
      | 'training_retry_later'
      | 'training_error_transcribe_not_available'
      | 'training_error_transcribe_inexsitant'
      | 'training_error_video_deleted'
      | 'training_error_max_retries'
      | 'training_error_damaged_file'
  }) {
    const { message } = params

    super(message, { status: 400, code: message })
  }

  async handle(error: this, ctx: HttpContext) {
    ctx.response
      .status(error.status)
      .send({ message: error.message, status: error.status, code: error.code })
  }

  async report(error: this, ctx: HttpContext) {
    ctx.logger.error({ err: error }, error.message)
  }
}
