{"name": "@insertchat/backend", "version": "2.0.0", "private": true, "type": "module", "license": "UNLICENSED", "exports": {"./api": "./.adonisjs/index.ts", "./validators": "./app/validators.ts"}, "scripts": {"start": "node bin/server.js", "build": "node ace build", "dev": "node ace serve --hmr", "test": "node ace test", "lint": "eslint .", "format": "prettier --write .", "typecheck": "tsc --noEmit"}, "imports": {"#app/*": "./app/*.js", "#app/**/*": "./app/**/*.js", "#node_modules": "./node_modules", "#providers/*": "./providers/*.js", "#start/*": "./start/*.js", "#config/*": "./config/*.js", "#controllers/*": "./app/controllers/*.js"}, "devDependencies": {"@adonisjs/assembler": "^7.8.2", "@adonisjs/eslint-config": "^2.0.0", "@adonisjs/prettier-config": "^1.4.4", "@adonisjs/tsconfig": "^1.4.0", "@aws-sdk/types": "^3.775.0", "@swc/core": "1.11.29", "@trivago/prettier-plugin-sort-imports": "^5.2.2", "@tuyau/utils": "^0.0.7", "@types/disposable-email-domains": "^1.0.6", "@types/download": "^8.0.5", "@types/fluent-ffmpeg": "^2.1.27", "@types/luxon": "^3.6.2", "@types/mime-types": "^2.1.4", "@types/node": "^22.15.3", "@types/nodemailer": "^6.4.17", "@types/pug": "^2.0.10", "@types/readable-stream": "^4.0.18", "@types/stopword": "^2.0.3", "@types/xml2js": "^0.4.14", "eslint": "^9.26.0", "hot-hook": "^0.4.0", "https": "^1.0.0", "pino-pretty": "^13.0.0", "prettier": "^3.5.3", "prettier-plugin-edgejs": "^1.0.0", "ts-node-maintained": "^10.9.5", "typescript": "~5.8.3"}, "dependencies": {"@adonisjs/ally": "^5.1.0", "@adonisjs/auth": "^9.4.0", "@adonisjs/core": "^6.17.2", "@adonisjs/cors": "^2.2.1", "@adonisjs/drive": "^3.4.1", "@adonisjs/lock": "^1.1.1", "@adonisjs/lucid": "^21.6.1", "@adonisjs/mail": "^9.2.2", "@adonisjs/redis": "^9.2.0", "@adonisjs/transmit": "^2.0.2", "@aws-crypto/sha256-js": "^5.2.0", "@aws-sdk/client-polly": "^3.799.0", "@aws-sdk/client-s3": "^3.802.0", "@aws-sdk/credential-provider-node": "^3.799.0", "@aws-sdk/lib-storage": "^3.802.0", "@aws-sdk/protocol-http": "^3.374.0", "@aws-sdk/s3-request-presigner": "^3.802.0", "@aws-sdk/signature-v4": "^3.374.0", "@fal-ai/client": "^1.4.0", "@rlanz/bull-queue": "^3.1.0", "@tuyau/client": "^0.2.7", "@tuyau/core": "^0.4.0", "@tuyau/openapi": "^1.0.2", "@tuyau/superjson": "^0.1.0", "@vinejs/vine": "^3.0.1", "@woocommerce/woocommerce-rest-api": "^1.0.1", "adonis-lucid-filter": "^5.2.0", "adonis5-scheduler": "^2.1.1", "adonisjs-scheduler": "^2.4.0", "argon2": "^0.43.0", "axios": "^1.9.0", "axios-retry": "^4.5.0", "bcrypt": "^6.0.0", "bull": "^4.16.5", "bullmq": "^5.52.1", "cheerio": "^1.0.0", "cloudflare": "^4.2.0", "cohere-ai": "^7.17.1", "crisp-api": "^9.9.0", "crisp-status-reporter": "^1.2.2", "csv-writer": "^1.6.0", "disposable-email-domains": "^1.0.62", "download": "^8.0.0", "edge.js": "^6.2.1", "entities": "^6.0.0", "fflate": "^0.8.2", "file-type": "21.0.0", "firebase-admin": "^13.3.0", "fluent-ffmpeg": "^2.1.3", "google-auth-library": "^9.15.1", "gpt-tokenizer": "^2.9.0", "groq-sdk": "^0.22.0", "he": "^1.2.0", "ioredis": "^5.6.1", "libphonenumber-js": "^1.12.7", "livekit-server-sdk": "^2.12.0", "luxon": "^3.6.1", "md5-file": "^5.0.0", "merge-anything": "^6.0.6", "mime-types": "^3.0.1", "node-cron": "^4.0.7", "nodemailer": "^7.0.2", "openai": "^4.97.0", "pexels": "^1.4.0", "pg": "^8.15.6", "promise-call-limit": "^3.0.2", "proxy-addr": "^2.0.7", "pug": "^3.0.3", "punycode": "^2.3.1", "readable-stream": "^4.7.0", "reflect-metadata": "^0.2.2", "rollbar": "^2.26.4", "sharp": "^0.34.1", "stopword": "^3.1.4", "stripe": "^18.1.1", "uuid": "^11.1.0", "xml2js": "^0.6.2", "zod": "^3.24.4", "zod-to-json-schema": "^3.24.5"}, "optionalDependencies": {"@img/sharp-linux-arm": "^0.34.1", "@img/sharp-linux-x64": "^0.34.1", "@img/sharp-win32-x64": "^0.34.1", "@swc/core-darwin-arm64": "^1.11.24", "@swc/core-darwin-x64": "1.11.29"}, "hotHook": {"boundaries": ["./app/controllers/**/*.ts", "./app/middleware/*.ts"]}, "overrides": {"strtok3": "10.2.2"}, "packageManager": "pnpm@10.11.0"}