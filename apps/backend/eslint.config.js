import { configApp } from '@adonisjs/eslint-config'

export default configApp({
  rules: {
    'no-unused-vars': 'off',
    '@typescript-eslint/no-unused-parameters': 'off',
    '@typescript-eslint/no-unused-locals': 'off',
    '@typescript-eslint/no-unused-vars': 'off',
    '@typescript-eslint/naming-convention': [
      'error',
      {
        selector: 'class',
        format: ['PascalCase'],
      },
      {
        selector: 'function',
        format: ['camelCase'],
      },
      {
        selector: 'variable',
        format: ['snake_case'],
      },
      {
        selector: 'interface',
        format: ['PascalCase'],
        custom: {
          regex: '^I[A-Z]',
          match: true,
        },
      },
    ],
  },
  overrides: [
    {
      files: ['src/**/*.{ts,js}'],
      rules: {
        '@typescript-eslint/naming-convention': [
          'error',
          {
            selector: 'interface',
            format: ['PascalCase'],
            custom: {
              regex: '^I[A-Z]',
              match: true,
            },
          },
          {
            selector: 'class',
            format: ['PascalCase'],
          },
          {
            selector: 'function',
            format: ['camelCase'],
          },
          {
            selector: 'variable',
            format: ['snake_case'],
          },
        ],
      },
    },
  ],
})
