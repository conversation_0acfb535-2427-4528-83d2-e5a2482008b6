import type { HasManyQueryBuilderContract } from '@adonisjs/lucid/types/relations'
import type { LucidModel } from '@adonisjs/lucid/types/model'

declare module '@adonisjs/lucid/types/relations' {
  interface HasManyQueryBuilderContract<Related extends LucidModel> {
    filter(options: {
      expand?: string[]
      filters?: Array<{ key: string; operator: string; value: any }>
      search?: string
      orderBy?: Array<{ key: string; direction: 'asc' | 'desc' }>
      groupBy?: string
    }): this
  }
}
