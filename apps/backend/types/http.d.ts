import { Infer } from '@vinejs/vine/types'
import { AccessToken } from '@adonisjs/auth/access_tokens'
import App from '#app/modules/apps/app_model'
import User from '#app/modules/users/user_model'
import {
  subscriptionProductValidator,
  subscriptionValidator,
} from '#app/modules/subscriptions/subscription_validator'

declare module '@adonisjs/core/http' {
  interface HttpContext {
    app: App
    user: (User & { currentAccessToken: AccessToken }) | undefined
    token: string | undefined
    planProduct: Infer<typeof subscriptionProductValidator> | undefined
    planSubscription: Infer<typeof subscriptionValidator> | undefined
  }
}
