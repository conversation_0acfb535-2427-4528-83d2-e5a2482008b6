stages:
  - build
  - deploy

variables:
  NODE_IMAGE: 'node:lts-alpine'
  APP_NAME: 'app'
  SERVER_USER: 'root'
  BASE_FOLDER: '/root'
  APP_FOLDER: '$BASE_FOLDER/$APP_NAME'
  GITLAB_KEY_FILE: '$BASE_FOLDER/.ssh/gitlab_key_for_$APP_NAME_project.pub'
  ENV_FILE: '$BASE_FOLDER/config/$APP_NAME'
  CPU_ARCHITECTURE: 'x64'
  ENTRY_POINT: 'build/server.js'

build:
  stage: build
  image: $NODE_IMAGE
  only:
    - production
  script:
    - echo "Running the build phase..."
    - npm install --os=linux --cpu=$CPU_ARCHITECTURE --include=optional --loglevel error
    - npm run build
  artifacts:
    paths:
      - build/

deploy:
  stage: deploy
  image: $NODE_IMAGE
  only:
    - production
  variables:
    GIT_STRATEGY: clone
    TARGET_BRANCH: 'production'
    REMOTE_REPO_URL: '**************:insertchat/server.git'
  before_script:
    - apk update && apk add --no-cache openssh-client
    - rm -rf /root/.ssh/*
    - eval $(ssh-agent -s)
    - echo "$SSH_PRIVATE_KEY" | tr -d '\r' | ssh-add -
    - ssh-add -l
    - mkdir -p /root/.ssh
    - chmod -R 400 /root/.ssh
    - ssh-keyscan -H $SSH_HOST >> /root/.ssh/known_hosts
    - ssh-keyscan -H $SSH_HOST >> ~/.ssh/known_hosts
    - cat /root/.ssh/known_hosts
  script: |
    echo "Starting deployment script..."
    echo "Current branch: $CI_COMMIT_REF_NAME"
    echo "Target branch: $TARGET_BRANCH"
    if [ "$CI_COMMIT_REF_NAME" = "$TARGET_BRANCH" ]; then
      echo "Deploying to target branch: $TARGET_BRANCH"
      echo "Executing commands on server $SSH_USER@$SSH_HOST"

      ssh $SSH_USER@$SSH_HOST "
        set -e  # Exit immediately if a command exits with a non-zero status

        echo 'Setting up SSH key...'
        mkdir -p \"$BASE_FOLDER/.ssh\"
        echo \"$GITLAB_PRIVATE_KEY\" > \"$GITLAB_KEY_FILE\"
        chmod 600 \"$GITLAB_KEY_FILE\"

        export GIT_SSH_COMMAND='ssh -i \"$GITLAB_KEY_FILE\" -o StrictHostKeyChecking=no'

        if [ -d \"$APP_FOLDER/.git\" ]; then
          echo 'Repository already exists. Pulling latest changes...'
          cd \"$APP_FOLDER\"
          git fetch origin \"$TARGET_BRANCH\"
          git reset --hard \"origin/$TARGET_BRANCH\"
          echo 'Repository updated to latest commit.'
        else
          echo 'Repository does not exist. Cloning repository...'
          rm -rf \"$APP_FOLDER\"
          mkdir -p \"$APP_FOLDER\"
          git clone -b \"$TARGET_BRANCH\" --single-branch \"$REMOTE_REPO_URL\" \"$APP_FOLDER\"
          echo 'Repository cloned.'
          cd \"$APP_FOLDER\"
        fi

        echo 'Building application...'
        cd \"$APP_FOLDER\"

        echo 'Cleaning app tmp folder...'
        rm -rf "$APP_FOLDER/tmp" || sudo rm -rf "$APP_FOLDER/tmp"
        mkdir -p "$APP_FOLDER/tmp"
        echo 'App tmp folder cleaned.'

        npm install --os=linux --cpu=$CPU_ARCHITECTURE --include=optional --loglevel error
        rm -rf "$APP_FOLDER/build" || sudo rm -rf "$APP_FOLDER/build"
        npm run build
        cd build
        npm install --os=linux --cpu=$CPU_ARCHITECTURE --omit=dev --include=optional --loglevel error
        echo 'Application built.'

        echo 'Linking .env file...'
        ln -sf \"$ENV_FILE\" \"$APP_FOLDER/.env\"
        ln -sf \"$ENV_FILE\" \"$APP_FOLDER/build/.env\"
        echo '.env file linked.'

        echo 'Managing application with pm2...'
        pm2 describe \"$APP_NAME\" > /dev/null
        APP_RUNNING=\$?
        if [ \$APP_RUNNING -eq 0 ]; then
          echo 'Application is already running.'
          pm2 reload \"$APP_NAME\"
        else
          echo 'Application is not running.'
          pm2 delete \"$APP_NAME\"
          pm2 start \"$ENTRY_POINT\" --name \"$APP_NAME\"
        fi
        pm2 save
        echo 'Application managed by pm2.'
      "

      exit_code=$?
      echo "Deployment script finished with exit code $exit_code"

      if [ "$exit_code" -eq 0 ]; then
        echo "Deployment successful"
      else
        echo "Deployment failed with exit code $exit_code"
        echo "Performing rollback..."

        ssh $SSH_USER@$SSH_HOST "
          set -e
          echo 'Rolling back changes...'
          cd \"$APP_FOLDER\"
          git reset --hard HEAD~1
          echo 'Reset to previous commit.'
          npm install --os=linux --cpu=$CPU_ARCHITECTURE --omit=dev --include=optional --loglevel error
          npm run build
          echo 'Rebuilt application.'
          pm2 restart \"$APP_NAME\"
          pm2 save
          echo 'Application restarted after rollback.'
        "

        rollback_exit_code=$?
        echo "Rollback script finished with exit code $rollback_exit_code"

        if [ "$rollback_exit_code" -eq 0 ]; then
          echo "Rollback successful"
          exit 1
        else
          echo "Rollback failed with exit code $rollback_exit_code"
          exit 1
        fi
      fi
    else
      echo "Skipping deployment for branch $CI_COMMIT_REF_NAME"
    fi
