import env from '#start/env'
import { CrispStatusReporter } from 'crisp-status-reporter'
import fs from 'node:fs'
import type { ApplicationService } from '@adonisjs/core/types'

export default class AppProvider {
  constructor(protected app: ApplicationService) {}

  /**
   * Register bindings to the container
   */
  register() {}

  /**
   * The container bindings have booted
   */
  async boot() {}

  /**
   * The application has been booted
   */
  async start() {}

  /**
   * The process has been started
   */
  async ready() {
    if (this.app.getEnvironment() !== 'web') {
      return
    }

    // Clean BullMQ
    // console.log('Clean BullMQ')
    // await queue.clear('training')
    // await queue.closeAll()

    if (fs.existsSync(this.app.tmpPath())) {
      try {
        fs.readdirSync(this.app.tmpPath()).forEach((file) => {
          fs.rmSync(`${this.app.tmpPath()}/${file}`, { recursive: true, force: true })
        })
      } catch (error) {
        console.error(error)
      }
    } else {
      fs.mkdirSync(this.app.tmpPath())
    }

    if (env.get('NODE_ENV') === 'production') {
      try {
        new CrispStatusReporter({
          token: env.get('CRISP_REPORTER_TOKEN'),
          service_id: env.get('CRISP_REPORTER_SERVICE_ID'),
          node_id: env.get('CRISP_REPORTER_NODE_ID'),
          replica_id: env.get('CRISP_REPORTER_REPLICA_ID'),
          interval: 30,
        })
      } catch (error) {
        console.error(error)
      }
    }

    /*
    try {
      const ace = await this.app.container.make('ace')

      await ace.exec('queue:listen', [])
    } catch (error) {
      console.error(error)
    }
    */

    try {
      const ace = await this.app.container.make('ace')

      await ace.exec('scheduler:run', [])
    } catch (error) {
      console.error(error)
    }
  }

  /**
   * Preparing to shutdown the app
   */
  async shutdown() {}
}
