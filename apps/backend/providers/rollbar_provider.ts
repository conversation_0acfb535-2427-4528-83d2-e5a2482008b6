import env from '#start/env'
import Rollbar from 'rollbar'
import type { ApplicationService } from '@adonisjs/core/types'

export default class RollbarProvider {
  constructor(protected app: ApplicationService) {}

  /**
   * Register bindings to the container
   */
  register() {
    if (env.get('NODE_ENV') !== 'production') {
      return
    }

    if (this.app.getEnvironment() !== 'web') {
      return
    }

    const rollbar = new Rollbar({
      accessToken: env.get('ROLLBAR_ACCESS_TOKEN'),
      addErrorContext: true,
      captureDeviceInfo: true,
      autoInstrument: true,
      captureIp: true,
      captureUncaught: true,
      captureUnhandledRejections: true,
      enabled: true,
      environment: env.get('NODE_ENV'),
      ignoreDuplicateErrors: true,
      includeItemsInTelemetry: true,
      nodeSourceMaps: true,
      verbose: false,
      checkIgnore: function (isUncaught: boolean, args: any[], payload: any) {
        const arg1 = args[0] || ''

        let errorString

        if (typeof arg1 === 'string') {
          errorString = arg1
        } else if (arg1 instanceof Error) {
          errorString = arg1.message
        } else {
          try {
            errorString = JSON.stringify(arg1)
          } catch (e) {
            errorString = 'Unknown error'
          }
        }

        const errorsToIgnore = [
          'Lock file is already being held',
          'Lock is not acquired/owned by you',
          'E_ROUTE_NOT_FOUND',
          'E_UNAUTHORIZED_ACCESS',
          'E_ROW_NOT_FOUND',
          '.lock',
          'Row not found',
          'ELOCKED',
          'invalid input syntax for type uuid',
          'Unable to update lock within the stale threshold',
        ]

        if (
          errorsToIgnore.some((error) =>
            errorString?.toLocaleLowerCase()?.includes(error?.toLocaleLowerCase())
          )
        ) {
          return true
        }

        return false
      },
    })

    this.app.container.singleton('rollbar', () => rollbar as Rollbar)
  }

  /**
   * The container bindings have booted
   */
  async boot() {}

  /**
   * The application has been booted
   */
  async start() {}

  /**
   * The process has been started
   */
  async ready() {}

  /**
   * Preparing to shutdown the app
   */
  async shutdown() {}
}
