import env from '#start/env'
import admin from 'firebase-admin'
import { applicationDefault } from 'firebase-admin/app'
import type { ApplicationService } from '@adonisjs/core/types'

export default class FirebaseProvider {
  constructor(protected app: ApplicationService) {}

  /**
   * Register bindings to the container
   */
  register() {
    this.app.container.singleton('firebase', () => {
      admin.initializeApp({
        credential: applicationDefault(),
        databaseURL: env.get('FIREBASE_DATABASE_URL'),
      })

      return admin.messaging()
    })
  }

  /**
   * The container bindings have booted
   */
  async boot() {}

  /**
   * The application has been booted
   */
  async start() {}

  /**
   * The process has been started
   */
  async ready() {}

  /**
   * Preparing to shutdown the app
   */
  async shutdown() {}
}
