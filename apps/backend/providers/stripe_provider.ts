import env from '#start/env'
import Stripe from 'stripe'
import type { ApplicationService } from '@adonisjs/core/types'

export default class StripeProvider {
  constructor(protected app: ApplicationService) {}

  /**
   * Register bindings to the container
   */
  register() {
    this.app.container.singleton('stripe', () => {
      return new Stripe(
        env.get('NODE_ENV') === 'production'
          ? env.get('STRIPE_PRODUCTION_SECRET_KEY')
          : env.get('STRIPE_LOCAL_SECRET_KEY'),
        {
          apiVersion: env.get('STRIPE_API_VERSION') as Stripe.LatestApiVersion,
          typescript: true,
          maxNetworkRetries: 3,
          timeout: 60 * 1000,
          telemetry: false,
        }
      )
    })
  }

  /**
   * The container bindings have booted
   */
  async boot() {}

  /**
   * The application has been booted
   */
  async start() {}

  /**
   * The process has been started
   */
  async ready() {}

  /**
   * Preparing to shutdown the app
   */
  async shutdown() {}
}
