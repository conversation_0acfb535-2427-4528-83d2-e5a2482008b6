import { defineConfig } from '@adonisjs/core/app'

export default defineConfig({
  /*
  |--------------------------------------------------------------------------
  | Commands
  |--------------------------------------------------------------------------
  |
  | List of ace commands to register from packages. The application commands
  | will be scanned automatically from the "./commands" directory.
  |
  */
  commands: [
    () => import('@adonisjs/core/commands'),
    () => import('@adonisjs/lucid/commands'),
    () => import('@adonisjs/mail/commands'),
    () => import('adonisjs-scheduler/commands'),
    () => import('adonis-lucid-filter/commands'),
    () => import('@tuyau/core/commands'),
    () => import('@tuyau/openapi/commands')
  ],

  /*
  |--------------------------------------------------------------------------
  | Service providers
  |--------------------------------------------------------------------------
  |
  | List of service providers to import and register when booting the
  | application
  |
  */
  providers: [
    {
      file: () => import('@adonisjs/core/providers/repl_provider'),
      environment: ['repl', 'test'],
    },
    () => import('@adonisjs/lucid/database_provider'),
    () => import('@adonisjs/cors/cors_provider'),
    () => import('@adonisjs/auth/auth_provider'),
    () => import('@adonisjs/mail/mail_provider'),
    {
      file: () => import('adonisjs-scheduler/scheduler_provider'),
      environment: ['console', 'web'],
    },
    () => import('@adonisjs/ally/ally_provider'),
    () => import('@adonisjs/drive/drive_provider'),
    () => import('@adonisjs/lock/lock_provider'),
    () => import('adonis-lucid-filter/provider'),
    () => import('@adonisjs/core/providers/hash_provider'),
    () => import('@adonisjs/redis/redis_provider'),
    () => import('@adonisjs/core/providers/vinejs_provider'),
    () => import('@adonisjs/core/providers/app_provider'),
    () => import('#providers/app_provider'),
    () => import('#providers/rollbar_provider'),
    () => import('#providers/stripe_provider'),
    () => import('#providers/firebase_provider'),
    () => import('#providers/job_provider'),
    () => import('@tuyau/core/tuyau_provider'),
    () => import('#providers/transmit_provider'),
    () => import('@tuyau/openapi/openapi_provider')
  ],

  /*
  |--------------------------------------------------------------------------
  | Preloads
  |--------------------------------------------------------------------------
  |
  | List of modules to import before starting the application.
  |
  */
  preloads: [() => import('#start/routes'), () => import('#start/kernel')],

  /*
  |--------------------------------------------------------------------------
  | Tests
  |--------------------------------------------------------------------------
  |
  | List of test suites to organize tests by their type. Feel free to remove
  | and add additional suites.
  |
  */
  tests: {
    suites: [
      {
        files: ['tests/unit/**/*.spec(.ts|.js)'],
        name: 'unit',
        timeout: 2000,
      },
      {
        files: ['tests/functional/**/*.spec(.ts|.js)'],
        name: 'functional',
        timeout: 30000,
      },
    ],
    forceExit: false,
  },
})
