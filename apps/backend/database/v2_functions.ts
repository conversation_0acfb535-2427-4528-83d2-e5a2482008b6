import { QueryClientContract } from '@adonisjs/lucid/types/database'

export const addColumnIfNotExists = async (
  db: QueryClientContract,
  tableName: string,
  columnName: string,
  columnType: string,
  options: string = ''
) => {
  try {
    // Use direct SQL query to check if column exists to avoid transaction issues
    const result = await db
      .rawQuery(
        `SELECT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = '${tableName}'
        AND column_name = '${columnName}'
        AND table_schema = current_schema()
      )`
      )
      .exec()

    const hasColumn = result.rows[0].exists

    if (!hasColumn) {
      console.log(`Adding column ${columnName} to ${tableName}`)
      await db
        .rawQuery(
          `ALTER TABLE ${tableName} ADD COLUMN IF NOT EXISTS ${columnName} ${columnType} ${options}`
        )
        .exec()
      console.log(`Successfully added column ${columnName}`)
    } else {
      console.log(`Column ${columnName} already exists in ${tableName}`)
    }
  } catch (error) {
    console.error(`Error adding column ${columnName}:`, error)
  }
}

export const dropColumnIfExists = async (
  db: QueryClientContract,
  tableName: string,
  columnName: string
) => {
  try {
    // Use direct SQL query to check if column exists to avoid transaction issues
    const result = await db
      .rawQuery(
        `SELECT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = '${tableName}'
        AND column_name = '${columnName}'
        AND table_schema = current_schema()
      )`
      )
      .exec()

    const hasColumn = result.rows[0].exists

    if (hasColumn) {
      console.log(`Dropping column ${columnName} from ${tableName}`)
      await db.rawQuery(`ALTER TABLE ${tableName} DROP COLUMN IF EXISTS ${columnName}`).exec()
      console.log(`Successfully dropped column ${columnName}`)
    } else {
      console.log(`Column ${columnName} does not exist in ${tableName}`)
    }
  } catch (error) {
    console.error(`Error dropping column ${columnName}:`, error)
    // Continue with migration even if this column fails
  }
}

export const renameColumnIfExists = async (
  db: QueryClientContract,
  tableName: string,
  oldName: string,
  newName: string
) => {
  try {
    // First check if the column exists using a separate connection
    // that won't be part of the main transaction
    const hasColumn = await db
      .rawQuery(
        `SELECT EXISTS (
            SELECT 1
            FROM information_schema.columns
            WHERE table_name = '${tableName}'
            AND column_name = '${oldName}'
            AND table_schema = current_schema()
          )`
      )
      .exec()

    const columnExists = hasColumn.rows[0]?.exists || false

    // Also check if the new column name already exists
    const hasNewColumn = await db
      .rawQuery(
        `SELECT EXISTS (
            SELECT 1
            FROM information_schema.columns
            WHERE table_name = '${tableName}'
            AND column_name = '${newName}'
            AND table_schema = current_schema()
          )`
      )
      .exec()

    const newColumnExists = hasNewColumn.rows[0]?.exists || false

    if (columnExists && !newColumnExists) {
      console.log(`Renaming column ${oldName} to ${newName} in ${tableName}`)
      await db.rawQuery(`ALTER TABLE ${tableName} RENAME COLUMN ${oldName} TO ${newName}`).exec()
      console.log(`Successfully renamed column ${oldName} to ${newName}`)
    } else if (!columnExists) {
      console.log(`Column ${oldName} does not exist in ${tableName}`)
    } else if (newColumnExists) {
      console.log(`Column ${newName} already exists in ${tableName}, skipping rename`)
    }
  } catch (error) {
    console.error(`Error renaming column ${oldName} to ${newName}:`, error)

    throw error
  }
}
