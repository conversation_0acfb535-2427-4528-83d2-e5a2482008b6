import { BaseSchema } from '@adonisjs/lucid/schema'
import { addColumnIfNotExists, dropColumnIfExists } from '../../v2_functions.js'

export default class extends BaseSchema {
  async renameTableIfExists(oldName: string, newName: string) {
    try {
      const hasTable = await this.schema.hasTable(oldName)
      console.log(`Checking if ${oldName} table exists:`, hasTable)

      if (hasTable) {
        console.log(`Attempting to rename ${oldName} to ${newName}`)
        await this.db.rawQuery(`ALTER TABLE ${oldName} RENAME TO ${newName}`).exec()
        console.log(`Successfully renamed ${oldName} to ${newName}`)
      } else {
        console.log(`Table ${oldName} does not exist, skipping rename operation`)
      }
    } catch (error) {
      console.error(`Error renaming table ${oldName} to ${newName}:`, error)
    }
  }

  async up() {
    console.log('Starting table rename migration...')

    // Rename widgets to agents
    await this.renameTableIfExists('widgets', 'agents')

    // Rename widget_providers to agent_providers
    await this.renameTableIfExists('widget_providers', 'agent_providers')
  }
}
