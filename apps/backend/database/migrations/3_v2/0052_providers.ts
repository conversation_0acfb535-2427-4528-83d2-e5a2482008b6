import { BaseSchema } from '@adonisjs/lucid/schema'
import { addColumnIfNotExists, dropColumnIfExists } from '../../v2_functions.js'

export default class extends BaseSchema {
  protected tableName = 'providers'

  async createIndexIfNotExists(columnName: string) {
    try {
      const hasColumn = await this.schema.hasColumn(this.tableName, columnName)
      if (hasColumn) {
        console.log(`Creating index on ${columnName} in ${this.tableName}`)
        await this.db
          .rawQuery(
            `CREATE INDEX IF NOT EXISTS ${this.tableName}__${columnName} ON ${this.tableName} (${columnName})`
          )
          .exec()
        console.log(`Successfully created index on ${columnName}`)
      } else {
        console.log(
          `Column ${columnName} does not exist in ${this.tableName}, skipping index creation`
        )
      }
    } catch (error) {
      console.error(`Error creating index on ${columnName}:`, error)
    }
  }

  async up() {
    console.log('Starting providers table migration...')

    // Add new columns
    await addColumnIfNotExists(this.db, this.tableName, 'mode', 'boolean', 'NOT NULL DEFAULT true')
    await addColumnIfNotExists(this.db, this.tableName, 'storage_config', 'jsonb', 'NULL')

    // Create indexes
    await this.createIndexIfNotExists('type')
  }
}
