import { BaseSchema } from '@adonisjs/lucid/schema'
import { addColumnIfNotExists, dropColumnIfExists } from '../../v2_functions.js'

export default class extends BaseSchema {
  protected tableName = 'chats'

  async up() {
    console.log('Starting chats table migration...')

    // Drop old columns
    const columnsToDrop = ['avatar_location', 'agents', 'analysis', 'model_uid']

    for (const column of columnsToDrop) {
      await dropColumnIfExists(this.db, this.tableName, column)
    }
  }

  async down() {
    console.log('Rolling back chats table migration...')
    // Add rollback logic if needed
  }
}
