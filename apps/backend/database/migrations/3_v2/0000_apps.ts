import { BaseSchema } from '@adonisjs/lucid/schema'
import {
  addColumnIfNotExists,
  dropColumnIfExists,
  renameColumnIfExists,
} from '../../v2_functions.js'

export default class extends BaseSchema {
  protected tableName = 'apps'

  async up() {
    console.log('Starting apps table migration...')

    // Add new columns
    await addColumnIfNotExists(this.db, this.tableName, 'gclid', 'varchar', 'NULL')
    await addColumnIfNotExists(
      this.db,
      this.tableName,
      'is_banned',
      'boolean',
      'NOT NULL DEFAULT false'
    )
    await addColumnIfNotExists(this.db, this.tableName, 'utm', 'jsonb', 'NULL')

    // Drop old columns
    const columnsToDrop = [
      'mode',
      'alerts',
      'available_training_words',
      'sender_name',
      'sender_email',
      'smtp_host',
      'smtp_port',
      'smtp_username',
      'smtp_password',
      'addons',
      'customer',
      'subscription',
      'subscriptions',
    ]

    for (const column of columnsToDrop) {
      await dropColumnIfExists(this.db, this.tableName, column)
    }

    // Rename columns
    await renameColumnIfExists(
      this.db,
      this.tableName,
      'is_first_cancellation',
      'is_first_time_churning'
    )

    await renameColumnIfExists(
      this.db,
      this.tableName,
      'is_first_subscription',
      'is_first_time_customer'
    )
  }
}
