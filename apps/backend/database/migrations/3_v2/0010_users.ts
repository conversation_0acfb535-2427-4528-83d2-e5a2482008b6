import { BaseSchema } from '@adonisjs/lucid/schema'
import {
  addColumnIfNotExists,
  dropColumnIfExists,
  renameColumnIfExists,
} from '../../v2_functions.js'

export default class extends BaseSchema {
  protected tableName = 'users'

  async up() {
    console.log('Starting users table migration...')

    // Drop old columns
    const columnsToDrop = [
      'gclid',
      'is_banned',
      'remember_me_token',
      'confirm_token',
      'is_confirmed',
      'google',
      'survey',
      'tracking',
      'utm',
    ]

    for (const column of columnsToDrop) {
      await dropColumnIfExists(this.db, this.tableName, column)
    }

    // Rename columns
    await renameColumnIfExists(this.db, this.tableName, 'assigned_widgets', 'assigned_agents')
  }
}
