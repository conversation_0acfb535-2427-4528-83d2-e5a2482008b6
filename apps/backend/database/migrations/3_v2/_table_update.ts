import { BaseSchema } from '@adonisjs/lucid/schema'
import {
  addColumnIfNotExists,
  dropColumnIfExists,
  renameColumnIfExists,
} from '../../v2_functions.js'

export default class extends BaseSchema {
  async up() {
    console.log('Starting migration...')

    const tables = [
      'tools',
      'providers',
      'agent_providers',
      'sources',
      'chats',
      'messages',
      'contacts',
      'feedbacks',
      'traces',
      'files',
    ]

    for (const table of tables) {
      console.log(`\n=== Processing ${table} table ===`)
      await renameColumnIfExists(this.db, table, 'widget_uid', 'agent_uid')
    }

    // Handle the UUID default values
    console.log('\n=== Setting UUID defaults ===')
    const tableOperations = [
      { table: 'apps', column: 'uid' },
      { table: 'users', column: 'uid' },
      { table: 'subscriptions', column: 'uid' },
      { table: 'agents', column: 'uid' },
      { table: 'tools', column: 'uid' },
      { table: 'providers', column: 'uid' },
      { table: 'sources', column: 'uid' },
      { table: 'chats', column: 'uid' },
      { table: 'messages', column: 'uid' },
      { table: 'models', column: 'uid' },
      { table: 'contacts', column: 'uid' },
      { table: 'feedbacks', column: 'uid' },
      { table: 'traces', column: 'uid' },
      { table: 'files', column: 'uid' },
      { table: 'google_ads', column: 'uid' },
      { table: 'copyrights', column: 'uid' },
      { table: 'domains', column: 'uid' },
      { table: 'smtps', column: 'uid' },
    ]

    for (const op of tableOperations) {
      try {
        if (
          (await this.schema.hasTable(op.table)) &&
          (await this.schema.hasColumn(op.table, op.column))
        ) {
          console.log(`Setting UUID default for ${op.table}.${op.column}`)
          await this.db
            .rawQuery(
              `ALTER TABLE ${op.table} ALTER COLUMN ${op.column} SET DEFAULT uuid_generate_v4()`
            )
            .exec()
          console.log(`Successfully set UUID default for ${op.table}.${op.column}`)
        } else {
          console.log(
            `Table ${op.table} or column ${op.column} does not exist, skipping default value update`
          )
        }
      } catch (error) {
        console.error(`Error setting UUID default for ${op.table}.${op.column}:`, error)
      }
    }
  }
}
