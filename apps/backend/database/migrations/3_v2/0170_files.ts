import { BaseSchema } from '@adonisjs/lucid/schema'
import {
  addColumnIfNotExists,
  dropColumnIfExists,
  renameColumnIfExists,
} from '../../v2_functions.js'

export default class extends BaseSchema {
  protected tableName = 'files'

  async up() {
    console.log('Starting files table migration...')

    try {
      await addColumnIfNotExists(this.db, this.tableName, 'extname', 'varchar', 'NULL')

      // Rename columns if they exist
      await renameColumnIfExists(this.db, this.tableName, 'extension', 'extname')
      await renameColumnIfExists(this.db, this.tableName, 'file_name', 'name')
      await renameColumnIfExists(this.db, this.tableName, 'file_location', 'location')
      await renameColumnIfExists(this.db, this.tableName, 'file_size', 'size')
      await renameColumnIfExists(this.db, this.tableName, 'md5', 'checksum')
      await renameColumnIfExists(this.db, this.tableName, 'file_md5', 'checksum')

      // Drop old columns
      const columnsToDrop = ['is_public', 'file_base64', 'public_url']

      for (const column of columnsToDrop) {
        await dropColumnIfExists(this.db, this.tableName, column)
      }

      console.log('Files table migration completed successfully')
    } catch (error) {
      console.error('Error in files table migration:', error)
      throw error
    }
  }

  async down() {}
}
