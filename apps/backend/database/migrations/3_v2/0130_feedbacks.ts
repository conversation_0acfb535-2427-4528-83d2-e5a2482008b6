import { BaseSchema } from '@adonisjs/lucid/schema'
import { addColumnIfNotExists, dropColumnIfExists } from '../../v2_functions.js'

export default class extends BaseSchema {
  protected tableName = 'feedbacks'

  async up() {
    console.log('Starting chats table migration...')

    // Drop old columns
    const columnsToDrop = ['user_feedback']

    for (const column of columnsToDrop) {
      await dropColumnIfExists(this.db, this.tableName, column)
    }
  }

  async down() {}
}
