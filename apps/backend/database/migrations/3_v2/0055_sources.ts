import { BaseSchema } from '@adonisjs/lucid/schema'
import { addColumnIfNotExists, dropColumnIfExists } from '../../v2_functions.js'

export default class extends BaseSchema {
  protected tableName = 'sources'

  async up() {
    console.log('Starting sources table migration...')

    try {
      // Make sure the table exists
      const tableExists = await this.db
        .rawQuery(
          `SELECT EXISTS (
          SELECT 1 FROM information_schema.tables
          WHERE table_name = '${this.tableName}'
          AND table_schema = current_schema()
        )`
        )
        .exec()

      if (!tableExists.rows[0].exists) {
        console.log(`Table ${this.tableName} does not exist, skipping migration`)
        return
      }

      // First, try to add new columns (except type which might conflict)
      await addColumnIfNotExists(this.db, this.tableName, 'youtube', 'text', 'NULL')
      await addColumnIfNotExists(this.db, this.tableName, 'document', 'jsonb', 'NULL')
      await addColumnIfNotExists(this.db, this.tableName, 'media', 'jsonb', 'NULL')
      await addColumnIfNotExists(this.db, this.tableName, 'quizz', 'jsonb', 'NULL')

      // Now handle columns to drop one by one with individual transactions
      const columnsToDrop = [
        'questions',
        'model',
        'word_count',
        'chunk_size',
        'characters',
        'tokens',
        'credits',
      ]

      for (const column of columnsToDrop) {
        try {
          // Use a direct query with IF EXISTS to avoid transaction issues
          await this.db
            .rawQuery(`ALTER TABLE ${this.tableName} DROP COLUMN IF EXISTS ${column}`)
            .exec()
          console.log(`Dropped column ${column} if it existed`)
        } catch (error) {
          console.error(`Error dropping column ${column}:`, error)
          // Continue with next column
        }
      }
    } catch (error) {
      console.error('Error in migration:', error)
    }
  }
}
