import { BaseSchema } from '@adonisjs/lucid/schema'
import { addColumnIfNotExists, dropColumnIfExists } from '../../v2_functions.js'

export default class extends BaseSchema {
  // Add this line to disable transactions for this migration
  static disableTransactions = true

  protected tableName = 'agents'

  async up() {
    console.log('Starting agents table migration...')

    await addColumnIfNotExists(this.db, this.tableName, 'gtm_id', 'string', 'NULL')
    await addColumnIfNotExists(this.db, this.tableName, 'onboarding', 'jsonb', 'NULL')
    await addColumnIfNotExists(
      this.db,
      this.tableName,
      'is_vision_capable',
      'boolean',
      'DEFAULT true'
    )

    // Drop old columns
    const columnsToDrop = [
      'switcher_agents',
      'is_vocal_only',
      'model_vision_uid',
      'type',
      'prompt_user',
      'prompt_append',
      'max_tokens',
      'top_p',
      'stream',
      'logprobs',
      'echo',
      'stop',
      'best_of',
      'logit_bias',
      'user',
      'document_relevance',
      'html_interpreter',
      'tts_answers',
      'presence_penalty',
      'frequency_penalty',
      'backup_settings',
      'backup_agents',
      'is_public',
      'is_nsfw',
      'sender_name',
      'sender_email',
      'smtp_host',
      'smtp_port',
      'smtp_username',
      'smtp_password',
      'openai_key',
      'anthropic_key',
      'openrouter_key',
      'whatsapp',
      'welcome_screen',
      'blocked_words',
      'gdpr',
      'copyright',
      'settings',
      'agents',
      'custom_css',
      'code_head',
      'code_body',
    ]

    for (const column of columnsToDrop) {
      await dropColumnIfExists(this.db, this.tableName, column)
    }

    // Commented out rename operations for future reference
    /*
    const columnsToRename = [
      { old: 'settings', new: 'legacy_settings' },
      { old: 'writing_language', new: 'legacy_writing_language' },
      { old: 'writing_tone', new: 'legacy_writing_tone' },
      { old: 'writing_style', new: 'legacy_writing_style' },
      { old: 'writing_format', new: 'legacy_writing_format' },
      { old: 'writing_length', new: 'legacy_writing_length' },
      { old: 'company_name', new: 'legacy_company_name' },
      { old: 'company_description', new: 'legacy_company_description' },
      { old: 'company_website', new: 'legacy_company_website' },
      { old: 'company_address', new: 'legacy_company_address' },
      { old: 'company_phone', new: 'legacy_company_phone' },
      { old: 'company_email', new: 'legacy_company_email' },
      { old: 'page_title', new: 'legacy_page_title' },
      { old: 'page_description', new: 'legacy_page_description' }
    ]
    */
  }
}
