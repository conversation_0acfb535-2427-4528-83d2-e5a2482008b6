import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'files'

  async up() {
    this.schema.alterTable(this.tableName, (table) => {
      table.uuid('app_uid').notNullable().unsigned().references('apps.uid').onDelete('CASCADE')
      table.uuid('agent_uid').notNullable().unsigned().references('agents.uid').onDelete('CASCADE')
      table.uuid('chat_uid').notNullable().unsigned().references('chats.uid').onDelete('CASCADE')
      table.uuid('message_uid').nullable().unsigned().references('messages.uid')

      table.index('app_uid', 'files__app_uid', 'btree')
      table.index('agent_uid', 'files__agent_uid', 'btree')
      table.index('chat_uid', 'files__chat_uid', 'btree')
      table.index('message_uid', 'files__message_uid', 'btree')
    })
  }
}
