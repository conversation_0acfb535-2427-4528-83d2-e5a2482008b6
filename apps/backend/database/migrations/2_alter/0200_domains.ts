import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'domains'

  async up() {
    this.schema.alterTable(this.tableName, (table) => {
      table.uuid('app_uid').references('uid').inTable('apps').onDelete('CASCADE')
      table.uuid('agent_uid').references('uid').inTable('agents').onDelete('CASCADE')

      table.index('app_uid', 'domains__app_uid', 'btree')
      table.index('agent_uid', 'domains__agent_uid', 'btree')
    })
  }

  async down() {}
}
