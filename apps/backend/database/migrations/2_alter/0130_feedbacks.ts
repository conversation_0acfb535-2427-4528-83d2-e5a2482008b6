import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'feedbacks'

  async up() {
    this.schema.alterTable(this.tableName, (table) => {
      table.uuid('app_uid').notNullable().unsigned().references('apps.uid').onDelete('CASCADE')
      table.uuid('agent_uid').notNullable().unsigned().references('agents.uid').onDelete('CASCADE')
      table.uuid('chat_uid').notNullable().unsigned().references('chats.uid').onDelete('CASCADE')
      table.uuid('message_uid').notNullable().unsigned().references('messages.uid')

      table.index('app_uid', 'feedbacks__app_uid', 'btree')
      table.index('agent_uid', 'feedbacks__agent_uid', 'btree')
      table.index('chat_uid', 'feedbacks__chat_uid', 'btree')
      table.index('message_uid', 'feedbacks__message_uid', 'btree')
    })
  }
}
