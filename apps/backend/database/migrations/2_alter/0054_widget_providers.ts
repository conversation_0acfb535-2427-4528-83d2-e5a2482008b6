import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'agent_providers'

  async up() {
    this.schema.alterTable(this.tableName, (table) => {
      table.uuid('agent_uid').notNullable().unsigned().references('agents.uid').onDelete('CASCADE')
      table
        .uuid('provider_uid')
        .notNullable()
        .unsigned()
        .references('providers.uid')
        .onDelete('CASCADE')

      table.unique(['agent_uid', 'provider_uid'])
      table.index('agent_uid', 'agent_providers__agent_uid', 'btree')
      table.index('provider_uid', 'agent_providers__provider_uid', 'btree')
    })
  }
}
