import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'google_ads'

  async up() {
    this.schema.alterTable(this.tableName, (table) => {
      table.uuid('app_uid').references('apps.uid').unsigned().nullable().onDelete('NO ACTION')
      table.uuid('user_uid').references('users.uid').unsigned().nullable().onDelete('NO ACTION')

      table.index('app_uid', 'google_ads__app_uid', 'btree')
      table.index('user_uid', 'google_ads__user_uid', 'btree')
    })
  }
}
