import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'sources'

  async up() {
    this.schema.alterTable(this.tableName, (table) => {
      table.uuid('app_uid').notNullable().unsigned().references('apps.uid').onDelete('CASCADE')
      table.uuid('agent_uid').notNullable().unsigned().references('agents.uid').onDelete('CASCADE')
      table
        .uuid('provider_uid')
        .notNullable()
        .unsigned()
        .references('providers.uid')
        .onDelete('CASCADE')
      table.index('app_uid', 'sources__app_uid', 'btree')
      table.index('agent_uid', 'sources__agent_uid', 'btree')
      table.index('provider_uid', 'sources__provider_uid', 'btree')
      table.index('status', 'sources__status', 'btree')
    })
  }
}
