import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'traces'

  async up() {
    this.schema.alterTable(this.tableName, (table) => {
      table.uuid('app_uid').unsigned().nullable()
      table.uuid('user_uid').unsigned().nullable()
      table.uuid('agent_uid').unsigned().nullable()
      table.uuid('chat_uid').unsigned().nullable()
      table.uuid('message_uid').unsigned().nullable()
      table.uuid('contact_uid').unsigned().nullable()
      table.uuid('feedback_uid').unsigned().nullable()
      table.uuid('provider_uid').unsigned().nullable()

      table.index('app_uid', 'traces__app_uid', 'btree')
      table.index('user_uid', 'traces__user_uid', 'btree')
      table.index('agent_uid', 'traces__agent_uid', 'btree')
      table.index('chat_uid', 'traces__chat_uid', 'btree')
      table.index('message_uid', 'traces__message_uid', 'btree')
      table.index('contact_uid', 'traces__contact_uid', 'btree')
      table.index('feedback_uid', 'traces__feedback_uid', 'btree')
      table.index('provider_uid', 'traces__provider_uid', 'btree')
    })
  }
}
