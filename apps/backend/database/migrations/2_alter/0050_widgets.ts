import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'agents'

  async up() {
    this.schema.alterTable(this.tableName, (table) => {
      table.uuid('app_uid').notNullable().unsigned().references('apps.uid').onDelete('CASCADE')
      table.uuid('model_uid').nullable().unsigned().references('models.uid').defaultTo(null)
      table.uuid('copyright_uid').references('uid').inTable('copyrights').onDelete('CASCADE')
      table.uuid('domain_uid').references('uid').inTable('domains').onDelete('CASCADE')
      table.uuid('smtp_uid').references('uid').inTable('smtps').onDelete('CASCADE')

      table.index('app_uid', 'agents__app_uid', 'btree')
      table.index('model_uid', 'agents__model_uid', 'btree')
      table.index('copyright_uid', 'agents__copyright_uid', 'btree')
      table.index('domain_uid', 'agents__domain_uid', 'btree')
      table.index('smtp_uid', 'agents__smtp_uid', 'btree')
    })
  }
}
