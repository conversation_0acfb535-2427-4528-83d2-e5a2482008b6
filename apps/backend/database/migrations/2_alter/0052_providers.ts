import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'providers'

  async up() {
    this.schema.alterTable(this.tableName, (table) => {
      table.uuid('app_uid').notNullable().unsigned().references('apps.uid').onDelete('CASCADE')
      table.uuid('agent_uid').notNullable().unsigned().references('agents.uid').onDelete('CASCADE')

      table.index('app_uid', 'providers__app_uid', 'btree')
      table.index('agent_uid', 'providers__agent_uid', 'btree')
      table.index('type', 'providers__type', 'btree')
      table.index('status', 'providers__status', 'btree')
    })
  }
}
