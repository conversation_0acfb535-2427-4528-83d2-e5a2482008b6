import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'subscriptions'

  async up() {
    this.schema.alterTable(this.tableName, (table) => {
      table.uuid('app_uid').notNullable().unsigned().references('apps.uid').onDelete('CASCADE')

      table.index('app_uid', 'subscriptions__app_uid', 'btree')
      table.index('stripe_customer_id', 'subscriptions__stripe_customer_id', 'btree')
    })
  }
}
