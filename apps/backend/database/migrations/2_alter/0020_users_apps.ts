import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'user_apps'

  async up() {
    this.schema.alterTable(this.tableName, (table) => {
      table.uuid('app_uid').notNullable().unsigned().references('apps.uid').onDelete('CASCADE')
      table.uuid('user_uid').notNullable().unsigned().references('users.uid').onDelete('CASCADE')

      table.unique(['app_uid', 'user_uid'])

      table.index('app_uid', 'user_apps__app_uid', 'btree')
      table.index('user_uid', 'user_apps__user_uid', 'btree')
    })
  }
}
