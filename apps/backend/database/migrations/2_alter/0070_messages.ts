import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'messages'

  async up() {
    this.schema.alterTable(this.tableName, (table) => {
      table.uuid('app_uid').notNullable().unsigned().references('apps.uid').onDelete('CASCADE')
      table.uuid('agent_uid').notNullable().unsigned().references('agents.uid').onDelete('CASCADE')
      table.uuid('chat_uid').notNullable().unsigned().references('chats.uid').onDelete('CASCADE')
      table.uuid('model_uid').notNullable().unsigned().references('models.uid')

      table.index('app_uid', 'messages__app_uid', 'btree')
      table.index('agent_uid', 'messages__agent_uid', 'btree')
      table.index('chat_uid', 'messages__chat_uid', 'btree')
      table.index('model_uid', 'messages__model_uid', 'btree')
    })
  }
}
