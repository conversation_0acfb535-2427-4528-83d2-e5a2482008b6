import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'contacts'

  async up() {
    this.schema.alterTable(this.tableName, (table) => {
      table.uuid('app_uid').notNullable().unsigned().references('apps.uid').onDelete('CASCADE')
      table.uuid('agent_uid').notNullable().unsigned().references('agents.uid').onDelete('CASCADE')
      table.uuid('chat_uid').nullable().unsigned().references('chats.uid').defaultTo(null)

      table.index('app_uid', 'contacts__app_uid', 'btree')
      table.index('agent_uid', 'contacts__agent_uid', 'btree')
      table.index('chat_uid', 'contacts__chat_uid', 'btree')
    })
  }
}
