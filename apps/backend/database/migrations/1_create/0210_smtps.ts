import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'smtps'

  async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.uuid('uid').primary().defaultTo(this.db.rawQuery('uuid_generate_v4()').knexQuery)
      table.string('host').notNullable()
      table.integer('port').notNullable()
      table.string('username').notNullable()
      table.string('password').notNullable()
      table.boolean('secure').defaultTo(true)
      table.string('from_email').notNullable()
      table.string('from_name').notNullable()
      table.boolean('is_verified').defaultTo(false)
      table.boolean('is_primary').defaultTo(false)
      table.jsonb('metadata').nullable()
      table.timestamp('created_at', { useTz: true }).notNullable().defaultTo(this.now())
      table.timestamp('updated_at', { useTz: true }).nullable()
      table.timestamp('deleted_at', { useTz: true }).nullable()
    })
  }

  async down() {
    this.schema.dropTable(this.tableName)
  }
}
