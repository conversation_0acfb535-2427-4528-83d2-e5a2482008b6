import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'chats'

  async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.uuid('uid').primary().defaultTo(this.db.rawQuery('uuid_generate_v4()').knexQuery)
      table.string('external_id').unique().nullable()
      table.string('label').notNullable()
      table.boolean('is_hidden').notNullable().defaultTo(false)
      table.boolean('is_archived').notNullable().defaultTo(false)
      table.boolean('is_favorite').notNullable().defaultTo(false)
      table.boolean('is_resolve').notNullable().defaultTo(false)
      table.boolean('is_email').notNullable().defaultTo(false)
      table.boolean('is_whatsapp').notNullable().defaultTo(false)
      table.boolean('has_feedback').notNullable().defaultTo(false)
      table.boolean('has_contact').notNullable().defaultTo(false)
      table.boolean('has_user_tagging').notNullable().defaultTo(false)
      table.boolean('has_messages').notNullable().defaultTo(false)
      table.boolean('is_ai_disabled').notNullable().defaultTo(false)
      table.text('loaded_context').nullable()
      table.jsonb('loaded_variables').nullable()
      table.jsonb('metadata').nullable()
      table.timestamp('created_at', { useTz: true }).notNullable().defaultTo(this.now())
      table.timestamp('updated_at', { useTz: true }).nullable()
      table.timestamp('deleted_at', { useTz: true }).nullable()
    })
  }

  async down() {
    this.schema.dropTable(this.tableName)
  }
}
