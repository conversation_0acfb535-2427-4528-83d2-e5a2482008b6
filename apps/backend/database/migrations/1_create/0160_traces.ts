import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'traces'

  async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.uuid('uid').primary().defaultTo(this.db.rawQuery('uuid_generate_v4()').knexQuery)
      table.string('trigger').nullable()
      table.string('action').nullable()
      table.integer('credits').nullable()
      table.string('label').nullable()
      table.string('description').nullable()
      table.string('request_method').nullable()
      table.string('request_url').nullable()
      table.jsonb('request_params').nullable()
      table.jsonb('request_qs').nullable()
      table.jsonb('request_body').nullable()
      table.jsonb('location').nullable()
      table.string('response').nullable()
      table.string('ip').nullable()
      table.string('status').nullable()
      table.jsonb('metadata').nullable()
      table.timestamp('created_at', { useTz: true }).notNullable().defaultTo(this.now())
      table.timestamp('updated_at', { useTz: true }).nullable()
      table.timestamp('deleted_at', { useTz: true }).nullable()
    })
  }

  async down() {
    this.schema.dropTable(this.tableName)
  }
}
