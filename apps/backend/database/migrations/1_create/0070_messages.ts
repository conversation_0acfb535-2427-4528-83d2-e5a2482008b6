import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'messages'

  async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.uuid('uid').primary().defaultTo(this.db.rawQuery('uuid_generate_v4()').knexQuery)
      table.string('role').notNullable()
      table.text('output_text').nullable()
      table.text('output_form').nullable()
      table.text('agent_choice').nullable()
      table.integer('credits').notNullable()
      table.text('context').nullable()
      table.string('tts_file_url').nullable()
      table.specificType('agents', 'text[]').nullable().defaultTo(this.raw('ARRAY[]::text[]'))
      table
        .specificType('suggestions', 'jsonb[]')
        .nullable()
        .defaultTo(this.raw('ARRAY[]::jsonb[]'))
      table.jsonb('sources').nullable()
      table.jsonb('metadata').nullable()
      table.timestamp('created_at', { useTz: true }).notNullable().defaultTo(this.now())
      table.timestamp('updated_at', { useTz: true }).nullable()
      table.timestamp('deleted_at', { useTz: true }).nullable()
    })
  }

  async down() {
    this.schema.dropTable(this.tableName)
  }
}
