import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'agents'

  async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.uuid('uid').primary().defaultTo(this.db.rawQuery('uuid_generate_v4()').knexQuery)
      table.string('model_embedding').notNullable().defaultTo('text-embedding-3-small')
      table.string('type').notNullable().defaultTo('chat')
      table.string('label').notNullable()
      table.string('purpose').notNullable()
      table.string('timezone').notNullable().defaultTo('America/New_York')
      table.text('prompt_system').nullable()
      table.string('context_behavior').notNullable().defaultTo('answer_with_context')
      table.float('temperature').notNullable().defaultTo(1)
      table.string('password').nullable()
      table.boolean('conversation_saver').defaultTo(true)
      table.boolean('source_discloser').defaultTo(false)
      table.string('voice').nullable().defaultTo('alloy')
      table.boolean('debug').defaultTo(false)
      table.boolean('is_vision_capable').defaultTo(true)
      table.boolean('hide_agent').defaultTo(false)
      table.boolean('hide_inbox').defaultTo(false)
      table.boolean('hide_navbar').defaultTo(false)
      table.boolean('hide_urls').defaultTo(false)
      table.boolean('hide_filenames').defaultTo(true)
      table.string('gtm_id').nullable()
      table.integer('credits_per_month').nullable()
      table.string('webhook_endpoint').nullable()
      table.specificType('greetings', 'jsonb[]').nullable().defaultTo(this.raw('ARRAY[]::jsonb[]'))
      table
        .specificType('suggestions', 'jsonb[]')
        .nullable()
        .defaultTo(this.raw('ARRAY[]::jsonb[]'))
      table
        .specificType('notifications', 'jsonb[]')
        .nullable()
        .defaultTo(this.raw('ARRAY[]::jsonb[]'))
      table.jsonb('mobile_app').nullable()
      table.jsonb('bubble').nullable()
      table.jsonb('buttons').nullable()
      table.jsonb('styling').nullable()
      table.jsonb('images').nullable()
      table.jsonb('whatsapp').nullable()
      table.jsonb('onboarding').nullable()
      table.jsonb('metadata').nullable()
      table.timestamp('created_at', { useTz: true }).notNullable().defaultTo(this.now())
      table.timestamp('updated_at', { useTz: true }).nullable()
      table.timestamp('deleted_at', { useTz: true }).nullable()
    })
  }

  async down() {
    this.schema.dropTable(this.tableName)
  }
}
