import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'users'

  async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.uuid('uid').primary().defaultTo(this.db.rawQuery('uuid_generate_v4()').knexQuery)
      table.string('email').notNullable().unique()
      table.string('password').notNullable()
      table.string('role').notNullable()
      table.string('first_name').nullable()
      table.string('last_name').nullable()
      table.string('civility').nullable()
      table.string('referrer_email').nullable()
      table.string('crisp_id').nullable()
      table.jsonb('metadata').nullable()
      table
        .specificType('assigned_agents', 'text[]')
        .nullable()
        .defaultTo(this.raw('ARRAY[]::text[]'))
      table.timestamp('last_activity_at', { useTz: true }).notNullable().defaultTo(this.now())
      table.timestamp('created_at', { useTz: true }).notNullable().defaultTo(this.now())
      table.timestamp('updated_at', { useTz: true }).nullable()
      table.timestamp('deleted_at', { useTz: true }).nullable()
    })
  }

  async down() {
    this.schema.dropTable(this.tableName)
  }
}
