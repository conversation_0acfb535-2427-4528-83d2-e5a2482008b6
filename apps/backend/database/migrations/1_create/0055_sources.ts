import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'sources'

  async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.uuid('uid').primary().defaultTo(this.db.rawQuery('uuid_generate_v4()').knexQuery)
      table.string('type').notNullable()
      table.text('url').nullable()
      table.text('youtube').nullable()
      table.text('text').nullable()
      table.jsonb('document').nullable()
      table.jsonb('media').nullable()
      table.jsonb('quizz').nullable()
      table.jsonb('product').nullable()
      table.text('content_original').nullable()
      table.text('content_enhanced').nullable()
      table.text('content_summary').nullable()
      table.string('status').nullable()
      table.jsonb('metadata').nullable()
      table.timestamp('created_at', { useTz: true }).notNullable().defaultTo(this.now())
      table.timestamp('updated_at', { useTz: true }).nullable()
      table.timestamp('deleted_at', { useTz: true }).nullable()
    })
  }

  async down() {
    this.schema.dropTable(this.tableName)
  }
}
