import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'google_ads'

  async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.uuid('uid').primary().defaultTo(this.db.rawQuery('uuid_generate_v4()').knexQuery)
      table.string('gclid').notNullable()
      table.string('stripe_customer_id').notNullable()
      table.string('email').notNullable()
      table.string('conversionName').notNullable()
      table.timestamp('conversion_time', { useTz: true }).notNullable().defaultTo(this.now())
      table.integer('conversionValue').notNullable()
      table.string('conversionCurrency').notNullable()
      table.jsonb('metadata').nullable()
      table.timestamp('created_at', { useTz: true }).notNullable().defaultTo(this.now())
      table.timestamp('updated_at', { useTz: true }).nullable()
      table.timestamp('deleted_at', { useTz: true }).nullable()
    })
  }

  async down() {
    this.schema.dropTable(this.tableName)
  }
}
