import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'contacts'

  async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.uuid('uid').primary().defaultTo(this.db.rawQuery('uuid_generate_v4()').knexQuery)
      table.string('first_name').nullable()
      table.string('last_name').nullable()
      table.string('email').nullable()
      table.string('phone').nullable()
      table.string('address').nullable()
      table.string('website').nullable()
      table.string('gender').nullable()
      table.string('company').nullable()
      table.string('job_title').nullable()
      table.string('job_role').nullable()
      table.text('message').nullable()
      table.timestamp('created_at', { useTz: true }).notNullable().defaultTo(this.now())
      table.timestamp('updated_at', { useTz: true }).nullable()
      table.timestamp('deleted_at', { useTz: true }).nullable()
    })
  }

  async down() {
    this.schema.dropTable(this.tableName)
  }
}
