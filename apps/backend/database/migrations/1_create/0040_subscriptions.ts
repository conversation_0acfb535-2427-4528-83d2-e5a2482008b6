import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'subscriptions'

  async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.uuid('uid').primary().defaultTo(this.db.rawQuery('uuid_generate_v4()').knexQuery)
      table.string('stripe_customer_id').nullable()
      table.jsonb('plan').nullable()
      table.jsonb('copyrights').nullable()
      table.jsonb('domains').nullable()
      table.jsonb('credits').nullable()
      table.jsonb('agents').nullable()
      table.jsonb('seats').nullable()
      table.jsonb('urls').nullable()
      table.jsonb('documents').nullable()
      table.jsonb('smtps').nullable()
      table.jsonb('metadata').nullable()
      table.timestamp('created_at', { useTz: true }).notNullable().defaultTo(this.now())
      table.timestamp('updated_at', { useTz: true }).nullable()
      table.timestamp('deleted_at', { useTz: true }).nullable()
    })
  }

  async down() {
    this.schema.dropTable(this.tableName)
  }
}
