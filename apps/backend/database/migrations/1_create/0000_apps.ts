import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'apps'

  async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.uuid('uid').primary().defaultTo(this.db.rawQuery('uuid_generate_v4()').knexQuery)
      table.integer('available_credits').notNullable().defaultTo(0)
      table.string('logo').nullable()
      table.string('favicon').nullable()
      table.string('stripe_customer_id').nullable().unique()
      table.boolean('is_banned').notNullable().defaultTo(false)
      table.boolean('is_first_time_customer').defaultTo(false)
      table.boolean('is_first_time_churning').defaultTo(false)
      table.jsonb('auto_topup').nullable()
      table.string('appsumo_license').nullable()
      table.string('gclid').nullable()
      table.jsonb('utm').nullable()
      table.jsonb('metadata').nullable()
      table.timestamp('created_at', { useTz: true }).notNullable().defaultTo(this.now())
      table.timestamp('updated_at', { useTz: true }).nullable()
      table.timestamp('deleted_at', { useTz: true }).nullable()
    })
  }

  async down() {
    this.schema.dropTable(this.tableName)
  }
}
