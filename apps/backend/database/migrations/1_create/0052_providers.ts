import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'providers'

  async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.uuid('uid').primary().defaultTo(this.db.rawQuery('uuid_generate_v4()').knexQuery)
      table.string('label').nullable()
      table.string('type').notNullable()
      table.string('language').nullable()
      table.integer('fetch_every_hours').notNullable().defaultTo(0)
      table.jsonb('storage_config').nullable()
      table.boolean('mode').notNullable().defaultTo(true)
      table.string('status').nullable()
      table.jsonb('metadata').nullable()
      table.timestamp('last_synch_at', { useTz: true }).nullable()
      table.timestamp('created_at', { useTz: true }).notNullable().defaultTo(this.now())
      table.timestamp('updated_at', { useTz: true }).nullable()
      table.timestamp('deleted_at', { useTz: true }).nullable()
    })
  }

  async down() {
    this.schema.dropTable(this.tableName)
  }
}
