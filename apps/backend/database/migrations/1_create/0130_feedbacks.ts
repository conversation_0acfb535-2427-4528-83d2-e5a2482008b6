import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'feedbacks'

  async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.uuid('uid').primary().defaultTo(this.db.rawQuery('uuid_generate_v4()').knexQuery)
      table.text('user_message').nullable()
      table.text('ai_message').notNullable()
      table.string('status').notNullable()
      table.boolean('is_trained').defaultTo(false).notNullable()
      table.timestamp('created_at', { useTz: true }).notNullable().defaultTo(this.now())
      table.timestamp('updated_at', { useTz: true }).nullable()
      table.timestamp('deleted_at', { useTz: true }).nullable()
    })
  }

  async down() {
    this.schema.dropTable(this.tableName)
  }
}
