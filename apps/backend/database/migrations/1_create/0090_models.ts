import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'models'

  async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.uuid('uid').primary().defaultTo(this.db.rawQuery('uuid_generate_v4()').knexQuery)
      table.integer('rank').notNullable()
      table.string('label').notNullable()
      table.string('key').nullable()
      table.string('model_openai').nullable()
      table.string('model_openrouter').nullable()
      table.string('model_anthropic').nullable()
      table.text('company').nullable()
      table.text('help').nullable()
      table.integer('credits').notNullable()
      table.integer('context').notNullable()
      table.boolean('is_default').notNullable().defaultTo(false)
      table.boolean('is_unfiltered').notNullable().defaultTo(false)
      table.boolean('is_connected').notNullable().defaultTo(false)
      table.boolean('is_beta').notNullable().defaultTo(false)
      table.boolean('is_text').notNullable().defaultTo(false)
      table.boolean('is_vision').notNullable().defaultTo(false)
      table.boolean('is_agent').notNullable().defaultTo(false)
      table.boolean('streaming').notNullable().defaultTo(true)
      table.timestamp('created_at', { useTz: true }).notNullable().defaultTo(this.now())
      table.timestamp('updated_at', { useTz: true }).nullable()
      table.timestamp('deleted_at', { useTz: true }).nullable()
    })
  }

  async down() {
    this.schema.dropTable(this.tableName)
  }
}
