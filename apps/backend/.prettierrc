{"trailingComma": "es5", "semi": false, "singleQuote": true, "useTabs": false, "quoteProps": "consistent", "bracketSpacing": true, "arrowParens": "always", "printWidth": 100, "plugins": ["@trivago/prettier-plugin-sort-imports", "prettier-plugin-edgejs"], "importOrder": ["^@adonisjs/(.*)$", "^#app/interfaces", "^#controllers", "^#app/modules/(.*)/(.*)_model$", "^#app/modules/(.*)/(.*)_service$", "^#app/modules/(.*)/(.*)_controllers$", "^#app/modules/(.*)/(.*)_validator$", "^#app/modules/(.*)$", "^[./]"], "importOrderParserPlugins": ["typescript", "jsx", "decorators"], "importOrderSeparation": false, "importOrderSortSpecifiers": true, "importOrderGroupNamespaceSpecifiers": true, "importOrderCaseInsensitive": true}