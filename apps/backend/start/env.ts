import { Env } from '@adonisjs/core/env'

export default await Env.create(new URL('../', import.meta.url), {
  // Server
  TZ: Env.schema.string(),
  HOST: Env.schema.string({ format: 'host' }),
  PORT: Env.schema.number(),
  NODE_ENV: Env.schema.enum(['development', 'production', 'testing'] as const),
  LOG_LEVEL: Env.schema.enum(['fatal', 'error', 'warn', 'info', 'debug', 'trace']),
  // App
  APP_KEY: Env.schema.string(),
  APP_NAME: Env.schema.string(),
  APP_EMAIL: Env.schema.string(),
  APP_PAGINATION_LIMIT: Env.schema.number(),
  ADMIN_PASSWORD: Env.schema.string(),
  CLIENT_URL: Env.schema.string(),
  EMBED_URL: Env.schema.string(),
  // Database
  DB_DEFAULT_CONNECTION: Env.schema.string(),
  DB_PRODUCTION_HOST: Env.schema.string({ format: 'host' }),
  DB_PRODUCTION_PORT: Env.schema.number(),
  DB_PRODUCTION_USER: Env.schema.string(),
  DB_PRODUCTION_PASSWORD: Env.schema.string(),
  DB_PRODUCTION_DB_NAME: Env.schema.string(),
  DB_PRODUCTION_SSL: Env.schema.boolean(),
  DB_LOCAL_HOST: Env.schema.string({ format: 'host' }),
  DB_LOCAL_PORT: Env.schema.number(),
  DB_LOCAL_USER: Env.schema.string(),
  DB_LOCAL_PASSWORD: Env.schema.string.optional(),
  DB_LOCAL_DB_NAME: Env.schema.string(),
  DB_LOCAL_SSL: Env.schema.boolean(),
  // Redis
  LOCK_STORE: Env.schema.enum(['redis', 'memory'] as const),
  QUEUE_REDIS_HOST: Env.schema.string(),
  QUEUE_REDIS_PORT: Env.schema.number(),
  QUEUE_REDIS_PASSWORD: Env.schema.string.optional(),
  QUEUE_REDIS_DB: Env.schema.number(),
  // CDN
  CDN_ENDPOINT: Env.schema.string(),
  CDN_LOGO_URL: Env.schema.string(),
  CDN_FAVICON_URL: Env.schema.string(),
  // Cloudflare Storage
  DRIVE_DISK: Env.schema.enum(['s3_private', 's3_public'] as const),
  S3_KEY: Env.schema.string(),
  S3_SECRET: Env.schema.string(),
  S3_BUCKET_PRIVATE: Env.schema.string(),
  S3_BUCKET_PUBLIC: Env.schema.string(),
  S3_REGION: Env.schema.string(),
  S3_ENDPOINT: Env.schema.string(),
  // Hetzner Storage
  S3_DEFAULT_KEY: Env.schema.string(),
  S3_DEFAULT_SECRET: Env.schema.string(),
  S3_DEFAULT_ENDPOINT: Env.schema.string(),
  S3_DEFAULT_KNOWLEDGE_BUCKET: Env.schema.string(),
  S3_DEFAULT_AGENTS_BUCKET: Env.schema.string(),
  S3_DEFAULT_APPS_BUCKET: Env.schema.string(),
  S3_DEFAULT_CHATS_BUCKET: Env.schema.string(),
  // SMTP
  SMTP_HOST: Env.schema.string({ format: 'host' }),
  SMTP_PORT: Env.schema.number(),
  SMTP_USERNAME: Env.schema.string(),
  SMTP_PASSWORD: Env.schema.string(),
  SENDER_EMAIL: Env.schema.string(),
  SENDER_NAME: Env.schema.string(),
  SENDER_LOGO: Env.schema.string(),
  // Rollbar
  ROLLBAR_ACCESS_TOKEN: Env.schema.string(),
  // Stripe
  STRIPE_API_VERSION: Env.schema.string(),
  STRIPE_PRODUCTION_SECRET_KEY: Env.schema.string(),
  STRIPE_PRODUCTION_PUBLIC_KEY: Env.schema.string(),
  STRIPE_PRODUCTION_WEBHOOK_SECRET: Env.schema.string(),
  STRIPE_LOCAL_SECRET_KEY: Env.schema.string(),
  STRIPE_LOCAL_PUBLIC_KEY: Env.schema.string(),
  STRIPE_LOCAL_WEBHOOK_SECRET: Env.schema.string(),
  // Crisp
  CRISP_REPORTER_TOKEN: Env.schema.string(),
  CRISP_REPORTER_SERVICE_ID: Env.schema.string(),
  CRISP_REPORTER_NODE_ID: Env.schema.string(),
  CRISP_REPORTER_REPLICA_ID: Env.schema.string(),
  CRISP_APP_IDENTIFIER: Env.schema.string(),
  CRISP_APP_KEY: Env.schema.string(),
  CRISP_APP_WEBSITE_ID: Env.schema.string(),
  // Training API
  TRAINING_API_URL: Env.schema.string(),
  // Gateway API
  GATEWAY_API_URL: Env.schema.string(),
  GATEWAY_API_KEY: Env.schema.string(),
  GATEWAY_DEFAULT_MODEL: Env.schema.string(),
  GATEWAY_RERANK_MODEL: Env.schema.string(),
  GATEWAY_EMBEDDING_MODEL: Env.schema.string(),
  GATEWAY_STT_MODEL: Env.schema.string(),
  GATEWAY_TTS_MODEL: Env.schema.string(),
  // Google Auth
  GOOGLE_CLIENT_ID: Env.schema.string(),
  GOOGLE_CLIENT_SECRET: Env.schema.string(),
  GOOGLE_REDIRECT_URI: Env.schema.string(),
  // Whatsapp
  WHATSAPP_API_BASE_URL: Env.schema.string(),
  WHATSAPP_API_SECRET: Env.schema.string(),
  // Cloudflare
  CLOUDFLARE_API_KEY: Env.schema.string(),
  CLOUDFLARE_ACCOUNT_ID: Env.schema.string(),
  // GROQ
  GROQ_API_KEY: Env.schema.string(),
  // Firebase
  FIREBASE_DATABASE_URL: Env.schema.string(),
  // Fal
  FAL_API_KEY: Env.schema.string(),
  // DataImpulse
  DATA_IMPULSE_LOGIN: Env.schema.string(),
  DATA_IMPULSE_PASSWORD: Env.schema.string(),
  DATA_IMPULSE_HOST: Env.schema.string(),
  DATA_IMPULSE_PORT_ROTATING: Env.schema.number(),
  DATA_IMPULSE_PORT_STICKY: Env.schema.number(),
})
