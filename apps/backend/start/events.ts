import TrainingJob from '#app/automations/jobs/training_job'
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, GoogleAds<PERSON>elper, <PERSON><PERSON><PERSON><PERSON> } from '#app/helpers'
import { transactionalEmailValidator } from '#app/validators'
import { Infer } from '@vinejs/vine/types'
import emitter from '@adonisjs/core/services/emitter'
import { ICreateGoogleConversion, ICreateTrace } from '#app/interfaces'
import App from '#app/modules/apps/app_model'
import User from '#app/modules/users/user_model'

// Error handler
emitter.onError((event, error, eventData) => {
  console.error({ event, error, eventData })
})

// Jobs
emitter.on('job:training', async (params: { uid: string }) => {
  const trainingJob = await TrainingJob.getInstance()

  await trainingJob.add(params.uid, { uid: params.uid })
})

// Emails
emitter.on('email:send', async (payload: Infer<typeof transactionalEmailValidator>) => {
  await EmailHelper.sendTransactionalEmail(payload)
})

// Crisp
/*
emitter.on(
  'crisp:push_event',
  async (params: { email: string; event: { text: string; data: object; color: string } }) => {
    if (params.email) {
      await CrispHelper.pushEvent(params.email, params.event)
    }
  }
)
*/

// Google Ads
emitter.on('google_ads:create', async (params: ICreateGoogleConversion) => {
  await GoogleAdsHelper.createConversion(params)
})

// Trace
emitter.on('trace:create', async (trace: ICreateTrace) => {
  await TraceHelper.createTrace(trace)
})

// Credits
emitter.on('credits:add', () => {
  console.log('credits:add')
})

emitter.on('credits:subtract', async (params: { app: App; credits: number }) => {
  const { app, credits } = params

  if (app) {
    await app
      .merge({
        available_credits: Math.max(app.available_credits - credits, 0),
      })
      .save()
  }
})

// Acticity
emitter.on('activity:update', async (user: User) => {
  await user.merge({ last_activity_at: DateHelper.getNow() }).save()
})
