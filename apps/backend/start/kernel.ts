/*
|--------------------------------------------------------------------------
| HTTP kernel file
|--------------------------------------------------------------------------
|
| The HTTP kernel file is used to register the middleware with the server
| or the router.
|
*/
import router from '@adonisjs/core/services/router'
import server from '@adonisjs/core/services/server'

/**
 * The error handler is used to convert an exception
 * to a HTTP response.
 */
server.errorHandler(() => import('#app/exceptions/handler'))

/**
 * The server middleware stack runs middleware on all the HTTP
 * requests, even if there is no route registered for
 * the request URL.
 */
server.use([
  () => import('#app/middleware/container_bindings_middleware'),
  () => import('#app/middleware/force_json_response_middleware'),
  () => import('@adonisjs/cors/cors_middleware'),
  // () => import('@tuyau/superjson/superjson_middleware')
])

/**
 * The router middleware stack runs middleware on all the HTTP
 * requests with a registered route.
 */
router.use([
  () => import('@adonisjs/core/bodyparser_middleware'),
  () => import('@adonisjs/auth/initialize_auth_middleware'),
  () => import('#app/middleware/silent_auth_middleware'),
])

/**
 * Named middleware collection must be explicitly assigned to
 * the routes or the routes group.
 */
export const middleware = router.named({
  app_ownership: () => import('#app/middleware/app_ownership_middleware'),
  auth: () => import('#app/middleware/auth_middleware'),
  container_bindings: () => import('#app/middleware/container_bindings_middleware'),
  force_json_response: () => import('#app/middleware/force_json_response_middleware'),
  silent_auth: () => import('#app/middleware/silent_auth_middleware'),
  no_timeout: () => import('#app/middleware/no_timeout_middleware'),
})
