import AutoTopUpsScheduler from '#app/automations/schedulers/auto_top_ups_scheduler'
// import CloudStorageScheduler from '#schedulers/cloud_storage_scheduler'
import IndexBuilderScheduler from '#app/automations/schedulers/index_builder_scheduler'
import MonthlyCreditsResetScheduler from '#app/automations/schedulers/monthly_credits_reset_scheduler'
import RefreshScheduler from '#app/automations/schedulers/refresh_scheduler'
import TrainingScheduler from '#app/automations/schedulers/training_scheduler'
import env from '#start/env'
import scheduler from 'adonisjs-scheduler/services/main'

scheduler.withoutOverlapping(() => {
  scheduler
    .call(() => {
      new AutoTopUpsScheduler().handle()
    })
    .cron('*/60 * * * *')
    .timezone(env.get('TZ'))
})

/*
scheduler.withoutOverlapping(() => {
  scheduler
    .call(() => {
      new CloudStorageScheduler().handle()
    })
    .cron('*\/60 * * * *')
    .timezone(env.get('TZ'))
})
*/

scheduler.withoutOverlapping(() => {
  scheduler
    .call(() => {
      new IndexBuilderScheduler().handle()
    })
    .cron('0 * * * *')
    .timezone(env.get('TZ'))
})

scheduler.withoutOverlapping(() => {
  scheduler
    .call(() => {
      new MonthlyCreditsResetScheduler().handle()
    })
    .cron('0 1 * * *')
    .timezone(env.get('TZ'))
})

scheduler.withoutOverlapping(() => {
  scheduler
    .call(() => {
      new RefreshScheduler().handle()
    })
    .cron('*/5 * * * *')
    .timezone(env.get('TZ'))
})

scheduler.withoutOverlapping(() => {
  scheduler
    .call(() => {
      new TrainingScheduler().handle()
    })
    .cron('*/3 * * * * *')
    .timezone(env.get('TZ'))
})
