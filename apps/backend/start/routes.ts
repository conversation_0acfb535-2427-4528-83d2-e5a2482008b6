/*
|--------------------------------------------------------------------------
| Routes file
|--------------------------------------------------------------------------
|
| The routes file is used for defining the HTTP routes.
|
*/
import { middleware } from '#start/kernel'
import vine from '@vinejs/vine'
import { HttpContext } from '@adonisjs/core/http'
import router from '@adonisjs/core/services/router'
import admin from '#app/modules/admin/admin_routes'
import AgentsController from '#app/modules/agents/agents_controller'
import agents from '#app/modules/agents/agents_routes'
import apps from '#app/modules/apps/apps_routes'
import auth from '#app/modules/auth/auth_routes'
import chats from '#app/modules/chats/chats_routes'
import contacts from '#app/modules/contacts/contacts_routes'
import embeds from '#app/modules/embeds/embeds_routes'
import feedbacks from '#app/modules/feedbacks/feedbacks_routes'
import firebase from '#app/modules/firebase/firebase_routes'
import hooks from '#app/modules/hooks/hooks_routes'
import models from '#app/modules/models/models_routes'
import open from '#app/modules/open/open_routes'
import providers from '#app/modules/rag/providers/providers_routes'
import sources from '#app/modules/rag/sources/sources_routes'
import stats from '#app/modules/stats/stats_routes'
import subscriptions from '#app/modules/subscriptions/subscriptions_routes'
import tools from '#app/modules/tools/tools_routes'
import users from '#app/modules/users/users_routes'
import whatsapp from '#app/modules/whatsapp/whatsapp_routes'
import whitelabel from '#app/modules/whitelabel/whitelabel_routes'

router.get('/', async () => {
  return {
    hello: 'world',
  }
})

// Auth not required
router
  .group(() => {
    auth(), hooks(), embeds(), models(), firebase(), open()
  })
  .prefix('/v2/')
  .use(middleware.silent_auth())

// Auth required
router
  .group(() => {
    admin()
  })
  .prefix('/v2/')
  .use(middleware.auth())

// Auth required
router
  .group(() => {
    subscriptions(),
      agents(),
      chats(),
      providers(),
      sources(),
      users(),
      contacts(),
      apps(),
      stats(),
      whatsapp(),
      feedbacks(),
      whitelabel(),
      tools()
  })
  .prefix('/v2/:app_uid')
  .use(middleware.auth())
  .use(middleware.app_ownership())
