### Test OpenAI Key
POST http://localhost:3000/v2/app_uid/keys/test
Content-Type: application/json
Authorization: Bearer your_token_here

{
  "provider": "openai",
  "key": "your_openai_key_here"
}

### Test Mistral Key
POST http://localhost:3000/v2/app_uid/keys/test
Content-Type: application/json
Authorization: Bearer your_token_here

{
  "provider": "mistral",
  "key": "your_mistral_key_here"
}

### Test Cohere Key
POST http://localhost:3000/v2/app_uid/keys/test
Content-Type: application/json
Authorization: Bearer your_token_here

{
  "provider": "cohere",
  "key": "your_cohere_key_here"
}

### Test Anthropic Key
POST http://localhost:3000/v2/app_uid/keys/test
Content-Type: application/json
Authorization: Bearer your_token_here

{
  "provider": "anthropic",
  "key": "your_anthropic_key_here"
}

### Test Groq Key
POST http://localhost:3000/v2/app_uid/keys/test
Content-Type: application/json
Authorization: Bearer your_token_here

{
  "provider": "groq",
  "key": "your_groq_key_here"
}

### Test OpenRouter Key
POST http://localhost:3000/v2/app_uid/keys/test
Content-Type: application/json
Authorization: Bearer your_token_here

{
  "provider": "openrouter",
  "key": "your_openrouter_key_here"
}

### Test All Keys for a specific key record
GET http://localhost:3000/v2/app_uid/keys/test-all/your_key_uid_here
Authorization: Bearer your_token_here
