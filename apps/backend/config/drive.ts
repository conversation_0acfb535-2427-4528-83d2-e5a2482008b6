import env from '#start/env'
import { defineConfig, services } from '@adonisjs/drive'

const driveConfig = defineConfig({
  default: env.get('DRIVE_DISK') as 's3_private' | 's3_public',

  /**
   * The services object can be used to configure multiple file system
   * services each using the same or a different driver.
   */
  services: {
    disk: services.fs({
      location: 'storage/app',
      visibility: 'private',
    }),
    s3_private: services.s3({
      credentials: {
        accessKeyId: env.get('S3_KEY'),
        secretAccessKey: env.get('S3_SECRET'),
      },
      region: env.get('S3_REGION'),
      bucket: env.get('S3_BUCKET_PRIVATE'),
      endpoint: env.get('S3_ENDPOINT'),
      visibility: 'private',
      forcePathStyle: true,
      responseChecksumValidation: 'WHEN_REQUIRED',
      requestChecksumCalculation: 'WHEN_REQUIRED',
    }),
    s3_public: services.s3({
      credentials: {
        accessKeyId: env.get('S3_KEY'),
        secretAccessKey: env.get('S3_SECRET'),
      },
      region: env.get('S3_REGION'),
      bucket: env.get('S3_BUCKET_PUBLIC'),
      endpoint: env.get('S3_ENDPOINT'),
      visibility: 'public',
      forcePathStyle: true,
      responseChecksumValidation: 'WHEN_REQUIRED',
      requestChecksumCalculation: 'WHEN_REQUIRED',
    }),
  },
})

export default driveConfig

declare module '@adonisjs/drive/types' {
  export interface IDriveDisks extends InferDriveDisks<typeof driveConfig> {}
}
