import env from '#start/env'

let stripeConfig = {}

if (env.get('DB_DEFAULT_CONNECTION') === 'production') {
  stripeConfig = {
    secretKey: env.get('STRIPE_PRODUCTION_SECRET_KEY'),
    publicKey: env.get('STRIPE_PRODUCTION_PUBLIC_KEY'),
    webhookSecret: env.get('STRIPE_PRODUCTION_WEBHOOK_SECRET'),
    options: {
      apiVersion: env.get('STRIPE_API_VERSION'),
      typescript: env.get('STRIPE_TYPESCRIPT', 'true') === 'true',
    },
  }
} else {
  stripeConfig = {
    secretKey: env.get('STRIPE_LOCAL_SECRET_KEY'),
    publicKey: env.get('STRIPE_LOCAL_PUBLIC_KEY'),
    webhookSecret: env.get('STRIPE_LOCAL_WEBHOOK_SECRET'),
    options: {
      apiVersion: env.get('STRIPE_API_VERSION'),
      typescript: env.get('STRIPE_TYPESCRIPT', 'true') === 'true',
    },
  }
}

export default stripeConfig
