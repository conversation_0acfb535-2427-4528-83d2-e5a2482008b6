import { defineConfig } from '@adonisjs/core/bodyparser'

const bodyParserConfig = defineConfig({
  /**
   * The bodyparser middleware will parse the request body
   * for the following HTTP methods.
   */
  allowedMethods: ['POST', 'PUT', 'PATCH', 'DELETE'],

  /**
   * Config for the "application/x-www-form-urlencoded"
   * content-type parser
   */
  form: {
    convertEmptyStringsToNull: true,
    types: ['application/x-www-form-urlencoded'],
  },

  /**
   * Config for the JSON parser
   */
  json: {
    convertEmptyStringsToNull: true,
    types: [
      'application/json',
      'application/json-patch+json',
      'application/vnd.api+json',
      'application/csp-report',
    ],
  },

  /**
   * Config for the "multipart/form-data" content-type parser.
   * File uploads are handled by the multipart parser.
   */
  multipart: {
    /**
     * Enabling auto process allows bodyparser middleware to
     * move all uploaded files inside the tmp folder of your
     * operating system
     */
    autoProcess: true,
    convertEmptyStringsToNull: true,
    processManually: [
      '/v2/:app_uid/providers/ingest/documents/:uid',
      '/v2/:app_uid/providers/ingest/medias/:uid',
      '/v2/:app_uid/agents/update-image/:uid',
      '/v2/:app_uid/domains/update-image/:uid',
      '/v2/embeds/audio/stt/:chat_uid',
      '/v2/embeds/store-files/:chat_uid',
    ],

    /**
     * Maximum limit of data to parse including all files
     * and fields
     */
    limit: '30mb',
    types: ['multipart/form-data'],
  },
})

export default bodyParserConfig
