import env from '#start/env'
import { defineConfig } from '@adonisjs/lucid'

const dbConfig = defineConfig({
  connection: env.get('DB_DEFAULT_CONNECTION'),
  connections: {
    local: {
      debug: true,
      client: 'pg',
      connection: {
        host: env.get('DB_LOCAL_HOST'),
        port: env.get('DB_LOCAL_PORT'),
        user: env.get('DB_LOCAL_USER'),
        password: env.get('DB_LOCAL_PASSWORD', ''),
        database: env.get('DB_LOCAL_DB_NAME'),
        ssl: env.get('DB_LOCAL_SSL') === false ? false : { rejectUnauthorized: false },
      },
      pool: {
        min: 1,
        max: 1,
        acquireTimeoutMillis: 10 * 1000,
      },
      migrations: {
        naturalSort: true,
        paths: ['database/migrations'],
      },
    },
    production: {
      debug: true,
      client: 'pg',
      connection: {
        host: env.get('DB_PRODUCTION_HOST'),
        port: env.get('DB_PRODUCTION_PORT'),
        user: env.get('DB_PRODUCTION_USER'),
        password: env.get('DB_PRODUCTION_PASSWORD', ''),
        database: env.get('DB_PRODUCTION_DB_NAME'),
        ssl: env.get('DB_PRODUCTION_SSL') === false ? false : { rejectUnauthorized: false },
      },
      pool: {
        min: 1,
        max: 50,
        acquireTimeoutMillis: 10 * 1000,
      },
      migrations: {
        naturalSort: true,
        paths: ['database/migrations'],
      },
    },
  },
})

export default dbConfig
