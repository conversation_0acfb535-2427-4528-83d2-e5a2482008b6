import {
  ADDONS_OPTIONS,
  AGENTS_BASIC_PLAN,
  AGENTS_BUSINESS_PLAN,
  AGENTS_ENTERPRISE_PLAN,
  AGENTS_STANDARD_PLAN,
  IS_PRODUCTION,
  MODELS_BY_KEY,
} from '#app/constants'
import { Infer } from '@vinejs/vine/types'
import {
  subscriptionPricingTableTemplateValidator,
  subscriptionProductValidator,
} from '#app/modules/subscriptions/subscription_validator'

export const products: Infer<typeof subscriptionProductValidator>[] = [
  {
    level: 1,
    id: 'simple',
    group: 'app',
    type: 'plan',
    billing_type: 'fixed',
    title: 'Simple',
    description: '',
    is_visible: false,
    is_popular: false,
    is_user_subscribed: false,
    min_quantity: 1,
    max_quantity: 1,
    monthly: {
      price: 19,
      default_stripe_price: IS_PRODUCTION
        ? 'price_1Mw57WF3j9uEmyHwvdTZ0cuX'
        : 'price_1MyCeWF3j9uEmyHwY1ZRD3bN',
      stripe_prices: ['price_1Mw57WF3j9uEmyHwvdTZ0cuX', 'price_1MyCeWF3j9uEmyHwY1ZRD3bN'],
    },
    yearly: {
      price: 15.83,
      default_stripe_price: IS_PRODUCTION
        ? 'price_1Mw57WF3j9uEmyHwCdPbyNwH'
        : 'price_1MyCeWF3j9uEmyHw48EmQG2w',
      stripe_prices: ['price_1Mw57WF3j9uEmyHwCdPbyNwH', 'price_1MyCeWF3j9uEmyHw48EmQG2w'],
    },
    pricing_table: [],
    limitations: {
      support: 'E-mail',
      agents: 1,
      credits: 1000,
      accounts: 0,
      knowledge: {
        urls: 50,
        youtube: 50,
        documents: 10,
        medias: 50,
        quizzes: 1000000,
        texts: 1000000,
        products: 0,
        storage: 0,
      },
      max_file_size: '5mb',
      is_ltd: false,
      is_free: false,
      copyrights: false,
      exports: false,
      auto_fetch: false,
      analytics: true,
      domains: 0,
      history: **********,
      speech_to_text: false,
      text_to_speech: true,
      mobile_app: true,
      seats: 0,
      byok: false,
      webhooks: false,
      tools: ['mark_as_resolved', 'capture_lead'],
      addons: [],
      models: MODELS_BY_KEY,
      location: false,
      logs: false,
      audio_mode: false,
      vision_mode: false,
    },
  },
  {
    level: 2,
    id: 'basic',
    group: 'app',
    type: 'plan',
    billing_type: 'fixed',
    title: 'Basic',
    description: '',
    is_visible: true,
    is_popular: false,
    is_user_subscribed: false,
    min_quantity: 1,
    max_quantity: 1,
    monthly: {
      price: 49,
      default_stripe_price: IS_PRODUCTION
        ? 'price_1OWuyOF3j9uEmyHwkF2Pezgv'
        : 'price_1MyCfSF3j9uEmyHwXa0RIXbK',
      stripe_prices: [
        'price_1PcwR7F3j9uEmyHwKK1oQypv',
        'price_1OWuyOF3j9uEmyHwkF2Pezgv',
        'price_1NhoKqF3j9uEmyHwiyEuaTbQ',
        'price_1MyCfSF3j9uEmyHwXa0RIXbK',
        'price_1MydSsF3j9uEmyHwfJ4DQ5Q7',
        'price_1NefmDF3j9uEmyHwkj4zQyMK',
      ],
    },
    yearly: {
      price: 39,
      default_stripe_price: IS_PRODUCTION
        ? 'price_1PQyYCF3j9uEmyHwhScf4mVp'
        : 'price_1MyCfSF3j9uEmyHwncAOCfjW',
      stripe_prices: [
        'price_1PQyYCF3j9uEmyHwhScf4mVp',
        'price_1OWuzeF3j9uEmyHw1hgZF6H4',
        'price_1NefmDF3j9uEmyHwjGoTR354',
        'price_1MyCfSF3j9uEmyHwncAOCfjW',
        'price_1MydTFF3j9uEmyHwKJTbSgW0',
      ],
    },
    pricing_table: [],
    limitations: {
      support: 'E-mail',
      agents: 2,
      credits: 4000,
      accounts: 1,
      knowledge: {
        urls: 200,
        youtube: 200,
        documents: 20,
        medias: 20,
        quizzes: 1000000,
        texts: 1000000,
        products: 0,
        storage: 0,
      },
      max_file_size: '10mb',
      is_ltd: false,
      is_free: false,
      copyrights: false,
      exports: false,
      auto_fetch: false,
      analytics: false,
      domains: 0,
      history: **********,
      speech_to_text: true,
      text_to_speech: true,
      mobile_app: true,
      seats: 1,

      byok: false,
      webhooks: false,
      tools: AGENTS_BASIC_PLAN,
      addons: ['branding'],
      models: MODELS_BY_KEY,
      location: false,
      logs: false,
      audio_mode: false,
      vision_mode: false,
    },
  },
  {
    level: 3,
    id: 'starter',
    group: 'app',
    type: 'plan',
    billing_type: 'fixed',
    title: 'Starter',
    description: '',
    is_visible: true,
    is_popular: false,
    is_user_subscribed: false,
    min_quantity: 1,
    max_quantity: 1,
    monthly: {
      price: 99,
      default_stripe_price: IS_PRODUCTION
        ? 'price_1OWv0gF3j9uEmyHwWZzDOGRy'
        : 'price_1MyCd1F3j9uEmyHwusFsjmon',
      stripe_prices: [
        'price_1OWv0gF3j9uEmyHwWZzDOGRy',
        'price_1PBwSZF3j9uEmyHwQEZVkg46',
        'price_1NefoQF3j9uEmyHwqOgQnFow',
        'price_1MyCd1F3j9uEmyHwusFsjmon',
        'price_1Mw5AgF3j9uEmyHwY5Hvjrtc',
      ],
    },
    yearly: {
      price: 79,
      default_stripe_price: IS_PRODUCTION
        ? 'price_1PQyX0F3j9uEmyHwd2YbflfV'
        : 'price_1MyCd1F3j9uEmyHwwUp2IKUF',
      stripe_prices: [
        'price_1PQyX0F3j9uEmyHwd2YbflfV',
        'price_1PBwSmF3j9uEmyHwqt5gVYtn',
        'price_1NefoQF3j9uEmyHwXp4ejE7V',
        'price_1MyCd1F3j9uEmyHwwUp2IKUF',
        'price_1Mw5AgF3j9uEmyHwMo750faK',
      ],
    },
    pricing_table: [],
    limitations: {
      support: 'Email & Live Chat',
      agents: 5,
      credits: 10000,
      accounts: 0,
      knowledge: {
        urls: 500,
        youtube: 500,
        documents: 50,
        medias: 50,
        quizzes: 1000000,
        texts: 1000000,
        products: 0,
        storage: 0,
      },
      max_file_size: '10mb',
      is_ltd: false,
      is_free: false,
      copyrights: false,
      exports: false,
      auto_fetch: true,
      analytics: true,
      domains: 0,
      history: **********,
      speech_to_text: true,
      text_to_speech: true,
      mobile_app: true,
      seats: 3,

      byok: false,
      webhooks: false,
      tools: AGENTS_STANDARD_PLAN,
      addons: ['branding', 'credits'],
      models: MODELS_BY_KEY,
      location: false,
      logs: false,
      audio_mode: true,
      vision_mode: true,
    },
  },
  {
    level: 4,
    id: 'pro',
    group: 'app',
    type: 'plan',
    billing_type: 'fixed',
    title: 'Pro',
    description: '',
    is_visible: false,
    is_popular: false,
    is_user_subscribed: false,
    min_quantity: 1,
    max_quantity: 1,
    monthly: {
      price: 299,
      default_stripe_price: IS_PRODUCTION
        ? 'price_1PQy8YF3j9uEmyHwVgLV2IJC'
        : 'price_1NsSyiF3j9uEmyHwmckABeIB',
      stripe_prices: [
        'price_1PQy8YF3j9uEmyHwVgLV2IJC',
        'price_1NefpdF3j9uEmyHwSMtpGgpz',
        'price_1NsSyiF3j9uEmyHwmckABeIB',
      ],
    },
    yearly: {
      price: 239,
      default_stripe_price: IS_PRODUCTION
        ? 'price_1PQy95F3j9uEmyHwwZt9PZiS'
        : 'price_1NsSyiF3j9uEmyHwlMAYRchV',
      stripe_prices: [
        'price_1PQy95F3j9uEmyHwwZt9PZiS',
        'price_1NefpdF3j9uEmyHwqJgbKUik',
        'price_1NsSyiF3j9uEmyHwlMAYRchV',
      ],
    },
    pricing_table: [],
    limitations: {
      support: 'Email & Live Chat',
      agents: 5,
      credits: 15000,
      accounts: 5,
      knowledge: {
        urls: 3000,
        youtube: 500,
        documents: 150,
        medias: 150,
        quizzes: 1000000,
        texts: 1000000,
        products: 150,
        storage: 0,
      },
      max_file_size: '15mb',
      is_ltd: false,
      is_free: false,
      copyrights: false,
      exports: false,
      auto_fetch: true,
      analytics: true,
      domains: 0,
      history: **********,
      speech_to_text: true,
      text_to_speech: true,
      mobile_app: true,
      seats: 2,

      byok: false,
      webhooks: false,
      tools: AGENTS_STANDARD_PLAN,
      addons: ['branding', 'credits'],
      models: MODELS_BY_KEY,
      location: false,
      logs: false,
      audio_mode: true,
      vision_mode: true,
    },
  },
  {
    level: 5,
    id: 'business',
    group: 'app',
    type: 'plan',
    billing_type: 'fixed',
    title: 'Business',
    description: 'For power users and small teams who need more features.',
    is_visible: true,
    is_popular: true,
    is_user_subscribed: false,
    min_quantity: 1,
    max_quantity: 1,
    monthly: {
      price: 399,
      default_stripe_price: IS_PRODUCTION
        ? 'price_1OWv9vF3j9uEmyHwrzDoyEtg'
        : 'price_1NsSzHF3j9uEmyHwIXD8fmJ9',
      stripe_prices: [
        'price_1PLQy0F3j9uEmyHwsl0I6yE7',
        'price_1OWv9vF3j9uEmyHwrzDoyEtg',
        'price_1OAXB7F3j9uEmyHwjac49iXr',
        'price_1NqLTeF3j9uEmyHwCgzuPlQH',
        'price_1NsSzHF3j9uEmyHwIXD8fmJ9',
        'price_1N810IF3j9uEmyHwt2LIhkTm',
      ],
    },
    yearly: {
      price: 299,
      default_stripe_price: IS_PRODUCTION
        ? 'price_1PgReVF3j9uEmyHw0TFV1vGh'
        : 'price_1NsSzHF3j9uEmyHwYhvSqrbB',
      stripe_prices: [
        'price_1PgReVF3j9uEmyHw0TFV1vGh',
        'price_1PQyV9F3j9uEmyHwV6WBXx88',
        'price_1PLQz4F3j9uEmyHwhwGfGixT',
        'price_1OWvAfF3j9uEmyHwHwVzc9dI',
        'price_1NqLUBF3j9uEmyHwFUjr9DtA',
        'price_1NsSzHF3j9uEmyHwYhvSqrbB',
        'price_1N80zqF3j9uEmyHwbscCDIp0',
      ],
    },
    pricing_table: [],
    limitations: {
      support: 'Live Priority',
      agents: 10,
      credits: 30000,
      accounts: 500,
      knowledge: {
        urls: 5000,
        youtube: 5000,
        documents: 500,
        medias: 500,
        quizzes: 1000000,
        texts: 1000000,
        products: 500,
        storage: 0,
      },
      max_file_size: '15mb',
      is_ltd: false,
      is_free: false,
      copyrights: true,
      exports: true,
      auto_fetch: true,
      analytics: true,
      domains: 0,
      history: **********,
      speech_to_text: true,
      text_to_speech: true,
      mobile_app: true,
      seats: 5,
      byok: true,
      webhooks: true,
      tools: AGENTS_BUSINESS_PLAN,
      addons: ADDONS_OPTIONS,
      models: MODELS_BY_KEY,
      location: false,
      logs: false,
      audio_mode: true,
      vision_mode: true,
    },
  },
  {
    level: 6,
    id: 'enterprise',
    group: 'app',
    type: 'plan',
    billing_type: 'fixed',
    title: 'Enterprise',
    description: 'Advanced features and dedicated support for large organizations.',
    is_visible: true,
    is_popular: false,
    is_user_subscribed: false,
    min_quantity: 1,
    max_quantity: 1,
    monthly: {
      price: 899,
      default_stripe_price: IS_PRODUCTION
        ? 'price_1OWvDRF3j9uEmyHw9iqJu3zF'
        : 'price_1OWxANF3j9uEmyHwjdJEd6CZ',
      stripe_prices: [
        'price_1PLR0hF3j9uEmyHw6D04XpqJ',
        'price_1OWvDRF3j9uEmyHw9iqJu3zF',
        'price_1OAXCEF3j9uEmyHwX33xcGKq',
        'price_1NqLWmF3j9uEmyHw5MLZkZV3',
        'price_1NsSztF3j9uEmyHwFReleHOC',
        'price_1NefsTF3j9uEmyHw1yhecA7J',
        'price_1OWxANF3j9uEmyHwjdJEd6CZ',
      ],
    },
    yearly: {
      price: 669,
      default_stripe_price: IS_PRODUCTION
        ? 'price_1PgRkjF3j9uEmyHwXQlg9we9'
        : 'price_1OWxANF3j9uEmyHwjdJEd6CZ',
      stripe_prices: [
        'price_1PgRkjF3j9uEmyHwXQlg9we9',
        'price_1PLZXdF3j9uEmyHwK8OsFNfO',
        'price_1P9B1wF3j9uEmyHw8hKg8C6Z',
        'price_1OAXCuF3j9uEmyHwIjB3A5Nj',
        'price_1NqLWmF3j9uEmyHw1gT6woj6',
        'price_1NsSztF3j9uEmyHwtpoyDRCV',
        'price_1NqLO0F3j9uEmyHwPgXttp5F',
        'price_1OWxANF3j9uEmyHwjdJEd6CZ',
      ],
    },
    pricing_table: [],
    limitations: {
      support: '24/7',
      agents: 15,
      credits: 50000,
      accounts: 1000,
      knowledge: {
        urls: 15000,
        youtube: 15000,
        documents: 1000,
        medias: 1000,
        quizzes: 1000000,
        texts: 1000000,
        products: 500,
        storage: 1,
      },
      max_file_size: '20mb',
      is_ltd: false,
      is_free: false,
      copyrights: true,
      exports: true,
      auto_fetch: true,
      analytics: true,
      domains: 1,
      history: **********,
      speech_to_text: true,
      text_to_speech: true,
      mobile_app: true,
      seats: 10,
      byok: true,
      webhooks: true,
      tools: AGENTS_ENTERPRISE_PLAN,
      addons: ADDONS_OPTIONS,
      models: MODELS_BY_KEY,
      location: false,
      logs: false,
      audio_mode: true,
      vision_mode: true,
    },
  },
  {
    level: 7,
    id: 'admin',
    group: 'app',
    type: 'plan',
    billing_type: 'fixed',
    title: 'Admin',
    description: '',
    is_visible: false,
    is_popular: false,
    is_user_subscribed: false,
    min_quantity: 1,
    max_quantity: 1,
    monthly: {
      price: 0,
      default_stripe_price: 'price_1MzK88F3j9uEmyHwugdhCNzF',
      stripe_prices: ['price_1MzK88F3j9uEmyHwugdhCNzF'],
    },
    yearly: {
      price: 0,
      default_stripe_price: 'price_1MzK88F3j9uEmyHwugdhCNzF',
      stripe_prices: ['price_1MzK88F3j9uEmyHwugdhCNzF'],
    },
    pricing_table: [],
    limitations: {
      support: 'Admin',
      agents: 100,
      credits: 99999,
      accounts: 99999,
      knowledge: {
        urls: 25000,
        youtube: 25000,
        documents: 1000,
        medias: 1000,
        quizzes: 1000000,
        texts: 1000000,
        products: 500,
        storage: 1,
      },
      max_file_size: '25mb',
      is_ltd: false,
      is_free: false,
      copyrights: true,
      exports: true,
      auto_fetch: true,
      analytics: true,
      domains: 1,
      history: **********,
      speech_to_text: true,
      text_to_speech: true,
      mobile_app: true,
      byok: true,
      webhooks: true,
      tools: AGENTS_ENTERPRISE_PLAN,
      addons: ADDONS_OPTIONS,
      models: MODELS_BY_KEY,
      seats: 10,
      location: false,
      logs: false,
      audio_mode: true,
      vision_mode: true,
    },
  },
  {
    level: 2,
    id: 'ast_1',
    group: 'app',
    type: 'plan',
    billing_type: 'fixed',
    title: 'AST 1',
    description: '',
    is_visible: false,
    is_popular: false,
    is_user_subscribed: false,
    min_quantity: 1,
    max_quantity: 1,
    monthly: {
      price: 0,
      default_stripe_price: IS_PRODUCTION
        ? 'price_1PctqGF3j9uEmyHwXC31NHXi'
        : 'price_1PcwFeF3j9uEmyHwswTc5hYi',
      stripe_prices: ['price_1PctqGF3j9uEmyHwXC31NHXi', 'price_1PcwFeF3j9uEmyHwswTc5hYi'],
    },
    yearly: {
      price: 0,
      default_stripe_price: IS_PRODUCTION
        ? 'price_1PctqGF3j9uEmyHwXC31NHXi'
        : 'price_1PcwFeF3j9uEmyHwswTc5hYi',
      stripe_prices: ['price_1PctqGF3j9uEmyHwXC31NHXi', 'price_1PcwFeF3j9uEmyHwswTc5hYi'],
    },
    pricing_table: [],
    limitations: {
      agents: 5,
      credits: 3000,
      seats: 1,
      accounts: 5,
      history: 250,
      knowledge: {
        urls: 200,
        youtube: 200,
        documents: 20,
        medias: 20,
        quizzes: 1000000,
        texts: 1000000,
        products: 0,
        storage: 0,
      },
      max_file_size: '5mb',
      support: '',
      is_ltd: true,
      is_free: false,
      copyrights: false,
      exports: false,
      auto_fetch: false,
      analytics: true,
      domains: 0,
      speech_to_text: true,
      text_to_speech: true,
      mobile_app: true,

      byok: false,
      webhooks: false,
      tools: AGENTS_BASIC_PLAN,
      addons: ADDONS_OPTIONS,
      models: [
        'gpt-4o',
        'gpt-4o-mini',
        'llama-405b',
        'llama-70b',
        'llama-8b',
        'claude-sonnet',
        'claude-opus',
        'claude-haiku',
        'perplexity-huge-online',
        'perplexity-large-online',
        'perplexity-small-online',
        'gemini-pro',
        'mistral-large',
        'mistral-medium',
        'mistral-small',
        'command-r-plus',
        'command-r',
      ],
      location: false,
      logs: false,
      audio_mode: false,
      vision_mode: false,
    },
  },
  {
    level: 3,
    id: 'ast_2',
    group: 'app',
    type: 'plan',
    billing_type: 'fixed',
    title: 'AST 2',
    description: '',
    is_visible: false,
    is_user_subscribed: false,
    is_popular: false,
    min_quantity: 1,
    max_quantity: 1,
    monthly: {
      price: 0,
      default_stripe_price: IS_PRODUCTION
        ? 'price_1PctqGF3j9uEmyHwJJKYjAkV'
        : 'price_1PcwFeF3j9uEmyHw4WWG7FIw',
      stripe_prices: ['price_1PctqGF3j9uEmyHwJJKYjAkV', 'price_1PcwFeF3j9uEmyHw4WWG7FIw'],
    },
    yearly: {
      price: 0,
      default_stripe_price: IS_PRODUCTION
        ? 'price_1PctqGF3j9uEmyHwJJKYjAkV'
        : 'price_1PcwFeF3j9uEmyHw4WWG7FIw',
      stripe_prices: ['price_1PctqGF3j9uEmyHwJJKYjAkV', 'price_1PcwFeF3j9uEmyHw4WWG7FIw'],
    },
    pricing_table: [],
    limitations: {
      agents: 10,
      credits: 8000,
      seats: 3,
      accounts: 25,
      history: 750,
      knowledge: {
        urls: 1000,
        youtube: 1000,
        documents: 50,
        medias: 50,
        quizzes: 1000000,
        texts: 1000000,
        products: 0,
        storage: 0,
      },
      max_file_size: '10mb',
      support: '',
      is_ltd: true,
      is_free: false,
      copyrights: false,
      exports: false,
      auto_fetch: false,
      analytics: true,
      domains: 0,
      speech_to_text: true,
      text_to_speech: true,
      mobile_app: true,

      byok: false,
      webhooks: false,
      tools: AGENTS_STANDARD_PLAN,
      addons: ['branding', 'credits'],
      models: [
        'gpt-4o',
        'gpt-4o-mini',
        'llama-405b',
        'llama-70b',
        'llama-8b',
        'claude-sonnet',
        'claude-opus',
        'claude-haiku',
        'perplexity-huge-online',
        'perplexity-large-online',
        'perplexity-small-online',
        'gemini-pro',
        'mistral-large',
        'mistral-medium',
        'mistral-small',
        'command-r-plus',
        'command-r',
      ],
      location: false,
      logs: false,
      audio_mode: false,
      vision_mode: false,
    },
  },
  {
    level: 4,
    id: 'ast_3',
    group: 'app',
    type: 'plan',
    billing_type: 'fixed',
    title: 'AST 3',
    description: '',
    is_visible: false,
    is_popular: false,
    is_user_subscribed: false,
    min_quantity: 1,
    max_quantity: 1,
    monthly: {
      price: 0,
      default_stripe_price: IS_PRODUCTION
        ? 'price_1PctqGF3j9uEmyHwWxrXquX1'
        : 'price_1PcwFeF3j9uEmyHwcyk1pcak',
      stripe_prices: ['price_1PctqGF3j9uEmyHwWxrXquX1', 'price_1PcwFeF3j9uEmyHwcyk1pcak'],
    },
    yearly: {
      price: 0,
      default_stripe_price: IS_PRODUCTION
        ? 'price_1PctqGF3j9uEmyHwWxrXquX1'
        : 'price_1PcwFeF3j9uEmyHwcyk1pcak',
      stripe_prices: ['price_1PctqGF3j9uEmyHwWxrXquX1', 'price_1PcwFeF3j9uEmyHwcyk1pcak'],
    },
    pricing_table: [],
    limitations: {
      agents: 20,
      credits: 15000,
      seats: 5,
      accounts: 100,
      history: 2000,
      knowledge: {
        urls: 3000,
        youtube: 3000,
        documents: 100,
        medias: 100,
        quizzes: 1000000,
        texts: 1000000,
        products: 0,
        storage: 0,
      },
      max_file_size: '15mb',
      support: '',
      is_ltd: true,
      is_free: false,
      copyrights: true,
      exports: true,
      auto_fetch: false,
      analytics: true,
      domains: 1,
      speech_to_text: true,
      text_to_speech: true,
      mobile_app: true,

      byok: false,
      webhooks: false,
      tools: AGENTS_STANDARD_PLAN,
      addons: ['branding', 'credits'],
      models: [
        'gpt-4o',
        'gpt-4o-mini',
        'llama-405b',
        'llama-70b',
        'llama-8b',
        'claude-sonnet',
        'claude-opus',
        'claude-haiku',
        'perplexity-huge-online',
        'perplexity-large-online',
        'perplexity-small-online',
        'gemini-pro',
        'mistral-large',
        'mistral-medium',
        'mistral-small',
        'command-r-plus',
        'command-r',
      ],
      location: false,
      logs: false,
      audio_mode: false,
      vision_mode: false,
    },
  },
  {
    level: 4,
    id: 'ast_4',
    group: 'app',
    type: 'plan',
    billing_type: 'fixed',
    title: 'AST 4',
    description: '',
    is_visible: false,
    is_popular: false,
    is_user_subscribed: false,
    min_quantity: 1,
    max_quantity: 1,
    monthly: {
      price: 0,
      default_stripe_price: IS_PRODUCTION
        ? 'price_1PctqGF3j9uEmyHwviw3XA8m'
        : 'price_1PcwFeF3j9uEmyHw1SUaDbwH',
      stripe_prices: ['price_1PctqGF3j9uEmyHwviw3XA8m', 'price_1PcwFeF3j9uEmyHw1SUaDbwH'],
    },
    yearly: {
      price: 0,
      default_stripe_price: IS_PRODUCTION
        ? 'price_1PctqGF3j9uEmyHwviw3XA8m'
        : 'price_1PcwFeF3j9uEmyHw1SUaDbwH',
      stripe_prices: ['price_1PctqGF3j9uEmyHwviw3XA8m', 'price_1PcwFeF3j9uEmyHw1SUaDbwH'],
    },
    pricing_table: [],
    limitations: {
      agents: 40,
      credits: 25000,
      seats: 7,
      accounts: 1000000,
      history: 10000,
      knowledge: {
        urls: 6000,
        youtube: 6000,
        documents: 250,
        medias: 250,
        quizzes: 1000000,
        texts: 1000000,
        products: 0,
        storage: 0,
      },
      max_file_size: '20mb',
      support: '',
      is_ltd: true,
      is_free: false,
      copyrights: true,
      exports: true,
      auto_fetch: false,
      analytics: true,
      domains: 1,
      speech_to_text: true,
      text_to_speech: true,
      mobile_app: true,

      byok: false,
      webhooks: false,
      tools: AGENTS_BUSINESS_PLAN,
      addons: ADDONS_OPTIONS,
      models: [
        'gpt-4o',
        'gpt-4o-mini',
        'llama-405b',
        'llama-70b',
        'llama-8b',
        'claude-sonnet',
        'claude-opus',
        'claude-haiku',
        'perplexity-huge-online',
        'perplexity-large-online',
        'perplexity-small-online',
        'gemini-pro',
        'mistral-large',
        'mistral-medium',
        'mistral-small',
        'command-r-plus',
        'command-r',
      ],
      location: false,
      logs: false,
      audio_mode: false,
      vision_mode: false,
    },
  },
  {
    level: 4,
    id: 'ast_5',
    group: 'app',
    type: 'plan',
    billing_type: 'fixed',
    title: 'AST 5',
    description: '',
    is_visible: false,
    is_popular: false,
    is_user_subscribed: false,
    min_quantity: 1,
    max_quantity: 1,
    monthly: {
      price: 0,
      default_stripe_price: IS_PRODUCTION
        ? 'price_1PctqGF3j9uEmyHwgjmDIZEW'
        : 'price_1PcwFeF3j9uEmyHwHnmBUtlK',
      stripe_prices: ['price_1PctqGF3j9uEmyHwgjmDIZEW', 'price_1PcwFeF3j9uEmyHwHnmBUtlK'],
    },
    yearly: {
      price: 0,
      default_stripe_price: IS_PRODUCTION
        ? 'price_1PctqGF3j9uEmyHwgjmDIZEW'
        : 'price_1PcwFeF3j9uEmyHwHnmBUtlK',
      stripe_prices: ['price_1PctqGF3j9uEmyHwgjmDIZEW', 'price_1PcwFeF3j9uEmyHwHnmBUtlK'],
    },
    pricing_table: [],
    limitations: {
      agents: 60,
      credits: 40000,
      seats: 10,
      accounts: 1000000,
      history: 50000,
      max_file_size: '25mb',
      support: '',
      knowledge: {
        urls: 10000,
        youtube: 10000,
        documents: 500,
        medias: 500,
        quizzes: 1000000,
        texts: 1000000,
        products: 0,
        storage: 0,
      },
      is_ltd: true,
      is_free: false,
      copyrights: true,
      exports: true,
      auto_fetch: false,
      analytics: true,
      domains: 1,
      speech_to_text: true,
      text_to_speech: true,
      mobile_app: true,

      byok: false,
      webhooks: true,
      tools: AGENTS_BUSINESS_PLAN,
      addons: ADDONS_OPTIONS,
      models: [
        'gpt-4o',
        'gpt-4o-mini',
        'llama-405b',
        'llama-70b',
        'llama-8b',
        'claude-sonnet',
        'claude-opus',
        'claude-haiku',
        'perplexity-huge-online',
        'perplexity-large-online',
        'perplexity-small-online',
        'gemini-pro',
        'mistral-large',
        'mistral-medium',
        'mistral-small',
        'command-r-plus',
        'command-r',
      ],
      location: false,
      logs: false,
      audio_mode: true,
      vision_mode: true,
    },
  },
  {
    level: 0,
    id: 'copyrights',
    group: 'whitelabel',
    type: 'addon',
    billing_type: 'volume',
    is_visible: true,
    is_popular: true,
    is_user_subscribed: false,
    title: 'Copyright',
    description: '',
    min_quantity: 1,
    max_quantity: 1000,
    monthly: {
      price: 49,
      default_stripe_price: IS_PRODUCTION
        ? 'price_1P4sr4F3j9uEmyHwoDjk5lCv'
        : 'price_1OWx6bF3j9uEmyHwQld2bUBh',
      stripe_prices: [
        'price_1PgS4IF3j9uEmyHwCqsKCBTf',
        'price_1P4sr4F3j9uEmyHwoDjk5lCv',
        'price_1OAXKLF3j9uEmyHwRnMtKhzp',
        'price_1NqemZF3j9uEmyHwHsFlU9vQ',
        'price_1OWx6bF3j9uEmyHwQld2bUBh',
        'price_1Na260F3j9uEmyHwYw4lJLuk',
        'price_1Na1AaF3j9uEmyHwBdtEepKf',
        'price_1Na1AaF3j9uEmyHwAsTSjQpp',
      ],
    },
    yearly: {
      price: 39,
      default_stripe_price: IS_PRODUCTION
        ? 'price_1PQyZxF3j9uEmyHwOm5Oiul3'
        : 'price_1OWx6tF3j9uEmyHwF822xsnQ',
      stripe_prices: [
        'price_1PgS6eF3j9uEmyHwKpvN3iCM',
        'price_1PQyZxF3j9uEmyHwOm5Oiul3',
        'price_1P4srPF3j9uEmyHw0J8kFyfV',
        'price_1OAXKkF3j9uEmyHw6PYy3aGr',
        'price_1NqemtF3j9uEmyHwefJSqd8V',
        'price_1OWx6tF3j9uEmyHwF822xsnQ',
        'price_1Na260F3j9uEmyHwYw4lJLuk',
        'price_1Na1AaF3j9uEmyHwBdtEepKf',
        'price_1Na1AaF3j9uEmyHwAsTSjQpp',
      ],
    },
    pricing_table: [
      {
        title: 'Features',
        features: [
          {
            icon: null,
            label: 'Remove "Powered By" from the iframe',
            access_level: 0,
          },
          {
            icon: null,
            label: 'Remove "Powered By" from the chat bubble',
            access_level: 0,
          },
        ],
      },
    ],
  },
  {
    level: 0,
    id: 'domains',
    group: 'whitelabel',
    type: 'addon',
    billing_type: 'volume',
    is_visible: true,
    is_popular: false,
    is_user_subscribed: false,
    title: 'White-label & Domain',
    description: '',
    min_quantity: 1,
    max_quantity: 1000,
    monthly: {
      price: 49,
      default_stripe_price: IS_PRODUCTION
        ? 'price_1P4soxF3j9uEmyHwH0EQs6Qw'
        : 'price_1NsSvJF3j9uEmyHwyIn5bzp4',
      stripe_prices: [
        'price_1PgS94F3j9uEmyHwbPEZWkzv',
        'price_1P4soxF3j9uEmyHwH0EQs6Qw',
        'price_1Nri3MF3j9uEmyHw0CWvKgdO',
        'price_1NsSvJF3j9uEmyHwyIn5bzp4',
        'price_1Nri3MF3j9uEmyHwIFC5cy97',
      ],
    },
    yearly: {
      price: 39,
      default_stripe_price: IS_PRODUCTION
        ? 'price_1PQyRtF3j9uEmyHwAnxywLoW'
        : 'price_1NsSvJF3j9uEmyHwQnBNDKQ0',
      stripe_prices: [
        'price_1PgSBJF3j9uEmyHw5aANfxD5',
        'price_1PQyRtF3j9uEmyHwAnxywLoW',
        'price_1P4spLF3j9uEmyHwXcqCrlzl',
        'price_1Nri3MF3j9uEmyHwIFC5cy97',
        'price_1NsSvJF3j9uEmyHwQnBNDKQ0',
        'price_1Nri3MF3j9uEmyHw0CWvKgdO',
      ],
    },
    pricing_table: [
      {
        title: 'Features',
        features: [
          {
            icon: null,
            label: 'Your logo on the dashboard, chat bubble, and favicon',
            access_level: 0,
          },
          {
            icon: null,
            label: 'Your Domain Name On The Iframe',
            access_level: 0,
          },
          {
            icon: null,
            label: 'Your Domain Name On The JS Script',
            access_level: 0,
          },
          {
            icon: null,
            label: 'Secure SSL Certificate By Google',
            access_level: 0,
          },
          {
            icon: null,
            label: 'Send emails with your personalized address',
            access_level: 0,
          },
          {
            icon: null,
            label: 'Branded emails with your unique logo',
            access_level: 0,
          },
        ],
      },
    ],
  },
  {
    level: 0,
    id: 'credits',
    group: 'app',
    type: 'addon',
    billing_type: 'volume',
    is_visible: true,
    is_popular: false,
    is_user_subscribed: false,
    title: 'Extra credits',
    description: '',
    min_quantity: 1,
    max_quantity: 100000,
    monthly: {
      price: 0.01,
      default_stripe_price: IS_PRODUCTION
        ? 'price_1Na1ATF3j9uEmyHwdpjkzDHU'
        : 'price_1NZfQnF3j9uEmyHwNbL6qICD',
      stripe_prices: ['price_1Na1ATF3j9uEmyHwdpjkzDHU', 'price_1NZfQnF3j9uEmyHwNbL6qICD'],
    },
    yearly: {
      price: 0.008,
      default_stripe_price: IS_PRODUCTION
        ? 'price_1Na1ATF3j9uEmyHwdpjkzDHU'
        : 'price_1NZfQnF3j9uEmyHwNbL6qICD',
      stripe_prices: ['price_1Na1ATF3j9uEmyHwdpjkzDHU', 'price_1NZfQnF3j9uEmyHwNbL6qICD'],
    },
    pricing_table: [
      {
        title: 'Features',
        features: [
          {
            icon: null,
            label: 'credits reset with every billing period',
            access_level: 0,
          },
        ],
      },
    ],
  },
  {
    level: 0,
    id: 'agents',
    group: 'app',
    type: 'addon',
    billing_type: 'volume',
    is_visible: true,
    is_popular: false,
    is_user_subscribed: false,
    title: 'Extra Agents',
    description: '',
    min_quantity: 1,
    max_quantity: 1000,
    monthly: {
      price: 49,
      default_stripe_price: IS_PRODUCTION
        ? 'price_1P4smJF3j9uEmyHwcHJOmYbR'
        : 'price_1OWx4rF3j9uEmyHwUf8wQXjt',
      stripe_prices: [
        'price_1P4smJF3j9uEmyHwcHJOmYbR',
        'price_1OWD7NF3j9uEmyHwuy4wmbwI',
        'price_1OWx4rF3j9uEmyHwUf8wQXjt',
      ],
    },
    yearly: {
      price: 39,
      default_stripe_price: 'price_1PQyPIF3j9uEmyHwSBA5szwb',
      stripe_prices: ['price_1PQyPIF3j9uEmyHwSBA5szwb', 'price_1P4smXF3j9uEmyHwUtUAYIdz'],
    },
    pricing_table: [],
  },
  {
    level: 0,
    id: 'seats',
    group: 'app',
    type: 'addon',
    billing_type: 'volume',
    is_visible: true,
    is_popular: false,
    is_user_subscribed: false,
    title: 'Extra Seats',
    description: '',
    min_quantity: 1,
    max_quantity: 100000,
    monthly: {
      price: 49,
      default_stripe_price: IS_PRODUCTION
        ? 'price_1P4smJF3j9uEmyHwcHJOmYbR'
        : 'price_1OWx4rF3j9uEmyHwUf8wQXjt',
      stripe_prices: [
        'price_1P4smJF3j9uEmyHwcHJOmYbR',
        'price_1OWD7NF3j9uEmyHwuy4wmbwI',
        'price_1OWx4rF3j9uEmyHwUf8wQXjt',
      ],
    },
    yearly: {
      price: 39,
      default_stripe_price: 'price_1PQyPIF3j9uEmyHwSBA5szwb',
      stripe_prices: ['price_1PQyPIF3j9uEmyHwSBA5szwb', 'price_1P4smXF3j9uEmyHwUtUAYIdz'],
    },
    pricing_table: [],
  },
  {
    level: 0,
    id: 'urls',
    group: 'knowledge',
    type: 'addon',
    billing_type: 'volume',
    is_visible: true,
    is_popular: false,
    is_user_subscribed: false,
    title: 'Extra Training URLs',
    description: '',
    min_quantity: 1,
    max_quantity: 100000,
    monthly: {
      price: 0.1,
      default_stripe_price: IS_PRODUCTION
        ? 'price_1P4snYF3j9uEmyHwiWQClkTJ'
        : 'price_1OWx3xF3j9uEmyHwZifEBClB',
      stripe_prices: [
        'price_1P4snYF3j9uEmyHwiWQClkTJ',
        'price_1OWBxBF3j9uEmyHwKKuuHc6Z',
        'price_1OWx3xF3j9uEmyHwZifEBClB',
      ],
    },
    yearly: {
      price: 0.08,
      default_stripe_price: 'price_1P4snkF3j9uEmyHwBrGg4WoJ',
      stripe_prices: ['price_1P4snkF3j9uEmyHwBrGg4WoJ'],
    },
    pricing_table: [],
  },
  {
    level: 0,
    id: 'documents',
    group: 'knowledge',
    type: 'addon',
    billing_type: 'volume',
    is_visible: true,
    is_popular: false,
    is_user_subscribed: false,
    title: 'Extra Training Documents',
    description: '',
    min_quantity: 1,
    max_quantity: 100000,
    monthly: {
      price: 0.1,
      default_stripe_price: IS_PRODUCTION
        ? 'price_1P4sjQF3j9uEmyHwTgV8u5PS'
        : 'price_1OWx1aF3j9uEmyHwPHvoyISR',
      stripe_prices: [
        'price_1P4sjQF3j9uEmyHwTgV8u5PS',
        'price_1OWWUAF3j9uEmyHwzRMgd34u',
        'price_1OWx1aF3j9uEmyHwPHvoyISR',
      ],
    },
    yearly: {
      price: 0.08,
      default_stripe_price: 'price_1P4slPF3j9uEmyHwqzfKtyTJ',
      stripe_prices: ['price_1P4slPF3j9uEmyHwqzfKtyTJ'],
    },
    pricing_table: [],
  },
]

export const pricingTableTemplate: Infer<typeof subscriptionPricingTableTemplateValidator> = [
  {
    title: 'AI & AGENTS',
    features: [
      {
        label: '{{agents}} Agents',
        icon: null,
        access_level: 0,
      },
      {
        label: '{{credits}} credits/mo',
        icon: null,
        access_level: 0,
      },
      {
        label: 'Access To All LLMs',
        icon: null,
        access_level: 2,
      },
      { label: 'Use your OpenAI Keys', icon: null, access_level: 6 },
    ],
  },
  {
    title: 'TRAIN ON',
    features: [
      {
        label: '{{urls}} URLs & Youtube',
        icon: null,
        access_level: 0,
      },
      {
        label: '{{documents}} Documents',
        icon: null,
        access_level: 0,
      },
      { label: 'Sitemaps', icon: null, access_level: 0 },
      { label: 'Raw Text', icon: null, access_level: 0 },
      { label: 'Q&A', icon: null, access_level: 0 },
      {
        label: 'Products & Services',
        icon: null,
        access_level: 0,
      },
      {
        label: 'Real Estate Listings',
        icon: null,
        access_level: 0,
      },
      {
        label: 'Cloud S3/R2 Buckets',
        icon: null,
        access_level: 6,
      },
    ],
  },
  {
    title: 'INSTALL & DEPLOY',
    features: [
      { label: 'Chat bubble', icon: null, access_level: 0 },
      {
        label: 'Embed via Iframe',
        icon: null,
        access_level: 0,
      },
      { label: 'Shareable Link', icon: null, access_level: 0 },
      { label: 'QR Code', icon: null, access_level: 0 },
      { label: 'API Access', icon: null, access_level: 0 },
      { label: 'Search bar', icon: null, access_level: 6 },
    ],
  },
  {
    title: 'CORE FEATURES',
    features: [
      {
        label: 'Source Citations',
        icon: null,
        access_level: 0,
      },
      {
        label: 'Hallucination Safeguards',
        icon: null,
        access_level: 0,
      },
      {
        label: 'Conversation Saving',
        icon: null,
        access_level: 0,
      },
      {
        label: 'PIN Code Protection',
        icon: null,
        access_level: 0,
      },
      { label: 'Simple CRM', icon: null, access_level: 0 },
      { label: 'Manual Resync', icon: null, access_level: 0 },
      { label: 'Advanced Analytics', icon: null, access_level: 0 },
      {
        label: 'Calendar Embedding',
        icon: null,
        access_level: 3,
      },
      { label: 'User Tagging', icon: null, access_level: 3 },
      { label: 'Auto Resync', icon: null, access_level: 3 },
      { label: 'Location & IP', icon: null, access_level: 4 },
      { label: 'Private User Accounts', icon: null, access_level: 4 },
      { label: 'Data Export', icon: null, access_level: 4 },
      { label: 'Advanced Logs', icon: null, access_level: 6 },
    ],
  },
  {
    title: 'CUSTOMIZATION',
    features: [
      {
        label: 'Custom AI Naming',
        icon: null,
        access_level: 2,
      },
      {
        label: 'AI Knowledge Restrictions',
        icon: null,
        access_level: 0,
      },
      {
        label: 'Texts, Sizes, Colors, Logos, Avatars, ...',
        icon: null,
        access_level: 0,
      },
      { label: 'Dark Mode', icon: null, access_level: 1 },
      {
        label: 'Multiple Fonts Choices',
        icon: null,
        access_level: 0,
      },
      {
        label: 'Welcome credits (Openers)',
        icon: null,
        access_level: 0,
      },
      {
        label: 'Chat Starters (Prompt Cards)',
        icon: null,
        access_level: 0,
      },
      {
        label: 'Message Input Placeholder',
        icon: null,
        access_level: 0,
      },
      {
        label: 'Behavior & Personality',
        icon: null,
        access_level: 0,
      },
      {
        label: 'AI Creativity Control',
        icon: null,
        access_level: 0,
      },
      {
        label: 'AI Writing Style',
        icon: null,
        access_level: 0,
      },
      {
        label: 'Custom CSS & JavaScript',
        icon: null,
        access_level: 0,
      },
    ],
  },
  {
    title: 'INTERACTION MODES',
    features: [
      { label: 'Text Chat', icon: null, access_level: 0 },
      { label: 'Text-to-Speech', icon: null, access_level: 0 },
      { label: 'Speech-to-Text', icon: null, access_level: 0 },
      { label: 'Voice Chat Mode', icon: null, access_level: 3 },
      { label: 'Vision Chat Mode', icon: null, access_level: 3 },
    ],
  },
  {
    title: 'INBOX & HISTORY',
    features: [
      {
        label: 'Advanced Search & Filters',
        icon: null,
        access_level: 5,
      },
      {
        label: 'Users Geolocation Data',
        icon: null,
        access_level: 5,
      },
      { label: 'Chat Summary', icon: null, access_level: 6 },
      {
        label: 'Sentiment Analysis',
        icon: null,
        access_level: 6,
      },
      {
        label: 'Knowledge Gap Analysis',
        icon: null,
        access_level: 6,
      },
      { label: 'Reports', icon: null, access_level: 6 },
    ],
  },
  {
    title: 'CRM & Leads',
    features: [{ label: 'Unlimited Leads', icon: null, access_level: 0 }],
  },
  {
    title: 'LIVE CHAT HANDOFF',
    features: [
      {
        label: 'Intercom',
        icon: 'https://cdn.insertchat.com/public/handoff/intercom.svg',
        access_level: 3,
      },
      {
        label: 'Tidio',
        icon: 'https://cdn.insertchat.com/public/handoff/tidio.svg',
        access_level: 3,
      },
      {
        label: 'Crisp',
        icon: 'https://cdn.insertchat.com/public/handoff/crisp.svg',
        access_level: 3,
      },
      {
        label: 'Kustomer',
        icon: 'https://cdn.insertchat.com/public/handoff/kustomer.svg',
        access_level: 3,
      },
      {
        label: 'HelpCrunch',
        icon: 'https://cdn.insertchat.com/public/handoff/helpcrunch.svg',
        access_level: 3,
      },
      {
        label: 'Olark',
        icon: 'https://cdn.insertchat.com/public/handoff/olark.svg',
        access_level: 3,
      },
      {
        label: 'HubSpot',
        icon: 'https://cdn.insertchat.com/public/handoff/hubspot.svg',
        access_level: 3,
      },
      {
        label: 'LiveChat',
        icon: 'https://cdn.insertchat.com/public/handoff/livechat.svg',
        access_level: 3,
      },
      {
        label: 'ClickDesk',
        icon: 'https://cdn.insertchat.com/public/handoff/clickdesk.svg',
        access_level: 3,
      },
      {
        label: 'Drift',
        icon: 'https://cdn.insertchat.com/public/handoff/drift.svg',
        access_level: 3,
      },
      {
        label: 'Tawk',
        icon: 'https://cdn.insertchat.com/public/handoff/tawk.svg',
        access_level: 3,
      },
      {
        label: 'LiveAgent',
        icon: 'https://cdn.insertchat.com/public/handoff/liveagent.svg',
        access_level: 3,
      },
      {
        label: 'Trengo',
        icon: 'https://cdn.insertchat.com/public/handoff/trengo.svg',
        access_level: 3,
      },
      {
        label: 'Gorgias',
        icon: 'https://cdn.insertchat.com/public/handoff/gorgias.svg',
        access_level: 3,
      },
      {
        label: 'Freshworks',
        icon: 'https://cdn.insertchat.com/public/handoff/freshworks.svg',
        access_level: 3,
      },
      {
        label: 'Front',
        icon: 'https://cdn.insertchat.com/public/handoff/front.svg',
        access_level: 3,
      },
    ],
  },
  {
    title: 'ACTIONS & AGENTS',
    features: [
      { label: 'Email Handoff', icon: null, access_level: 0 },
      { label: 'Web Browsing', icon: null, access_level: 2 },
      { label: 'Google Search', icon: null, access_level: 2 },
      {
        label: 'Wikipedia Search',
        icon: null,
        access_level: 2,
      },
      {
        label: 'Perplexity Search',
        icon: null,
        access_level: 2,
      },
      {
        label: 'Question & Answer (Q&A)',
        icon: null,
        access_level: 1,
      },
      {
        label: 'Smart Suggestions',
        icon: null,
        access_level: 0,
      },
      { label: 'Interactive buttons', icon: null, access_level: 1 },
      { label: 'Lead Collection', icon: null, access_level: 3 },
      {
        label: 'Auto Resolve Conversations',
        icon: null,
        access_level: 3,
      },
      { label: 'Email Transcriptions', icon: null, access_level: 3 },
      {
        label: 'Guided Conversations (Flows)',
        icon: null,
        access_level: 3,
      },
      {
        label: 'Live Chatbot Handoff',
        icon: null,
        access_level: 3,
      },
      { label: 'Calculator', icon: null, access_level: 3 },
      { label: 'DALL·E 3', icon: null, access_level: 4 },
      {
        label: 'Chat with Your API',
        icon: null,
        access_level: 4,
      },
      { label: 'Wolfram|Alpha', icon: null, access_level: 5 },
      { label: 'Shopify', icon: null, access_level: 5 },
      { label: 'WooCommerce', icon: null, access_level: 5 },
      { label: 'IFTTT Automation', icon: null, access_level: 6 },
      {
        label: 'Chat With Your DB',
        icon: null,
        access_level: 6,
      },
      { label: 'Live AI Calls', icon: null, access_level: 6 },
    ],
  },
  {
    title: 'INTEGRATIONS',
    features: [
      { label: 'JavaScript Events', icon: null, access_level: 2 },
      { label: 'Zapier', icon: null, access_level: 2 },
      { label: 'WhatsApp', icon: null, access_level: 2 },
      { label: 'Calendly', icon: null, access_level: 3 },
      { label: 'Google Calendar', icon: null, access_level: 3 },
      { label: 'Shopify', icon: null, access_level: 5 },
      { label: 'WooCommerce', icon: null, access_level: 5 },
      { label: 'Webhooks', icon: null, access_level: 5 },
      { label: 'Messenger', icon: null, access_level: 6 },
      { label: 'Telegram', icon: null, access_level: 6 },
    ],
  },
  {
    title: 'TEAM',
    features: [
      {
        label: '{{seats}} seat(s)',
        icon: null,
        access_level: 2,
      },
      {
        label: 'Roles Management',
        icon: null,
        access_level: 2,
      },
    ],
  },
  {
    title: 'SUPPORT',
    features: [
      {
        label: 'Email Support',
        icon: null,
        access_level: 1,
      },
      {
        label: 'Priority Support',
        icon: null,
        access_level: 5,
      },
      {
        label: 'Technical Support',
        icon: null,
        access_level: 6,
      },
      {
        label: 'Dedicated Support Agent',
        icon: null,
        access_level: 6,
      },
    ],
  },
  {
    title: 'SECURITY & PRIVACY',
    features: [
      { label: 'Future-Ready', icon: null, access_level: 0 },
      { label: 'Monthly Updates', icon: null, access_level: 0 },
      { label: 'GDPR Compliance', icon: null, access_level: 0 },
      {
        label: 'Complete Data Ownership',
        icon: null,
        access_level: 0,
      },
      {
        label: 'End-To-End Encryption',
        icon: null,
        access_level: 0,
      },
      {
        label: '99.99% Guaranteed Uptime',
        icon: null,
        access_level: 0,
      },
      {
        label: 'Choose Servers Location',
        icon: null,
        access_level: 6,
      },
    ],
  },
  {
    title: 'PURCHASABLE ADD-ONS',
    features: [
      { label: 'Copyright', icon: null, access_level: 2 },
      { label: 'Extra Queries', icon: null, access_level: 3 },
      { label: 'Extra Training', icon: null, access_level: 3 },
      { label: 'Extra Agents', icon: null, access_level: 5 },
      { label: 'Custom Domain', icon: null, access_level: 5 },
      { label: 'Extra Seats', icon: null, access_level: 6 },
      {
        label: 'White Glove Onboarding',
        icon: null,
        access_level: 6,
      },
      {
        label: 'Custom Data Sources',
        icon: null,
        access_level: 6,
      },
    ],
  },
  {
    title: 'MISCELLANEOUS',
    features: [
      {
        label: 'Unlimited Embedding',
        icon: null,
        access_level: 0,
      },
      {
        label: 'Unlimited Chat Sessions',
        icon: null,
        access_level: 0,
      },
      {
        label: 'Monetization Friendly',
        icon: null,
        access_level: 0,
      },
      {
        label: 'Reseller-Friendly',
        icon: null,
        access_level: 5,
      },
      {
        label: 'Faster AI Responses',
        icon: null,
        access_level: 6,
      },
      {
        label: 'Priority Feature Request',
        icon: null,
        access_level: 6,
      },
    ],
  },
]
