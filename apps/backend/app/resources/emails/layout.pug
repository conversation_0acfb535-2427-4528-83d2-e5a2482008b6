doctype html
html(lang="en")
  head
    meta(charset="UTF-8")
    meta(name="viewport", content="width=device-width, initial-scale=1.0")
    title #{subject}
    style.
      body {
        font-family: 'Helvetica Neue', Arial, sans-serif;
        background-color: #f4f4f4;
        color: #333;
        line-height: 1.6;
        margin: 0;
        padding: 0;
      }
      .container {
        width: 100%;
        max-width: 600px;
        margin: 20px auto;
        background-color: #ffffff;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 4px 12px rgba(0,0,0,0.05);
      }
      header {
        padding: 30px;
        text-align: center;
        background-color: #0e111b;
        color: #ffffff;
      }
      header h1.title {
        margin: 0;
        font-size: 28px;
      }
      section {
        padding: 30px;
      }
      .messages {
        list-style-type: none;
        padding: 0;
        margin: 0;
      }
      .message {
        background-color: #f9f9f9;
        border: 1px solid #e0e0e0;
        border-radius: 5px;
        padding: 20px;
        margin-bottom: 15px;
      }
      .message .name {
        font-weight: bold;
        color: #333;
        margin-bottom: 8px;
      }
      .message .content {
        margin-left: 0;
        color: #666;
      }
      footer {
        text-align: center;
        padding: 20px;
        background-color: #f1f1f1;
        color: #777;
      }
      footer p.notice {
        font-size: 0.9em;
        color: #999;
        margin-bottom: 10px;
      }
      footer img.logo {
        width: 120px;
        height: auto;
        margin-top: 10px;
      }
      @media only screen and (max-width: 600px) {
        body {
          font-size: 16px;
        }
        .container {
          margin: 10px;
        }
        header, section, footer {
          padding: 20px;
        }
        .message {
          padding: 15px;
        }
        header h1.title {
          font-size: 24px;
        }
      }
  body
    .container
      include header.pug
      section
        block content
      include footer.pug