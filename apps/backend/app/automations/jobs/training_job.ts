import Lite<PERSON><PERSON>Helper from '#app/ai/helpers/lite_llm_helper'
import { Job } from 'bullmq'
import { v4 as uuid } from 'uuid'
import Source from '#app/modules/rag/sources/source_model'
import ModelService from '#app/modules/models/model_service'
import EmbeddingService from '#app/modules/rag/embeddings/embedding_service'
import TrainingService from '#app/modules/rag/training/training_service'
import BaseJob from '#app/modules/base/base_job'

export default class TrainingJob extends BaseJob {
  private static instance: TrainingJob
  private static readonly NAME = 'training_job'

  private constructor() {
    super(TrainingJob.NAME)
  }

  static async getInstance(): Promise<TrainingJob> {
    if (!TrainingJob.instance) {
      TrainingJob.instance = new TrainingJob()
      await TrainingJob.instance.drain()
    }

    return TrainingJob.instance
  }

  protected async handle(job: Job) {
    try {
      const { uid } = job.data
      const trainingService = new TrainingService()
      const embeddingService = new EmbeddingService()
      const modelService = new ModelService()

      const source = await Source.findByOrFail('uid', uid)

      await Promise.all([
        source.preload('app'),
        source.preload('provider'),
        source.preload('agent'),
      ])

      const { app, provider, agent } = source
      const { type } = provider

      let ingestResponse

      switch (type) {
        case 'urls':
          ingestResponse = await trainingService.ingestUrl({
            url: source?.url || '',
            improve: false,
          })
          break

        case 'youtube':
          ingestResponse = await trainingService.ingestYoutube({
            url: source?.youtube || '',
            improve: false,
            lang_iso_639_1: provider?.language_iso_639_1,
            lang_iso_639_2: provider?.language_iso_639_2,
          })
          break

        case 'documents':
          ingestResponse = await trainingService.ingestDocument({
            lang_iso_639_1: provider?.language_iso_639_1,
            lang_iso_639_2: provider?.language_iso_639_2,
            improve: false,
            storage_path: source?.document?.location || '',
            credentials: {
              access_key: provider?.storage_config?.access_key || '',
              secret_key: provider?.storage_config?.secret_key || '',
              bucket: provider?.storage_config?.bucket_name || '',
              region: provider?.storage_config?.bucket_region || '',
              endpoint: provider?.storage_config?.bucket_endpoint || '',
            },
          })
          break

        case 'medias':
          ingestResponse = await trainingService.ingestMedia({
            lang_iso_639_1: provider?.language_iso_639_1,
            lang_iso_639_2: provider?.language_iso_639_2,
            improve: false,
            storage_path: source?.document?.location || '',
            credentials: {
              access_key: provider?.storage_config?.access_key || '',
              secret_key: provider?.storage_config?.secret_key || '',
              bucket: provider?.storage_config?.bucket_name || '',
              region: provider?.storage_config?.bucket_region || '',
              endpoint: provider?.storage_config?.bucket_endpoint || '',
            },
          })
          break

        case 'quizzes':
          ingestResponse = await trainingService.ingestQuiz({
            question: source?.quizz?.question || '',
            answer: source?.quizz?.answer || '',
          })
          break

        case 'texts':
          ingestResponse = await trainingService.ingestText({
            text: source?.text || '',
          })
          break

        case 'products':
          ingestResponse = await trainingService.ingestProduct({
            product: source?.product || null,
          })
          break
      }

      if (!ingestResponse) {
        throw new Error('training_error_no_content')
      }

      try {
        const creditsCost = await modelService.getCreditsCostPerOutput({
          model_key: agent.model_embedding,
          output: ingestResponse.content_original || '',
        })

        await app.canConsumeCredits({
          throwError: true,
          toConsume: creditsCost,
        })
      } catch (error) {
        throw new Error('training_error_credit_consumption')
      }

      const embeddings = await LiteLlmHelper.createEmbeddings({
        app,
        texts: ingestResponse.chunks,
        modelName: agent.model_embedding,
      })

      await embeddingService.deleteEmbeddingsBySources({
        table: agent.getEmbeddingTableName(),
        source_uids: [source.uid],
      })

      await embeddingService.multiInsert({
        table: agent.getEmbeddingTableName(),
        embeddings: embeddings.map((item) => {
          return {
            uid: uuid(),
            source_uid: source.uid,
            embedding: JSON.stringify(item.embedding),
            metadata: {
              chunk_id: item.index,
              content: item.chunk,
              //
              app_uid: source.app_uid,
              agent_uid: source.agent_uid,
              provider_uid: source.provider_uid,
              //
              type: provider.type,
              language: provider?.language,
              //
              url: source.url,
              youtube: source.youtube,
              document: source.document,
              media: source.media,
              quizz: source.quizz,
              product: source.product,
              //
              model_embedding: agent.model_embedding,
              model_dimensions: 1536,
            },
          }
        }),
      })

      await source
        .merge({
          status: 'trained',
          content_original: ingestResponse.content_original,
          content_enhanced: ingestResponse.content_enhanced,
          content_summary: ingestResponse.content_summary,
        })
        .save()

      return
    } catch (error) {
      console.error(error)
    }
  }
}
