import { IS_PRODUCTION } from '#app/constants'
import { DateHelper } from '#app/helpers'
import db from '@adonisjs/lucid/services/db'
import Provider from '#app/modules/rag/providers/provider_model'

export default class RefreshScheduler {
  async handle() {
    if (!IS_PRODUCTION) {
      return
    }

    const { rows: providers } = await db.rawQuery(
      `SELECT app_uid, uid, last_synch_at, fetch_every_hours FROM providers WHERE fetch_every_hours > 0 AND type = 'urls' AND (last_synch_at IS NULL OR last_synch_at + (fetch_every_hours || ' hours')::interval <= NOW())`
    )

    if (providers.length === 0) {
      return
    }

    console.debug(`---> RefreshTask | ${providers.length} providers to process`)

    for (const item of providers) {
      try {
        const provider = await Provider.findByOrFail('uid', item.uid)
        await provider.load('app')

        const { app } = provider
        const { currentPlanProduct } = app.subscriptions?.getPlan() || {}
        const hasAutoFetch = currentPlanProduct?.limitations?.auto_fetch

        if (!hasAutoFetch) {
          await provider
            .merge({
              fetch_every_hours: 0,
            })
            .save()

          continue
        }

        const sources = await provider.related('sources').query().select('uid')
        const sourcesUids = sources.map((s) => s.uid)

        await db.from('sources').whereIn('uid', sourcesUids).update({
          status: 'training_processing',
          content_original: null,
          content_enhanced: null,
          content_summary: null,
        })

        await provider
          .merge({
            last_synch_at: DateHelper.getNow(),
          })
          .save()
      } catch (error) {
        console.error(error)
      }
    }
  }
}
