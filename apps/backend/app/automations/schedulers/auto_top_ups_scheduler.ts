import { IS_PRODUCTION } from '#app/constants'
import { <PERSON>eHelper } from '#app/helpers'
import Strip<PERSON> from 'stripe'
import App from '#app/modules/apps/app_model'

export default class AutoTopUpsScheduler {
  async handle() {
    if (!IS_PRODUCTION) {
      //return
    }

    const apps = await App.query().whereRaw(
      "auto_topup->>'queries_auto_topup' = :queries_auto_topup AND auto_topup->>'queries_below' >= available_credits",
      { queries_auto_topup: true }
    )

    console.info(`Auto top-ups: ${apps.length} apps to process`)

    for (const app of apps) {
      try {
        const { queries_purchase } = app?.auto_topup ?? {}
        const { stripe_customer_id } = app
        const costPerCredit = 0.01
        const stripe = await StripeHelper.getStripe()

        const stripeCustomer: Stripe.Customer = (await stripe.customers.retrieve(
          stripe_customer_id
        )) as Stripe.Customer

        if (!stripeCustomer) {
          throw new Error('No customer found')
        }

        let paymentMethodId = stripeCustomer?.invoice_settings?.default_payment_method as string

        if (!paymentMethodId) {
          const paymentMethods = await stripe.paymentMethods.list({
            customer: stripe_customer_id,
            type: 'card',
          })

          paymentMethodId = paymentMethods.data[0]?.id
        }

        if (!paymentMethodId) {
          throw new Error('No payment method found')
        }

        // Skip payment processing if queries_purchase is null, undefined, or 0
        if (!queries_purchase) {
          return
        }

        await stripe.paymentIntents.create({
          amount: Math.round(costPerCredit * queries_purchase * 100),
          currency: 'usd',
          description: `Auto top-up - ${queries_purchase} credits`,
          customer: stripe_customer_id,
          payment_method: paymentMethodId,
          off_session: true,
          confirm: true,
          automatic_payment_methods: {
            enabled: true,
            allow_redirects: 'never',
          },
        })

        await app
          .merge({
            available_credits: Math.max(app?.available_credits, 0) + queries_purchase,
          })
          .save()

        return
      } catch (error) {
        console.error(error)
      }
    }
  }
}
