import { IS_PRODUCTION } from '#app/constants'
import { <PERSON><PERSON>Hel<PERSON> } from '#app/helpers'
import { DateTime } from 'luxon'
import db from '@adonisjs/lucid/services/db'
import App from '#app/modules/apps/app_model'

export default class MonthlyCreditsResetScheduler {
  async handle() {
    if (!IS_PRODUCTION) {
      return
    }

    const apps = await App.all()
    const currentDay = DateTime.now()

    console.debug(`${Object.keys(apps).length} apps to process`)

    for (const app of apps) {
      try {
        const { subscriptions } = app
        const { currentPlanProduct, currentPlanSubscription } =
          (await subscriptions?.getPlan()) || {}

        if (!currentPlanProduct || !currentPlanSubscription) {
          continue
        }

        const { stripe_status, stripe_interval, stripe_current_period_end } =
          currentPlanSubscription

        if (stripe_status === 'active' && stripe_interval === 'year' && stripe_current_period_end) {
          const nextBillingDate = DateTime.fromMillis(stripe_current_period_end * 1000)

          if (nextBillingDate.day === currentDay.day) {
            await db.rawQuery(`UPDATE apps SET available_credits = ? WHERE uid = ?`, [
              currentPlanProduct?.limitations?.credits ?? 0,
              currentDay.toSQL(),
              app.uid,
            ])
          }
        }
      } catch (error) {
        console.error(error)
      }
    }
  }
}
