import { IS_PRODUCTION } from '#app/constants'
import db from '@adonisjs/lucid/services/db'
import EmbeddingService from '#app/modules/rag/embeddings/embedding_service'

export default class IndexScheduler {
  private readonly EMBEDDING_SCHEMA = 'embeddings'

  async handle() {
    if (!IS_PRODUCTION) {
      // return
    }

    const embeddingService = new EmbeddingService()

    const { rows: tables } = await db.rawQuery(
      `SELECT table_name FROM information_schema.tables WHERE table_schema = '${this.EMBEDDING_SCHEMA}'`
    )

    console.debug(`${Object.keys(tables?.rows).length} tables to process`)

    for (const table of tables) {
      try {
        const { table_name } = table
        const indexName = `idx_hnsw_${table_name}_embedding`

        let shouldCreateIndex = true

        const indexExistenceResponse = await db.rawQuery(
          `SELECT index_name FROM pg_indexes WHERE schemaname = '${this.EMBEDDING_SCHEMA}' AND tablename = '${table_name}' AND index_name = '${indexName}'`
        )

        const indexExistence =
          indexExistenceResponse.rows && indexExistenceResponse.rows.length
            ? indexExistenceResponse.rows[0]?.index_name
            : null

        if (indexExistence) {
          const indexValidityResponse = await db.rawQuery(
            `SELECT indisvalid FROM pg_class JOIN pg_index ON pg_class.oid = pg_index.indexrelid WHERE pg_index.indisvalid = false AND pg_class.relname = '${indexName}'`
          )

          const indexValidity =
            indexValidityResponse.rows && indexValidityResponse.rows.length ? false : true

          if (indexValidity) {
            shouldCreateIndex = false
          } else {
            try {
              await db.rawQuery(
                `DROP INDEX CONCURRENTLY IF EXISTS ${this.EMBEDDING_SCHEMA}.${indexName}`
              )
            } catch (error) {
              console.error(error)
            }
          }
        }

        if (shouldCreateIndex) {
          await embeddingService.createIndex({ table: table_name, vacuum: true })
        }
      } catch (error) {
        console.error(error)
      }
    }
  }
}
