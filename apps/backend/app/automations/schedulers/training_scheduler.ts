import emitter from '@adonisjs/core/services/emitter'
import db from '@adonisjs/lucid/services/db'

export default class TrainingScheduler {
  async handle() {
    const { rows: sources } = await db.rawQuery(
      "SELECT uid FROM sources WHERE status = 'training_processing'"
    )

    if (!sources.length) {
      return
    }

    for (const source of sources) {
      emitter.emit('job:training', { uid: source.uid })
    }
  }
}
