import { IS_PRODUCTION } from '#app/constants'
import { <PERSON><PERSON><PERSON><PERSON>, StorageHelper } from '#app/helpers'
import { _Object } from '@aws-sdk/client-s3'
import db from '@adonisjs/lucid/services/db'
import Provider from '#app/modules/rag/providers/provider_model'
import EmbeddingService from '#app/modules/rag/embeddings/embedding_service'
import ProviderService from '#app/modules/rag/providers/provider_service'

export default class CloudStorageScheduler {
  async handle() {
    if (!IS_PRODUCTION) {
      // return
    }

    try {
      const { rows: providers } = await db.rawQuery(
        `SELECT app_uid, uid, last_synch_at, fetch_every_hours FROM providers WHERE fetch_every_hours > 0 AND type = 'storage' AND (last_synch_at IS NULL OR last_synch_at + (fetch_every_hours || ' hours')::interval <= NOW())`
      )

      if (providers.length === 0) {
        return
      }

      for (const provider of providers) {
        const { app_uid, uid } = provider

        await this.processCloudStorageProvider({
          appUid: app_uid,
          providerUid: uid,
        })
      }
    } catch (error) {
      console.error(error)
    }
  }

  private async processCloudStorageProvider(params: { appUid: string; providerUid: string }) {
    const { providerUid } = params

    try {
      const embeddingService = new EmbeddingService()
      const providerService = new ProviderService()

      const provider = await Provider.findByOrFail('uid', providerUid)
      await provider.load('app')

      const { app, agent, storage_config } = provider
      const { currentPlanProduct } = app.subscriptions?.getPlan() || {}
      const hasStorageTraining = currentPlanProduct?.limitations?.knowledge?.storage

      if (!hasStorageTraining) {
        await provider
          .merge({
            fetch_every_hours: 0,
          })
          .save()

        return
      }

      const sourceClient = new StorageHelper('custom', storage_config)

      const destinationClient = new StorageHelper('knowledge')

      const storageDocuments = await sourceClient.listAllFiles()

      if (!storageDocuments || storageDocuments.length === 0) {
        return
      }

      const { rows: etagsRowsInDb } = await db.rawQuery(
        `SELECT uid, document->>'etag' as etag 
         FROM sources 
         WHERE type = 'storage' 
         AND document->>'etag' IS NOT NULL`
      )

      const etagsInDb: Map<string, string> = new Map(
        etagsRowsInDb.map(({ etag, uid }: { etag: string; uid: string }) => [etag, uid])
      )

      const etagsInStorage: Map<string, _Object> = new Map(
        storageDocuments.map((file) => [file['ETag']?.replace(/"/g, '') || '', file])
      )

      const storageDocumentsToAdd: _Object[] = []

      for (const [etag, file] of etagsInStorage) {
        if (etag && !etagsInDb.has(etag)) {
          storageDocumentsToAdd.push(file)
        }
      }

      const sourcesUidsToDelete: string[] = []

      for (const [etag, uid] of etagsInDb) {
        if (etag && !etagsInStorage.has(etag)) {
          sourcesUidsToDelete.push(uid)
        }
      }

      // Mass delete sources that are no longer in storage
      if (sourcesUidsToDelete.length > 0) {
        try {
          const table = agent.getEmbeddingTableName()

          await db.transaction(async (trx) => {
            await Promise.all([
              embeddingService.deleteEmbeddingsBySources({
                table,
                source_uids: sourcesUidsToDelete,
              }),
              trx.rawQuery(
                `DELETE FROM sources WHERE uid IN (${sourcesUidsToDelete.map(() => '?').join(',')})`,
                sourcesUidsToDelete
              ),
            ])
          })
        } catch (error) {
          console.error(error)
        }
      }

      // Check limitations
      try {
        await app.canConsumeSources({
          sourceType: 'documents',
          throwError: true,
          toConsume: storageDocumentsToAdd.length,
        })
      } catch (error) {
        console.error(error)

        await provider.merge({
          metadata: {
            ...(provider.metadata || {}),
            warning: `We can't sync your storage, you are over your documents limit. Please upgrade your plan or buy more documents.`,
          },
        })

        // TODO: Send a warning email to the user
      }

      // Add new documents
      for (const storageFile of storageDocumentsToAdd) {
        try {
          if (!storageFile['Key']) {
            continue
          }

          const downloadedFile = await sourceClient.getFile(storageFile['Key'])

          if (!downloadedFile) {
            continue
          }

          const destinationBasePath = `${provider.app_uid}/${provider.agent_uid}`

          const { direct_url } = await sourceClient.copyToStorage({
            keyToDownload: storageFile['Key'],
            destinationClient: destinationClient.getClient(),
            destinationBasePath: destinationBasePath,
          })

          if (direct_url) {
            await providerService.insertDocuments({
              uid: providerUid,
              storage_paths: [destinationBasePath],
            })
          }
        } catch (error) {
          console.error(error)
        }
      }

      await provider
        .merge({
          last_synch_at: DateHelper.getNow(),
        })
        .save()

      return
    } catch (error) {
      console.error(error)
    }
  }
}
