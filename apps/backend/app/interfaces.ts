import { HttpContext } from '@adonisjs/core/http'

export interface ICreateGoogleConversion {
  appUid: string
  conversionName: string
  conversionValue: number
  conversionCurrency: string
  metadata?: object
}

export interface ICreateTrace {
  appUid: string
  userUid?: string | null
  agentUid?: string | null
  chatUid?: string | null
  messageUid?: string | null
  contactUid?: string | null
  feedbackUid?: string | null
  providerUid?: string | null
  label?: string
  description?: string
  status?: string | null
  ctx?: HttpContext
  credits: number
  sendWebhook: boolean
  metadata?: object
  trigger:
    | 'auth'
    | 'embeddings_create'
    | 'ai_engine'
    | 'training_engine'
    | 'chatbot'
    | 'agent'
    | 'crawler'
  action:
    | 'auth_register'
    | 'auth_login'
    | 'auth_confirm'
    | 'ai_engine_embeddings_create'
    | 'training_engine_embeddings_create'
    | 'crawler_crawling'
    | 'chatbot_load'
    | 'chatbot_open'
    | 'chatbot_chat_session_create'
    | 'chatbot_chat_session_load'
    | 'chatbot_chat_session_delete'
    | 'chatbot_message_create'
    | 'chatbot_message_listen'
    | 'chatbot_message_transcribe'
    | 'chatbot_answer_create'
    | 'chatbot_account_register'
    | 'chatbot_account_login'
    | 'chatbot_file_create'
    | 'chatbot_feedback_create'
    | 'agent_start'
    | 'agent_end'
    | 'agent_success_smart_suggestions'
    | 'agent_success_mark_as_resolved'
    | 'agent_success_request_human'
    | 'agent_success_perplexity_search'
    | 'agent_success_capture_lead'
    | 'agent_success_transcribe'
    | 'agent_success_book_calendar_meeting'
    | 'agent_success_custom_http'
    | 'agent_success_shopify_check_order_status'
    | 'agent_success_shopify_track_shipment'
    | 'agent_success_shopify_view_product_details'
    | 'agent_success_shopify_list_available_products'
    | 'agent_success_shopify_check_refund_status'
    | 'agent_success_shopify_view_store_policies'
    | 'agent_success_shopify_update_customer_profile'
    | 'agent_success_shopify_request_return'
    | 'agent_success_astra_boats_search'
    | 'agent_success_astra_boat_by_id'
    | 'agent_success_astra_boat_price'
    | 'agent_success_hubspot_create_lead'
    | 'agent_success_hubspot_create_ticket'
    | 'agent_success_zendesk_create_lead'
    | 'agent_success_zendesk_create_ticket'
    | 'agent_success_woocommerce_check_order_status'
    | 'agent_success_woocommerce_get_product'
    | 'agent_success_woocommerce_list_products'
    | 'agent_success_woocommerce_check_refund_status'
    | 'agent_success_woocommerce_get_coupons'
    | 'agent_success_woocommerce_check_customer_downloads'
    | 'agent_success_shipping_tracking'
    | 'agent_success_article_generator'
}

export interface ISourceDetails {
  urls: string[]
  files: { name: string; location: string; file_url: string | undefined }[]
}

export interface IPerson {
  email: string
  name?: string
}
