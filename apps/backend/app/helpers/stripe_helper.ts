import Stripe from 'stripe'
import adonisApp from '@adonisjs/core/services/app'

export class StripeHelper {
  static async getStripe() {
    return await adonisApp.container.make('stripe')
  }

  static async createCustomer(params: {
    name: string
    email: string
    metadata: Stripe.Emptyable<Stripe.MetadataParam>
  }): Promise<Stripe.Customer> {
    const { name, email, metadata } = params

    const stripe = await StripeHelper.getStripe()

    return await stripe.customers.create({
      name,
      email,
      metadata,
    })
  }

  static async updateCustomer(params: {
    customerId: string
    name: string
    email: string
  }): Promise<Stripe.Customer> {
    const { name, email } = params

    const stripe = await StripeHelper.getStripe()

    return await stripe.customers.create({
      name,
      email,
    })
  }

  static async createSubscription(params: {
    stripeCustomerId: string
    priceId: string
    metadata: Stripe.Emptyable<Stripe.MetadataParam>
  }): Promise<Stripe.Subscription> {
    const { stripeCustomerId, priceId, metadata } = params

    const stripe = await StripeHelper.getStripe()

    return await stripe.subscriptions.create({
      customer: stripeCustomerId,
      trial_from_plan: true,
      payment_behavior: 'default_incomplete',
      // one item max
      items: [
        {
          price: priceId,
          quantity: 1,
        },
      ],
      metadata,
    })
  }

  static async getAllStripeSubscriptions(params: {
    stripeCustomerId: string
  }): Promise<Stripe.Subscription[]> {
    const { stripeCustomerId } = params

    const stripe = await StripeHelper.getStripe()

    const [{ data: activeSubscriptions }, { data: trialingSubscriptions }] = await Promise.all([
      stripe.subscriptions.list({
        customer: stripeCustomerId,
        limit: 100,
        status: 'active',
      }),
      stripe.subscriptions.list({
        customer: stripeCustomerId,
        limit: 100,
        status: 'trialing',
      }),
    ])

    return [...activeSubscriptions, ...trialingSubscriptions]
  }
}
