import download from 'download'
import md5File from 'md5-file'
import mime from 'mime-types'
import fs from 'node:fs'
import path from 'node:path'
import { MultipartFile } from '@adonisjs/core/bodyparser'
import { cuid } from '@adonisjs/core/helpers'

export class FileHelper {
  static getContentType(filePath: string) {
    return mime.lookup(filePath) || 'application/octet-stream'
  }

  static getFileName(filePath: string) {
    const parts = filePath.split('/')
    const fileNameWithExtension = parts[parts.length - 1]
    const fileNameParts = fileNameWithExtension.split('.')

    if (fileNameParts.length === 1) {
      return fileNameWithExtension
    }

    fileNameParts.pop()

    return fileNameParts.join('.')
  }

  static getFileExtension(filePath: string) {
    if (!filePath) {
      return ''
    }

    let pathName = filePath

    try {
      const url = new URL(filePath)

      pathName = url.pathname
    } catch (error) {}

    const extension = path.extname(pathName).toLowerCase()

    if (extension === '' || extension === pathName) {
      return ''
    }

    return extension.startsWith('.') ? extension.slice(1) : extension
  }

  static async download(url: string, name?: string) {
    const extension = name ? FileHelper.getFileExtension(name) : FileHelper.getFileExtension(url)

    const tmpPath = path.join('/tmp', `${cuid()}.${extension}`)
    const data = await download(url)

    if (data) {
      await fs.promises.writeFile(tmpPath, data)

      return tmpPath
    }
  }

  static getChecksum(filePath: string): string | undefined {
    return md5File.sync(filePath)
  }

  static async convertFileToAdonisFormat(tmpPath: string): Promise<MultipartFile> {
    const buffer = await fs.promises.readFile(tmpPath)
    const extname = FileHelper.getFileExtension(tmpPath)
    const name = `${FileHelper.getFileName(tmpPath)}.${extname}`
    const size = Buffer.byteLength(buffer)
    const checksum = FileHelper.getChecksum(tmpPath)
    const type = mime.lookup(tmpPath)

    return {
      buffer,
      clientName: name,
      extname,
      tmpPath,
      size,
      fieldName: 'file',
      headers: {},
      isMultipartFile: true,
      validated: true,
      state: { consumed: false },
      meta: { name, extname, size, checksum },
      moved: false,
      type: type || 'application/octet-stream',
      subtype: (type || 'application/octet-stream')?.split('/')[1],
    } as unknown as MultipartFile
  }
}
