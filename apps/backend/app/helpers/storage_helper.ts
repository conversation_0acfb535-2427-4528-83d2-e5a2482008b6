import { FileHelper } from '#app/helpers'
import { bucketConfigValidator, bucketTypeValidator, storageValidator } from '#app/validators'
import env from '#start/env'
import {
  _Object,
  DeleteObjectCommand,
  GetObjectCommand,
  GetObjectCommandOutput,
  HeadObjectCommand,
  ListObjectsV2Command,
  S3Client,
} from '@aws-sdk/client-s3'
import { Upload } from '@aws-sdk/lib-storage'
import { getSignedUrl } from '@aws-sdk/s3-request-presigner'
import { Infer } from '@vinejs/vine/types'
import { Readable } from 'node:stream'
import stream from 'stream'
import { MultipartFile } from '@adonisjs/core/bodyparser'
import { cuid } from '@adonisjs/core/helpers'

export type BucketType = Infer<typeof bucketTypeValidator>
type BucketConfig = Infer<typeof bucketConfigValidator>

export class StorageHelper {
  private readonly MAX_KEYS = 1000
  private readonly DEFAULT_EXPIRES_IN = 60 * 60 * 24 * 6 // 6 days
  private bucketEndpoint: string
  private bucketName: string
  private accessKey: string
  private secretKey: string
  private client: S3Client
  private isPublic: boolean

  private readonly BUCKET_CONFIGS: Record<BucketType, BucketConfig> = {
    knowledge: {
      bucket_name: env.get('S3_DEFAULT_KNOWLEDGE_BUCKET'),
      is_public: false,
    },
    agents: {
      bucket_name: env.get('S3_DEFAULT_AGENTS_BUCKET'),
      is_public: true,
    },
    apps: {
      bucket_name: env.get('S3_DEFAULT_APPS_BUCKET'),
      is_public: true,
    },
    chats: {
      bucket_name: env.get('S3_DEFAULT_CHATS_BUCKET'),
      is_public: false,
    },
    custom: {
      bucket_name: '',
      is_public: false,
    },
  }

  constructor(bucketType: BucketType, storageConfig?: Infer<typeof storageValidator>) {
    const bucketConfig = this.BUCKET_CONFIGS[bucketType]

    if (!bucketConfig) {
      throw {
        message: `Invalid bucket type: ${bucketType}`,
        status: 400,
        code: 'base/invalid-bucket-type',
      }
    }

    this.bucketName = bucketConfig.bucket_name
    this.isPublic = bucketConfig.is_public

    if (storageConfig) {
      this.bucketEndpoint = storageConfig.bucket_endpoint
      this.accessKey = storageConfig.access_key
      this.secretKey = storageConfig.secret_key
      this.bucketName = storageConfig.bucket_name
      this.isPublic = false
    } else {
      this.bucketEndpoint = env.get('S3_DEFAULT_ENDPOINT')
      this.accessKey = env.get('S3_DEFAULT_KEY')
      this.secretKey = env.get('S3_DEFAULT_SECRET')
    }

    this.client = new S3Client({
      endpoint: this.bucketEndpoint.replace(/\/+$/, ''),
      credentials: {
        accessKeyId: this.accessKey,
        secretAccessKey: this.secretKey,
      },
      forcePathStyle: true,
      responseChecksumValidation: 'WHEN_REQUIRED',
      requestChecksumCalculation: 'WHEN_REQUIRED',
    })
  }

  getClient() {
    return this.client
  }

  getBucketEndpoint() {
    return this.bucketEndpoint
  }

  async saveStream(params: {
    multipartStream: Readable & {
      file: MultipartFile
    }
    basePath: string
    isPublic?: boolean
  }): Promise<{
    directUrl: string
    storagePath: string
    extname: string | undefined
    size: number
  }> {
    const { multipartStream, basePath } = params
    const isPublic = params.isPublic ?? this.isPublic
    const { file } = multipartStream
    const { type, size } = file
    const extname = FileHelper.getFileExtension(file.clientName)
    const storagePath = `${basePath}/${cuid()}_${cuid()}.${extname}`
    const passThroughStream = new stream.PassThrough()
    const parallelUpload = new Upload({
      client: this.client,
      params: {
        Bucket: this.bucketName,
        Key: storagePath,
        Body: passThroughStream,
        ContentType: type,
        ACL: isPublic ? 'public-read' : 'private',
      },
    })

    multipartStream.pipe(passThroughStream)

    await parallelUpload.done()

    let directUrl

    if (isPublic) {
      directUrl = this.bucketEndpoint + '/' + this.bucketName + '/' + storagePath
    } else {
      directUrl = await this.getSignedUrl(storagePath)
    }

    return {
      directUrl,
      storagePath,
      extname,
      size,
    }
  }

  async copyToStorage(params: {
    keyToDownload: string
    destinationClient: S3Client
    destinationBasePath: string
  }): Promise<{ directUrl: string | undefined }> {
    const { destinationClient, destinationBasePath, keyToDownload } = params

    const downloadedFile = await this.getFile(keyToDownload)

    if (!downloadedFile || !downloadedFile.Body) {
      return { directUrl: undefined }
    }

    const extname = FileHelper.getFileExtension(keyToDownload)
    const storagePath = `${destinationBasePath}/${cuid()}_${cuid()}.${extname}`
    const multipartStream = Readable.from(downloadedFile.Body as Readable)
    const passThroughStream = new stream.PassThrough()
    const parallelUpload = new Upload({
      client: destinationClient,
      params: {
        Bucket: this.bucketName,
        Key: storagePath,
        Body: passThroughStream,
        ContentType: downloadedFile.ContentType,
      },
    })

    multipartStream.pipe(passThroughStream)

    await parallelUpload.done()

    return { directUrl: this.bucketEndpoint + '/' + this.bucketName + '/' + storagePath }
  }

  async getFileMetadata(storagePath: string): Promise<{
    name: string
    extname: string
    etag: string
    size: number
    type: string
    subtype: string
  } | null> {
    const headObjectResponse = await this.client.send(
      new HeadObjectCommand({ Bucket: this.bucketName, Key: storagePath })
    )

    const extname = FileHelper.getFileExtension(storagePath)
    const etag = headObjectResponse.ETag?.replace(/"/g, '') || ''
    const size = headObjectResponse.ContentLength
    const [type, subtype] = headObjectResponse.ContentType?.split('/') || []

    return {
      name: storagePath,
      extname,
      etag,
      size: size || 0,
      type,
      subtype,
    }
  }

  async getSignedUrl(key: string): Promise<string> {
    const command = new GetObjectCommand({
      Bucket: this.bucketName,
      Key: key,
      ResponseContentDisposition: 'inline',
      ResponseContentType: FileHelper.getContentType(key),
      ResponseCacheControl: `public, max-age=${this.DEFAULT_EXPIRES_IN}`,
    })

    return await getSignedUrl(this.client, command, { expiresIn: this.DEFAULT_EXPIRES_IN })
  }

  async listAllFiles() {
    const files: _Object[] = []
    let continuationToken: string | undefined
    let retryCount = 0
    const maxRetries = 3

    const listObjectsWithRetry = async (): Promise<void> => {
      while (true) {
        try {
          const command = new ListObjectsV2Command({
            Bucket: this.bucketName,
            MaxKeys: this.MAX_KEYS,
            ContinuationToken: continuationToken,
          })

          const response = await this.client.send(command)

          files.push(...(response.Contents || []))

          continuationToken = response.NextContinuationToken

          if (!continuationToken) {
            break
          }

          retryCount = 0
        } catch (error) {
          if (retryCount >= maxRetries) {
            throw new Error(`Failed to list objects after ${maxRetries} retries: ${error.message}`)
          }

          retryCount++
        }
      }
    }

    await listObjectsWithRetry()

    return files
  }

  async testConnection(): Promise<void> {
    try {
      await this.client.send(new ListObjectsV2Command({ Bucket: this.bucketName, MaxKeys: 1 }))
    } catch (error) {
      if (error.name === 'NoSuchBucket') {
        throw {
          message: `Bucket ${this.bucketName} does not exist.`,
          status: 400,
          code: 'base/bucket-not-found',
        }
      } else if (error.name === 'AccessDenied') {
        throw {
          message: `Access denied to bucket ${this.bucketName}. Check your permissions.`,
          status: 400,
          code: 'base/access-denied',
        }
      } else if (error.name === 'CredentialsProviderError') {
        throw {
          message: 'Invalid credentials.',
          status: 400,
          code: 'base/invalid-credentials',
        }
      } else {
        throw {
          message: `Error accessing bucket ${this.bucketName}: ${error.message}`,
          status: 400,
          code: 'base/error-accessing-bucket',
        }
      }
    }
  }

  async getFile(key: string): Promise<GetObjectCommandOutput> {
    try {
      const getObjectCommand = new GetObjectCommand({
        Bucket: this.bucketName,
        Key: key,
      })

      return await this.client.send(getObjectCommand)
    } catch (error) {
      throw {
        message: `Failed to get file ${key}: ${error.message}`,
        status: 400,
        code: 'base/error-getting-file',
      }
    }
  }

  async deleteFile(key: string): Promise<void> {
    try {
      await this.client.send(
        new DeleteObjectCommand({
          Bucket: this.bucketName,
          Key: key,
        })
      )
    } catch (error) {
      throw {
        message: `Failed to delete file ${key}: ${error.message}`,
        status: 400,
        code: 'base/error-deleting-file',
      }
    }
  }
}
