import { StringHelper } from '#app/helpers'
import axios from 'axios'
import type { Request } from '@adonisjs/core/http'
import type { ICreateTrace } from '#app/interfaces'
import Agent from '#app/modules/agents/agent_model'
import App from '#app/modules/apps/app_model'
import Trace from '#app/modules/traces/trace_model'

export class TraceHelper {
  private static async sendWebhook(url: string, trace: Trace) {
    try {
      axios({
        method: 'POST',
        url,
        headers: {},
        data: trace,
        timeout: 60 * 1000,
      })
    } catch (error) {
      console.error(`Failed to send webhook to ${url}`, error)
    }
  }

  static async createTrace(params: ICreateTrace) {
    const {
      appUid,
      userUid,
      agentUid,
      chatUid,
      messageUid,
      contactUid,
      feedbackUid,
      providerUid,
      label,
      description,
      trigger,
      action,
      status,
      ctx,
      metadata,
      credits,
      sendWebhook,
    } = params

    let trace = new Trace()

    trace.app_uid = appUid
    trace.user_uid = userUid || null
    trace.agent_uid = agentUid || null
    trace.chat_uid = chatUid || null
    trace.message_uid = messageUid || null
    trace.contact_uid = contactUid || null
    trace.feedback_uid = feedbackUid || null
    trace.provider_uid = providerUid || null
    trace.trigger = trigger || null
    trace.action = action || null
    trace.credits = credits || 0
    trace.request_url = null
    trace.request_params = null
    trace.request_qs = null
    trace.request_body = null
    trace.ip = null
    trace.location = null
    trace.metadata = metadata || null
    trace.label = label || null
    trace.description = description || null
    trace.status = status || null

    try {
      if (ctx?.request) {
        const request: Request = ctx?.request as Request

        trace.request_url = request?.request?.url || null
        trace.request_method = request?.request?.method || null
        trace.request_body = request.body()
        trace.request_params = request.params()
        trace.request_qs = request.qs()
        trace.ip = request?.ip()
        trace.location = TraceHelper.extractLocationHeaders(request)
      }

      const createdTrace = await Trace.create(trace)

      try {
        if (agentUid && sendWebhook) {
          const app = await App.findByOrFail('uid', appUid)
          const { currentPlanProduct } = app.subscriptions?.getPlan() || {}

          if (currentPlanProduct?.limitations?.webhooks) {
            const agent = await Agent.find(agentUid)

            if (agent && agent?.webhook_endpoint) {
              TraceHelper.sendWebhook(agent.webhook_endpoint, createdTrace)
            }
          }
        }
      } catch (error) {
        console.error(error)
      }

      return createdTrace
    } catch (error) {
      console.error(trace)
    }
  }

  static extractLocationHeaders(request: Request): Record<string, any> {
    const headers = request?.request?.headers || {}
    const query = request?.qs() || {}
    const url = request?.parsedUrl

    const getHeader = (key: string): string | null => {
      const val = headers[key]

      if (Array.isArray(val) && val.length > 0) {
        return val[0]
      }

      return typeof val === 'string' ? val : null
    }

    const acceptLanguage = getHeader('accept-language')
    const languages = acceptLanguage
      ? acceptLanguage.split(',').map((l) => l.split(';')[0].trim())
      : null

    const country = getHeader('cf-ipcountry')
    const saveData = getHeader('save-data')

    const data: Record<string, any> = {
      // Standard headers
      referer: getHeader('referer'),
      origin: getHeader('origin'),

      // Client hints (browser & device) - Using standard Sec-CH-UA headers
      sec_ch_ua: getHeader('sec-ch-ua'),
      sec_ch_ua_mobile: StringHelper.safeParseBoolean(getHeader('sec-ch-ua-mobile')),
      sec_ch_ua_arch: getHeader('sec-ch-ua-arch')?.replace(/"/g, ''),
      sec_ch_ua_bitness: getHeader('sec-ch-ua-bitness')?.replace(/"/g, ''),
      sec_ch_ua_full_version: getHeader('sec-ch-ua-full-version')?.replace(/"/g, ''),
      sec_ch_ua_full_version_list: getHeader('sec-ch-ua-full-version-list'),
      sec_ch_ua_platform: getHeader('sec-ch-ua-platform')?.replace(/"/g, ''),
      sec_ch_ua_platform_version: getHeader('sec-ch-ua-platform-version')?.replace(/"/g, ''),
      sec_ch_ua_model: getHeader('sec-ch-ua-model')?.replace(/"/g, ''),

      // Navigator & performance - Some might need client-side JS to capture accurately
      user_agent: getHeader('user-agent'),
      cookie_enabled: StringHelper.safeParseBoolean(getHeader('cookie') ? 'true' : 'false'),
      languages: languages,
      device_memory: StringHelper.safeParseFloat(getHeader('device-memory')),
      max_touch_points: StringHelper.safeParseInt(getHeader('max-touch-points')),
      device_pixel_ratio: StringHelper.safeParseFloat(getHeader('device-pixel-ratio')),
      dpr: StringHelper.safeParseFloat(getHeader('dpr')),

      // Screen properties - These typically require client-side JS
      screen_width: StringHelper.safeParseInt(getHeader('screen-width')),
      screen_height: StringHelper.safeParseInt(getHeader('screen-height')),
      avail_width: StringHelper.safeParseInt(getHeader('avail-width')),
      avail_height: StringHelper.safeParseInt(getHeader('avail-height')),
      orientation_type: getHeader('orientation-type'),
      orientation_angle: StringHelper.safeParseInt(getHeader('orientation-angle')),
      width: StringHelper.safeParseInt(getHeader('width')),
      viewport_width: StringHelper.safeParseInt(getHeader('viewport-width')),

      // Network & connection
      network_type: getHeader('ect'),
      network_downlink: StringHelper.safeParseFloat(getHeader('downlink')),
      network_rtt: StringHelper.safeParseFloat(getHeader('rtt')),
      network_save_data: saveData ? ['1', 'on'].includes(saveData.toLowerCase()) : null,

      // Time & locale
      timezone: getHeader('cf-timezone'),
      timezone_offset: StringHelper.safeParseInt(getHeader('timezone-offset')),
      locale_date: getHeader('locale-date'),
      locale_number: getHeader('locale-number'),
      accept_language: acceptLanguage,

      // Vendor & product
      vendor: getHeader('vendor'),
      product: getHeader('product'),
      product_sub: getHeader('product-sub'),

      // Geolocation from CF headers
      ip: getHeader('cf-connecting-ip') || request.ip(),
      city: getHeader('cf-ipcity'),
      region: getHeader('cf-region'),
      country: country,
      continent: getHeader('cf-ipcontinent'),
      postal_code: getHeader('cf-postal-code'),
      latitude: getHeader('cf-iplatitude'),
      longitude: getHeader('cf-iplongitude'),
      country_emoji: country ? StringHelper.countryIsoToEmoji(country) : null,

      // Application context (UTM parameters from Query String)
      utm_source: query?.utm_source || null,
      utm_medium: query?.utm_medium || null,
      utm_campaign: query?.utm_campaign || null,
      utm_term: query?.utm_term || null,
      utm_content: query?.utm_content || null,

      // Page (from request URL and Query String)
      page_path: url?.pathname || null,
      page_query: url?.search || null,

      // Other headers
      dnt: getHeader('dnt'),
    }

    Object.keys(data).forEach((key) => (data[key] == null ? delete data[key] : {}))

    return data
  }
}
