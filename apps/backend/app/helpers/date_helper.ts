import { DateTime, Settings } from 'luxon'

Settings.defaultLocale = 'en-US'
Settings.defaultZone = 'America/New_York'

export class DateHelper {
  static getNow(): DateTime {
    return DateTime.now().setZone('America/New_York').setLocale('en-US')
  }

  static convertToDateTime(value: string | DateTime) {
    return typeof value === 'string'
      ? DateTime.fromISO(value).setZone('America/New_York').setLocale('en-US')
      : value
  }

  static formatDate(value: string | DateTime, format: string = 'MM/dd/yyyy') {
    let dateString = ''
    const dateValue = DateHelper.convertToDateTime(value)

    if (dateValue) {
      dateString = dateValue.setZone('America/New_York').setLocale('en-US').toFormat(format)

      if (dateString.includes('Invalid')) {
        return ''
      }
    }

    return dateString
  }
}
