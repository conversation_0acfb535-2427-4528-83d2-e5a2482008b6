import env from '#start/env'
import axios from 'axios'

export class <PERSON><PERSON>lareHelper {
  private static accountId = env.get('CLOUDFLARE_ACCOUNT_ID')
  private static headers = {
    'Authorization': `Bearer ${env.get('CLOUDFLARE_API_KEY')}`,
    'Content-Type': 'application/json',
  }

  static async addDomain(domainName: string) {
    const existingDomain = await this.getDomain(domainName)

    if (existingDomain) {
      return true
    }

    await axios.post(
      `https://api.cloudflare.com/client/v4/accounts/${CloudflareHelper.accountId}/pages/projects/insertchat/domains`,
      {
        name: domainName,
      },
      {
        headers: CloudflareHelper.headers,
      }
    )

    return true
  }

  static async deleteDomain(domainName: string) {
    const existingDomain = await this.getDomain(domainName)

    if (!existingDomain) {
      return true
    }

    await axios.delete(
      `https://api.cloudflare.com/client/v4/accounts/${CloudflareHelper.accountId}/pages/projects/insertchat/domains/${domainName}`,
      {
        headers: CloudflareHelper.headers,
      }
    )

    return true
  }

  static async getDomain(domainName: string) {
    try {
      await axios.get(
        `https://api.cloudflare.com/client/v4/accounts/${CloudflareHelper.accountId}/pages/projects/insertchat/domains/${domainName}`,
        {
          headers: CloudflareHelper.headers,
        }
      )

      return true
    } catch (error) {
      if (error.response && error.response.status === 404) {
        return false
      } else {
        throw error
      }
    }
  }

  static async clearCache() {
    const { data } = await axios.post(
      `https://api.cloudflare.com/client/v4/accounts/${CloudflareHelper.accountId}/cache/purge_cache`,
      {
        purge_everything: true,
      },
      {
        headers: CloudflareHelper.headers,
      }
    )

    return data?.success
  }
}
