import { APP_SENDER, APP_SMTP, DISPOSABLE_EMAIL_DOMAINS } from '#app/constants'
import {
  senderValidator,
  simpleEmailValidator,
  smtpValidator,
  transactionalEmailValidator,
} from '#app/validators'
import env from '#start/env'
import { Infer } from '@vinejs/vine/types'
import nodemailer from 'nodemailer'
import pug from 'pug'
import adonisApp from '@adonisjs/core/services/app'
import Agent from '#app/modules/agents/agent_model'
import App from '#app/modules/apps/app_model'

export class EmailHelper {
  static isDisposableEmail(email: string): boolean {
    const domain = email.split('@')[1]

    if (email.includes('+')) {
      return true
    }

    return DISPOSABLE_EMAIL_DOMAINS.includes(domain) || email.includes('+')
  }

  static testSMTP = async (params: {
    recipient?: string
    sender: Infer<typeof senderValidator>
    smtp: Infer<typeof smtpValidator>
  }): Promise<Infer<typeof smtpValidator> | null> => {
    const { recipient, sender, smtp } = params
    const { host, port, auth } = smtp || {}
    const { user, pass } = auth || {}
    const { name, email } = sender || {}

    if (!host || !port || !user || !pass || !name || !email) {
      return null
    }

    const secure = [465].includes(port) ? true : false
    const config = {
      host,
      port,
      secure,
      auth: {
        user,
        pass,
      },
      tls: {
        rejectUnauthorized: false,
      },
      logger: false, // Enable detailed logging
      debug: false, // Enable debug mode
      requireTLS: port === 587,
      connectionTimeout: 60 * 1000,
      greetingTimeout: 60 * 1000,
      socketTimeout: 60 * 1000,
    }

    const transporter = nodemailer.createTransport(config)
    await transporter.verify()

    // Send a test email
    if (recipient) {
      await transporter.sendMail({
        from: `"${name}" <${email}>`,
        to: recipient,
        subject: 'SMTP Configuration Test Email',
        text: 'This is a test email to verify your SMTP settings.',
        html: '<p>This is a <strong>test email</strong> to verify your SMTP settings.</p>',
      })
    }

    return config
  }

  static sendSimpleEmail = async (config: Infer<typeof simpleEmailValidator>) => {
    const { to, cc, bcc, subject, content } = config

    const transporter = nodemailer.createTransport(APP_SMTP)

    const options: nodemailer.SendMailOptions = {
      from: `"${env.get('SENDER_NAME')}" <${env.get('SENDER_EMAIL')}>`,
      to: to.map((recipient) => `"${recipient?.name || ''}" <${recipient?.email}>`).join(', '),
      cc: cc?.map((recipient) => `"${recipient?.name || ''}" <${recipient?.email}>`).join(', '),
      bcc: bcc?.map((recipient) => `"${recipient?.name || ''}" <${recipient?.email}>`).join(', '),
      subject: subject,
      html: content,
    }

    return await transporter.sendMail(options)
  }

  static sendTransactionalEmail = async (config: Infer<typeof transactionalEmailValidator>) => {
    const { template_name, subject, to, cc, bcc, agent_uid, app_uid, variables } = config

    let smtp: Infer<typeof smtpValidator> | null = null
    let sender: Infer<typeof senderValidator> | null = null
    let logo: string | null = null
    let domain: string | null = null

    const [agent, app] = await Promise.all([
      agent_uid ? Agent.findByOrFail('uid', agent_uid) : null,
      App.findByOrFail('uid', app_uid),
    ])

    if (agent) {
      const [agentSmtp, agentDomain] = await Promise.all([agent.getSMTP(), agent.getDomain()])

      if (agent?.images?.logo?.url) {
        logo = agent.images.logo.url
      }

      if (agentDomain) {
        domain = agentDomain?.domain_name ?? null
      }

      if (agentSmtp) {
        smtp = {
          host: agentSmtp?.host ?? '',
          port: agentSmtp?.port ?? 587,
          secure: agentSmtp?.secure ?? false,
          auth: {
            user: agentSmtp?.username ?? '',
            pass: agentSmtp?.password ?? '',
          },
        }

        sender = {
          name: agentSmtp?.from_name ?? '',
          email: agentSmtp?.from_email ?? '',
        }
      }
    } else if (app) {
      const [appSmtp, appDomain] = await Promise.all([app.getPrimarySMTP(), app.getPrimaryDomain()])

      if (app?.logo) {
        logo = app.logo
      }

      if (appDomain) {
        domain = appDomain?.domain_name ?? null
      }

      if (appSmtp) {
        smtp = {
          host: appSmtp?.host ?? '',
          port: appSmtp?.port ?? 587,
          secure: appSmtp?.secure ?? false,
          auth: {
            user: appSmtp?.username ?? '',
            pass: appSmtp?.password ?? '',
          },
        }

        sender = {
          name: appSmtp?.from_name ?? '',
          email: appSmtp?.from_email ?? '',
        }
      }
    }

    if (!smtp) {
      smtp = APP_SMTP
      sender = APP_SENDER
    }

    if (!sender) {
      sender = APP_SENDER
    }

    if (!logo) {
      logo = env.get('SENDER_LOGO')
    }

    if (!domain) {
      domain = env.get('CLIENT_URL')
    }

    try {
      const config = await EmailHelper.testSMTP({
        smtp,
        sender,
      })

      if (config) {
        smtp = config
      }
    } catch (error) {
      console.error(error)

      smtp = APP_SMTP
    }

    const transporter = nodemailer.createTransport(smtp)

    const emailTemplatePath = adonisApp.makePath(`app/resources/emails/${template_name}.pug`)

    const html = pug.renderFile(emailTemplatePath, {
      ...variables,
      subject,
      sender,
    })

    const mailOptions: nodemailer.SendMailOptions = {
      from: `"${sender.name}" <${sender.email}>`,
      to: to.map((recipient) => `"${recipient?.name || ''}" <${recipient?.email}>`).join(', '),
      cc: cc?.map((recipient) => `"${recipient?.name || ''}" <${recipient?.email}>`).join(', '),
      bcc: bcc?.map((recipient) => `"${recipient?.name || ''}" <${recipient?.email}>`).join(', '),
      subject,
      html,
    }

    await transporter.sendMail(mailOptions)
  }
}
