export class StringHelper {
  static safeDecodeURIComponent(encodedStr: string): string {
    try {
      return decodeURIComponent(encodedStr)
    } catch (error) {
      console.error('safeDecodeURIComponent error', error)

      return encodedStr
    }
  }

  static countryIsoToEmoji(countryCode: string) {
    if (!countryCode) {
      return
    }

    const codePoints = countryCode
      .toUpperCase()
      .split('')
      .map((char) => 127397 + char.charCodeAt(0))

    return String.fromCodePoint(...codePoints)
  }

  // Helper function to safely parse a number from a string
  static safeParseFloat(value: string | null): number | null {
    if (value === null || value === undefined) return null

    const parsed = parseFloat(value)

    return isNaN(parsed) ? null : parsed
  }

  // Helper function to safely parse an integer from a string
  static safeParseInt(value: string | null): number | null {
    if (value === null || value === undefined) return null

    const parsed = parseInt(value, 10)

    return isNaN(parsed) ? null : parsed
  }

  // Helper function to safely parse a boolean from a string
  static safeParseBoolean(value: string | null): boolean | null {
    if (value === null || value === undefined) return null

    const lowerCaseValue = value.toLowerCase()

    if (lowerCaseValue === 'true' || lowerCaseValue === '1' || lowerCaseValue === 'on') return true

    if (lowerCaseValue === 'false' || lowerCaseValue === '0' || lowerCaseValue === 'off')
      return false

    return null
  }
}
