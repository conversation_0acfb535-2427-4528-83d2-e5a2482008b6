import type { ICreateGoogleConversion } from '#app/interfaces'
import App from '#app/modules/apps/app_model'
import GoogleAds from '#app/modules/google_ads/google_ads_model'

export class GoogleAdsHelper {
  static async createConversion(params: ICreateGoogleConversion) {
    const { appUid, conversionName, conversionValue, conversionCurrency, metadata } = params

    const app = await App.findByOrFail('uid', appUid)
    const user = await app.getOwner()

    if (!user) {
      return
    }

    const gclid = app?.gclid
    const email = user?.email

    if (gclid) {
      try {
        await GoogleAds.create({
          app_uid: appUid,
          user_uid: user?.uid,
          gclid,
          stripe_customer_id: app?.stripe_customer_id,
          email,
          conversionName: conversionName,
          conversionValue: conversionValue,
          conversionCurrency: conversionCurrency,
          metadata,
        })
      } catch (error) {
        console.error(error)
      }
    }

    return true
  }
}
