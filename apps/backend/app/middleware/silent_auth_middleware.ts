import { errors as authErrors } from '@adonisjs/auth'
import { AccessToken } from '@adonisjs/auth/access_tokens'
import { HttpContext } from '@adonisjs/core/http'
import { NextFn } from '@adonisjs/core/types/http'
import User from '#app/modules/users/user_model'
import SubscriptionHelper from '#app/modules/subscriptions/subscription_helper'

export default class SilentAuthMiddleware {
  async handle(ctx: HttpContext, next: NextFn) {
    const { auth, request }: HttpContext = ctx
    const token: string | undefined =
      request.header('authorization')?.split('Bearer ')?.[1] ?? request.param('token')

    if (token) {
      try {
        // await auth.use('api').authenticate()
        await auth.authenticateUsing(['api'])

        const user = auth?.user as (User & { currentAccessToken: AccessToken }) | undefined

        if (user) {
          ctx.token = token
          ctx.user = user
        }

        const app = await user?.getApp()

        if (app) {
          const hasValidSubscription = await SubscriptionHelper.computeAppSubscriptions({
            app,
            updateExisting: true,
          })

          if (!hasValidSubscription) {
            throw {
              code: 'SUBSCRIPTION_NOT_FOUND',
              message: 'No subscription found',
              status: 401,
            }
          }
        }
      } catch (error) {
        console.error('>> SilentAuthMiddleware', error)
      }
    }

    return next()
  }
}
