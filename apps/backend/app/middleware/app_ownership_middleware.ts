import { SubscriptionHelper } from '#app/helpers'
import { errors as authErrors } from '@adonisjs/auth'
import { HttpContext } from '@adonisjs/core/http'
import type { NextFn } from '@adonisjs/core/types/http'

export default class AppOwnershipMiddleware {
  async handle(ctx: HttpContext, next: NextFn) {
    const { request, user } = ctx

    if (!user) {
      throw new authErrors.E_UNAUTHORIZED_ACCESS('Unauthorized access', {
        guardDriverName: 'api',
      })
    }

    const appUid = request.param('app_uid')

    if (!appUid) {
      throw new authErrors.E_UNAUTHORIZED_ACCESS('Unauthorized access', {
        guardDriverName: 'api',
      })
    }

    const app = await user.getApp()

    if (!app) {
      throw new authErrors.E_UNAUTHORIZED_ACCESS('Unauthorized access', {
        guardDriverName: 'api',
      })
    }

    if (app?.is_banned) {
      throw new authErrors.E_UNAUTHORIZED_ACCESS('Unauthorized access', {
        guardDriverName: 'api',
      })
    }

    if (app?.uid !== appUid) {
      throw new authErrors.E_UNAUTHORIZED_ACCESS('Unauthorized access', {
        guardDriverName: 'api',
      })
    }

    const hasValidSubscription = await SubscriptionHelper.computeAppSubscriptions({
      app,
      updateExisting: true,
    })

    if (!hasValidSubscription) {
      throw {
        code: 'SUBSCRIPTION_NOT_FOUND',
        message: 'No subscription found',
        status: 401,
      }
    }

    const { currentPlanProduct, currentPlanSubscription } = app.subscriptions?.getPlan() || {}

    ctx.app = app
    ctx.planProduct = currentPlanProduct
    ctx.planSubscription = currentPlanSubscription

    return next()
  }
}
