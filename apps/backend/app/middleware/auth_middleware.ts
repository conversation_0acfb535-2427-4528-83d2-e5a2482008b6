import { AccessToken } from '@adonisjs/auth/access_tokens'
import { HttpContext } from '@adonisjs/core/http'
import type { NextFn } from '@adonisjs/core/types/http'
import User from '#app/modules/users/user_model'

export default class AuthMiddleware {
  async handle(ctx: HttpContext, next: NextFn) {
    const { auth, request }: HttpContext = ctx
    const token: string | undefined =
      request.header('authorization')?.split('Bearer ')?.[1] ?? request.param('token')

    await auth.authenticateUsing(['api'])

    const user = (await auth.getUserOrFail()) as User & { currentAccessToken: AccessToken }

    if (user) {
      ctx.token = token
      ctx.user = user
    }

    return next()
  }
}
