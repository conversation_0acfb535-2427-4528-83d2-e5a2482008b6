import { Exception } from '@adonisjs/core/exceptions'
import string from '@adonisjs/core/helpers/string'
import { HttpContext } from '@adonisjs/core/http'
import adonisApp from '@adonisjs/core/services/app'

export default class NotFoundException extends Exception {
  protected debug = !adonisApp.inProduction

  constructor(modelName: string) {
    let message = `${string.capitalCase(modelName)} not found`
    let status = 400
    let code = 'base/uid-not-found'

    super(message, { status, code })
  }

  async handle(error: this, ctx: HttpContext) {
    ctx.response.status(error.status).send({
      message: error.message,
      status: error.status,
      code: error.code,
    })
  }

  async report(error: this, ctx: HttpContext) {
    ctx.logger.error({ err: error }, error.message)
  }
}
