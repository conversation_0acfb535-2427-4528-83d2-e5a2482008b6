import env from '#start/env'
import Rollbar, { LogArgument } from 'rollbar'
import { Exception } from '@adonisjs/core/exceptions'
import { HttpContext } from '@adonisjs/core/http'
import adonisApp from '@adonisjs/core/services/app'

export default class MissingParamsException extends Exception {
  constructor() {
    super('Missing parameters', { status: 400, code: 'missing_params' })
  }

  async handle(error: this, ctx: HttpContext) {
    ctx.response
      .status(error.status)
      .send({ message: error.message, status: error.status, code: error.code })
  }

  async report(error: this, ctx: HttpContext) {
    ctx.logger.error({ err: error }, error.message)

    if (env.get('NODE_ENV') === 'production') {
      const rollbar = (await adonisApp.container.make('rollbar')) as Rollbar

      if (rollbar) {
        rollbar.error(error as LogArgument, ctx.request.request)
      }
    }
  }

  static validate(params: Record<string, any>) {
    const missingParams = Object.keys(params).filter(
      (key) => params[key] === null || params[key] === undefined
    )

    if (missingParams.length > 0) {
      throw new MissingParamsException()
    }
  }
}
