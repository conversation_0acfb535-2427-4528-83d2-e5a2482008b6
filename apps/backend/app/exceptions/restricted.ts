import { Exception } from '@adonisjs/core/exceptions'
import { HttpContext } from '@adonisjs/core/http'
import adonisApp from '@adonisjs/core/services/app'

export default class RestrictedException extends Exception {
  protected debug = !adonisApp.inProduction

  constructor() {
    let message = 'Action restricted for your role.'
    let status = 400
    let code = 'base/invalid-permission'

    super(message, { status, code })
  }

  async handle(error: this, ctx: HttpContext) {
    ctx.response.status(error.status).send({
      message: error.message,
      status: error.status,
      code: error.code,
    })
  }

  async report(error: this, ctx: HttpContext) {
    ctx.logger.error({ err: error }, error.message)
  }
}
