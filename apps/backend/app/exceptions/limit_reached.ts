import { Exception } from '@adonisjs/core/exceptions'
import { HttpContext } from '@adonisjs/core/http'
import adonisApp from '@adonisjs/core/services/app'

export default class LimitReachedException extends Exception {
  protected debug = !adonisApp.inProduction

  constructor(name: string) {
    let message = `You've reached your ${name?.toLowerCase()} limit, please upgrade to unlock more!`
    let status = 426
    let code = 'base/limit-reached'

    super(message, { status, code })
  }

  async handle(error: this, ctx: HttpContext) {
    ctx.response.status(error.status).send({
      message: error.message,
      status: error.status,
      code: error.code,
    })
  }

  async report(error: this, ctx: HttpContext) {
    ctx.logger.error({ err: error }, error.message)
  }
}
