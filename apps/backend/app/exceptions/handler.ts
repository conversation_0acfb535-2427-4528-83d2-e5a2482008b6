import axios, { AxiosError } from 'axios'
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, HttpContext } from '@adonisjs/core/http'
import app from '@adonisjs/core/services/app'

export default class HttpExceptionHandler extends ExceptionHandler {
  /**
   * In debug mode, the exception handler will display verbose errors
   * with pretty printed stack traces.
   */
  protected debug = !app.inProduction

  /**
   * The method is used for handling errors and returning
   * response to the client
   */
  async handle(error: unknown, ctx: HttpContext) {
    // Custom error handling logic
    let message = 'Internal Server Error'
    let status = 500
    let code = 'base/error'

    if (error instanceof Error) {
      message = error.message
    }

    // Axios error handling
    if (axios.isAxiosError(error)) {
      const { data, status: axiosStatus } = error.response || {}
      if (typeof data === 'string' && data.length > 0) {
        message = data
      } else {
        message = data?.message || data?.error || data?.detail || data?.error_description || message
      }
      status = axiosStatus || 500
    } else if (typeof error === 'object' && error !== null) {
      // If error has message/status/code properties
      // @ts-ignore
      if (error.message) message = error.message
      // @ts-ignore
      if (error.status) status = error.status
      // @ts-ignore
      if (error.code) code = error.code
    }

    console.log('debug - error', { message, status, code })

    ctx.response.status(status).json({ message, status, code })
  }

  /**
   * The method is used to report error to the logging service or
   * the third party error monitoring service.
   *
   * @note You should not attempt to send a response from this method.
   */
  async report(error: unknown, ctx: HttpContext) {
    ctx.logger.error({ err: error }, error instanceof Error ? error.message : String(error))
  }
}
