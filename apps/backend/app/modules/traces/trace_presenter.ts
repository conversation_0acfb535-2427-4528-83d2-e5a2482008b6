import { ModelPaginatorContract } from '@adonisjs/lucid/types/model'
import Trace from '#app/modules/traces/trace_model'

export default class TracePresenter {
  static serialize(item: Trace) {
    return {
      uid: item.uid,
      app_uid: item.app_uid,
      user_uid: item.user_uid,
      agent_uid: item.agent_uid,
      chat_uid: item.chat_uid,
      message_uid: item.message_uid,
      contact_uid: item.contact_uid,
      feedback_uid: item.feedback_uid,
      provider_uid: item.provider_uid,
      trigger: item.trigger,
      action: item.action,
      credits: item.credits,
      label: item.label,
      description: item.description,
      request_method: item.request_method,
      request_url: item.request_url,
      request_params: item.request_params,
      request_qs: item.request_qs,
      request_body: item.request_body,
      location: item.location,
      response: item.response,
      ip: item.ip,
      status: item.status,
      metadata: item.metadata,
      created_at: item.created_at,
      updated_at: item.updated_at,
    }
  }

  static serializeMany(items: Trace[]) {
    return items.map((item) => this.serialize(item))
  }

  static serializePaginated(items: ModelPaginatorContract<Trace>) {
    return {
      page: items.currentPage,
      per_page: items.perPage,
      total: items.total,
      last_page: items.lastPage,
      data: this.serializeMany(items.all()),
    }
  }
}
