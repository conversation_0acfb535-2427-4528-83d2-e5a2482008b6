import { Filterable } from 'adonis-lucid-filter'
import { DateTime } from 'luxon'
import { v4 as uuid } from 'uuid'
import { compose } from '@adonisjs/core/helpers'
import { beforeCreate, belongsTo, column, SnakeCaseNamingStrategy } from '@adonisjs/lucid/orm'
import { BaseModel } from '@adonisjs/lucid/orm'
import type { BelongsTo } from '@adonisjs/lucid/types/relations'
import Agent from '#app/modules/agents/agent_model'
import App from '#app/modules/apps/app_model'
import Chat from '#app/modules/chats/chat_model'
import Contact from '#app/modules/contacts/contact_model'
import Feedback from '#app/modules/feedbacks/feedback_model'
import Message from '#app/modules/messages/message_model'
import Provider from '#app/modules/rag/providers/provider_model'
import User from '#app/modules/users/user_model'
import BaseFilter from '#app/modules/base/base_filter'

export default class Trace extends compose(BaseModel, Filterable) {
  serializeExtras = false
  static selfAssignPrimaryKey = true
  static namingStrategy = new SnakeCaseNamingStrategy()
  static $filter = () => BaseFilter

  @beforeCreate()
  static createUUID(model: Trace) {
    model.uid = uuid()
  }

  @belongsTo(() => App, {
    localKey: 'uid',
    foreignKey: 'app_uid',
  })
  declare app: BelongsTo<typeof App>

  @belongsTo(() => User, {
    localKey: 'uid',
    foreignKey: 'user_uid',
  })
  declare user: BelongsTo<typeof User>

  @belongsTo(() => Agent, {
    localKey: 'uid',
    foreignKey: 'agent_uid',
  })
  declare agent: BelongsTo<typeof Agent>

  @belongsTo(() => Chat, {
    localKey: 'uid',
    foreignKey: 'chat_uid',
  })
  declare chat: BelongsTo<typeof Chat>

  @belongsTo(() => Message, {
    localKey: 'uid',
    foreignKey: 'message_uid',
  })
  declare message: BelongsTo<typeof Message>

  @belongsTo(() => Contact, {
    localKey: 'uid',
    foreignKey: 'contact_uid',
  })
  declare contact: BelongsTo<typeof Contact>

  @belongsTo(() => Feedback, {
    localKey: 'uid',
    foreignKey: 'feedback_uid',
  })
  declare feedback: BelongsTo<typeof Feedback>

  @belongsTo(() => Provider, {
    localKey: 'uid',
    foreignKey: 'provider_uid',
  })
  declare provider: BelongsTo<typeof Provider>

  @column({
    isPrimary: true,
    meta: { searchable: true, type: 'string', validations: [{ name: 'uuid' }] },
  })
  declare uid: string

  @column({
    meta: {
      searchable: true,
      type: 'string',
      validations: [{ name: 'uuid' }],
    },
  })
  declare app_uid: string

  @column({
    meta: {
      searchable: true,
      type: 'string',
      validations: [{ name: 'uuid' }],
    },
  })
  declare user_uid: string | null

  @column({
    meta: {
      searchable: true,
      type: 'string',
      validations: [{ name: 'uuid' }],
    },
  })
  declare agent_uid: string | null

  @column({
    meta: {
      searchable: true,
      type: 'string',
      validations: [{ name: 'uuid' }],
    },
  })
  declare chat_uid: string | null

  @column({
    meta: {
      searchable: true,
      type: 'string',
      validations: [{ name: 'uuid' }],
    },
  })
  declare message_uid: string | null

  @column({
    meta: {
      searchable: true,
      type: 'string',
      validations: [{ name: 'uuid' }],
    },
  })
  declare contact_uid: string | null

  @column({
    meta: {
      searchable: true,
      type: 'string',
      validations: [{ name: 'uuid' }],
    },
  })
  declare feedback_uid: string | null

  @column({
    meta: {
      searchable: true,
      type: 'string',
      validations: [{ name: 'uuid' }],
    },
  })
  declare provider_uid: string | null

  @column({ meta: { searchable: true, type: 'string' } })
  declare trigger: string

  @column({ meta: { searchable: true, type: 'string' } })
  declare action: string

  @column({ meta: { searchable: true, type: 'number' } })
  declare credits: number

  @column({ meta: { searchable: true, type: 'string' } })
  declare label: string | null

  @column({ meta: { searchable: true, type: 'string' } })
  declare description: string | null

  @column({ meta: { searchable: true, type: 'string' } })
  declare request_method: string | null

  @column({ meta: { searchable: true, type: 'string' } })
  declare request_url: string | null

  @column({ meta: { searchable: true, type: 'object' } })
  declare request_params: object | null

  @column({ meta: { searchable: true, type: 'object' } })
  declare request_qs: object | null

  @column({ meta: { searchable: true, type: 'object' } })
  declare request_body: object | null

  @column({ meta: { searchable: true, type: 'object' } })
  declare location: object | null

  @column({ meta: { searchable: true, type: 'string' } })
  declare response: string | null

  @column({ meta: { searchable: true, type: 'string' } })
  declare ip: string | null

  @column({ meta: { searchable: true, type: 'string' } })
  declare status: string | null

  @column({ meta: { searchable: true, type: 'object' } })
  declare metadata: { [key: string]: any } | null

  @column.dateTime({ autoCreate: true, meta: { searchable: true, type: 'date' } })
  declare created_at: DateTime

  @column.dateTime({
    autoCreate: true,
    autoUpdate: true,
    meta: { searchable: true, type: 'date' },
  })
  declare updated_at: DateTime

  @column({ serializeAs: null, meta: { searchable: true, type: 'date' } })
  declare deleted_at?: DateTime | null
}
