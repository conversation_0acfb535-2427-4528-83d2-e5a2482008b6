import { Infer } from '@vinejs/vine/types'
import { DateTime } from 'luxon'
import { inject } from '@adonisjs/core'
import { HttpContext } from '@adonisjs/core/http'
import App from '#app/modules/apps/app_model'
import { providerTypesValidator } from '#app/modules/rag/providers/provider_validator'

// Define the payload type, mirroring the one in AppModel
type StatsFiltersPayload = {
  start_date?: DateTime
  end_date?: DateTime
  agent_uid?: string
}

@inject()
export default class StatsService {
  private ctx: HttpContext

  constructor(ctx: HttpContext) {
    this.ctx = ctx
  }

  async getSourceCounts(payload: StatsFiltersPayload): Promise<{
    result: { [key in Infer<typeof providerTypesValidator>]?: number }
    total: number
  }> {
    return this.ctx.app!.getSourceCounts(payload)
  }

  async getChatCount(payload: StatsFiltersPayload) {
    return this.ctx.app!.getChatCount(payload)
  }

  async getContactCount(payload: StatsFiltersPayload) {
    return this.ctx.app!.getContactCount(payload)
  }

  async getMessageCount(payload: StatsFiltersPayload) {
    return this.ctx.app!.getMessageCount(payload)
  }

  async getFeedbackCount(payload: StatsFiltersPayload) {
    return this.ctx.app!.getFeedbackCount(payload)
  }

  async getFileCount(payload: StatsFiltersPayload) {
    return this.ctx.app!.getFileCount(payload)
  }

  async getTotalCreditsUsed(payload: StatsFiltersPayload) {
    return this.ctx.app!.getTotalCreditsUsed(payload)
  }

  async getResolvedChatCount(payload: StatsFiltersPayload) {
    return this.ctx.app!.getResolvedChatCount(payload)
  }
}
