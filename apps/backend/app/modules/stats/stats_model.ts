import { Filterable } from 'adonis-lucid-filter'
import { compose } from '@adonisjs/core/helpers'
import { BaseModel, SnakeCaseNamingStrategy } from '@adonisjs/lucid/orm'
import BaseFilter from '#app/modules/base/base_filter'

export default class Stats extends compose(BaseModel, Filterable) {
  serializeExtras = false
  static selfAssignPrimaryKey = true
  static namingStrategy = new SnakeCaseNamingStrategy()
  static $filter = () => BaseFilter
}
