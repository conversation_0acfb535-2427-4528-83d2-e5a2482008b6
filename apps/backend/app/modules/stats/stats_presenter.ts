import { ModelPaginatorContract } from '@adonisjs/lucid/types/model'
import Stats from '#app/modules/stats/stats_model'

export default class StatsPresenter {
  static serialize(item: Stats) {
    return {}
  }

  static serializeMany(items: Stats[]) {
    return items.map((item) => this.serialize(item))
  }

  static serializePaginated(items: ModelPaginatorContract<Stats>) {
    return {
      page: items.currentPage,
      per_page: items.perPage,
      total: items.total,
      last_page: items.lastPage,
      data: this.serializeMany(items.all()),
    }
  }
}
