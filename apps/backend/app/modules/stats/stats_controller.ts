import env from '#start/env'
import { DateTime } from 'luxon'
import { mergeAndConcat } from 'merge-anything'
import { inject } from '@adonisjs/core'
import { HttpContext } from '@adonisjs/core/http'
import Stats from '#app/modules/stats/stats_model'
import StatsService from '#app/modules/stats/stats_service'

@inject()
export default class StatsController {
  protected model = Stats

  constructor(private StatsService: StatsService) {}

  @inject()
  async getAllStats({ request, response }: HttpContext, statsService: StatsService) {
    // Extract potential filters
    const { start_date: startDateString, end_date: endDateString } = request.qs()
    const agent_uid = request.param('agent_uid')

    // Prepare payload object
    const payload: { start_date?: DateTime; end_date?: DateTime; agent_uid?: string } = {}

    // Validate and parse dates, adding to payload
    if (startDateString) {
      const startDate = DateTime.fromISO(startDateString)

      if (!startDate.isValid) {
        return response.badRequest({ error: 'Invalid start_date format. Use ISO 8601.' })
      }

      payload.start_date = startDate
    }

    if (endDateString) {
      const endDate = DateTime.fromISO(endDateString)

      if (!endDate.isValid) {
        return response.badRequest({ error: 'Invalid end_date format. Use ISO 8601.' })
      }

      payload.end_date = endDate
    }

    // Add agentUid to payload if present
    if (agent_uid) {
      payload.agent_uid = agent_uid
    }

    // Call service methods in parallel with payload
    const [
      source_counts,
      chat_count,
      contact_count,
      message_count,
      feedback_count,
      file_count,
      total_credits_used,
      resolved_chat_count,
    ] = await Promise.all([
      this.StatsService.getSourceCounts(payload),
      this.StatsService.getChatCount(payload),
      this.StatsService.getContactCount(payload),
      this.StatsService.getMessageCount(payload),
      this.StatsService.getFeedbackCount(payload),
      this.StatsService.getFileCount(payload),
      this.StatsService.getTotalCreditsUsed(payload),
      this.StatsService.getResolvedChatCount(payload),
    ])

    // Combine results (structure remains the same)
    const allStats = {
      sources: { total: source_counts.total },
      chats: { total: chat_count, resolved: resolved_chat_count },
      contacts: { total: contact_count },
      messages: { total: message_count },
      feedbacks: { total: feedback_count },
      files: { total: file_count },
      credits_used: { total: total_credits_used },
    }

    return response.ok(allStats)
  }
}
