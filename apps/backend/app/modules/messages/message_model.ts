import { Filterable } from 'adonis-lucid-filter'
import { DateTime } from 'luxon'
import { v4 as uuid } from 'uuid'
import { compose } from '@adonisjs/core/helpers'
import {
  beforeCreate,
  belongsTo,
  column,
  hasMany,
  hasOne,
  SnakeCaseNamingStrategy,
} from '@adonisjs/lucid/orm'
import { BaseModel } from '@adonisjs/lucid/orm'
import type { BelongsTo, HasMany, HasOne } from '@adonisjs/lucid/types/relations'
import type { ISourceDetails } from '#app/interfaces'
import Agent from '#app/modules/agents/agent_model'
import App from '#app/modules/apps/app_model'
import Chat from '#app/modules/chats/chat_model'
import Feedback from '#app/modules/feedbacks/feedback_model'
import File from '#app/modules/files/file_model'
import Model from '#app/modules/models/model_model'
import BaseFilter from '#app/modules/base/base_filter'

export default class Message extends compose(BaseModel, Filterable) {
  serializeExtras = false
  static selfAssignPrimaryKey = true
  static namingStrategy = new SnakeCaseNamingStrategy()
  static $filter = () => BaseFilter

  @beforeCreate()
  static createUUID(model: Message) {
    model.uid = uuid()
  }

  @belongsTo(() => App, {
    localKey: 'uid',
    foreignKey: 'app_uid',
  })
  declare app: BelongsTo<typeof App>

  @belongsTo(() => Agent, {
    localKey: 'uid',
    foreignKey: 'agent_uid',
  })
  declare agent: BelongsTo<typeof Agent>

  @belongsTo(() => Chat, {
    localKey: 'uid',
    foreignKey: 'chat_uid',
  })
  declare chat: BelongsTo<typeof Chat>

  @belongsTo(() => Model, {
    localKey: 'uid',
    foreignKey: 'model_uid',
  })
  declare model: BelongsTo<typeof Model>

  @hasOne(() => Feedback, {
    localKey: 'uid',
    foreignKey: 'message_uid',
  })
  declare feedback: HasOne<typeof Feedback>

  @hasMany(() => File, {
    localKey: 'uid',
    foreignKey: 'message_uid',
  })
  declare files: HasMany<typeof File>

  @column({
    isPrimary: true,
    meta: { searchable: true, type: 'string', validations: [{ name: 'uuid' }] },
  })
  declare uid: string

  @column({
    meta: {
      searchable: true,
      type: 'string',
      validations: [{ name: 'uuid' }],
    },
  })
  declare app_uid: string

  @column({
    meta: {
      searchable: true,
      type: 'string',
      validations: [{ name: 'uuid' }],
    },
  })
  declare agent_uid: string

  @column({
    meta: {
      searchable: true,
      type: 'string',
      validations: [{ name: 'uuid' }],
    },
  })
  declare chat_uid: string

  @column({
    meta: {
      searchable: true,
      type: 'string',
      validations: [{ name: 'uuid' }],
    },
  })
  declare model_uid: string

  @column({ meta: { searchable: true, type: 'string' } })
  declare role: string

  @column({ meta: { searchable: true, type: 'string' } })
  declare output_text: string

  @column({ meta: { searchable: true, type: 'string' } })
  declare output_form: string

  @column({ meta: { searchable: true, type: 'string' } })
  declare agent_choice: string

  @column({ serializeAs: null, meta: { searchable: true, type: 'number' } })
  declare credits: number

  @column({ meta: { searchable: true, type: 'string' } })
  declare context: string

  @column({ meta: { searchable: true, type: 'string' } })
  declare tts_file_url: string

  @column({
    meta: { searchable: true, type: 'array', children_type: 'string' },
  })
  declare agents: string[] | null

  @column({
    serializeAs: null,
    meta: { searchable: true, type: 'array', children_type: 'object' },
  })
  declare suggestions: object[] | null

  @column({ serializeAs: null, meta: { searchable: true, type: 'object' } })
  declare sources: ISourceDetails | null

  @column({ meta: { searchable: true, type: 'object' } })
  declare metadata: { [key: string]: any } | null

  @column.dateTime({ autoCreate: true, meta: { searchable: true, type: 'date' } })
  declare created_at: DateTime

  @column.dateTime({
    autoCreate: true,
    autoUpdate: true,
    meta: { searchable: true, type: 'date' },
  })
  declare updated_at: DateTime

  @column({ serializeAs: null, meta: { searchable: true, type: 'date' } })
  declare deleted_at?: DateTime | null
}
