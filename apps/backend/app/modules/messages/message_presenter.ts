import { ModelPaginatorContract } from '@adonisjs/lucid/types/model'
import Message from '#app/modules/messages/message_model'

export default class MessagePresenter {
  static serialize(item: Message) {
    return {
      uid: item.uid,
      app_uid: item.app_uid,
      agent_uid: item.agent_uid,
      chat_uid: item.chat_uid,
      model_uid: item.model_uid,
      role: item.role,
      output_text: item.output_text,
      output_form: item.output_form,
      agent_choice: item.agent_choice,
      context: item.context,
      tts_file_url: item.tts_file_url,
      agents: item.agents,
      metadata: item.metadata,
      created_at: item.created_at,
      updated_at: item.updated_at,
    }
  }

  static serializeMany(items: Message[]) {
    return items.map((item) => this.serialize(item))
  }

  static serializePaginated(items: ModelPaginatorContract<Message>) {
    return {
      page: items.currentPage,
      per_page: items.perPage,
      total: items.total,
      last_page: items.lastPage,
      data: this.serializeMany(items.all()),
    }
  }
}
