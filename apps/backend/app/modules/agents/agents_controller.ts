import {
  DEFAULT_AGENT_SETTINGS,
  DEFAULT_AGENTS_TOOLS,
  TRAINING_IMAGE_EXTENSIONS,
} from '#app/constants'
import RestrictedException from '#app/exceptions/restricted'
import { StorageHelper } from '#app/helpers'
import { requestFilterValidator } from '#app/validators'
import vine from '@vinejs/vine'
import { Infer } from '@vinejs/vine/types'
import { v4 as uuid } from 'uuid'
import { inject } from '@adonisjs/core'
import { MultipartFile } from '@adonisjs/core/bodyparser'
import { HttpContext } from '@adonisjs/core/http'
import Embedding from '#app/modules/rag/embeddings/embedding_model'
import AgentService from '#app/modules/agents/agent_service'
import EmbeddingService from '#app/modules/rag/embeddings/embedding_service'
import ProviderService from '#app/modules/rag/providers/provider_service'
import SourceService from '#app/modules/rag/sources/source_service'
import {
  agent<PERSON><PERSON>Validator,
  agentUpdateImageKeyValidator,
  agentUpdateOnboardingValidator,
  agentUpdateValidator,
} from '#app/modules/agents/agent_validator'
import AgentPresenter from '#app/modules/agents/agent_presenter'

@inject()
export default class AgentsController {
  constructor(
    private AgentService: AgentService,
    private providerService: ProviderService,
    private sourceService: SourceService,
    private embeddingService: EmbeddingService
  ) {}

  async index({ request, response }: HttpContext) {
    const payload = await request.validateUsing(requestFilterValidator)

    const agents = await this.AgentService.index(payload)

    return response.status(200).json(AgentPresenter.serializePaginated(agents))
  }

  async show({ request, response }: HttpContext) {
    const uid = request.param('uid')

    const agent = await this.AgentService.show(uid)

    return response.status(200).json(AgentPresenter.serialize(agent))
  }

  async getFactorySettings({ response }: HttpContext) {
    return response.status(200).json(DEFAULT_AGENT_SETTINGS)
  }

  async getFactoryTools({ response }: HttpContext) {
    return response.status(200).json(DEFAULT_AGENTS_TOOLS)
  }

  async store({ app, user, request, response }: HttpContext) {
    const { label, purpose, how_did_you_find_us } = await request.validateUsing(agentStoreValidator)

    if (!user?.role || !['admin', 'owner', 'manager'].includes(user?.role)) {
      throw new RestrictedException()
    }

    await app.canConsumeAgents({ throwError: true })

    const agent = await this.AgentService.store({
      label,
      purpose,
      how_did_you_find_us,
    })

    await this.embeddingService.createTable(agent.getEmbeddingTableName())

    return response.status(201).json(AgentPresenter.serialize(agent))
  }

  async update({ request, response }: HttpContext) {
    const uid = request.param('uid')

    const payload = await request.validateUsing(agentUpdateValidator)
    const agent = await this.AgentService.update(uid, payload)

    await this.embeddingService.createTable(agent.getEmbeddingTableName())

    return response.status(201).json(AgentPresenter.serialize(agent))
  }

  async updateImage({ app, request, response }: HttpContext) {
    const uid = request.param('uid')
    let key: Infer<typeof agentUpdateImageKeyValidator> = 'logo'

    request.multipart.onFile(
      '*',
      {
        size: '10mb',
        extnames: TRAINING_IMAGE_EXTENSIONS,
      },
      async (multipartStream, reporter) => {
        key = multipartStream.name as Infer<typeof agentUpdateImageKeyValidator>

        multipartStream.pause()
        multipartStream.on('data', reporter)

        const storageClient = new StorageHelper('agents')

        const { directUrl } = await storageClient.saveStream({
          multipartStream,
          basePath: `${app.uid}/${uid}`,
        })

        return { directUrl }
      }
    )

    await request.multipart.process()

    const imageMultipart = request.file(key) as MultipartFile
    const directUrl: string = imageMultipart.meta.directUrl

    if (key && directUrl) {
      const agent = await this.AgentService.updateImage({
        uid,
        key,
        url: directUrl,
      })

      return response.status(201).json(AgentPresenter.serialize(agent))
    }
  }

  async updateOnboarding({ request, response }: HttpContext) {
    const uid = request.param('uid')

    const { status, key } = await request.validateUsing(agentUpdateOnboardingValidator)

    const agent = await this.AgentService.updateOnboarding({ uid, key, status })

    return response.status(201).json(AgentPresenter.serialize(agent))
  }

  async duplicateWithoutTraining({ app, request, response, user }: HttpContext) {
    const uid = request.param('uid')

    if (!user?.role || !['admin', 'owner', 'manager'].includes(user?.role)) {
      throw new RestrictedException()
    }

    await app.canConsumeAgents({ throwError: true })

    const agent = await this.AgentService.duplicate(uid)

    return response.status(200).json(AgentPresenter.serialize(agent))
  }

  async duplicateWithTraining(ctx: HttpContext) {
    const { app, user, request, response } = ctx
    const uid = request.param('uid')

    if (!user?.role || !['admin', 'owner', 'manager'].includes(user?.role)) {
      throw new RestrictedException()
    }

    await app.canConsumeAgents({ throwError: true })

    const savedAgent = await this.AgentService.duplicate(uid)

    await this.embeddingService.createTable(savedAgent.getEmbeddingTableName())

    const oldAgent = await this.AgentService.show(uid)

    await oldAgent.preload('providers', (query) => query.preload('sources'))

    if (oldAgent?.providers && oldAgent?.providers?.length) {
      const providerPromises = oldAgent.providers.map(async (oldProvider) => {
        const newProvider = await this.providerService.copyToAgent({
          uid: oldProvider.uid,
          target_agent_uid: savedAgent.uid,
        })

        if (oldProvider?.sources && oldProvider?.sources?.length) {
          const sourcePromises = oldProvider.sources.map(async (oldSource) => {
            const newSource = await this.sourceService.copyToProvider({
              source_uid: oldSource.uid,
              target_provider_uid: newProvider.uid,
            })

            const embeddings = await this.embeddingService.getAllBySource({
              table: oldAgent.getEmbeddingTableName(),
              source_uid: oldSource.uid,
            })

            if (embeddings) {
              const updatedEmbeddings = embeddings.map((embedding: Embedding) => ({
                uid: uuid(),
                source_uid: newSource.uid,
                embedding: JSON.stringify(embedding.embedding),
                metadata: {
                  chunk_id: embedding.metadata?.chunk_id || 0,
                  content: embedding.metadata?.content || '',
                  app_uid: newSource.app_uid,
                  agent_uid: savedAgent.uid,
                  provider_uid: newProvider.uid,
                  type: oldProvider.type,
                  language: oldProvider.language,
                  url: newSource.url,
                  youtube: newSource.youtube,
                  document: newSource.document,
                  media: newSource.media,
                  quizz: newSource.quizz,
                  product: newSource.product,
                  model_embedding: savedAgent.model_embedding,
                  model_dimensions: 1536,
                },
              }))

              await this.embeddingService.multiInsert({
                table: savedAgent.getEmbeddingTableName(),
                embeddings: updatedEmbeddings,
              })
            }
          })

          await Promise.all(sourcePromises)
        }
      })

      await Promise.all(providerPromises)
    }

    return response.status(200).json(AgentPresenter.serialize(savedAgent))
  }

  async destroy(ctx: HttpContext) {
    const { user, request, response } = ctx
    const uid = request.param('uid')

    if (!user?.role || !['admin', 'owner', 'manager'].includes(user?.role)) {
      throw new RestrictedException()
    }

    const agent = await this.AgentService.show(uid)

    await Promise.all([
      this.embeddingService.deleteTable(agent.getEmbeddingTableName()),
      this.AgentService.destroy(uid),
    ])

    return response.status(202).json({ success: true })
  }
}
