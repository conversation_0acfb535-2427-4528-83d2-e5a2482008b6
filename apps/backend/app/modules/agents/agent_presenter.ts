import { ModelPaginatorContract } from '@adonisjs/lucid/types/model'
import Agent from '#app/modules/agents/agent_model'

export default class AgentPresenter {
  static serialize(item: Agent) {
    return {
      uid: item.uid,
      app_uid: item.app_uid,
      model_uid: item.model_uid,
      copyright_uid: item.copyright_uid,
      domain_uid: item.domain_uid,
      smtp_uid: item.smtp_uid,
      model_embedding: item.model_embedding,
      label: item.label,
      purpose: item.purpose,
      timezone: item.timezone,
      prompt_system: item.prompt_system,
      context_behavior: item.context_behavior,
      temperature: item.temperature,
      password: item.password,
      conversation_saver: item.conversation_saver,
      source_discloser: item.source_discloser,
      voice: item.voice,
      debug: item.debug,
      is_vision_capable: item.is_vision_capable,
      hide_agent: item.hide_agent,
      hide_inbox: item.hide_inbox,
      hide_navbar: item.hide_navbar,
      hide_urls: item.hide_urls,
      hide_filenames: item.hide_filenames,
      gtm_id: item.gtm_id,
      credits_per_month: item.credits_per_month,
      webhook_endpoint: item.webhook_endpoint,
      greetings: item.greetings,
      suggestions: item.suggestions,
      notifications: item.notifications,
      mobile_app: item.mobile_app,
      bubble: item.bubble,
      buttons: item.buttons,
      styling: item.styling,
      images: item.images,
      onboarding: item.onboarding,
      whatsapp: item.whatsapp,
      metadata: item.metadata,
      created_at: item.created_at,
      updated_at: item.updated_at,
      computed_files_path: item.computed_files_path,
      computed_greetings: item.computed_greetings,
      computed_suggestions: item.computed_suggestions,
      computed_notifications: item.computed_notifications,
      computed_mobile_app: item.computed_mobile_app,
      computed_bubble: item.computed_bubble,
      computed_buttons: item.computed_buttons,
      computed_styling: item.computed_styling,
      computed_images: item.computed_images,
      computed_onboarding: item.computed_onboarding,
      embed_url: item.embed_url,
      is_password_protected: item.is_password_protected,
    }
  }

  static serializeMany(items: Agent[]) {
    return items.map((item) => this.serialize(item))
  }

  static serializePaginated(items: ModelPaginatorContract<Agent>) {
    return {
      page: items.currentPage,
      per_page: items.perPage,
      total: items.total,
      last_page: items.lastPage,
      data: this.serializeMany(items.all()),
    }
  }
}
