import { DEFAULT_AGENT_SETTINGS } from '#app/constants'
import env from '#start/env'
import type { Infer } from '@vinejs/vine/types'
import { Filterable } from 'adonis-lucid-filter'
import { DateTime } from 'luxon'
import { v4 as uuid } from 'uuid'
import { compose } from '@adonisjs/core/helpers'
import {
  beforeCreate,
  belongsTo,
  column,
  computed,
  hasMany,
  hasOne,
  manyToMany,
  SnakeCaseNamingStrategy,
} from '@adonisjs/lucid/orm'
import { BaseModel } from '@adonisjs/lucid/orm'
import db from '@adonisjs/lucid/services/db'
import type { BelongsTo, HasMany, HasOne, ManyToMany } from '@adonisjs/lucid/types/relations'
import App from '#app/modules/apps/app_model'
import Chat from '#app/modules/chats/chat_model'
import Contact from '#app/modules/contacts/contact_model'
import Message from '#app/modules/messages/message_model'
import Model from '#app/modules/models/model_model'
import Provider from '#app/modules/rag/providers/provider_model'
import Source from '#app/modules/rag/sources/source_model'
import Tool from '#app/modules/tools/tool_model'
import Copyright from '#app/modules/whitelabel/copyrights/copyright_model'
import Domain from '#app/modules/whitelabel/domains/domain_model'
import Key from '#app/modules/whitelabel/keys/key_model'
import SMTP from '#app/modules/whitelabel/smtps/smtp_model'
import {
  agentBubbleValidator,
  agentButtonsValidator,
  agentGreetingsValidator,
  agentImagesValidator,
  agentMobileAppValidator,
  agentNotificationsValidator,
  agentOnboardingValidator,
  agentStylingValidator,
  agentSuggestionsValidator,
} from '#app/modules/agents/agent_validator'
import BaseFilter from '#app/modules/base/base_filter'

export default class Agent extends compose(BaseModel, Filterable) {
  serializeExtras = false
  static selfAssignPrimaryKey = true
  static namingStrategy = new SnakeCaseNamingStrategy()
  static $filter = () => BaseFilter

  @beforeCreate()
  static createUUID(model: Agent) {
    model.uid = uuid()
  }

  @belongsTo(() => App, {
    localKey: 'uid',
    foreignKey: 'app_uid',
  })
  declare app: BelongsTo<typeof App>

  @hasOne(() => Model, {
    localKey: 'model_uid',
    foreignKey: 'uid',
  })
  declare model: HasOne<typeof Model>

  @hasMany(() => Message, {
    localKey: 'uid',
    foreignKey: 'agent_uid',
  })
  declare messages: HasMany<typeof Message>

  @hasMany(() => Contact, {
    localKey: 'uid',
    foreignKey: 'agent_uid',
  })
  declare contacts: HasMany<typeof Contact>

  @manyToMany(() => Provider, {
    pivotTable: 'agent_providers',
    pivotTimestamps: true,
  })
  declare providers: ManyToMany<typeof Provider>

  @hasMany(() => Source, {
    localKey: 'uid',
    foreignKey: 'agent_uid',
  })
  declare sources: HasMany<typeof Source>

  @hasMany(() => Chat, {
    localKey: 'uid',
    foreignKey: 'agent_uid',
  })
  declare chats: HasMany<typeof Chat>

  @hasMany(() => Tool, {
    localKey: 'uid',
    foreignKey: 'agent_uid',
  })
  declare tools: HasMany<typeof Tool>

  @hasOne(() => Copyright, {
    foreignKey: 'uid',
    localKey: 'copyright_uid',
  })
  declare copyright: HasOne<typeof Copyright>

  @hasOne(() => Domain, {
    foreignKey: 'uid',
    localKey: 'domain_uid',
  })
  declare domain: HasOne<typeof Domain>

  @hasOne(() => SMTP, {
    foreignKey: 'uid',
    localKey: 'smtp_uid',
  })
  declare smtp: HasOne<typeof SMTP>

  @hasOne(() => Key, {
    foreignKey: 'uid',
    localKey: 'key_uid',
  })
  declare key: HasOne<typeof Key>

  @column({
    isPrimary: true,
    meta: { searchable: true, type: 'string', validations: [{ name: 'uuid' }] },
  })
  declare uid: string

  @column({
    meta: {
      searchable: true,
      type: 'string',
      validations: [{ name: 'uuid' }],
    },
  })
  declare app_uid: string

  @column({
    meta: {
      searchable: true,
      type: 'string',
      validations: [{ name: 'uuid' }],
    },
  })
  declare model_uid: string

  @column({
    meta: { searchable: true, type: 'string', validations: [{ name: 'uuid' }] },
  })
  declare copyright_uid: string | null

  @column({
    meta: { searchable: true, type: 'string', validations: [{ name: 'uuid' }] },
  })
  declare domain_uid: string | null

  @column({
    meta: { searchable: true, type: 'string', validations: [{ name: 'uuid' }] },
  })
  declare smtp_uid: string | null

  @column({ meta: { searchable: true, type: 'string' } })
  declare model_embedding: string

  @column({ meta: { searchable: true, type: 'string' } })
  declare label: string

  @column({ meta: { searchable: true, type: 'string' } })
  declare purpose: string

  @column({
    meta: { searchable: true, type: 'string', validations: [{ name: 'noEscape' }] },
  })
  declare timezone: string

  @column({
    meta: { searchable: true, type: 'string', validations: [{ name: 'noEscape' }] },
  })
  declare prompt_system: string

  @column({ meta: { searchable: true, type: 'string' } })
  declare context_behavior: string

  @column({ meta: { searchable: true, type: 'number' } })
  declare temperature: number

  @column({ meta: { searchable: true, type: 'string' } })
  declare password: string

  @column({ meta: { searchable: true, type: 'boolean' } })
  declare conversation_saver: boolean

  @column({ meta: { searchable: true, type: 'boolean' } })
  declare source_discloser: boolean

  @column({ meta: { searchable: true, type: 'string' } })
  declare voice: 'alloy' | 'ash' | 'coral' | 'echo' | 'fable' | 'onyx' | 'nova' | 'sage' | 'shimmer'

  @column({ meta: { searchable: true, type: 'boolean' } })
  declare debug: boolean

  @column({ meta: { searchable: true, type: 'boolean' } })
  declare is_vision_capable: boolean

  @column({ meta: { searchable: true, type: 'boolean' } })
  declare hide_agent: boolean

  @column({ meta: { searchable: true, type: 'boolean' } })
  declare hide_inbox: boolean

  @column({ meta: { searchable: true, type: 'boolean' } })
  declare hide_navbar: boolean

  @column({ meta: { searchable: true, type: 'boolean' } })
  declare hide_urls: boolean

  @column({ meta: { searchable: true, type: 'boolean' } })
  declare hide_filenames: boolean

  @column({
    meta: { searchable: true, type: 'string', validations: [{ name: 'noEscape' }] },
  })
  declare gtm_id: string

  @column({ meta: { searchable: false, type: 'number' } })
  declare credits_per_month: number

  @column({
    meta: { searchable: false, type: 'string', validations: [{ name: 'noEscape' }] },
  })
  declare webhook_endpoint: string

  @column({ meta: { searchable: true, type: 'array' } })
  declare greetings: Infer<typeof agentGreetingsValidator>

  @column({ meta: { searchable: true, type: 'array' } })
  declare suggestions: Infer<typeof agentSuggestionsValidator>

  @column({ meta: { searchable: true, type: 'array' } })
  declare notifications: Infer<typeof agentNotificationsValidator>

  @column({ meta: { searchable: true, type: 'object' } })
  declare mobile_app: Infer<typeof agentMobileAppValidator>

  @column({ meta: { searchable: true, type: 'object' } })
  declare bubble: Infer<typeof agentBubbleValidator>

  @column({ meta: { searchable: true, type: 'object' } })
  declare buttons: Infer<typeof agentButtonsValidator>

  @column({ meta: { searchable: true, type: 'object' } })
  declare styling: Infer<typeof agentStylingValidator>

  @column({ meta: { searchable: true, type: 'object' } })
  declare images: Infer<typeof agentImagesValidator>

  @column({ meta: { searchable: true, type: 'object' } })
  declare onboarding: Infer<typeof agentOnboardingValidator>

  @column({ meta: { searchable: true, type: 'object' } })
  declare whatsapp: object

  @column({ meta: { searchable: true, type: 'object' } })
  declare metadata: { [key: string]: any } | null

  @column.dateTime({ autoCreate: true, meta: { searchable: true, type: 'date' } })
  declare created_at: DateTime

  @column.dateTime({
    autoCreate: true,
    autoUpdate: true,
    meta: { searchable: true, type: 'date' },
  })
  declare updated_at: DateTime

  @column({ serializeAs: null, meta: { searchable: true, type: 'date' } })
  declare deleted_at?: DateTime | null

  @computed()
  get avatar(): string {
    return `https://api.dicebear.com/9.x/bottts-neutral/svg?seed=${this.uid}`
  }

  @computed()
  get computed_files_path(): string {
    return `${env.get('NODE_ENV')}/${this.app_uid}/${this.uid}`
  }

  @computed()
  get computed_greetings(): Infer<typeof agentGreetingsValidator> {
    return this.greetings || DEFAULT_AGENT_SETTINGS.greetings
  }

  @computed()
  get computed_suggestions(): Infer<typeof agentSuggestionsValidator> {
    return this.suggestions || DEFAULT_AGENT_SETTINGS.suggestions
  }

  @computed()
  get computed_notifications(): Infer<typeof agentNotificationsValidator> {
    return this.notifications || DEFAULT_AGENT_SETTINGS.notifications
  }

  @computed()
  get computed_mobile_app(): Infer<typeof agentMobileAppValidator> {
    return this.mobile_app || DEFAULT_AGENT_SETTINGS.mobile_app
  }

  @computed()
  get computed_bubble(): Infer<typeof agentBubbleValidator> {
    return this.bubble || DEFAULT_AGENT_SETTINGS.bubble
  }

  @computed()
  get computed_buttons(): Infer<typeof agentButtonsValidator> {
    return this.buttons || DEFAULT_AGENT_SETTINGS.buttons
  }

  @computed()
  get computed_styling(): Infer<typeof agentStylingValidator> {
    return this.styling || DEFAULT_AGENT_SETTINGS.styling
  }

  @computed()
  get computed_images(): Infer<typeof agentImagesValidator> {
    return this.images || DEFAULT_AGENT_SETTINGS.images
  }

  @computed()
  get computed_onboarding(): Infer<typeof agentOnboardingValidator> {
    return this.onboarding || DEFAULT_AGENT_SETTINGS.onboarding
  }

  async getCopyright() {
    if (this.copyright_uid) {
      const copyright = await Copyright.find(this.copyright_uid)

      if (copyright) {
        return copyright
      }
    }

    const app = await App.find(this.app_uid)

    if (app) {
      return app.getPrimaryCopyright()
    }

    return null
  }

  async getDomain() {
    if (this.domain_uid) {
      const domain = await Domain.find(this.domain_uid)

      if (domain) {
        return domain
      }
    }

    const app = await App.find(this.app_uid)

    if (app) {
      return app.getPrimaryDomain()
    }

    return null
  }

  async getSMTP() {
    if (this.smtp_uid) {
      const smtp = await SMTP.find(this.smtp_uid)

      if (smtp) {
        return smtp
      }
    }

    const app = await App.find(this.app_uid)

    if (app) {
      return app.getPrimarySMTP()
    }

    return null
  }

  @computed()
  get embed_url(): string {
    return `${env.get('EMBED_URL')}/embed/${this?.uid}`
  }

  @computed()
  get is_password_protected(): boolean {
    return !!this.password
  }

  async isSourceUnique(params: {
    key: 'url' | 'youtube' | 'document->>etag' | 'media->>etag'
    value: string
  }) {
    const { key, value } = params

    let query_builder = db.from('sources').where('agent_uid', this.uid)

    if (key === 'document->>etag') {
      query_builder = query_builder.whereRaw("document->>'etag' = ?", [value])
    } else if (key === 'media->>etag') {
      query_builder = query_builder.whereRaw("media->>'etag' = ?", [value])
    } else {
      query_builder = query_builder.where(key, value)
    }

    const sources = await query_builder.limit(1)

    return !sources?.length
  }

  getEmbeddingTableName(): string {
    return `e_${this.uid?.replace(/-/g, '_')}`
  }

  getEmbeddingIndexName(): string {
    return `idx_hnsw_${this.uid?.replace(/-/g, '_')}`
  }

  prewarmIndex() {
    db.rawQuery(`SELECT pg_prewarm('embeddings.${this.getEmbeddingIndexName()}')`)
  }
}
