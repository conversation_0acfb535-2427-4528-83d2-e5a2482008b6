import { requestFilterValidator } from '#app/validators'
import env from '#start/env'
import { Infer } from '@vinejs/vine/types'
import { inject } from '@adonisjs/core'
import { HttpContext } from '@adonisjs/core/http'
import emitter from '@adonisjs/core/services/emitter'
import Agent from '#app/modules/agents/agent_model'
import {
  agentStoreValidator,
  agentUpdateImageKeyValidator,
  agentUpdateOnboardingValidator,
  agentUpdateValidator,
} from '#app/modules/agents/agent_validator'

@inject()
export default class AgentService {
  private ctx: HttpContext

  constructor(ctx: HttpContext) {
    this.ctx = ctx
  }

  async index(filter: Infer<typeof requestFilterValidator>) {
    const page = filter.page || 1

    if (this.ctx.user?.role === 'client') {
      const assignedAgents = this.ctx.user?.assigned_agents ?? []

      return await this.ctx.app
        .related('agents')
        .query()
        .filter(filter)
        .whereIn('uid', assignedAgents)
        .paginate(page, env.get('APP_PAGINATION_LIMIT'))
    } else {
      return await this.ctx.app
        .related('agents')
        .query()
        .filter(filter)
        .paginate(page, env.get('APP_PAGINATION_LIMIT'))
    }
  }

  async store(payload: Infer<typeof agentStoreValidator>) {
    const agent = await this.ctx.app.related('agents').create({
      label: payload.label,
      purpose: payload.purpose,
      temperature: 1,
      voice: 'alloy',
      timezone: 'America/New_York',
      context_behavior: 'answer_without_context',
      model_embedding: 'text-embedding-3-small',
    })

    emitter.emit('google_ads:create', {
      appUid: agent?.app_uid,
      conversionName: 'created_chatbot',
      conversionValue: 2,
      conversionCurrency: 'USD',
    })

    return agent
  }

  async show(uid: string) {
    return await this.ctx.app.related('agents').query().where('uid', uid).firstOrFail()
  }

  async update(uid: string, payload: Infer<typeof agentUpdateValidator>) {
    const agent = await this.ctx.app.related('agents').query().where('uid', uid).firstOrFail()

    return agent.merge(payload).save()
  }

  async updateOnboarding(params: {
    uid: string
    key: 'created_message' | 'created_training' | 'change_interface' | 'change_settings'
    status: boolean
  }) {
    const { uid, key, status } = params

    const agent = await this.ctx.app.related('agents').query().where('uid', uid).firstOrFail()

    await agentUpdateOnboardingValidator.validate({
      key,
      status,
    })

    return await agent
      .merge({
        onboarding: {
          ...agent.computed_onboarding,
          [key]: status,
        },
      })
      .save()
  }

  async updateImage(params: {
    uid: string
    key: Infer<typeof agentUpdateImageKeyValidator>
    url: string
  }) {
    const { uid, key, url } = params

    const agent = await this.ctx.app.related('agents').query().where('uid', uid).firstOrFail()

    if (key === 'mobile_app_icon_512_512') {
      const mobileApp = { ...agent.computed_mobile_app }

      if (mobileApp && mobileApp.icon_512_512) {
        mobileApp.icon_512_512.url = url

        await agent.merge({ mobile_app: mobileApp }).save()
      }

      return agent
    } else {
      const images = { ...agent.computed_images }

      if (images[key]) {
        images[key].url = url

        await agent.merge({ images }).save()
      }

      return agent
    }
  }

  async destroy(uid: string) {
    const agent = await this.ctx.app.related('agents').query().where('uid', uid).firstOrFail()

    return await agent.delete()
  }

  async duplicate(uid: string) {
    const agent = await this.ctx.app.related('agents').query().where('uid', uid).firstOrFail()

    const payload = agent.serializeAttributes()

    delete payload?.uid

    payload.label += ' - Duplicate'

    return await this.ctx.app.related('agents').create(payload)
  }

  async getOnboardingStatus(uid: string) {
    const { onboarding } = await this.ctx.app
      .related('agents')
      .query()
      .where('uid', uid)
      .firstOrFail()

    return onboarding
  }
}
