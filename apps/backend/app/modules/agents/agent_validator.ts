import vine from '@vinejs/vine'

const purposeSchema = vine.enum([
  'support',
  'ecommerce',
  'sales',
  'marketing',
  'education',
  'healthcare',
  'real_estate',
  'personal_assistant',
  'character',
  'other',
])

const greetingsSchema = vine.array(
  vine.object({
    text: vine.string(),
  })
)

const suggestionsSchema = vine.array(
  vine.object({
    text: vine.string(),
  })
)

const notificationsSchema = vine.array(vine.object({}).allowUnknownProperties())

const copyrightSchema = vine.object({
  status: vine.boolean(),
  text: vine.string().trim(),
  url: vine.string().trim(),
})

const gdprSchema = vine.object({
  status: vine.boolean(),
  text: vine.string().trim(),
  url: vine.string().trim(),
})

const mobileAppSchema = vine.object({
  status: vine.boolean(),
  name: vine.string().trim(),
  short_name: vine.string().trim(),
  description: vine.string().trim(),
  text: vine.string().trim(),
  categories: vine.array(vine.string()),
  icon_512_512: vine.object({
    url: vine.string().trim(),
  }),
})

const bubbleSchema = vine.object({
  is_online: vine.boolean(),
  position: vine.string().trim(),
  background: vine.string().trim(),
  size: vine.number(),
  bottom: vine.number(),
  alignment: vine.number(),
  placeholder: vine.string().trim(),
  is_open: vine.boolean(),
})

const buttonsSchema = vine.object({
  status_history_button: vine.boolean(),
  status_share_button: vine.boolean(),
  status_feedback_button: vine.boolean(),
  status_copy_button: vine.boolean(),
  status_listen_button: vine.boolean(),
  status_microphone_button: vine.boolean(),
})

const stylingWindowSchema = vine.object({
  background: vine.string().trim(),
  color: vine.string().trim(),
  icons: vine.string().trim(),
  icons_size: vine.number(),
  border: vine.string().trim(),
  size: vine.number(),
  font: vine.string().nullable(),
})

const stylingMessageSchema = vine.object({
  color: vine.string().trim(),
  background: vine.string().trim(),
  width: vine.string().trim(),
})

const stylingSchema = vine.object({
  window: stylingWindowSchema,
  user_message: stylingMessageSchema,
  assistant_message: stylingMessageSchema,
})

const iconSchema = vine.object({
  url: vine.string().trim(),
})

const imageSchema = vine.object({
  status: vine.boolean(),
  url: vine.string().trim(),
})

const imagesSchema = vine.object({
  logo: imageSchema,
  favicon: imageSchema,
  user_avatar: imageSchema,
  assistant_avatar: imageSchema,
  open_icon: iconSchema,
  close_icon: iconSchema,
})

const onboardingSchema = vine.object({
  created_message: vine.boolean(),
  created_training: vine.boolean(),
  change_interface: vine.boolean(),
  change_settings: vine.boolean(),
})

const agentSchema = vine.object({
  uid: vine.string().uuid(),
  model_uid: vine.string().uuid(),
  model_embedding: vine.string().uuid(),
  type: vine.string(),
  label: vine.string().trim(),
  purpose: purposeSchema,
  name: vine.string().trim(),
  timezone: vine.string().trim(),
  prompt_system: vine.string().trim(),
  context_behavior: vine.string().trim(),
  temperature: vine.number().min(0).max(1),
  password: vine.string().trim(),
  conversation_saver: vine.boolean(),
  source_discloser: vine.boolean(),
  voice: vine.string().trim(),
  debug: vine.boolean(),
  is_vision_capable: vine.boolean(),
  hide_agent: vine.boolean(),
  hide_inbox: vine.boolean(),
  hide_navbar: vine.boolean(),
  hide_urls: vine.boolean(),
  hide_filenames: vine.boolean(),
  custom_css: vine.string().trim(),
  code_head: vine.string().trim(),
  code_body: vine.string().trim(),
  credits_per_month: vine.number().min(0),
  webhook_endpoint: vine.string().trim(),
  metadata: vine.object({}).allowUnknownProperties(),
  greetings: greetingsSchema,
  suggestions: suggestionsSchema,
  notifications: notificationsSchema,
  copyright: copyrightSchema,
  gdpr: gdprSchema,
  mobile_app: mobileAppSchema,
  bubble: bubbleSchema,
  buttons: buttonsSchema,
  styling: stylingSchema,
  images: imagesSchema,
  onboarding: onboardingSchema,
})

const storeSchema = vine.object({
  label: vine.string().trim(),
  purpose: purposeSchema,
  how_did_you_find_us: vine.enum([
    'search_engine',
    'social_media',
    'word_of_mouth',
    'review_sites',
    'youtube',
    'event',
    'newsletter',
    'footer_copyright',
    'influencer',
    'partnership',
    'other',
  ]),
})

const agentStoreValidator = vine.compile(storeSchema)
const agentValidator = vine.compile(agentSchema)

const updateSchema = vine.object(agentSchema.getProperties())
const agentUpdateValidator = vine.compile(updateSchema)

const updateImageKeySchema = vine.enum([
  'logo',
  'favicon',
  'user_avatar',
  'assistant_avatar',
  'open_icon',
  'close_icon',
  'mobile_app_icon_512_512',
])
const agentUpdateImageKeyValidator = vine.compile(updateImageKeySchema)

const updateOnboardingSchema = vine.object({
  status: vine.boolean(),
  key: vine.enum(['created_message', 'created_training', 'change_interface', 'change_settings']),
})
const agentUpdateOnboardingValidator = vine.compile(updateOnboardingSchema)

const settingsSchema = vine.object({
  greetings: greetingsSchema,
  suggestions: suggestionsSchema,
  notifications: notificationsSchema,
  copyright: copyrightSchema,
  gdpr: gdprSchema,
  mobile_app: mobileAppSchema,
  bubble: bubbleSchema,
  buttons: buttonsSchema,
  styling: stylingSchema,
  images: imagesSchema,
  onboarding: onboardingSchema,
})
const agentSettingsValidator = vine.compile(settingsSchema)
const agentPurposeValidator = vine.compile(purposeSchema)
const agentGreetingsValidator = vine.compile(greetingsSchema)
const agentSuggestionsValidator = vine.compile(suggestionsSchema)
const agentNotificationsValidator = vine.compile(notificationsSchema)
const agentCopyrightValidator = vine.compile(copyrightSchema)
const agentGdprValidator = vine.compile(gdprSchema)
const agentMobileAppValidator = vine.compile(mobileAppSchema)
const agentBubbleValidator = vine.compile(bubbleSchema)
const agentButtonsValidator = vine.compile(buttonsSchema)
const agentStylingValidator = vine.compile(stylingSchema)
const agentImagesValidator = vine.compile(imagesSchema)
const agentOnboardingValidator = vine.compile(onboardingSchema)

export {
  agentStoreValidator,
  agentValidator,
  agentUpdateValidator,
  agentUpdateImageKeyValidator,
  agentUpdateOnboardingValidator,
  agentSettingsValidator,
  agentPurposeValidator,
  agentGreetingsValidator,
  agentSuggestionsValidator,
  agentNotificationsValidator,
  agentCopyrightValidator,
  agentGdprValidator,
  agentMobileAppValidator,
  agentBubbleValidator,
  agentButtonsValidator,
  agentStylingValidator,
  agentImagesValidator,
  agentOnboardingValidator,
}
