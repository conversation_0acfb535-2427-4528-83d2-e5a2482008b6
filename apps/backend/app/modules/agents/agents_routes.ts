import { middleware } from '#start/kernel'
import router from '@adonisjs/core/services/router'

export default () => {
  const controller = () => import('#app/modules/agents/agents_controller')

  router
    .group(() => {
      router.get('/duplicate/:uid', [controller, 'duplicateWithoutTraining'])
      router
        .get('/duplicate-with-training/:uid', [controller, 'duplicateWithTraining'])
        .use(middleware.no_timeout())
      router.put('/update-image/:uid', [controller, 'updateImage'])
      router.put('/update-onboarding/:uid', [controller, 'updateOnboarding'])
      router.get('/factory-settings', [controller, 'getFactorySettings'])
      router.get('/factory-tools', [controller, 'getFactoryTools'])
      router.get('/', [controller, 'index'])
      router.get('/:uid', [controller, 'show'])
      router.post('/', [controller, 'store'])
      router.put('/:uid', [controller, 'update'])
      router.delete('/:uid', [controller, 'destroy'])
    })
    .prefix('/agents')
}
