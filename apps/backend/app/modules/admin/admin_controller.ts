import { HttpContext } from '@adonisjs/core/http'
import Agent from '#app/modules/agents/agent_model'
import User from '#app/modules/users/user_model'

export default class AdminController {
  async v2_step_1({ response }: HttpContext) {
    // User module
    /*
    const users = await User.query().select('uid', 'gclid')

    for (const user of users) {
      const app = await user.getApp()

      if (user?.gclid) {
        await app.merge({ gclid: user?.gclid }).save()
      }
    }

    // Agent module
    const agents = await Agent.query().select(
      'legacy_company_name',
      'legacy_company_description',
      'legacy_company_website',
      'legacy_company_address',
      'legacy_company_phone',
      'legacy_company_email',
      'legacy_page_title',
      'legacy_page_description',
      'legacy_writing_language',
      'legacy_writing_tone',
      'legacy_writing_style',
      'legacy_writing_format',
      'legacy_writing_length'
    )

    for (const agent of agents) {
      agent.company = {
        name: agent?.legacy_company_name,
        description: agent?.legacy_company_description,
        website: agent?.legacy_company_website,
        address: agent?.legacy_company_address,
        phone: agent?.legacy_company_phone,
        email: agent?.legacy_company_email,
      }

      agent.page = {
        title: agent?.legacy_page_title,
        description: agent?.legacy_page_description,
      }

      agent.writing = {
        language: agent?.legacy_writing_language,
        tone: agent?.legacy_writing_tone,
        style: agent?.legacy_writing_style,
        format: agent?.legacy_writing_format,
        length: agent?.legacy_writing_length,
      }

      if (agent?.mobile_app?.icon_512_512) {
        agent.mobile_app.icon = agent.mobile_app.icon_512_512

        delete agent.mobile_app.icon_512_512
      }

      if (agent?.bubble) {
        agent.buttons.status_history = agent.bubble.status_history_button
        agent.buttons.status_share = agent.bubble.status_share_button
        agent.buttons.status_feedback = agent.bubble.status_feedback_button
        agent.buttons.status_copy = agent.bubble.status_copy_button
        agent.buttons.status_listen = agent.bubble.status_listen_button
        agent.buttons.status_microphone = agent.bubble.status_microphone_button

        delete agent.bubble.status_history_button
        delete agent.bubble.status_share_button
        delete agent.bubble.status_feedback_button
        delete agent.bubble.status_copy_button
        delete agent.bubble.status_listen_button
        delete agent.bubble.status_microphone_button
      }

      await agent.save()
    }
    */

    /* 
    - Sources module
    - Move from questions to question
    - Add answer
    */

    return response.json({ success: true })
  }
}
