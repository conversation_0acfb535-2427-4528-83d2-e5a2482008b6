import router from '@adonisjs/core/services/router'

export default () => {
  const ENDPOINT_PREFIX = '/tools'
  const CONTROLLER_PATH = '#app/modules/tools/tools_controller'

  router
    .group(() => {
      router.get('/', `${CONTROLLER_PATH}.index`)
      router.get('/:uid', `${CONTROLLER_PATH}.show`)
      router.post('/', `${CONTROLLER_PATH}.store`)
      router.put('/:uid', `${CONTROLLER_PATH}.update`)
      router.patch('/toggle/:uid', `${CONTROLLER_PATH}.toggleStatus`)
      router.delete('/:uid', `${CONTROLLER_PATH}.destroy`)
    })
    .prefix(`${ENDPOINT_PREFIX}`)
}
