import { Infer } from '@vinejs/vine/types'
import { Filterable } from 'adonis-lucid-filter'
import { DateTime } from 'luxon'
import { v4 as uuid } from 'uuid'
import { compose } from '@adonisjs/core/helpers'
import { beforeCreate, belongsTo, column, SnakeCaseNamingStrategy } from '@adonisjs/lucid/orm'
import { BaseModel } from '@adonisjs/lucid/orm'
import type { BelongsTo } from '@adonisjs/lucid/types/relations'
import Agent from '#app/modules/agents/agent_model'
import { toolStoreValidator } from '#app/modules/tools/tool_validator'
import BaseFilter from '#app/modules/base/base_filter'

export default class Tool extends compose(BaseModel, Filterable) {
  serializeExtras = false
  static selfAssignPrimaryKey = true
  static namingStrategy = new SnakeCaseNamingStrategy()
  static $filter = () => BaseFilter

  @beforeCreate()
  static createUUID(model: Tool) {
    model.uid = uuid()
  }

  @belongsTo(() => Agent, {
    localKey: 'uid',
    foreignKey: 'agent_uid',
  })
  declare agent: BelongsTo<typeof Agent>

  @column({
    isPrimary: true,
    meta: { searchable: true, type: 'string', validations: [{ name: 'uuid' }] },
  })
  declare uid: string

  @column({
    meta: {
      searchable: true,
      type: 'string',
      validations: [{ name: 'uuid' }],
    },
  })
  declare app_uid: string

  @column({
    meta: {
      searchable: true,
      type: 'string',
      validations: [{ name: 'uuid' }],
    },
  })
  declare agent_uid: string

  @column({ meta: { searchable: true, type: 'string' } })
  declare name: Infer<typeof toolStoreValidator>['name']

  @column({ meta: { searchable: true, type: 'string' } })
  declare type: Infer<typeof toolStoreValidator>['type']

  @column({ meta: { searchable: true, type: 'boolean' } })
  declare status: boolean

  @column({ meta: { searchable: true, type: 'object' } })
  declare config: Infer<typeof toolStoreValidator>['config'] | null

  @column.dateTime({ autoCreate: true, meta: { searchable: true, type: 'date' } })
  declare created_at: DateTime

  @column.dateTime({
    autoCreate: true,
    autoUpdate: true,
    meta: { searchable: true, type: 'date' },
  })
  declare updated_at: DateTime

  @column({ serializeAs: null, meta: { searchable: true, type: 'date' } })
  declare deleted_at?: DateTime | null

  get is_active(): boolean {
    return this.status === true
  }

  get is_native(): boolean {
    return this.type === 'native'
  }

  get is_http(): boolean {
    return this.type === 'http'
  }
}
