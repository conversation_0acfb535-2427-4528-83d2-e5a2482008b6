import { inject } from '@adonisjs/core'
import { HttpContext } from '@adonisjs/core/http'
import ToolsService from '#app/modules/tools/tool_service'
import { toolStoreValidator } from '#app/modules/tools/tool_validator'
import Tool<PERSON>resenter from '#app/modules/tools/tool_presenter'

@inject()
export default class ToolsController {
  constructor(protected toolsService: ToolsService) {}

  async index({ request, response }: HttpContext) {
    const { agent_uid } = request.qs()

    if (!agent_uid) {
      return response.badRequest('Agent UID is required')
    }

    const tools = await this.toolsService.indexByAgent(agent_uid)

    return response.status(200).json(ToolPresenter.serializeMany(tools))
  }

  async show({ request, response }: HttpContext) {
    const { uid } = request.params()

    const tool = await this.toolsService.show(uid)

    return response.status(200).json(ToolPresenter.serialize(tool))
  }

  async store({ request, response }: HttpContext) {
    const payload = await request.validateUsing(toolStoreValidator)

    const tool = await this.toolsService.store(payload)

    return response.status(201).json(ToolPresenter.serialize(tool))
  }

  async update({ request, response }: HttpContext) {
    const { uid } = request.params()

    const payload = await request.validateUsing(toolStoreValidator)
    const tool = await this.toolsService.update(uid, payload)

    return response.status(200).json(ToolPresenter.serialize(tool))
  }

  async toggleStatus({ request, response }: HttpContext) {
    const { uid } = request.params()

    const tool = await this.toolsService.toggleStatus(uid)

    return response.status(200).json(ToolPresenter.serialize(tool))
  }

  async destroy({ request, response }: HttpContext) {
    const { uid } = request.params()

    await this.toolsService.destroy(uid)

    return response.status(202).json({ success: true })
  }
}
