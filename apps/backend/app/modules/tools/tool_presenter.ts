import { ModelPaginatorContract } from '@adonisjs/lucid/types/model'
import Tool from '#app/modules/tools/tool_model'

export default class ToolPresenter {
  static serialize(item: Tool) {
    return {
      uid: item.uid,
      app_uid: item.app_uid,
      agent_uid: item.agent_uid,
      name: item.name,
      type: item.type,
      status: item.status,
      config: item.config,
      created_at: item.created_at,
      updated_at: item.updated_at,
      is_active: item.is_active,
      is_native: item.is_native,
      is_http: item.is_http,
    }
  }

  static serializeMany(items: Tool[]) {
    return items.map((item) => this.serialize(item))
  }

  static serializePaginated(items: ModelPaginatorContract<Tool>) {
    return {
      page: items.currentPage,
      per_page: items.perPage,
      total: items.total,
      last_page: items.lastPage,
      data: this.serializeMany(items.all()),
    }
  }
}
