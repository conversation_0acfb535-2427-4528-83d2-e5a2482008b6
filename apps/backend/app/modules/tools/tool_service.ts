import env from '#start/env'
import { Infer } from '@vinejs/vine/types'
import { inject } from '@adonisjs/core'
import { HttpContext } from '@adonisjs/core/http'
import Tool from '#app/modules/tools/tool_model'
import { toolStoreValidator } from '#app/modules/tools/tool_validator'

@inject()
export default class ToolsService {
  private ctx: HttpContext

  constructor(ctx: HttpContext) {
    this.ctx = ctx
  }

  async indexByAgent(agent_uid: string) {
    const { app } = this.ctx

    return await app
      .related('tools')
      .query()
      .where('agent_uid', agent_uid)
      .paginate(1, env.get('APP_PAGINATION_LIMIT'))
  }

  async show(uid: string) {
    const { app } = this.ctx

    return await app.related('tools').query().where('uid', uid).firstOrFail()
  }

  async findByName(params: { agent_uid: string; name: string }) {
    const { app } = this.ctx
    const { agent_uid, name } = params

    return await app
      .related('tools')
      .query()
      .where('agent_uid', agent_uid)
      .andWhere('name', name)
      .first()
  }

  async store(payload: Infer<typeof toolStoreValidator>) {
    const { app } = this.ctx
    const { agent_uid, name, type, status = true, config = null } = payload

    const tool = await this.findByName({ agent_uid, name })

    if (tool) {
      return await tool
        .merge({
          config,
          status,
          type,
        })
        .save()
    } else {
      return await app.related('tools').create({
        agent_uid,
        name,
        type,
        status,
        config,
      })
    }
  }

  async update(uid: string, params: Infer<typeof toolStoreValidator>) {
    const { app } = this.ctx
    const { agent_uid, ...data } = params

    const tool = await app
      .related('tools')
      .query()
      .where('agent_uid', agent_uid)
      .andWhere('uid', uid)
      .firstOrFail()

    return await tool.merge(data).save()
  }

  async toggleStatus(uid: string) {
    const { app } = this.ctx

    const tool = await app.related('tools').query().where('uid', uid).firstOrFail()

    if (!tool) {
      throw new Error(`Tool with uid ${uid} not found`)
    }

    return await tool.merge({ status: !tool.status }).save()
  }

  async destroy(uid: string) {
    const { app } = this.ctx

    const tool = await app.related('tools').query().where('uid', uid).firstOrFail()

    await tool.delete()

    return true
  }
}
