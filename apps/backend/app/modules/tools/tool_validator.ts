import vine from '@vinejs/vine'

const storeSchema = vine.object({
  agent_uid: vine.string().uuid(),
  name: vine.enum([
    'image_generator',
    'web_search',
    'perplexity_search',
    'shopify',
    'woocommerce',
    'shipping_tracking',
    'calendar_meeting',
    'request_human',
    'mark_as_resolved',
    'transcribe',
    'capture_lead',
    'astra',
    'zendesk',
    'hubspot',
  ]),
  type: vine.enum(['native', 'http']),
  status: vine.boolean(),
  config: vine.object({}).allowUnknownProperties(),
})

const functionSchema = vine.object({
  description: vine.string().trim(),
  triggers: vine.array(vine.string()),
  schema: vine.any(),
  execute: vine.any(),
})

const inputsSchema = vine.array(
  vine.object({
    label: vine.string().trim(),
    name: vine.string().trim(),
    type: vine.string().trim(),
    status: vine.boolean(),
  })
)

const imageGeneratorConfigSchema = vine.object({})

const webSearchConfigSchema = vine.object({ status: vine.boolean() })

const perplexitySearchConfigSchema = vine.object({ status: vine.boolean() })

const shopifyConfigSchema = vine.object({
  access_token: vine.string().trim(),
  shop_name: vine.string().trim(),
})

const woocommerceConfigSchema = vine.object({
  consumer_key: vine.string().trim(),
  consumer_secret: vine.string().trim(),
  url: vine.string().url({ require_protocol: true, protocols: ['http', 'https'] }),
})

const shippingTrackingConfigSchema = vine.object({
  api_key: vine.string().trim(),
})

const calendarMeetingConfigSchema = vine.object({
  url: vine.string().trim(),
})

const httpConfigSchema = vine.object({
  name: vine.string().trim(),
  description: vine.string().trim(),
  method: vine.string().trim(),
  url: vine.string().trim(),
  headers: vine.array(
    vine.object({
      key: vine.string().trim(),
      value: vine.string().trim(),
    })
  ),
  inputs: inputsSchema,
})

const requestHumanConfigSchema = vine.object({ status: vine.boolean() })

const markAsResolvedConfigSchema = vine.object({ status: vine.boolean() })

const transcribeConfigSchema = vine.object({
  email_title: vine.string().trim(),
  emails: vine.array(vine.string().email().normalizeEmail({ all_lowercase: true }).trim()),
  inputs: inputsSchema,
})

const captureLeadConfigSchema = vine.object({
  email_title: vine.string().trim(),
  emails: vine.array(vine.string().email().normalizeEmail({ all_lowercase: true }).trim()),
  inputs: inputsSchema,
})

const astraConfigSchema = vine.object({
  api_token: vine.string().trim(),
  api_url: vine.string().url({ require_protocol: true, protocols: ['http', 'https'] }),
})

const zendeskConfigSchema = vine.object({
  api_key: vine.string().trim(),
  api_email: vine.string().trim(),
  subdomain: vine.string().trim(),
  email_title: vine.string().trim(),
  emails: vine.array(vine.string().email().normalizeEmail({ all_lowercase: true }).trim()),
})

const hubspotConfigSchema = vine.object({
  api_key: vine.string().trim(),
  email_title: vine.string().trim(),
  emails: vine.array(vine.string().email().normalizeEmail({ all_lowercase: true }).trim()),
})

const toolStoreValidator = vine.compile(storeSchema)
const toolFunctionValidator = vine.compile(functionSchema)
const toolInputsValidator = vine.compile(inputsSchema)
const toolImageGeneratorConfigValidator = vine.compile(imageGeneratorConfigSchema)
const toolWebSearchConfigValidator = vine.compile(webSearchConfigSchema)
const toolPerplexitySearchConfigValidator = vine.compile(perplexitySearchConfigSchema)
const toolShopifyConfigValidator = vine.compile(shopifyConfigSchema)
const toolShippingTrackingConfigValidator = vine.compile(shippingTrackingConfigSchema)
const toolWoocommerceConfigValidator = vine.compile(woocommerceConfigSchema)
const toolCalendarMeetingConfigValidator = vine.compile(calendarMeetingConfigSchema)
const toolHttpConfigValidator = vine.compile(httpConfigSchema)
const toolRequestHumanConfigValidator = vine.compile(requestHumanConfigSchema)
const toolMarkAsResolvedConfigValidator = vine.compile(markAsResolvedConfigSchema)
const toolTranscribeConfigValidator = vine.compile(transcribeConfigSchema)
const toolCaptureLeadConfigValidator = vine.compile(captureLeadConfigSchema)
const toolAstraConfigValidator = vine.compile(astraConfigSchema)
const toolZendeskConfigValidator = vine.compile(zendeskConfigSchema)
const toolHubspotConfigValidator = vine.compile(hubspotConfigSchema)

export {
  toolStoreValidator,
  toolFunctionValidator,
  toolInputsValidator,
  toolImageGeneratorConfigValidator,
  toolWebSearchConfigValidator,
  toolPerplexitySearchConfigValidator,
  toolShopifyConfigValidator,
  toolShippingTrackingConfigValidator,
  toolWoocommerceConfigValidator,
  toolCalendarMeetingConfigValidator,
  toolHttpConfigValidator,
  toolRequestHumanConfigValidator,
  toolMarkAsResolvedConfigValidator,
  toolTranscribeConfigValidator,
  toolCaptureLeadConfigValidator,
  toolAstraConfigValidator,
  toolZendeskConfigValidator,
  toolHubspotConfigValidator,
}
