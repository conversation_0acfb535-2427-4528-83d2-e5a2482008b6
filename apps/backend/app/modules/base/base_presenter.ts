import { ModelPaginatorContract } from '@adonisjs/lucid/types/model'
import Base from '#app/modules/base/base_model'

export default class BasePresenter {
  static serialize(item: Base) {
    return {
      uid: item.uid,
      id: item.id,
      created_at: item.created_at,
      updated_at: item.updated_at,
    }
  }

  static serializeMany(items: Base[]) {
    return items.map((item) => this.serialize(item))
  }

  static serializePaginated(items: ModelPaginatorContract<Base>) {
    return {
      page: items.currentPage,
      per_page: items.perPage,
      total: items.total,
      last_page: items.lastPage,
      data: this.serializeMany(items.all()),
    }
  }
}
