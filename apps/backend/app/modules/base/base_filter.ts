import { <PERSON>ida<PERSON><PERSON>elper } from '#app/helpers'
import { requestFilterValidator } from '#app/validators'
import { Infer } from '@vinejs/vine/types'
import { BaseModelFilter } from 'adonis-lucid-filter'
import type { LucidModel, ModelQueryBuilderContract } from '@adonisjs/lucid/types/model'

export default class BaseFilter extends BaseModelFilter {
  declare $query: ModelQueryBuilderContract<LucidModel>

  blacklist: string[] = ['id', 'uid', 'password', 'password_hash']
  dropId = false
  camelCase = false
  removeEmptyInput = true

  static operators: Array<string> = [
    '=',
    '<',
    '>',
    '<=',
    '>=',
    '!=',
    'LIKE',
    'IN',
    'REGEX',
    'IS',
    'IS NULL',
  ]

  async expand(expands: string[]) {
    if (!Array.isArray(expands)) {
      throw {
        message: 'BaseFilter - Expand must be an array',
        status: 400,
        code: 'base/invalid-payload',
      }
    }

    expands?.forEach((expand) => {
      const relations = expand.split('.')
      const relationName = relations.shift()

      if (relationName) {
        this.$query.preload(relationName as any, (query) => {
          relations.forEach((relation) => {
            query.preload(relation)
          })
        })
      }
    })
  }

  filters(filters: Infer<typeof requestFilterValidator>['filters']) {
    if (!filters || (filters && !filters.length)) {
      return
    }

    if (!Array.isArray(filters)) {
      throw {
        message: 'BaseFilter - Filters must be an array',
        status: 400,
        code: 'base/invalid-payload',
      }
    }

    filters?.forEach(({ key, operator, value }) => {
      if (BaseFilter.operators.includes(operator)) {
        if (operator === 'is null' || value === null) {
          this.$query.whereNull(key)
        } else if (operator === 'in' && Array.isArray(value)) {
          this.$query.whereIn(key, value)
        } else {
          this.$query.where(key, operator, value)
        }
      } else {
        throw {
          message: `BaseFilter - Unsupported operator: ${operator}`,
          status: 400,
          code: 'base/invalid-operator',
        }
      }
    })
  }

  search(value: any) {
    if (value === undefined || value === null || value === '') {
      return
    }

    const supportedTypes = ['string', 'array', 'uuid', 'boolean', 'number']
    const columns = Array.from(this.$query.model.$columnsDefinitions.values())
    const columnsToProcess: { column: string; type: string }[] = []

    // Remove blacklisted columns and filter by supported types
    columns.forEach((column) => {
      if (column) {
        const { serializeAs, meta } = column
        const { searchable, type } = meta || {}

        if (
          serializeAs &&
          !this.blacklist.includes(serializeAs) &&
          searchable &&
          supportedTypes.includes(type)
        ) {
          columnsToProcess.push({ column: serializeAs, type })
        }
      }
    })

    // Search in columns
    this.$query.where((query) => {
      columnsToProcess.forEach(({ column, type }) => {
        switch (type) {
          case 'string':
            query.orWhere(column, 'ILIKE', `%${value}%`)
            break
          case 'array':
            query.orWhereRaw(`array_to_string(${column}, ', ') ILIKE ?`, [`%${value}%`])
            break
          case 'uuid':
            if (ValidationHelper.isUUID(value)) {
              query.orWhere(column, '=', value)
            }
            break
          case 'boolean':
            if (typeof value === 'boolean') {
              query.orWhere(column, '=', value)
            }
            break
          case 'number':
            if (typeof value === 'number' && !Number.isNaN(value)) {
              query.orWhere(column, '=', value)
            }
            break
          default:
            break
        }
      })
    })
  }

  orderBy(orderBy: Infer<typeof requestFilterValidator>['orderBy']) {
    this.$query.orderBy('created_at', 'desc')

    if (!orderBy || (orderBy && !orderBy.length)) {
      return
    }

    if (!Array.isArray(orderBy)) {
      throw {
        message: 'BaseFilter - orderBy must be an array',
        status: 400,
        code: 'base/invalid-payload',
      }
    }

    orderBy.forEach(({ key, direction }) => {
      const dir: 'asc' | 'desc' = direction.toLowerCase() as 'asc' | 'desc'

      if (!['asc', 'desc'].includes(dir)) {
        throw {
          message: 'BaseFilter - Unsupported direction',
          status: 400,
          code: 'base/invalid-direction',
        }
      }

      this.$query.orderBy(key, dir)
    })
  }

  groupBy(key: string) {
    if (key && typeof key === 'string') {
      this.$query.groupBy(key)
    }
  }
}
