import { Filterable } from 'adonis-lucid-filter'
import { DateTime } from 'luxon'
import { v4 as uuid } from 'uuid'
import { compose } from '@adonisjs/core/helpers'
import { BaseModel, beforeCreate, column, SnakeCaseNamingStrategy } from '@adonisjs/lucid/orm'
import BaseFilter from '#app/modules/base/base_filter'

BaseModel.namingStrategy = new SnakeCaseNamingStrategy()

export default class Base extends compose(BaseModel, Filterable) {
  serializeExtras = false
  static selfAssignPrimaryKey = true
  static namingStrategy = new SnakeCaseNamingStrategy()
  static $filter = () => BaseFilter

  @column({
    isPrimary: true,
    meta: { searchable: true, type: 'string', validations: [{ name: 'uuid' }] },
  })
  declare uid: string

  @column({
    isPrimary: false,
    meta: { searchable: true, type: 'string', validations: [] },
  })
  declare id: number

  @column.dateTime({ autoCreate: true, meta: { searchable: true, type: 'date' } })
  declare created_at: DateTime

  @column.dateTime({
    autoCreate: true,
    autoUpdate: true,
    meta: { searchable: true, type: 'date' },
  })
  declare updated_at: DateTime

  @column({ serializeAs: null, meta: { searchable: true, type: 'date' } })
  declare deleted_at?: DateTime | null

  @beforeCreate()
  static async createUUID(model: Base) {
    if (!model?.uid) {
      model.uid = uuid()
    }
  }
}
