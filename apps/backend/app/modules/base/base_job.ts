import env from '#start/env'
import { Job, Queue, Worker } from 'bullmq'

export default abstract class BaseQueueJob {
  protected readonly debug: boolean = true
  protected readonly concurrency: number = 5
  protected readonly queue: Queue
  protected readonly worker: Worker
  protected readonly connection = {
    host: env.get('QUEUE_REDIS_HOST'),
    port: env.get('QUEUE_REDIS_PORT'),
  }

  constructor(protected readonly queueName: string) {
    this.queue = new Queue(queueName, {
      connection: this.connection,
    })

    this.worker = new Worker(queueName, this.handle.bind(this), {
      concurrency: this.concurrency,
      connection: this.connection,
    })

    if (this.debug) {
      this.worker.on('active', async (job) => {
        console.log('Working on job', { id: job.id, data: job.data })

        const activeJobs = await this.queue.getActiveCount()
        console.log(`Active jobs count: ${activeJobs}`)
      })

      this.worker.on('completed', async (job) => {
        console.log('Worker completed job', { id: job.id, data: job.data })

        const activeJobs = await this.queue.getActiveCount()
        console.log(`Active jobs count: ${activeJobs}`)
      })

      this.worker.on('failed', (job, error) => {
        console.error('Worker failed job', { id: job?.id, error })
      })
    }
  }

  async drain() {
    await this.queue.drain()
    await this.queue.clean(0, 99999, 'delayed')
    await this.queue.clean(0, 99999, 'wait')
    await this.queue.clean(0, 99999, 'active')
    await this.queue.clean(0, 99999, 'completed')
    await this.queue.clean(0, 99999, 'failed')
  }

  getQueue(): Queue {
    return this.queue
  }

  async add(jobId: string, data: Record<string, any>) {
    this.queue.add(jobId, data, {
      removeOnComplete: true,
      removeOnFail: true,
      deduplication: {
        id: jobId,
      },
    })
  }

  protected abstract handle(job: Job): Promise<void>
}
