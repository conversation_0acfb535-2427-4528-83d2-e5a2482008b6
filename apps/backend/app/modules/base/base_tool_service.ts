import Agent from '#app/modules/agents/agent_model'
import Chat from '#app/modules/chats/chat_model'
import Tool from '#app/modules/tools/tool_model'

export type ToolContext = {
  agent: Agent
  chat: Chat
  tool: Tool
}

export default abstract class BaseToolService {
  protected readonly agent: Agent
  protected readonly chat: Chat
  protected readonly tool: Tool
  protected readonly context: ToolContext

  constructor(context: ToolContext) {
    const { agent, chat, tool } = context

    this.agent = agent
    this.chat = chat
    this.tool = tool
    this.context = context
  }

  /**
   * Clean input parameters to ensure consistent data
   * - Trims string values
   * - Filters empty array values
   * - Removes duplicates from arrays
   */
  protected cleanParams(data: Record<string, any>): Record<string, any> {
    return Object.fromEntries(
      Object.entries(data).map(([key, value]) => [
        key,
        typeof value === 'string'
          ? value.trim() || null
          : Array.isArray(value)
            ? [...new Set(value)].filter(Boolean)
            : value,
      ])
    )
  }

  /**
   * Format error messages in a consistent way
   */
  protected formatError(message: string): { error: string } {
    return { error: message }
  }

  /**
   * Format success messages in a consistent way
   */
  protected formatSuccess(message: string): { success: string } {
    return { success: message }
  }
}
