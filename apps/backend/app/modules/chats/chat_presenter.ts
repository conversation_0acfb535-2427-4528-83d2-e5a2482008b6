import { ModelPaginatorContract } from '@adonisjs/lucid/types/model'
import Chat from '#app/modules/chats/chat_model'

export default class ChatPresenter {
  static serialize(item: Chat) {
    return {
      uid: item.uid,
      app_uid: item.app_uid,
      agent_uid: item.agent_uid,
      external_id: item.external_id,
      label: item.label,
      is_hidden: item.is_hidden,
      is_archived: item.is_archived,
      is_favorite: item.is_favorite,
      is_resolve: item.is_resolve,
      is_email: item.is_email,
      is_whatsapp: item.is_whatsapp,
      has_feedback: item.has_feedback,
      has_contact: item.has_contact,
      has_user_tagging: item.has_user_tagging,
      has_messages: item.has_messages,
      is_ai_disabled: item.is_ai_disabled,
      loaded_context: item.loaded_context,
      loaded_variables: item.loaded_variables,
      loaded_user: item.loaded_user,
      metadata: item.metadata,
      created_at: item.created_at,
      updated_at: item.updated_at,
      computed_files_path: item.computed_files_path,
      computed_has_user_tagging: item.computed_has_user_tagging,
    }
  }

  static serializeMany(items: Chat[]) {
    return items.map((item) => this.serialize(item))
  }

  static serializePaginated(items: ModelPaginatorContract<Chat>) {
    return {
      page: items.currentPage,
      per_page: items.perPage,
      total: items.total,
      last_page: items.lastPage,
      data: this.serializeMany(items.all()),
    }
  }
}
