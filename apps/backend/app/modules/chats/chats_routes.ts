import router from '@adonisjs/core/services/router'

export default () => {
  const ENDPOINT_PREFIX = '/chats'
  const CONTROLLER_PATH = '#app/modules/chats/chats_controller'

  router
    .group(() => {
      router.get('/', `${CONTROLLER_PATH}.index`)
      router.get('/search/:agent_uid', `${CONTROLLER_PATH}.search`)
      router.get('/history/:agent_uid', `${CONTROLLER_PATH}.history`)
      router.get('/:uid', `${CONTROLLER_PATH}.show`)
      router.post('/', `${CONTROLLER_PATH}.store`)
      router.put('/:uid', `${CONTROLLER_PATH}.update`)
      router.delete('/:uid', `${CONTROLLER_PATH}.destroy`)
      router.get('/archive/:uid', `${CONTROLLER_PATH}.archive`)
      router.put('/metadata/:uid', `${CONTROLLER_PATH}.appendMetadata`)
    })
    .prefix(`${ENDPOINT_PREFIX}`)
}
