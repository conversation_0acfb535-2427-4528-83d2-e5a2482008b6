import { requestFilterValidator } from '#app/validators'
import { inject } from '@adonisjs/core'
import { HttpContext } from '@adonisjs/core/http'
import ChatService from '#app/modules/chats/chat_service'
import {
  chatMetadataUpdateValidator,
  chatStoreValidator,
  chatUpdateValidator,
} from '#app/modules/chats/chat_validator'
import Chat<PERSON>resenter from '#app/modules/chats/chat_presenter'

@inject()
export default class ChatsController {
  constructor(private ChatService: ChatService) {}

  async index({ request, response }: HttpContext) {
    const payload = await request.validateUsing(requestFilterValidator)

    const chats = await this.ChatService.index(payload)

    return response.status(200).json(ChatPresenter.serializePaginated(chats))
  }

  async show({ request, response }: HttpContext) {
    const uid = request.param('uid')

    const chat = await this.ChatService.show(uid)

    return response.status(200).json(ChatPresenter.serialize(chat))
  }

  async store({ request, response }: HttpContext) {
    const payload = await request.validateUsing(chatStoreValidator)
    const chat = await this.ChatService.store(payload)

    return response.status(201).json(ChatPresenter.serialize(chat))
  }

  async update({ request, response }: HttpContext) {
    const uid = request.param('uid')

    const payload = await request.validateUsing(chatUpdateValidator)
    const chat = await this.ChatService.update(uid, payload)

    return response.status(200).json(ChatPresenter.serialize(chat))
  }

  async destroy({ request, response }: HttpContext) {
    const uid = request.param('uid')

    await this.ChatService.destroy(uid)

    return response.status(202).json({ success: true })
  }

  async archive({ request, response }: HttpContext) {
    const uid = request.param('uid')

    await this.ChatService.archive(uid)

    return response.json({ success: true })
  }

  async appendMetadata({ request, response }: HttpContext) {
    const uid = request.param('uid')
    const metadata = await request.validateUsing(chatMetadataUpdateValidator)

    const chat = await this.ChatService.appendMetadata(uid, metadata)

    return response.status(200).json(ChatPresenter.serialize(chat))
  }

  async history({ request, response }: HttpContext) {
    const agent_uid = request.param('agent_uid')
    const payload = await request.validateUsing(requestFilterValidator)

    const result = await this.ChatService.history({
      agent_uid,
      filter: payload,
    })

    return response.status(200).json(ChatPresenter.serializePaginated(result))
  }

  async search({ request, response }: HttpContext) {
    const agent_uid = request.param('agent_uid')
    const query = request.input('query')
    const payload = await request.validateUsing(requestFilterValidator)

    if (!query) {
      throw {
        message: 'query parameter is required',
        status: 400,
        code: 'base/invalid-query-parameter',
      }
    }

    const result = await this.ChatService.search({
      agent_uid,
      query,
      filter: payload,
    })

    return response.status(200).json(ChatPresenter.serializePaginated(result))
  }
}
