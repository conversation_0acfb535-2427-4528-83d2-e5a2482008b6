import { <PERSON><PERSON><PERSON>Helper } from '#app/helpers'
import { requestFilterValidator } from '#app/validators'
import env from '#start/env'
import { Infer } from '@vinejs/vine/types'
import { mergeAndConcat } from 'merge-anything'
import { inject } from '@adonisjs/core'
import { HttpContext } from '@adonisjs/core/http'
import { chatStoreValidator, chatUpdateValidator } from '#app/modules/chats/chat_validator'

@inject()
export default class ChatService {
  private ctx: HttpContext

  constructor(ctx: HttpContext) {
    this.ctx = ctx
  }

  async index(filter: Infer<typeof requestFilterValidator>) {
    const page = filter.page || 1

    return await this.ctx.app
      .related('chats')
      .query()
      .filter(filter)
      .paginate(page, env.get('APP_PAGINATION_LIMIT'))
  }

  async show(uid: string) {
    return await this.ctx.app.related('chats').query().where('uid', uid).firstOrFail()
  }

  async store(payload: Infer<typeof chatStoreValidator>) {
    return await this.ctx.app.related('chats').create(payload)
  }

  async update(uid: string, payload: Infer<typeof chatUpdateValidator>) {
    const chat = await this.ctx.app.related('chats').query().where('uid', uid).firstOrFail()

    return await chat.merge(payload).save()
  }

  async destroy(uid: string) {
    const chat = await this.ctx.app.related('chats').query().where('uid', uid).firstOrFail()

    return await chat.delete()
  }

  async search(params: {
    agent_uid: string
    query: string
    filter: Infer<typeof requestFilterValidator>
  }) {
    const { agent_uid, query, filter } = params
    const page = filter.page || 1

    if (!query) {
      return this.ctx.app
        .related('chats')
        .query()
        .where('agent_uid', agent_uid)
        .whereRaw('1 = 0') // Condition that is always false
        .paginate(page, env.get('APP_PAGINATION_LIMIT'))
    }

    const isUUID = ValidationHelper.isUUID(query)

    const dbQuery = this.ctx.app
      .related('chats')
      .query()
      .where('agent_uid', agent_uid)
      .where('has_messages', true)
      .andWhere((builder) => {
        if (isUUID) {
          builder.where('uid', '=', query)
        } else {
          builder.where('external_id', 'ILIKE', `%${query}%`)
        }

        builder
          .orWhere('external_id', 'ILIKE', `%${query}%`)
          .orWhereHas('messages', (messageQuery) => {
            messageQuery.where('output_text', 'ILIKE', `%${query}%`)
          })
          .orWhereHas('contacts', (contactQuery) => {
            contactQuery
              .where('first_name', 'ILIKE', `%${query}%`)
              .orWhere('last_name', 'ILIKE', `%${query}%`)
              .orWhere('email', 'ILIKE', `%${query}%`)
              .orWhere('phone', 'ILIKE', `%${query}%`)
          })
      })
      .filter(filter)

    return await dbQuery.paginate(page, env.get('APP_PAGINATION_LIMIT'))
  }

  async archive(uid: string) {
    const chat = await this.ctx.app.related('chats').query().where('uid', uid).firstOrFail()

    await chat.merge({ is_archived: true }).save()
  }

  async appendMetadata(uid: string, metadata: object) {
    const chat = await this.ctx.app.related('chats').query().where('uid', uid).firstOrFail()

    const merged_metadata = chat?.metadata ? mergeAndConcat(chat?.metadata, metadata) : metadata

    return await chat.merge({ metadata: merged_metadata }).save()
  }

  async history(params: { agent_uid: string; filter: Infer<typeof requestFilterValidator> }) {
    const { agent_uid, filter } = params
    const page = filter.page || 1
    const {
      search = null,
      filters = [],
      orderBy = [{ key: 'created_at', direction: 'desc' }],
      expand = [],
      groupBy = null,
    } = filter || {}

    const agent = await this.ctx.app.related('agents').query().where('uid', agent_uid).firstOrFail()

    let filterByChatUids: string[] = []

    if (search) {
      const isUUID = ValidationHelper.isUUID(search)

      const [messages, chats] = await Promise.all([
        agent
          .related('messages')
          .query()
          .where((query) => {
            if (isUUID) {
              query.where('uid', search)
            } else {
              query.where('output_text', 'ILIKE', `%${search}%`)
            }
          })
          .distinct('chat_uid')
          .select('chat_uid'),
        agent
          .related('chats')
          .query()
          .where((query) => {
            if (isUUID) {
              query.where('uid', search)
            } else {
              query.whereRaw('metadata::text ILIKE ?', [`%${search}%`])
            }
          })
          .select('uid'),
      ])

      filterByChatUids = Array.from(
        new Set([...messages.map((item) => item.chat_uid), ...chats.map((item) => item.uid)])
      )

      if (filterByChatUids.length === 0) {
        return {
          meta: {},
          data: [],
        }
      }
    }

    const computedFilters = filters.map((filter) => ({
      ...filter,
      value: filter.value === 'true' ? 't' : filter.value === 'false' ? 'f' : filter.value,
    }))

    let chatsQuery = agent
      .related('chats')
      .query()
      .select('*')
      .andWhere('is_hidden', false)
      .andWhere('is_archived', false)
      .andWhere('has_messages', true)

    if (search && filterByChatUids.length > 0) {
      chatsQuery = chatsQuery.whereIn('uid', filterByChatUids)
    }

    chatsQuery = chatsQuery.filter({
      filters: computedFilters,
      expand,
      orderBy,
      groupBy: groupBy ?? undefined,
    })

    const chats = await chatsQuery
      .orderBy(orderBy[0].key, orderBy[0].direction)
      .paginate(page, env.get('APP_PAGINATION_LIMIT'))

    if (expand.length > 0) {
      for (const relation of expand) {
        for (const chat of chats.all()) {
          await chat.load(relation)
        }
      }
    }

    const getAvatar = (chatUid: string): string => {
      const hash = Array.from(chatUid).reduce((acc, char) => acc + char.charCodeAt(0), 0)
      const number = (hash % 12) + 1
      return `/images/avatars/svg/user-${number}.svg`
    }

    const updatedChats = chats.toJSON().data.map((chat) => {
      const chatObj = typeof chat.toObject === 'function' ? chat.toObject() : chat
      const computedContact =
        typeof chat.getComputedContact === 'function' ? chat.getComputedContact() : null

      const expandedFields: Record<string, any> = {}
      if (expand.length > 0 && chat.$preloaded) {
        for (const key of expand) {
          if (chat.$preloaded[key]) {
            expandedFields[key] = chat.$preloaded[key]
          }
        }
      }

      return {
        ...chatObj,
        computedContact,
        avatar: getAvatar(chat.uid),
        ...expandedFields,
      }
    })

    return {
      meta: chats.toJSON().meta ?? {},
      data: updatedChats,
    }
  }
}
