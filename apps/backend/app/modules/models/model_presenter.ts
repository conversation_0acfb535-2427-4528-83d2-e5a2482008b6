import { ModelPaginatorContract } from '@adonisjs/lucid/types/model'
import Model from '#app/modules/models/model_model'

export default class ModelPresenter {
  static serialize(item: Model) {
    return {
      uid: item.uid,
      rank: item.rank,
      label: item.label,
      key: item.key,
      model_openai: item.model_openai,
      model_openrouter: item.model_openrouter,
      model_anthropic: item.model_anthropic,
      company: item.company,
      help: item.help,
      credits: item.credits,
      context: item.context,
      is_default: item.is_default,
      is_unfiltered: item.is_unfiltered,
      is_connected: item.is_connected,
      is_beta: item.is_beta,
      is_text: item.is_text,
      is_vision: item.is_vision,
      is_agent: item.is_agent,
      streaming: item.streaming,
      model_name: item.model_name,
    }
  }

  static serializeMany(items: Model[]) {
    return items.map((item) => this.serialize(item))
  }

  static serializePaginated(items: ModelPaginatorContract<Model>) {
    return {
      page: items.currentPage,
      per_page: items.perPage,
      total: items.total,
      last_page: items.lastPage,
      data: this.serializeMany(items.all()),
    }
  }
}
