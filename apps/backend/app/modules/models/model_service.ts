import env from '#start/env'
import { inject } from '@adonisjs/core'
import Model from '#app/modules/models/model_model'

@inject()
export default class ModelService {
  constructor() {}

  async index() {
    return await Model.query().orderBy('rank').paginate(1, env.get('APP_PAGINATION_LIMIT'))
  }

  async getCreditsCostPerOutput(params: { model_key: string; output: string }) {
    const { model_key, output } = params

    const model = await Model.findBy('key', model_key)

    const { credits } = model || {}

    const words = output.split(' ').length

    return Math.max(1, Math.ceil(words / 1000)) * (credits || 1)
  }
}
