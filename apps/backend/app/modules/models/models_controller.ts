import { inject } from '@adonisjs/core'
import { HttpContext } from '@adonisjs/core/http'
import ModelService from '#app/modules/models/model_service'
import ModelPresenter from '#app/modules/models/model_presenter'

@inject()
export default class ModelsController {
  constructor(private modelService: ModelService) {}

  async index({ response }: HttpContext) {
    const models = await this.modelService.index()

    return response.status(200).json(ModelPresenter.serializePaginated(models))
  }
}
