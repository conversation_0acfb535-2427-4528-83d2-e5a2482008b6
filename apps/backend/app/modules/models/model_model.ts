import { Filterable } from 'adonis-lucid-filter'
import { v4 as uuid } from 'uuid'
import { compose } from '@adonisjs/core/helpers'
import { beforeCreate, column, computed, SnakeCaseNamingStrategy } from '@adonisjs/lucid/orm'
import { BaseModel } from '@adonisjs/lucid/orm'
import Base from '#app/modules/base/base_model'
import BaseFilter from '#app/modules/base/base_filter'

export default class Model extends compose(BaseModel, Filterable) {
  serializeExtras = false
  static selfAssignPrimaryKey = true
  static namingStrategy = new SnakeCaseNamingStrategy()
  static $filter = () => BaseFilter

  @beforeCreate()
  static createUUID(model: Model) {
    model.uid = uuid()
  }

  @column({
    isPrimary: true,
    meta: { searchable: true, type: 'string', validations: [{ name: 'uuid' }] },
  })
  declare uid: string

  @column({ meta: { searchable: true, type: 'number' } })
  declare rank: number

  @column({ meta: { searchable: true, type: 'string' } })
  declare label: string

  @column({ meta: { searchable: true, type: 'string' } })
  declare key: string

  @column({ meta: { searchable: true, type: 'string' } })
  declare model_openai: string

  @column({ meta: { searchable: true, type: 'string' } })
  declare model_openrouter: string

  @column({ meta: { searchable: true, type: 'string' } })
  declare model_anthropic: string

  @column({ meta: { searchable: true, type: 'string' } })
  declare company: string

  @column({ meta: { searchable: true, type: 'string' } })
  declare help: string

  @column({ meta: { searchable: true, type: 'number' } })
  declare credits: number

  @column({ meta: { searchable: true, type: 'number' } })
  declare context: number

  @column({ meta: { searchable: true, type: 'boolean' } })
  declare is_default: boolean

  @column({ meta: { searchable: true, type: 'boolean' } })
  declare is_unfiltered: boolean

  @column({ meta: { searchable: true, type: 'boolean' } })
  declare is_connected: boolean

  @column({ meta: { searchable: true, type: 'boolean' } })
  declare is_beta: boolean

  @column({ meta: { searchable: true, type: 'boolean' } })
  declare is_text: boolean

  @column({ meta: { searchable: true, type: 'boolean' } })
  declare is_vision: boolean

  @column({ meta: { searchable: true, type: 'boolean' } })
  declare is_agent: boolean

  @column({ meta: { searchable: true, type: 'boolean' } })
  declare streaming: boolean

  @computed()
  get model_name(): string {
    return this.model_openai || this.model_anthropic || this.model_openrouter
  }
}
