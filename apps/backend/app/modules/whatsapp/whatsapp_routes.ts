import router from '@adonisjs/core/services/router'

export default () => {
  const ENDPOINT_PREFIX = '/whatsapp'
  const CONTROLLER_PATH = '#app/modules/whatsapp/whatsapp_controller'

  router
    .group(() => {
      router.get('/get-session/:agent_uid', `${CONTROLLER_PATH}.getSession`)
      router.delete('/delete-session/:agent_uid', `${CONTROLLER_PATH}.deleteSession`)
      router.post('/get-qr-code/:agent_uid', `${CONTROLLER_PATH}.getQrCode`)
    })
    .prefix(`${ENDPOINT_PREFIX}`)
}
