import { inject } from '@adonisjs/core'
import { HttpContext } from '@adonisjs/core/http'
import WhatsappService from '#app/modules/whatsapp/whatsapp_service'
import { whatsappGetQrCodeValidator } from '#app/modules/whatsapp/whatsapp_validation'

@inject()
export default class WhatsappController {
  constructor(private whatsappService: WhatsappService) {}

  async getSession({ request, response }: HttpContext) {
    const agent_uid = request.param('agent_uid')

    const sessionResponse = await this.whatsappService.getSession(agent_uid)

    return response.status(200).json(sessionResponse)
  }

  async deleteSession({ request, response }: HttpContext) {
    const agent_uid = request.param('agent_uid')

    await this.whatsappService.deleteSession(agent_uid)

    return response.status(200).json({ success: true })
  }

  async getQrCode({ request, response }: HttpContext) {
    const agent_uid = request.param('agent_uid')
    const { phone } = await request.validateUsing(whatsappGetQrCodeValidator)

    const { qrCode } = await this.whatsappService.getQrCode({ agent_uid, phone })

    return response.status(200).json({ qr_code: qrCode })
  }
}
