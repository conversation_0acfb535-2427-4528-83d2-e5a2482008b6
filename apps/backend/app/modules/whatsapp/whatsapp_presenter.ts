import { ModelPaginatorContract } from '@adonisjs/lucid/types/model'
import Whatsapp from '#app/modules/whatsapp/whatsapp_model'

export default class WhatsappPresenter {
  static serialize(item: Whatsapp) {
    return {
      uid: item.uid,
      app_uid: item.app_uid,
      label: item.label,
      body: item.body,
      created_at: item.created_at,
      updated_at: item.updated_at,
    }
  }

  static serializeMany(items: Whatsapp[]) {
    return items.map((item) => this.serialize(item))
  }

  static serializePaginated(items: ModelPaginatorContract<Whatsapp>) {
    return {
      page: items.currentPage,
      per_page: items.perPage,
      total: items.total,
      last_page: items.lastPage,
      data: this.serializeMany(items.all()),
    }
  }
}
