import MissingParamsException from '#app/exceptions/missing_params'
import env from '#start/env'
import axios from 'axios'
import parse from 'libphonenumber-js'
import { inject } from '@adonisjs/core'
import { HttpContext } from '@adonisjs/core/http'
import Chat from '#app/modules/chats/chat_model'

@inject()
export default class WhatsappService {
  private ctx: HttpContext
  private commands: string[] = ['/ai', '/reset', '/pause', '/resume', '/help']
  private headers = {
    'X-Api-Key': env.get('WHATSAPP_API_SECRET'),
    'Accept': 'application/json',
  }

  constructor(ctx: HttpContext) {
    this.ctx = ctx
  }

  isCommand(input: string) {
    return this.commands.some((command) => input.includes(command))
  }

  async getSession(agent_uid: string) {
    const { data } = await axios({
      method: 'GET',
      url: `${env.get('WHATSAPP_API_BASE_URL')}/sessions/${agent_uid}`,
      headers: this.headers,
    })

    return {
      status: data?.status || 'NOT_FOUND',
      id: data?.me?.id?.replace('@c.us', '') || null,
      name: data?.me?.pushName || null,
      phone: data?.config?.metadata?.phone || null,
    }
  }

  async deleteSession(agent_uid: string) {
    const { data } = await axios({
      method: 'DELETE',
      url: `${env.get('WHATSAPP_API_BASE_URL')}/sessions/${agent_uid}`,
      headers: this.headers,
    })

    return data
  }

  async createSession(params: { app_uid: string; agent_uid: string; phone: string }) {
    const { app_uid, agent_uid, phone } = params

    const phoneObject = parse('+' + phone)

    if (!phoneObject) {
      throw new Error('Invalid phone number')
    }

    const country = phoneObject.country
    const valid = phoneObject.isValid()

    if (!country || !valid) {
      throw new Error('Invalid phone number')
    }

    const { data } = await axios({
      method: 'POST',
      url: `${env.get('WHATSAPP_API_BASE_URL')}/sessions`,
      headers: this.headers,
      data: {
        start: true,
        debug: false,
        name: agent_uid,
        config: {
          metadata: {
            app_uid,
            agent_uid,
          },
          proxy: {
            server: `${env.get('DATA_IMPULSE_HOST')}:${env.get('DATA_IMPULSE_PORT_STICKY')}`,
            username: `${env.get('DATA_IMPULSE_LOGIN')}__cr.${country?.toLocaleLowerCase()}`,
            password: env.get('DATA_IMPULSE_PASSWORD'),
          },
          noweb: {
            markOnline: true,
            store: {
              enabled: true,
              fullSync: true,
            },
          },
        },
      },
    })

    return data
  }

  async startSession(agent_uid: string) {
    const { data } = await axios({
      method: 'POST',
      url: `${env.get('WHATSAPP_API_BASE_URL')}/sessions/${agent_uid}/start`,
      headers: this.headers,
    })

    return data
  }

  async getQrCode(params: { agent_uid: string; phone: string }): Promise<{ qrCode: string }> {
    const { app } = this.ctx
    const { agent_uid, phone } = params

    if (!agent_uid || !phone) {
      throw new MissingParamsException()
    }

    await app.related('agents').query().where('uid', agent_uid).firstOrFail()

    const sessionResponse = await this.getSession(agent_uid)

    if (sessionResponse?.name) {
      await this.startSession(agent_uid)
    } else {
      await this.createSession({ app_uid: app.uid, agent_uid, phone })
      await this.startSession(agent_uid)
    }

    const { data } = await axios({
      method: 'GET',
      url: `${env.get('WHATSAPP_API_BASE_URL')}/${agent_uid}/auth/qr`,
      headers: this.headers,
    })

    return {
      qrCode: data?.qr_code,
    }
  }

  async sendText(payload: {
    chat_id: string
    agent_uid: string
    text: string
    reply_to?: string
    mentions?: string[]
  }) {
    const { chat_id, agent_uid, text, reply_to, mentions } = payload

    const { data } = await axios.post(
      `${env.get('WHATSAPP_API_BASE_URL')}/sendText`,
      {
        text,
        reply_to,
        mentions,
        linkPreview: true,
        chat_id,
        session: agent_uid,
      },
      { headers: this.headers }
    )

    return data
  }

  async sendSeen(payload: {
    agent_uid: string
    chat_id: string
    message_id: string
    participant?: string
  }) {
    const { agent_uid, chat_id, message_id, participant } = payload

    const { data } = await axios.post(
      `${env.get('WHATSAPP_API_BASE_URL')}/sendSeen`,
      {
        session: agent_uid,
        chat_id,
        message_id,
        participant,
      },
      { headers: this.headers }
    )

    return data
  }

  async sendImage(payload: {
    agent_uid: string
    chat_id: string
    reply_to?: string
    file: { url?: string; data?: string }
    mentions?: string[]
    caption?: string
  }) {
    const { agent_uid, chat_id, reply_to, file, mentions, caption } = payload

    const { data } = await axios.post(
      `${env.get('WHATSAPP_API_BASE_URL')}/sendImage`,
      {
        reply_to,
        mentions,
        caption,
        session: agent_uid,
        chat_id,
        file: {
          mimetype: 'image/jpeg',
          filename: 'image.jpeg',
          url: file.url,
          data: file.data,
        },
      },
      { headers: this.headers }
    )

    return data
  }

  async sendFile(payload: {
    agent_uid: string
    chat_id: string
    reply_to?: string
    file: { mimetype: string; filename: string; url?: string; data?: string }
    mentions?: string[]
    caption?: string
  }) {
    const { agent_uid, chat_id, reply_to, file, mentions, caption } = payload

    const { data } = await axios.post(
      `${env.get('WHATSAPP_API_BASE_URL')}/sendFile`,
      {
        session: agent_uid,
        chat_id,
        reply_to,
        file,
        mentions,
        caption: caption || '🧠',
      },
      { headers: this.headers }
    )

    return data
  }

  async sendVoice(payload: {
    agent_uid: string
    chat_id: string
    reply_to?: string
    file: { url?: string; data?: string }
    mentions?: string[]
  }) {
    const { agent_uid, chat_id, reply_to, file, mentions } = payload

    const { data } = await axios.post(
      `${env.get('WHATSAPP_API_BASE_URL')}/sendVoice`,
      {
        session: agent_uid,
        chat_id,
        reply_to,
        mentions,
        file: {
          mimetype: 'audio/ogg; codecs=opus',
          filename: 'audio.opus',
          url: file?.url,
          data: file?.data,
        },
      },
      { headers: this.headers }
    )

    return data
  }

  async sendVideo(payload: {
    agent_uid: string
    chat_id: string
    file: { url?: string; data?: string }
    caption?: string
    asNote?: boolean
  }) {
    const { agent_uid, chat_id, file, caption, asNote } = payload

    const { data } = await axios.post(
      `${env.get('WHATSAPP_API_BASE_URL')}/sendVideo`,
      {
        session: agent_uid,
        chat_id,
        caption: caption || '🧠',
        asNote,
        file: {
          mimetype: 'video/mp4',
          filename: 'video.mp4',
          url: file?.url,
          data: file?.data,
        },
      },
      { headers: this.headers }
    )

    return data
  }

  async sendButtons(payload: {
    agent_uid: string
    chat_id: string
    header?: string
    header_image?: { mimetype: string; filename: string; url: string }
    body: string
    footer?: string
    buttons: any[]
  }) {
    const { header, header_image, body, footer, buttons, agent_uid, chat_id } = payload

    const { data } = await axios.post(
      `${env.get('WHATSAPP_API_BASE_URL')}/sendButtons`,
      {
        header,
        body,
        footer,
        buttons,
        session: agent_uid,
        chat_id,
        headerImage: header_image,
      },
      { headers: this.headers }
    )

    return data
  }

  async sendLinkPreview(payload: { chat_id: string; agent_uid: string; url: string }) {
    const { chat_id, agent_uid, url } = payload

    const { data } = await axios.post(
      `${env.get('WHATSAPP_API_BASE_URL')}/sendLinkPreview`,
      {
        url,
        chat_id,
        session: agent_uid,
      },
      { headers: this.headers }
    )

    return data
  }

  async startTyping(payload: { agent_uid: string; chat_id: string }) {
    const { agent_uid, chat_id } = payload

    const { data } = await axios.post(
      `${env.get('WHATSAPP_API_BASE_URL')}/startTyping`,
      {
        session: agent_uid,
        chat_id,
      },
      { headers: this.headers }
    )

    return data
  }

  async stopTyping(payload: { agent_uid: string; chat_id: string }) {
    const { agent_uid, chat_id } = payload

    const { data } = await axios.post(
      `${env.get('WHATSAPP_API_BASE_URL')}/stopTyping`,
      {
        session: agent_uid,
        chat_id,
      },
      { headers: this.headers }
    )

    return data
  }

  async presence(payload: { agent_uid: string; chat_id: string; presence: string }) {
    const { agent_uid, chat_id, presence } = payload

    const { data } = await axios.post(
      `${env.get('WHATSAPP_API_BASE_URL')}/presence`,
      {
        session: agent_uid,
        chat_id,
        presence,
      },
      { headers: this.headers }
    )

    return data
  }

  async addReaction(payload: {
    agent_uid: string
    chat_id: string
    message_id: string
    reaction: string
  }) {
    const { agent_uid, chat_id, message_id, reaction } = payload

    const { data } = await axios.put(
      `${env.get('WHATSAPP_API_BASE_URL')}/reaction`,
      {
        session: agent_uid,
        chat_id,
        message_id,
        reaction,
      },
      { headers: this.headers }
    )

    return data
  }

  async starMessage(payload: {
    agent_uid: string
    chat_id: string
    message_id: string
    star: boolean
  }) {
    const { agent_uid, chat_id, message_id, star } = payload

    const { data } = await axios.put(
      `${env.get('WHATSAPP_API_BASE_URL')}/star`,
      {
        session: agent_uid,
        chat_id,
        message_id,
        star,
      },
      { headers: this.headers }
    )

    return data
  }

  async sendContactVcard(payload: { agent_uid: string; chat_id: string; contacts: any[] }) {
    const { agent_uid, chat_id, contacts } = payload

    const { data } = await axios.post(
      `${env.get('WHATSAPP_API_BASE_URL')}/sendContactVcard`,
      {
        session: agent_uid,
        chat_id,
        contacts,
      },
      { headers: this.headers }
    )

    return data
  }

  async getMessages(params: { agent_uid: string; chat_id: string }) {
    const { agent_uid, chat_id } = params

    const { data } = await axios.get(`${env.get('WHATSAPP_API_BASE_URL')}/messages`, {
      params: {
        session: agent_uid,
        chat_id,
      },
      headers: this.headers,
    })

    return data
  }

  async resetChat(payload: { agent_uid: string; chat_id: string; message_id: string }) {
    const { agent_uid, chat_id, message_id } = payload

    const chat = await Chat.query()
      .where('agent_uid', agent_uid)
      .andWhere('external_id', chat_id)
      .firstOrFail()

    await this.sendSeen({ agent_uid, chat_id, message_id })
    await this.startTyping({ agent_uid, chat_id })
    await chat.merge({ external_id: null }).save()
    await this.stopTyping({ agent_uid, chat_id })
    await this.sendText({
      agent_uid,
      chat_id,
      reply_to: message_id,
      text: '✅ Reset',
    })
  }

  async sendHelp(payload: {
    agent_uid: string
    chat_id: string
    message_id: string
    message: string
  }) {
    const { agent_uid, chat_id, message_id, message } = payload

    await this.sendSeen({ agent_uid, chat_id, message_id })
    await this.startTyping({ agent_uid, chat_id })
    await this.stopTyping({ agent_uid, chat_id })
    await this.sendText({
      agent_uid,
      chat_id,
      reply_to: message_id,
      text: message,
    })
  }

  async toggleAI(payload: {
    agent_uid: string
    chat_id: string
    message_id: string
    disable: boolean
  }) {
    const { agent_uid, chat_id, message_id, disable } = payload

    const chat = await Chat.query()
      .where('agent_uid', agent_uid)
      .andWhere('external_id', chat_id)
      .firstOrFail()

    await this.sendSeen({ agent_uid, chat_id, message_id })
    await this.startTyping({ agent_uid, chat_id })
    await chat.merge({ is_ai_disabled: disable }).save()
    await this.stopTyping({ agent_uid, chat_id })
    await this.sendText({
      agent_uid,
      chat_id,
      reply_to: message_id,
      text: disable ? '✅ AI paused' : '✅ AI enabled',
    })
  }

  async sendResponse(payload: {
    agent_uid: string
    chat_id: string
    message_id: string
    responseData: any
  }) {
    const { agent_uid, chat_id, message_id, responseData } = payload

    if (responseData?.tts_file_url) {
      await this.sendVoice({
        agent_uid,
        chat_id,
        reply_to: message_id,
        file: { url: responseData.tts_file_url },
      })
    } else {
      await this.sendText({
        agent_uid,
        chat_id,
        reply_to: message_id,
        text: `🧠 ${responseData?.output_text?.trim()}`,
      })
    }
  }
}
