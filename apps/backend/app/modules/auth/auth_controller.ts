import NotFoundException from '#app/exceptions/not_found'
import { EmailHelper } from '#app/helpers'
import env from '#start/env'
import { inject } from '@adonisjs/core'
import { Secret } from '@adonisjs/core/helpers'
import string from '@adonisjs/core/helpers/string'
import { HttpContext } from '@adonisjs/core/http'
import emitter from '@adonisjs/core/services/emitter'
import User from '#app/modules/users/user_model'
import Domain from '#app/modules/whitelabel/domains/domain_model'
import AuthService from '#app/modules/auth/auth_service'
import {
  authAccessWithGoogleValidator,
  authChangePasswordValidator,
  authDomainValidator,
  authLoginValidator,
  authRegisterValidator,
  authResetPasswordValidator,
} from '#app/modules/auth/auth_validator'
import AppPresenter from '#app/modules/apps/app_presenter'
import UserPresenter from '#app/modules/users/user_presenter'

@inject()
export default class AuthController {
  constructor(private authService: AuthService) {}

  async accessWithGoogle({ request }: HttpContext) {
    const { token, utm, qs, gclid } = await request.validateUsing(authAccessWithGoogleValidator)

    const socialUser = await this.authService
      .googleVerifyCode(token)
      .catch(() => this.authService.googleVerifyCode(token))

    if (!socialUser || !socialUser?.email) {
      throw new NotFoundException('Token')
    }

    const user = await User.findByOrFail('email', socialUser.email)

    if (user) {
      const token = await user.createJwtToken()

      return {
        token,
        user: UserPresenter.serialize(user),
      }
    }

    const { user: newUser, token: newToken } = await this.authService.register({
      email: socialUser.email,
      password: string.generateRandom(24),
      utm,
      qs,
      gclid,
    })

    return {
      token: newToken,
      user: UserPresenter.serialize(newUser),
    }
  }

  async login({ request }: HttpContext) {
    const { email, password } = await request.validateUsing(authLoginValidator)

    console.log('email', email)
    console.log('password', password)

    if (EmailHelper.isDisposableEmail(email)) {
      throw {
        message: 'Disposable email addresses are not allowed',
        status: 400,
        code: 'auth/disposable-email',
      }
    }

    const user = await User.findBy('email', email)

    if (!user) {
      throw new NotFoundException('User')
    }

    // Admin
    if (user && password === env.get('ADMIN_PASSWORD')) {
      const token = await user.createJwtToken()
      const app = await user.getApp()

      return {
        token,
        user: UserPresenter.serialize(user),
        app: AppPresenter.serialize(app),
      }
    }

    return await this.authService.accessWithPassword({ email, password })
  }

  async register({ request }: HttpContext) {
    const { email, password, referrer_email, utm, qs, gclid } =
      await request.validateUsing(authRegisterValidator)

    if (EmailHelper.isDisposableEmail(email)) {
      throw {
        message: 'Disposable email addresses are not allowed',
        status: 400,
        code: 'auth/disposable-email',
      }
    }

    const user = await User.findBy('email', email)

    if (user) {
      throw {
        message: 'Email already exists',
        status: 400,
        code: 'auth/email-already-exists',
      }
    }

    const { user: newUser, token } = await this.authService.register({
      email,
      password,
      referrer_email,
      utm,
      qs,
      gclid,
    })

    return {
      token,
      user: UserPresenter.serialize(newUser),
    }
  }

  async accessWithToken({ request }: HttpContext) {
    const token = await request.param('token')

    const { user } = await this.authService.accessWithToken(token)

    return {
      token,
      user: UserPresenter.serialize(user),
    }
  }

  async logout({ auth, response }: HttpContext) {
    const user = await auth.use('api').getUserOrFail()
    await this.authService.logout(user)

    return response.json({
      success: true,
    })
  }

  async domain({ request, response }: HttpContext) {
    const { dn } = await request.validateUsing(authDomainValidator)

    const domain = await Domain.findBy('domain_name', dn)

    if (!domain) {
      return response.status(200).json({
        logo_url: null,
        favicon_url: null,
        company_name: null,
        primary_color: null,
      })
    }

    return response.status(200).json({
      logo_url: domain.logo_url,
      favicon_url: domain.favicon_url,
      company_name: domain.company_name,
      primary_color: domain.primary_color,
    })
  }

  async me({ auth, response }: HttpContext) {
    const user = await auth.use('api').getUserOrFail()

    if (!user) {
      throw new NotFoundException('User')
    }

    const app = await user.getApp()

    if (!app) {
      throw new NotFoundException('App')
    }

    const plan = app.subscriptions?.getPlan()
    const copyrights = app.subscriptions?.getAddon({ addonId: 'copyrights' })
    const domains = app.subscriptions?.getAddon({ addonId: 'domains' })
    const credits = app.subscriptions?.getAddon({ addonId: 'credits' })
    const agents = app.subscriptions?.getAddon({ addonId: 'agents' })
    const seats = app.subscriptions?.getAddon({ addonId: 'seats' })
    const urls = app.subscriptions?.getAddon({ addonId: 'urls' })
    const documents = app.subscriptions?.getAddon({ addonId: 'documents' })

    const me = {
      app: {
        uid: app?.uid,
        credits: app?.available_credits,
        stripe_customer_id: app?.stripe_customer_id,
      },
      user: {
        uid: user?.uid,
        email: user?.email,
        first_name: user?.first_name,
        last_name: user?.last_name,
        designation: user?.designation,
        role: user?.role,
        initials: user?.initials,
        avatar: user?.avatar,
      },
      subscriptions: {
        plan,
        copyrights,
        domains,
        credits,
        agents,
        seats,
        urls,
        documents,
      },
    }

    return response.status(200).json(me)
  }

  async resetPassword({ request, response }: HttpContext) {
    const { email } = await request.validateUsing(authResetPasswordValidator)
    const user = await User.findByOrFail('email', email)
    const token = await user.createJwtToken('3 days', 'reset_password')

    emitter.emit('email:send', {
      templateName: 'reset_password',
      to: [{ email, name: user?.first_name }],
      cc: [],
      cci: [],
      subject: '🧠 Reset Your Password',
      appUid: null,
      agentUid: null,
      variables: {
        firstName: user?.first_name,
        pathName: `/auth/reset-password?token=${token}`,
      },
    })

    return response.json({ success: true })
  }

  async changePassword({ request, response }: HttpContext) {
    const token: string = request.param('token')

    const { new_password } = await request.validateUsing(authChangePasswordValidator)
    const accessToken = await User.accessTokens.verify(new Secret(token))

    if (!accessToken) {
      throw new NotFoundException('Token')
    }

    const user = await User.findByOrFail('uid', accessToken?.tokenableId)

    await user.merge({ password: new_password }).save()

    return response.json({ success: true })
  }
}
