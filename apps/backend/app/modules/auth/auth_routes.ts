import router from '@adonisjs/core/services/router'

export default () => {
  const ENDPOINT_PREFIX = '/auth'
  const CONTROLLER_PATH = '#app/modules/auth/auth_controller'

  router
    .group(() => {
      router.post('/login', `${CONTROLLER_PATH}.login`)
      router.post('/register', `${CONTROLLER_PATH}.register`)
      router.post('/access-google', `${CONTROLLER_PATH}.accessWithGoogle`)
      router.get('/access-token/:token', `${CONTROLLER_PATH}.accessWithToken`)
      router.get('/logout', `${CONTROLLER_PATH}.logout`)
      router.get('/me', `${CONTROLLER_PATH}.me`)
      router.get('/domain', `${CONTROLLER_PATH}.domain`)
      router.post('/reset-password', `${CONTROLLER_PATH}.resetPassword`)
      router.post('/change-password/:token', `${CONTROLLER_PATH}.changePassword`)
    })
    .prefix(`${ENDPOINT_PREFIX}`)
}
