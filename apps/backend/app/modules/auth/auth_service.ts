import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from '#app/helpers'
import env from '#start/env'
import { OAuth2Client } from 'google-auth-library'
import { AccessToken } from '@adonisjs/auth/access_tokens'
import { inject } from '@adonisjs/core'
import { Secret } from '@adonisjs/core/helpers'
import string from '@adonisjs/core/helpers/string'
import { HttpContext } from '@adonisjs/core/http'
import emitter from '@adonisjs/core/services/emitter'
import App from '#app/modules/apps/app_model'
import User from '#app/modules/users/user_model'

@inject()
export default class AuthService {
  private ctx: HttpContext

  constructor(ctx: HttpContext) {
    this.ctx = ctx
  }

  async accessWithPassword(params: { email: string; password: string }) {
    const { email, password } = params

    const user = await User.verifyCredentials(email, password)
    const app = await user.getApp()
    const token = await user.createJwtToken()

    if (user && token) {
      emitter.emit('trace:create', {
        userUid: user?.uid,
        trigger: 'auth',
        action: 'auth_login',
        credits: 0,
        sendWebhook: false,
        metadata: { email },
      })

      return {
        token,
        user,
        app,
      }
    }

    throw {
      message: 'Invalid credentials',
      status: 400,
      code: 'auth/invalid-credentials',
    }
  }

  async accessWithToken(token: string) {
    try {
      const accessToken = await User.accessTokens.verify(new Secret(token))

      if (!accessToken) {
        throw {
          message: 'Invalid or expired token',
          status: 401,
          code: 'auth/invalid-token',
        }
      }

      // Find the user associated with the token
      const user = await User.findByOrFail('uid', accessToken.tokenableId)

      emitter.emit('trace:create', {
        userUid: user?.uid,
        trigger: 'auth',
        action: 'auth_login',
        credits: 0,
        sendWebhook: false,
        metadata: { email: user?.email },
      })

      return {
        token,
        user,
      }
    } catch (error) {
      throw {
        message: 'Unauthorized access',
        status: 401,
        code: 'auth/unauthorized',
        error,
      }
    }
  }

  async register(params: {
    email: string
    password: string | null
    referrer_email?: string | null
    utm?: object | null
    qs?: object | null
    gclid?: string | null
  }) {
    const { email, password, referrer_email, utm, qs, gclid } = params
    let computedPassword: string = password || string.generateRandom(24)

    // Verify email
    const user = await User.findBy('email', email)

    if (user) {
      throw {
        message: 'This email address is already in use',
        status: 400,
        code: 'auth/email-already-used',
      }
    }

    const metadata = {
      email,
      gclid: gclid || null,
      ...(qs || {}),
      ...(utm || {}),
    }

    // Create user
    const savedUser: User = await User.create({
      first_name: 'User',
      email: email,
      password: computedPassword,
      role: 'owner',
      referrer_email: email === referrer_email ? null : referrer_email,
    })

    // Create Stripe customer
    const customer = await StripeHelper.createCustomer({
      name: 'User',
      email,
      metadata,
    })

    // Create app
    const savedApp = await App.create({
      available_credits: 0,
      stripe_customer_id: customer.id,
      gclid: gclid || null,
      is_first_time_customer: true,
      is_first_time_churning: false,
    })

    // Attach app to user
    await savedUser.related('apps').save(savedApp)

    if (password !== computedPassword) {
      emitter.emit('email:send', {
        templateName: 'account_password',
        to: [{ email }],
        cc: [],
        cci: [],
        subject: '🧠 Your Account Has Been Created',
        appUid: savedApp?.uid,
        agentUid: savedUser?.uid,
        variables: {
          email,
          password: computedPassword,
          pathName: `/auth/access`,
        },
      })
    }

    emitter.emit('google_ads:create', {
      appUid: savedApp?.uid,
      conversionName: 'registered',
      conversionValue: 1,
      conversionCurrency: 'USD',
    })

    emitter.emit('trace:create', {
      appUid: savedApp?.uid,
      userUid: savedUser?.uid,
      trigger: 'auth',
      action: 'auth_register',
      credits: 0,
      sendWebhook: false,
      metadata: { email },
    })

    emitter.emit('crisp:push_event', {
      email,
      event: {
        text: 'send_welcome_email',
        data: { timestamp: DateHelper.getNow().toMillis() },
        color: 'red',
      },
    })

    // Login
    return await this.accessWithPassword({ email, password: computedPassword })
  }

  async logout(user: User & { currentAccessToken: AccessToken }) {
    try {
      if (!user?.currentAccessToken) {
        throw {
          message: 'No active token found',
          status: 400,
          code: 'auth/no-token',
        }
      }

      await user.deleteJwtToken()
      return true
    } catch (error) {
      throw {
        message: 'Failed to logout',
        status: 500,
        code: 'auth/logout-failed',
        error,
      }
    }
  }

  async googleVerifyCredentials(credential: string) {
    const client = new OAuth2Client(env.get('GOOGLE_CLIENT_ID'))
    const loginTicket = await client.verifyIdToken({ idToken: credential })
    const googleUser = loginTicket?.getPayload()

    return {
      email: googleUser?.email,
      first_name: googleUser?.given_name,
      last_name: googleUser?.family_name,
      email_verified: googleUser?.email_verified,
    }
  }

  async googleVerifyCode(code: string): Promise<{
    email?: string
    first_name?: string
    last_name?: string
    email_verified?: boolean
  }> {
    const googleUser = await this.ctx.ally.use('google').userFromToken(code)

    return {
      email: googleUser?.email,
      first_name: googleUser?.original?.given_name,
      last_name: googleUser?.original?.family_name,
      email_verified: googleUser?.original?.email_verified,
    }
  }
}
