import vine from '@vinejs/vine'

const authLoginValidator = vine.compile(
  vine.object({
    email: vine.string().email().normalizeEmail({ all_lowercase: true }).trim(),
    password: vine.string().trim(),
  })
)

const authDomainValidator = vine.compile(
  vine.object({
    dn: vine.string().trim(),
  })
)

const authRegisterValidator = vine.compile(
  vine.object({
    email: vine.string().email().normalizeEmail({ all_lowercase: true }).trim(),
    password: vine.string().trim(),
    referrer_email: vine
      .string()
      .email()
      .normalizeEmail({ all_lowercase: true })
      .trim()
      .optional()
      .nullable(),
    gclid: vine.string().trim().optional().nullable(),
    utm: vine.object({}).allowUnknownProperties().optional().nullable(),
    qs: vine.object({}).allowUnknownProperties().optional().nullable(),
  })
)

const authAccessWithGoogleValidator = vine.compile(
  vine.object({
    token: vine.string().trim(),
    gclid: vine.string().trim(),
    utm: vine.object({}).allowUnknownProperties(),
    qs: vine.object({}).allowUnknownProperties(),
  })
)

const authAccessWithEmailValidator = vine.compile(
  vine.object({
    email: vine.string().email().normalizeEmail({ all_lowercase: true }).trim(),
    password: vine.string().trim(),
  })
)

const authResetPasswordValidator = vine.compile(
  vine.object({
    email: vine.string().email().normalizeEmail({ all_lowercase: true }).trim(),
  })
)

const authChangePasswordValidator = vine.compile(
  vine.object({
    new_password: vine.string().trim(),
  })
)

export {
  authDomainValidator,
  authLoginValidator,
  authRegisterValidator,
  authAccessWithGoogleValidator,
  authAccessWithEmailValidator,
  authResetPasswordValidator,
  authChangePasswordValidator,
}
