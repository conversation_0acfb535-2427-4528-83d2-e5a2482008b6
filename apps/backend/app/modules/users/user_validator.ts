import vine from '@vinejs/vine'
import string from '@adonisjs/core/helpers/string'

const rolesSchema = vine.enum(['admin', 'owner', 'manager', 'client'])

const storeSchema = vine.object({
  first_name: vine
    .string()
    .trim()
    .transform((value: string) => string.sentenceCase(value)),
  last_name: vine
    .string()
    .trim()
    .transform((value: string) => string.sentenceCase(value))
    .nullable()
    .optional(),
  email: vine.string().email().normalizeEmail({ all_lowercase: true }).trim(),
  role: rolesSchema,
  assigned_agents: vine.array(vine.string().uuid().trim()).nullable().optional(),
})

const updateSchema = vine.object({
  uid: vine.string().uuid().trim().nullable().optional(),
  ...storeSchema.getProperties(),
})

const changeInfosSchema = vine.object({
  first_name: vine
    .string()
    .trim()
    .transform((value: string) => string.sentenceCase(value)),
  last_name: vine
    .string()
    .trim()
    .transform((value: string) => string.sentenceCase(value))
    .nullable()
    .optional(),
  civility: vine.string().trim().nullable().optional(),
})

const changeEmailSchema = vine.object({
  password: vine.string().trim(),
  new_email: vine.string().email().normalizeEmail({ all_lowercase: true }).trim(),
})

const changePasswordSchema = vine.object({
  password: vine.string().trim(),
  password_confirm: vine.string().trim(),
})

const inviteFriendsSchema = vine.object({
  emails: vine.array(vine.string().email().normalizeEmail({ all_lowercase: true }).trim()),
})

const userStoreValidator = vine.compile(storeSchema)
const userUpdateValidator = vine.compile(updateSchema)
const userChangeInfosValidator = vine.compile(changeInfosSchema)
const userChangeEmailValidator = vine.compile(changeEmailSchema)
const userChangePasswordValidator = vine.compile(changePasswordSchema)
const userInviteFriendsValidator = vine.compile(inviteFriendsSchema)
const userRolesValidator = vine.compile(rolesSchema)

export {
  userStoreValidator,
  userUpdateValidator,
  userChangeInfosValidator,
  userChangeEmailValidator,
  userChangePasswordValidator,
  userInviteFriendsValidator,
  userRolesValidator,
}
