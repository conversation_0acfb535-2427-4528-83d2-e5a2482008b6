import { DateHelper } from '#app/helpers/date_helper'
import { ModelPaginatorContract } from '@adonisjs/lucid/types/model'
import User from '#app/modules/users/user_model'

export default class UserPresenter {
  static serialize(item: User) {
    return {
      uid: item.uid,
      email: item.email,
      role: item.role,
      first_name: item.first_name,
      last_name: item.last_name,
      civility: item.civility,
      referrer_email: item.referrer_email,
      metadata: item.metadata,
      assigned_agents: item.assigned_agents,
      last_activity_at: item.last_activity_at,
      created_at: DateHelper.formatDate(item.created_at),
      updated_at: DateHelper.formatDate(item.updated_at),
      designation: item.designation,
      initials: item.initials,
      name: item.name,
    }
  }

  static serializeMany(items: User[]) {
    return items.map((item) => this.serialize(item))
  }

  static serializePaginated(items: ModelPaginatorContract<User>) {
    return {
      page: items.currentPage,
      per_page: items.perPage,
      total: items.total,
      last_page: items.lastPage,
      data: this.serializeMany(items.all()),
    }
  }
}
