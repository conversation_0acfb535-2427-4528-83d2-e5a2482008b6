import { requestFilterValidator } from '#app/validators'
import env from '#start/env'
import { Infer } from '@vinejs/vine/types'
import { inject } from '@adonisjs/core'
import { cuid } from '@adonisjs/core/helpers'
import { HttpContext } from '@adonisjs/core/http'
import emitter from '@adonisjs/core/services/emitter'
import User from '#app/modules/users/user_model'
import { userStoreValidator, userUpdateValidator } from '#app/modules/users/user_validator'

@inject()
export default class UserService {
  private ctx: HttpContext

  constructor(ctx: HttpContext) {
    this.ctx = ctx
  }

  async index(filter: Infer<typeof requestFilterValidator>) {
    const page = filter.page || 1

    return await this.ctx.app
      .related('users')
      .query()
      .paginate(page, env.get('APP_PAGINATION_LIMIT'))
  }

  async show(uid: string) {
    return await this.ctx.app.related('users').query().where('uid', uid).firstOrFail()
  }

  async store(payload: Infer<typeof userStoreValidator>) {
    const { first_name, last_name, email, role, assigned_agents } = payload

    const existingUser = await User.findBy('email', email)

    if (existingUser) {
      throw {
        message: 'Email already used',
        status: 400,
        code: 'auth/user-already-exists',
      }
    }

    const password = cuid()

    const savedUser = await this.ctx.app.related('users').create({
      first_name,
      last_name,
      email,
      password,
      role,
      assigned_agents,
    })

    emitter.emit('email:send', {
      templateName: 'join_invite',
      to: [{ email, name: first_name }],
      cc: [],
      cci: [],
      subject: "🧠 You're invited to join us",
      appUid: this.ctx.app.uid,
      agentUid: null,
      variables: {
        firstName: first_name,
        email,
        password,
        pathName: '/auth/access',
      },
    })

    return savedUser
  }

  async update(uid: string, payload: Infer<typeof userUpdateValidator>) {
    console.log('update', uid)

    const { first_name, last_name, email, role, assigned_agents } = payload

    const existingEmail = await User.query()
      .where('email', email)
      .andWhere('uid', '!=', uid)
      .first()

    console.log('existingEmail', existingEmail)
    console.log('uid', uid)

    if (existingEmail) {
      throw {
        message: 'Email already used',
        status: 400,
        code: 'auth/user-already-exists',
      }
    }

    const existingUser = await this.ctx.app.related('users').query().where('uid', uid).firstOrFail()

    const savedUser = await existingUser
      .merge({
        first_name,
        last_name,
        email,
        role,
        assigned_agents,
      })
      .save()

    return savedUser
  }

  async destroy(uid: string) {
    const user = await this.ctx.app.related('users').query().where('uid', uid).firstOrFail()

    return await user.delete()
  }

  async changeEmail(user: User, email: string) {
    await user?.merge({ email }).save()
    await user?.deleteJwtToken()
  }

  async changePassword(user: User, password: string) {
    await user?.merge({ password }).save()
    await user?.deleteJwtToken()
  }

  async sendNewPassword(uid: string) {
    const founderUser = await this.ctx.app.related('users').query().where('uid', uid).firstOrFail()

    const password = cuid()
    const savedUser: User = await founderUser.merge({ password }).save()

    emitter.emit('email:send', {
      templateName: 'join_invite',
      to: [{ email: savedUser?.email, name: savedUser?.first_name }],
      cc: [],
      cci: [],
      subject: "🧠 You're invited to join us",
      appUid: this.ctx.app.uid,
      agentUid: null,
      variables: {
        firstName: savedUser?.first_name,
        email: savedUser?.email,
        password,
        pathName: '/auth/access',
      },
    })

    return savedUser
  }
}
