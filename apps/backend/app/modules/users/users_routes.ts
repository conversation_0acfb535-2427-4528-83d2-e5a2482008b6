import router from '@adonisjs/core/services/router'

export default () => {
  const ENDPOINT_PREFIX = '/users'
  const CONTROLLER_PATH = '#app/modules/users/users_controller'

  router
    .group(() => {
      router.get('/', `${CONTROLLER_PATH}.index`)
      router.get('/:uid', `${CONTROLLER_PATH}.show`)
      router.post('/', `${CONTROLLER_PATH}.store`)
      router.put('/:uid', `${CONTROLLER_PATH}.update`)
      router.delete('/:uid', `${CONTROLLER_PATH}.destroy`)
      router.post('/change-infos', `${CONTROLLER_PATH}.changeInfos`)
      router.post('/change-email', `${CONTROLLER_PATH}.changeEmail`)
      router.post('/change-password', `${CONTROLLER_PATH}.changePassword`)
      router.get('/send-new-password/:uid', `${CONTROLLER_PATH}.sendNewPassword`)
    })
    .prefix(`${ENDPOINT_PREFIX}`)
}
