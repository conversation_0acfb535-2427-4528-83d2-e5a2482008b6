import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from '#app/helpers'
import type { Infer } from '@vinejs/vine/types'
import { Filterable } from 'adonis-lucid-filter'
import { DateTime } from 'luxon'
import { v4 as uuid } from 'uuid'
import { AccessToken, DbAccessTokensProvider } from '@adonisjs/auth/access_tokens'
import { withAuthFinder } from '@adonisjs/auth/mixins/lucid'
import { compose, cuid } from '@adonisjs/core/helpers'
import string from '@adonisjs/core/helpers/string'
import hash from '@adonisjs/core/services/hash'
import {
  afterUpdate,
  beforeCreate,
  column,
  computed,
  manyToMany,
  SnakeCaseNamingStrategy,
} from '@adonisjs/lucid/orm'
import { BaseModel } from '@adonisjs/lucid/orm'
import { ModelPaginatorContract } from '@adonisjs/lucid/types/model'
import type { ManyToMany } from '@adonisjs/lucid/types/relations'
import App from '#app/modules/apps/app_model'
import { userRolesValidator } from '#app/modules/users/user_validator'
import BaseFilter from '#app/modules/base/base_filter'

const AuthFinder = withAuthFinder(() => hash.use('scrypt'), {
  uids: ['email'],
  passwordColumnName: 'password',
})

export default class User extends compose(BaseModel, Filterable, AuthFinder) {
  serializeExtras = false
  static selfAssignPrimaryKey = true
  static namingStrategy = new SnakeCaseNamingStrategy()
  static $filter = () => BaseFilter
  static accessTokens = DbAccessTokensProvider.forModel(User, {
    expiresIn: '30 days',
    prefix: 'users_',
    table: 'auth_access_tokens',
    type: 'user_token',
    tokenSecretLength: 40,
  })
  declare currentAccessToken?: AccessToken | null

  @beforeCreate()
  static assignUid(user: User) {
    user.uid = uuid()
  }

  @manyToMany(() => App, {
    pivotTable: 'user_apps',
    localKey: 'uid',
    relatedKey: 'uid',
    pivotForeignKey: 'user_uid',
    pivotRelatedForeignKey: 'app_uid',
    pivotTimestamps: true,
  })
  declare apps: ManyToMany<typeof App>

  @column({
    isPrimary: true,
    meta: { searchable: true, type: 'string', validations: [{ name: 'uuid' }] },
  })
  declare uid: string

  @column({
    prepare: (value: string) => value?.toLowerCase()?.trim(),
    consume: (value: string) => value?.toLowerCase()?.trim(),
    meta: {
      searchable: true,
      type: 'string',
      validations: [{ name: 'email' }],
    },
  })
  declare email: string

  @column({
    serializeAs: null,
    meta: {
      searchable: false,
      type: 'string',
      validations: [{ name: 'noEscape' }],
    },
  })
  declare password: string

  @column({ meta: { searchable: false, type: 'string' } })
  declare role: Infer<typeof userRolesValidator>

  @column({
    prepare: (value: string) => (value ? string.sentenceCase(value)?.trim() : null),
    consume: (value: string) => (value ? string.sentenceCase(value)?.trim() : null),
    meta: { searchable: true, type: 'string', validations: [] },
  })
  declare first_name: string

  @column({
    prepare: (value: string) => (value ? string.sentenceCase(value)?.trim() : null),
    consume: (value: string) => (value ? string.sentenceCase(value)?.trim() : null),
    meta: { searchable: true, type: 'string', validations: [] },
  })
  declare last_name: string | null

  @column({ meta: { searchable: true, type: 'string' } })
  declare civility: string

  @column({
    prepare: (value: string) => value?.toLowerCase()?.trim(),
    consume: (value: string) => value?.toLowerCase()?.trim(),
    meta: {
      searchable: true,
      type: 'string',
      validations: [{ name: 'email' }],
    },
  })
  declare referrer_email: string | null

  @column({ serializeAs: null, meta: { searchable: false, type: 'string' } })
  declare crisp_id: string

  @column({ meta: { searchable: true, type: 'object' } })
  declare metadata: { [key: string]: any } | null

  @column({
    meta: { searchable: true, type: 'array', children_type: 'string' },
  })
  declare assigned_agents: string[] | null

  @column({ meta: { searchable: true, type: 'date' } })
  declare last_activity_at?: DateTime | null

  @column.dateTime({ autoCreate: true, meta: { searchable: true, type: 'date' } })
  declare created_at: DateTime

  @column.dateTime({
    autoCreate: true,
    autoUpdate: true,
    meta: { searchable: true, type: 'date' },
  })
  declare updated_at: DateTime

  @computed()
  get designation(): string {
    return `${this.first_name ? this.first_name : ''} ${
      this.last_name ? this.last_name : ''
    }`.trim()
  }

  @computed()
  get initials(): string {
    return `${this.first_name ? this.first_name.charAt(0) : ''}${
      this.last_name ? this.last_name.charAt(0) : ''
    }`
  }

  @computed()
  get name(): string {
    return `${this.first_name ?? ''} ${this.last_name ?? ''}`.trim()
  }

  @computed()
  get avatar(): string {
    return `https://api.dicebear.com/9.x/fun-emoji/svg?seed=${this.uid}`
  }

  async createJwtToken(expiresIn: string = '365 days', name?: string) {
    const token = await User.accessTokens.create(this, ['*'], { expiresIn, name })

    return token.value!.release() || null
  }

  async deleteJwtToken() {
    if (this?.currentAccessToken?.identifier) {
      return await User.accessTokens.delete(this, this.currentAccessToken.identifier)
    }

    // await db.from('auth_access_tokens').where('tokenable_id', this.uid).delete()

    return null
  }

  async getCurrentJwtToken() {
    return (await this.currentAccessToken?.value?.release()) || null
  }

  async getApp() {
    const user: User = this

    return await user?.related('apps').query().firstOrFail()
  }

  @afterUpdate()
  static async updateStripeCustomer(user: User) {
    if (
      user.role === 'owner' &&
      (user.$dirty.email || user.$dirty.first_name || user.$dirty.last_name)
    ) {
      try {
        const app = await user.getApp()

        await StripeHelper.updateCustomer({
          customerId: app?.stripe_customer_id,
          email: user.email,
          name: user.designation,
        })
      } catch (error) {
        console.error(error)
      }
    }
  }

  serialize() {
    return {
      uid: this.uid,
      email: this.email,
      role: this.role,
      first_name: this.first_name,
      last_name: this.last_name,
      civility: this.civility,
      referrer_email: this.referrer_email,
      metadata: this.metadata,
      assigned_agents: this.assigned_agents,
      last_activity_at: this.last_activity_at,
      created_at: this.created_at,
      updated_at: this.updated_at,
      designation: this.designation,
      initials: this.initials,
      name: this.name,
    }
  }

  serializeMany(users: User[]) {
    return users.map((user) => user.serialize())
  }

  serializePaginated(users: ModelPaginatorContract<User>) {
    return {
      page: users.currentPage,
      perPage: users.perPage,
      total: users.total,
      lastPage: users.lastPage,
      data: this.serializeMany(users.all()),
    }
  }
}
