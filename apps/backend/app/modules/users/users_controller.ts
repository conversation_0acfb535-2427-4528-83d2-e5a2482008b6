import NotFoundException from '#app/exceptions/not_found'
import { requestFilterValidator } from '#app/validators'
import env from '#start/env'
import { inject } from '@adonisjs/core'
import { Hash } from '@adonisjs/core/hash'
import { cuid } from '@adonisjs/core/helpers'
import { HttpContext } from '@adonisjs/core/http'
import emitter from '@adonisjs/core/services/emitter'
import hash from '@adonisjs/core/services/hash'
import { ModelPaginatorContract } from '@adonisjs/lucid/types/model'
import { SimplePaginatorMetaKeys } from '@adonisjs/lucid/types/querybuilder'
import User from '#app/modules/users/user_model'
import UserService from '#app/modules/users/user_service'
import {
  userChangeEmailValidator,
  userChangeInfosValidator,
  userChangePasswordValidator,
  userStoreValidator,
  userUpdateValidator,
} from '#app/modules/users/user_validator'
import UserPresenter from '#app/modules/users/user_presenter'

@inject()
export default class UsersController {
  constructor(protected userService: UserService) {}

  async index({ request, response, user }: HttpContext) {
    if (user && !['admin', 'owner', 'manager'].includes(user?.role)) {
      throw {
        message: 'Invalid permission',
        status: 400,
        code: 'base/invalid-permission',
      }
    }

    const payload = await request.validateUsing(requestFilterValidator)

    const users = await this.userService.index(payload)

    return response.status(200).json(UserPresenter.serializePaginated(users))
  }

  async show({ request, response }: HttpContext) {
    const uid = request.param('uid')

    const user = await this.userService.show(uid)

    return response.status(200).json(UserPresenter.serialize(user))
  }

  async store({ app, user, request, response }: HttpContext) {
    if (user && !['admin', 'owner'].includes(user?.role)) {
      throw {
        message: 'Invalid permission (Allowed Roles: Owners)',
        status: 400,
        code: 'base/invalid-permission',
      }
    }

    await app.canConsumeSeats({ throwError: true })

    const payload = await request.validateUsing(userStoreValidator)
    const createdUser = await this.userService.store(payload)

    return response.status(200).json(UserPresenter.serialize(createdUser))
  }

  async update({ user, request, response }: HttpContext) {
    const uid = request.param('uid')

    if (user && !['admin', 'owner'].includes(user?.role)) {
      throw {
        message: 'Invalid permission (Allowed Roles: Owners)',
        status: 400,
        code: 'base/invalid-permission',
      }
    }

    const payload = await request.validateUsing(userUpdateValidator)
    const updatedUser = await this.userService.update(uid, payload)

    return response.status(200).json(UserPresenter.serialize(updatedUser))
  }

  async destroy({ app, request, response, user }: HttpContext) {
    const uid = request.param('uid')

    if (user && !['admin', 'owner'].includes(user?.role)) {
      throw {
        message: 'Invalid permission (Allowed Roles: Owners)',
        status: 400,
        code: 'base/invalid-permission',
      }
    }

    await this.userService.destroy(uid)

    return response.status(202).json({ success: true })
  }

  async changeInfos({ user, request, response }: HttpContext) {
    const { first_name, last_name, civility } =
      await request.validateUsing(userChangeInfosValidator)

    if (!user) {
      throw new NotFoundException('User')
    }

    await user.merge({ first_name, last_name, civility }).save()

    return response.json(UserPresenter.serialize(user))
  }

  async changeEmail({ user, request, response }: HttpContext) {
    const { password, new_email } = await request.validateUsing(userChangeEmailValidator)

    if (!user) {
      throw new NotFoundException('User')
    }

    const isPasswordValid = await hash.verify(user.password, password)

    if (!isPasswordValid) {
      throw {
        message: 'Invalid password',
        status: 400,
        code: 'auth/invalid-password',
      }
    }

    await this.userService.changeEmail(user as User, new_email)

    return response.json(UserPresenter.serialize(user))
  }

  async changePassword({ user, request, response }: HttpContext) {
    const { password, password_confirm } = await request.validateUsing(userChangePasswordValidator)

    if (password !== password_confirm) {
      throw {
        message: 'Passwords do not match',
        status: 400,
        code: 'auth/passwords-does-not-match',
      }
    }

    await this.userService.changePassword(user as User, password)

    return response.json({ success: true })
  }

  async sendNewPassword({ user, request, response }: HttpContext) {
    const uid = request.param('uid')

    if (user && !['admin', 'owner', 'manager'].includes(user?.role)) {
      throw {
        message: 'Invalid permission (Allowed Roles: Owners and Managers)',
        status: 400,
        code: 'base/invalid-permission',
      }
    }

    const foundUser = await this.userService.sendNewPassword(uid)

    return response.status(200).json(UserPresenter.serialize(foundUser))
  }
}
