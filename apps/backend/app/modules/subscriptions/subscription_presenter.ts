import { ModelPaginatorContract } from '@adonisjs/lucid/types/model'
import Subscription from '#app/modules/subscriptions/subscription_model'

export default class SubscriptionPresenter {
  static serialize(item: Subscription) {
    return {
      uid: item.uid,
      app_uid: item.app_uid,
      stripe_customer_id: item.stripe_customer_id,
      plan: item.plan,
      copyrights: item.copyrights,
      domains: item.domains,
      credits: item.credits,
      agents: item.agents,
      seats: item.seats,
      urls: item.urls,
      documents: item.documents,
      smtps: item.smtps,
      metadata: item.metadata,
    }
  }

  static serializeMany(items: Subscription[]) {
    return items.map((item) => this.serialize(item))
  }

  static serializePaginated(items: ModelPaginatorContract<Subscription>) {
    return {
      page: items.currentPage,
      per_page: items.perPage,
      total: items.total,
      last_page: items.lastPage,
      data: this.serializeMany(items.all()),
    }
  }
}
