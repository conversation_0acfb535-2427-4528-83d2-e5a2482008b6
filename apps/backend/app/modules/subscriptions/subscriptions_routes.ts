import router from '@adonisjs/core/services/router'

export default () => {
  const ENDPOINT_PREFIX = '/subscriptions'
  const CONTROLLER_PATH = '#app/modules/subscriptions/subscriptions_controller'

  router
    .group(() => {
      router.get(
        '/get-embedded-checkout-session/:stripe_price_id/:quantity',
        `${CONTROLLER_PATH}.getEmbeddedCheckoutSession`
      )
      router.get(
        '/get-portal-session-url/:stripe_customer_id',
        `${CONTROLLER_PATH}.getPortalSessionUrl`
      )
    })
    .prefix(`${ENDPOINT_PREFIX}`)
}
