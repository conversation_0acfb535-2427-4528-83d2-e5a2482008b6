import vine from '@vinejs/vine'

const cancellationFeedback = vine.enum([
  'customer_service',
  'low_quality',
  'missing_features',
  'other',
  'switched_service',
  'too_complex',
  'too_expensive',
  'unused',
])

const productIds = vine.enum([
  'simple',
  'basic',
  'starter',
  'pro',
  'business',
  'enterprise',
  'admin',
  'admin',
  'ast_1',
  'ast_2',
  'ast_3',
  'ast_4',
  'ast_5',
  'copyrights',
  'domains',
  'credits',
  'agents',
  'seats',
  'urls',
  'documents',
])

const addonsIds = vine.enum([
  'copyrights',
  'domains',
  'credits',
  'agents',
  'seats',
  'urls',
  'documents',
])

const sourcesTypes = vine.object({
  urls: vine.number().min(0),
  youtube: vine.number().min(0),
  documents: vine.number().min(0),
  medias: vine.number().min(0),
  quizzes: vine.number().min(0),
  texts: vine.number().min(0),
  products: vine.number().min(0),
  storage: vine.number().min(0),
})

const pricingTableTemplate = vine.array(
  vine.object({
    title: vine.string().trim(),
    features: vine.array(
      vine.object({
        label: vine.string().trim(),
        icon: vine.string().trim().nullable(),
        access_level: vine.number().min(0),
      })
    ),
  })
)

const subscription = vine.object({
  app_product_id: vine.string().trim(),
  app_product_type: vine.enum(['plan', 'addon']),
  app_product_quantity: vine.number().min(0),
  app_product_level: vine.number().min(0),
  stripe_product_id: vine.string().trim(),
  stripe_price_id: vine.string().trim(),
  stripe_subscription_id: vine.string().trim(),
  stripe_item_id: vine.string().trim(),
  stripe_trial_end: vine.number().min(0).nullable(),
  stripe_interval: vine.enum(['day', 'month', 'week', 'year']),
  stripe_current_period_start: vine.number().min(0).nullable(),
  stripe_current_period_end: vine.number().min(0).nullable(),
  stripe_status: vine.string().trim(),
})

const subscriptions = vine.object({
  plan: subscription.nullable(),
  copyrights: subscription.nullable(),
  domains: subscription.nullable(),
  credits: subscription.nullable(),
  agents: subscription.nullable(),
  seats: subscription.nullable(),
  urls: subscription.nullable(),
  documents: subscription.nullable(),
})

const product = vine.object({
  group: vine.enum(['app', 'whitelabel', 'knowledge']),
  level: vine.number().min(0),
  id: productIds,
  type: vine.enum(['plan', 'addon']),
  billing_type: vine.enum(['fixed', 'volume']),
  description: vine.string().trim(),
  title: vine.string().trim(),
  is_visible: vine.boolean(),
  is_popular: vine.boolean(),
  is_user_subscribed: vine.boolean(),
  min_quantity: vine.number().min(1),
  max_quantity: vine.number().min(1),
  monthly: vine.object({
    price: vine.number().min(0),
    default_stripe_price: vine.string().trim(),
    stripe_prices: vine.array(vine.string().trim()),
  }),
  yearly: vine.object({
    price: vine.number().min(0),
    default_stripe_price: vine.string().trim(),
    stripe_prices: vine.array(vine.string().trim()),
  }),
  pricing_table: pricingTableTemplate,
  limitations: vine
    .object({
      support: vine.string().trim(),
      agents: vine.number().min(0),
      credits: vine.number().min(0),
      accounts: vine.number().min(0),
      knowledge: sourcesTypes,
      max_file_size: vine.string().trim(),
      domains: vine.number().min(0),
      seats: vine.number().min(0),
      history: vine.number().min(0),
      is_free: vine.boolean(),
      is_ltd: vine.boolean(),
      copyrights: vine.boolean(),
      exports: vine.boolean(),
      auto_fetch: vine.boolean(),
      analytics: vine.boolean(),
      speech_to_text: vine.boolean(),
      text_to_speech: vine.boolean(),
      mobile_app: vine.boolean(),
      byok: vine.boolean(),
      webhooks: vine.boolean(),
      location: vine.boolean(),
      logs: vine.boolean(),
      audio_mode: vine.boolean(),
      vision_mode: vine.boolean(),
      tools: vine.array(vine.string().trim()),
      addons: vine.array(vine.string().trim()),
      models: vine.array(vine.string().trim()),
    })
    .optional()
    .requiredWhen('type', '=', 'plan'),
})

const embeddedCheckoutSessionSchema = vine.object({
  stripe_price_id: vine.string().trim(),
  quantity: vine.number().min(1),
})

const subscriptionProductValidator = vine.compile(product)
const subscriptionProductIdsValidator = vine.compile(productIds)
const subscriptionAddonsIdsValidator = vine.compile(addonsIds)
const subscriptionValidator = vine.compile(subscription)
const subscriptionsValidator = vine.compile(subscriptions)
const subscriptionPricingTableTemplateValidator = vine.compile(pricingTableTemplate)
const subscriptionsCancellationFeedbackValidator = vine.compile(cancellationFeedback)
const embeddedCheckoutSessionValidator = vine.compile(embeddedCheckoutSessionSchema)

export {
  subscriptionProductValidator,
  subscriptionProductIdsValidator,
  subscriptionAddonsIdsValidator,
  subscriptionValidator,
  subscriptionsValidator,
  subscriptionPricingTableTemplateValidator,
  subscriptionsCancellationFeedbackValidator,
  embeddedCheckoutSessionValidator,
}
