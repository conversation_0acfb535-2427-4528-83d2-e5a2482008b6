import { SubscriptionHelper } from '#app/helpers'
import type { Infer } from '@vinejs/vine/types'
import { Filterable } from 'adonis-lucid-filter'
import { v4 as uuid } from 'uuid'
import { compose } from '@adonisjs/core/helpers'
import { beforeCreate, belongsTo, column, SnakeCaseNamingStrategy } from '@adonisjs/lucid/orm'
import { BaseModel } from '@adonisjs/lucid/orm'
import type { BelongsTo } from '@adonisjs/lucid/types/relations'
import App from '#app/modules/apps/app_model'
import {
  subscriptionAddonsIdsValidator,
  subscriptionProductValidator,
  subscriptionValidator,
} from '#app/modules/subscriptions/subscription_validator'
import BaseFilter from '#app/modules/base/base_filter'

export default class Subscription extends compose(BaseModel, Filterable) {
  serializeExtras = false
  static selfAssignPrimaryKey = true
  static namingStrategy = new SnakeCaseNamingStrategy()
  static $filter = () => BaseFilter

  @beforeCreate()
  static createUUID(model: Subscription) {
    model.uid = uuid()
  }

  @belongsTo(() => App, {
    localKey: 'uid',
    foreignKey: 'app_uid',
  })
  declare app: BelongsTo<typeof App>

  @column({
    isPrimary: true,
    meta: { searchable: true, type: 'string', validations: [{ name: 'uuid' }] },
  })
  declare uid: string

  @column({
    meta: {
      searchable: true,
      type: 'string',
      validations: [{ name: 'uuid' }],
    },
  })
  declare app_uid: string

  @column({ meta: { searchable: true, type: 'string' } })
  declare stripe_customer_id: string

  @column({ serializeAs: null, meta: { searchable: true, type: 'object' } })
  declare plan: Infer<typeof subscriptionValidator> | null

  @column({ serializeAs: null, meta: { searchable: true, type: 'object' } })
  declare copyrights: Infer<typeof subscriptionValidator> | null

  @column({ serializeAs: null, meta: { searchable: true, type: 'object' } })
  declare domains: Infer<typeof subscriptionValidator> | null

  @column({ serializeAs: null, meta: { searchable: true, type: 'object' } })
  declare credits: Infer<typeof subscriptionValidator> | null

  @column({ serializeAs: null, meta: { searchable: true, type: 'object' } })
  declare agents: Infer<typeof subscriptionValidator> | null

  @column({ serializeAs: null, meta: { searchable: true, type: 'object' } })
  declare seats: Infer<typeof subscriptionValidator> | null

  @column({ serializeAs: null, meta: { searchable: true, type: 'object' } })
  declare urls: Infer<typeof subscriptionValidator> | null

  @column({ serializeAs: null, meta: { searchable: true, type: 'object' } })
  declare documents: Infer<typeof subscriptionValidator> | null

  @column({ serializeAs: null, meta: { searchable: true, type: 'object' } })
  declare smtps: Infer<typeof subscriptionValidator> | null

  @column({ serializeAs: null, meta: { searchable: true, type: 'object' } })
  declare metadata: { [key: string]: any } | null

  getPlan(): {
    product: Infer<typeof subscriptionProductValidator>
    subscription: Infer<typeof subscriptionValidator>
  } | null {
    if (!this.plan || !this.plan?.stripe_price_id) {
      return null
    }

    const product = SubscriptionHelper.getProductByStripePriceId({
      stripePriceId: this.plan.stripe_price_id,
    })

    if (!product) {
      return null
    }

    return {
      product,
      subscription: this.plan,
    }
  }

  getAddon(params: { addonId: Infer<typeof subscriptionAddonsIdsValidator> }): {
    product: Infer<typeof subscriptionProductValidator>
    subscription: Infer<typeof subscriptionValidator>
  } | null {
    const { addonId } = params
    const subscription: Subscription = this

    if (!subscription[addonId] || !subscription[addonId]?.stripe_price_id) {
      return null
    }

    const product = SubscriptionHelper.getProductByStripePriceId({
      stripePriceId: subscription[addonId].stripe_price_id,
    })

    if (!product) {
      return null
    }

    return {
      product,
      subscription: subscription[addonId],
    }
  }
}
