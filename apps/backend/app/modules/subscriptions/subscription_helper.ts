import { StripeHelper } from '#app/helpers'
import { pricingTableTemplate, products } from '#config/products'
import { Infer } from '@vinejs/vine/types'
import string from '@adonisjs/core/helpers/string'
import App from '#app/modules/apps/app_model'
import {
  subscriptionProductValidator,
  subscriptionValidator,
} from '#app/modules/subscriptions/subscription_validator'

export default class SubscriptionHelper {
  static async computeAppSubscriptions(params: { app: App; updateExisting: boolean }) {
    const { app, updateExisting } = params

    if (!app || (app.subscriptions && !updateExisting)) {
      return true
    }

    const stripeExistingSubscriptions = await StripeHelper.getAllStripeSubscriptions({
      stripeCustomerId: app.stripe_customer_id,
    })

    let hasValidSubscription = false

    for (const stripe_existing_subscription of stripeExistingSubscriptions) {
      const stripeExistingSubscriptionItem = stripe_existing_subscription.items.data[0]
      const appExistingProduct = SubscriptionHelper.getProductByStripePriceId({
        stripePriceId: stripeExistingSubscriptionItem.price.id,
      })

      if (!appExistingProduct) {
        continue
      }

      hasValidSubscription = true

      const appProductId = appExistingProduct.id
      const appProductType = appExistingProduct.type
      const appProductQuantity = stripeExistingSubscriptionItem.quantity || 0
      const appProductLevel = appExistingProduct.level
      const stripeProductId = stripeExistingSubscriptionItem.price.product as string
      const stripePriceId = stripeExistingSubscriptionItem.price.id
      const stripeSubscriptionId = stripeExistingSubscriptionItem.subscription
      const stripeItemId = stripeExistingSubscriptionItem.id
      const stripeTrialEnd = stripe_existing_subscription.trial_end
      const stripeInterval = stripeExistingSubscriptionItem.price.recurring?.interval || 'month'
      const stripeStatus = stripe_existing_subscription.status
      const stripeCurrentPeriodStart = stripe_existing_subscription.current_period_start
      const stripeCurrentPeriodEnd = stripe_existing_subscription.current_period_end
      const appSubscriptionKey = appExistingProduct.type === 'plan' ? 'plan' : appExistingProduct.id

      const computedSubscription: Infer<typeof subscriptionValidator> = {
        app_product_id: appProductId,
        app_product_type: appProductType,
        app_product_quantity: appProductQuantity,
        app_product_level: appProductLevel,
        stripe_product_id: stripeProductId,
        stripe_price_id: stripePriceId,
        stripe_subscription_id: stripeSubscriptionId,
        stripe_item_id: stripeItemId,
        stripe_trial_end: stripeTrialEnd,
        stripe_interval: stripeInterval,
        stripe_current_period_start: stripeCurrentPeriodStart,
        stripe_current_period_end: stripeCurrentPeriodEnd,
        stripe_status: stripeStatus,
      }

      await app.related('subscriptions').updateOrCreate(
        { app_uid: app.uid },
        {
          stripe_customer_id: app.stripe_customer_id,
          [appSubscriptionKey]: computedSubscription,
        }
      )
    }

    return hasValidSubscription
  }

  static getProductById(params: { id: string }): Infer<typeof subscriptionProductValidator> | null {
    const { id } = params

    const product = products.find((product) => product.id === id)

    return product || null
  }

  static getProductByStripePriceId(params: {
    stripePriceId: string
  }): Infer<typeof subscriptionProductValidator> | null {
    const { stripePriceId } = params

    if (!stripePriceId) {
      return null
    }

    const product = products.find(
      (product) =>
        product.monthly.stripe_prices.includes(stripePriceId) ||
        product.yearly.stripe_prices.includes(stripePriceId)
    )

    return product || null
  }

  static getProductPricingTable(params: { product: Infer<typeof subscriptionProductValidator> }) {
    const { product } = params
    const { level, id, type, limitations, is_visible, pricing_table } = product

    if (!is_visible || id === 'admin') {
      return null
    }

    if (pricing_table && pricing_table.length > 0) {
      return product
    }

    if (id === 'starter') {
      product.is_user_subscribed = true
    }

    if (id === 'copyright') {
      product.is_user_subscribed = true
    }

    if (type === 'plan') {
      for (const [index, category] of pricingTableTemplate.entries()) {
        const { title, features } = category

        product.pricing_table.push({
          title,
          features: [],
        })

        for (const feature of features) {
          let { label, icon, access_level } = feature

          product.pricing_table[index].features.push({
            label: string.interpolate(label, limitations),
            icon: icon ? icon : level >= access_level ? 'ph:check-circle-fill' : 'ph:x-circle-fill',
            access_level,
          })
        }
      }
    }

    return product
  }

  static getFullPricingTable() {
    return products
      .map((product) => SubscriptionHelper.getProductPricingTable({ product }))
      .filter((product) => product !== null)
  }
}
