import { Date<PERSON>elper, SubscriptionHelper } from '#app/helpers'
import env from '#start/env'
import { Infer } from '@vinejs/vine/types'
import Stripe from 'stripe'
import { inject } from '@adonisjs/core'
import { HttpContext } from '@adonisjs/core/http'
import adonisApp from '@adonisjs/core/services/app'
import emitter from '@adonisjs/core/services/emitter'
import {
  subscriptionAddonsIdsValidator,
  subscriptionProductIdsValidator,
  subscriptionProductValidator,
  subscriptionsCancellationFeedbackValidator,
  subscriptionValidator,
} from '#app/modules/subscriptions/subscription_validator'

@inject()
export default class SubscriptionService {
  private ctx: HttpContext

  constructor(ctx: HttpContext) {
    this.ctx = ctx
  }

  async getStripe(): Promise<Stripe> {
    return await adonisApp.container.make('stripe')
  }

  async getEmbeddedCheckoutSession(params: { stripe_price_id: string; quantity: number }) {
    const { stripe_price_id, quantity } = params
    const { app, user } = this.ctx
    const receivedProduct = SubscriptionHelper.getProductByStripePriceId({
      stripePriceId: stripe_price_id,
    })

    console.log('getEmbeddedCheckoutSession - receivedProduct', receivedProduct)
    console.log('getEmbeddedCheckoutSession - stripe_price_id', stripe_price_id)
    console.log('getEmbeddedCheckoutSession - quantity', quantity)

    if (!receivedProduct) {
      throw { message: 'Product not found' }
    }

    const { product } = app?.subscriptions?.getPlan() || {}

    if (!product) {
      throw { message: 'No current plan found' }
    }

    const stripe = await this.getStripe()

    const sessionConfig: Stripe.Checkout.SessionCreateParams = {
      allow_promotion_codes: true,
      // ui_mode: 'embedded',
      ui_mode: 'hosted',
      mode: 'subscription',
      billing_address_collection: 'required',
      // redirect_on_completion: 'never',
      // redirect_on_completion: 'always',
      success_url: `${env.get('CLIENT_URL')}/subscriptions/success`,
      cancel_url: `${env.get('CLIENT_URL')}/subscriptions/cancel`,
      customer: app?.stripe_customer_id,
      line_items: [
        {
          price: stripe_price_id,
          quantity: quantity || 1,
        },
      ],
      metadata: {
        user_uid: user?.uid || null,
        app_uid: app?.uid || null,
        gclid: app?.gclid || null,
      },
      customer_update: {
        address: 'auto',
      },
      automatic_tax: {
        enabled: true,
      },
      consent_collection: {
        terms_of_service: 'required',
        payment_method_reuse_agreement: {
          position: 'auto',
        },
      },
      saved_payment_method_options: {
        allow_redisplay_filters: ['always', 'limited', 'unspecified'],
      },
    }

    if (app?.is_first_time_customer && receivedProduct.type === 'plan') {
      sessionConfig.subscription_data = {
        trial_period_days: 7,
      }
    }

    // If the user has no plan, we start the checkout session right away
    if (receivedProduct.type === 'plan' && !app?.subscriptions?.plan) {
      return await stripe.checkout.sessions.create(sessionConfig)
    }

    // If the user has a plan, we check if the received plan is higher level (upgrade not downgrade)
    else if (
      receivedProduct.type === 'plan' &&
      product?.level &&
      receivedProduct.level > product.level
    ) {
      return await stripe.checkout.sessions.create(sessionConfig)
    } else {
      const canDowngrade = await this.canDowngrade({ received_product: receivedProduct })

      if (canDowngrade) {
        return await stripe.checkout.sessions.create(sessionConfig)
      } else {
        throw {
          message: 'Cannot downgrade - Your current usage exceeds new limits!',
        }
      }
    }
  }

  async getPortalSessionUrl(params: { stripe_customer_id: string }) {
    const { stripe_customer_id } = params
    const stripe = await this.getStripe()

    const portalSession = await stripe.billingPortal.sessions.create({
      customer: stripe_customer_id,
      return_url: env.get('CLIENT_URL'),
    })

    return portalSession.url
  }

  async getAllPaidInvoices() {
    const stripe = await this.getStripe()
    const invoices = await stripe.invoices.list({
      customer: this.ctx.app?.stripe_customer_id,
      status: 'paid',
      limit: 100,
    })

    return invoices
  }

  async cancel(params: {
    product_id:
      | Infer<typeof subscriptionProductIdsValidator>
      | Infer<typeof subscriptionAddonsIdsValidator>
    product_type: 'plan' | 'addon'
    comment: string
    feedback: Infer<typeof subscriptionsCancellationFeedbackValidator>
  }) {
    const { app, user } = this.ctx
    const { product_id, product_type, comment, feedback } = params

    let subscriptionTocancel = null
    let receivedProduct = null

    if (product_type === 'plan') {
      const { subscription, product } = app?.subscriptions?.getPlan() || {}

      subscriptionTocancel = subscription
      receivedProduct = product
    } else if (product_type === 'addon') {
      const { subscription, product } =
        app?.subscriptions?.getAddon({
          addonId: product_id as Infer<typeof subscriptionAddonsIdsValidator>,
        }) || {}

      subscriptionTocancel = subscription
      receivedProduct = product
    }

    if (!subscriptionTocancel || !receivedProduct) {
      throw {
        message: 'No matching subscriptions found.',
      }
    }

    // Check if the subscription is on a free trial and allow cancellation only on the last day
    if (subscriptionTocancel.app_product_type === 'plan') {
      if (subscriptionTocancel.stripe_status === 'trialing') {
        const stripeTrialEnd = subscriptionTocancel.stripe_trial_end || 0
        const now = Math.floor(Date.now() / 1000)
        const timeLeft = stripeTrialEnd - now

        if (timeLeft > 24 * 60 * 60) {
          // More than 1 day left in trial
          throw {
            code: 'subscription/cancel-impossible',
            message:
              'Cancellation is only allowed on the last day of your trial - This helps prevent misuse of the trial period through repeated subscriptions and ensures you have the opportunity to fully explore the product’s features.',
          }
        }
      }
    }

    if (subscriptionTocancel.app_product_type === 'addon') {
      const canDowngrade = await this.canDowngrade({ received_product: receivedProduct })

      if (!canDowngrade) {
        throw {
          message:
            'Cannot Cancel – Your current usage exceeds the limits. Please resolve any overusage before canceling the addon.',
        }
      }
    }

    const stripe = await this.getStripe()

    stripe.subscriptions.cancel(subscriptionTocancel.stripe_subscription_id)

    await stripe.subscriptions.cancel(subscriptionTocancel.stripe_subscription_id, {
      cancellation_details: { comment, feedback },
    })

    await app?.merge({ is_first_time_churning: true }).save()

    emitter.emit('crisp:push_event', {
      email: user?.email,
      text: 'send_churn_feedback_email',
      data: { timestamp: DateHelper.getNow().toMillis(), comment, feedback },
      color: 'red',
    })

    emitter.emit('google_ads:create', {
      appUid: app?.uid,
      conversionName: 'churned',
      conversionValue: 0,
      conversionCurrency: 'USD',
      metadata: { comment, feedback },
    })

    return true
  }

  async canDowngrade(params: { received_product: Infer<typeof subscriptionProductValidator> }) {
    const { received_product } = params
    const { app } = this.ctx
    const {
      agents: agents_subscription,
      urls: urls_subscription,
      documents: documents_subscription,
      seats: seats_subscription,
      domains: domains_subscription,
    } = app?.subscriptions || {}

    const consumedAgents = await app.getAgentsCount()
    const consumedUrls = await app.getSourcesCount('urls')
    const consumedDocuments = await app.getSourcesCount('documents')
    const consumedSeats = await app.getSeatsCount()
    const consumedDomains = await app.getDomainsCount()
    // Add SMTP check

    // If the update is a plan, we need to check everything
    if (received_product.type === 'plan') {
      const { limitations } = received_product || {}
      const { knowledge } = limitations || {}

      const newAgentsCount =
        (limitations?.agents || 0) + (agents_subscription?.app_product_quantity || 0)
      const newUrlsCount = (knowledge?.urls || 0) + (urls_subscription?.app_product_quantity || 0)
      const newDocumentsCount =
        (knowledge?.documents || 0) + (documents_subscription?.app_product_quantity || 0)
      const newDomainsCount =
        (limitations?.domains || 0) + (domains_subscription?.app_product_quantity || 0)
      const newSeatsCount =
        (limitations?.seats || 0) + (seats_subscription?.app_product_quantity || 0)

      if (consumedAgents > newAgentsCount) {
        return false
      }

      if (consumedUrls > newUrlsCount) {
        return false
      }

      if (consumedDocuments > newDocumentsCount) {
        return false
      }

      if (consumedDomains > newDomainsCount) {
        return false
      }

      if (consumedSeats > newSeatsCount) {
        return false
      }
    }

    // If the update is an addon, we need to check the specific addon
    if (received_product.type === 'addon') {
      const { product } = app?.subscriptions?.getPlan() || {}

      if (!product) {
        throw { message: 'No current plan found' }
      }

      const { limitations } = product || {}
      const { knowledge } = limitations || {}

      if (received_product.id === 'agents') {
        const newAgentsCount =
          (limitations?.agents || 0) + (agents_subscription?.app_product_quantity || 0)

        if (consumedAgents > newAgentsCount) {
          return false
        }
      }

      if (received_product.id === 'urls') {
        const newUrlsCount = (knowledge?.urls || 0) + (urls_subscription?.app_product_quantity || 0)

        if (consumedUrls > newUrlsCount) {
          return false
        }
      }

      if (received_product.id === 'documents') {
        const newDocumentsCount =
          (knowledge?.documents || 0) + (documents_subscription?.app_product_quantity || 0)

        if (consumedDocuments > newDocumentsCount) {
          return false
        }
      }

      if (received_product.id === 'domains') {
        const newDomainsCount =
          (limitations?.domains || 0) + (domains_subscription?.app_product_quantity || 0)

        if (consumedDomains > newDomainsCount) {
          return false
        }
      }

      if (received_product.id === 'seats') {
        const newSeatsCount =
          (limitations?.seats || 0) + (seats_subscription?.app_product_quantity || 0)

        if (consumedSeats > newSeatsCount) {
          return false
        }
      }
    }

    return true
  }
}
