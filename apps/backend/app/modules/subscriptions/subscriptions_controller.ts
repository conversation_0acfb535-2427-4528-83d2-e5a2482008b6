import { SubscriptionHelper } from '#app/helpers'
import { embeddedCheckoutSessionValidator } from '#app/validators'
import { inject } from '@adonisjs/core'
import { HttpContext } from '@adonisjs/core/http'
import SubscriptionService from '#app/modules/subscriptions/subscription_service'

@inject()
export default class SubscriptionsController {
  constructor(private subscriptionService: SubscriptionService) {}

  async getEmbeddedCheckoutSession({ request, response }: HttpContext) {
    const { stripe_price_id, quantity } = await request.validateUsing(
      embeddedCheckoutSessionValidator
    )

    const session = await this.subscriptionService.getEmbeddedCheckoutSession({
      stripe_price_id,
      quantity,
    })

    return response.status(200).json(session)
  }

  async getPortalSessionUrl({ request, response }: HttpContext) {
    const stripe_customer_id = request.param('stripe_customer_id')

    const url = await this.subscriptionService.getPortalSessionUrl({
      stripe_customer_id,
    })

    return response.status(200).json({ url })
  }

  async getFullPricingTable({ response }: HttpContext) {
    return response.status(200).json(SubscriptionHelper.getFullPricingTable())
  }

  async getAllPaidInvoices({ request, response }: HttpContext) {
    const invoices = await this.subscriptionService.getAllPaidInvoices()

    return response.status(200).json({ data: invoices?.data || [], meta: {} })
  }

  async cancel({ request, response }: HttpContext) {
    const { product_id, product_type, comment, feedback } = request.params()

    const status = await this.subscriptionService.cancel({
      product_id,
      product_type,
      comment,
      feedback,
    })

    return response.status(200).json({ success: status })
  }
}
