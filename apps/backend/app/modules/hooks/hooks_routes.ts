import router from '@adonisjs/core/services/router'

export default () => {
  const ENDPOINT_PREFIX = '/hooks'

  router
    .group(() => {
      router.post(
        '/notify-p6LA4DmUcZHmvW8vWqVhjqf8',
        `#app/modules/hooks/notify_hooks_controller.handle`
      )
      router.post(
        '/whatsapp-O9or0bwyffW5Q8hQEaUYqvXp',
        `#app/modules/hooks/whatsapp_hooks_controller.handle`
      )
      router.post(
        '/stripe-LfGP1fBY127T4fwyvE3YKpKD',
        `#app/modules/hooks/stripe_hooks_controller.handle`
      )
    })
    .prefix(`${ENDPOINT_PREFIX}`)
}
