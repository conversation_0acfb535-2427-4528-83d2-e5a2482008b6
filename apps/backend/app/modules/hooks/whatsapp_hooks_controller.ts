import { FileHelper } from '#app/helpers'
import fs from 'node:fs'
import { inject } from '@adonisjs/core'
import { HttpContext } from '@adonisjs/core/http'
import drive from '@adonisjs/drive/services/main'
import Agent from '#app/modules/agents/agent_model'
import Chat from '#app/modules/chats/chat_model'
import File from '#app/modules/files/file_model'
import EmbedService from '#app/modules/embeds/embed_service'
import WhatsappService from '#app/modules/whatsapp/whatsapp_service'
import EmbedsController from '#app/modules/embeds/embeds_controller'

@inject()
export default class WhatsappHooksController {
  constructor(
    private whatsappService: WhatsappService,
    private embedService: EmbedService
  ) {}

  async handle({ request, response, auth }: HttpContext) {
    try {
      const debug = false
      const body = request.body()

      debug && console.log('whatsapp', body)

      const { event, me, payload, metadata } = body || {}
      const { agent_uid, app_uid } = metadata || {}
      const {
        from: whatsappChatId,
        id: whatsappMessageId,
        hasMedia,
        mediaUrl,
        fromMe,
      } = payload || {}
      const senderName = payload?._data?.pushName || whatsappChatId
      const mediaType = payload?.media?.mimetype?.split('/')[0]
      const isGroup = whatsappChatId?.includes('@g.us')
      const helperMessage = isGroup
        ? `*Commands*\n╰┈➤ /help - Show commands\n╰┈➤ /ai - Ask AI (in groups)`
        : `*Commands*\n╰┈➤ /help - Show commands\n╰┈➤ /reset - Reset the AI\n╰┈➤ /pause - Pause the AI\n╰┈➤ /resume - Resume the AI\n╰┈➤ /ai - Ask AI (in groups)`

      let userInput = payload?.body?.trim()
      let files: File[] = []

      if (!['message.any'].includes(event)) {
        return response.status(200).json({ success: false })
      }

      if (!whatsappChatId && (!userInput || !hasMedia)) {
        return response.status(200).json({ success: false })
      }

      if (fromMe) {
        if (userInput.includes('*Commands*')) {
          return response.status(200).json({ success: false })
        }

        if (!this.whatsappService.isCommand(userInput)) {
          return response.status(200).json({ success: false })
        }
      }

      /*
      // Testing number
      if (!['637527169', '120363383899923331'].some((id) => whatsappChatId.includes(id))) {
        return response.status(200).json({ success: false })
      }
      */

      if (whatsappChatId.includes('@newsletter')) {
        return response.status(200).json({ success: false })
      }

      if (isGroup && !this.whatsappService.isCommand(userInput)) {
        return response.status(200).json({ success: false })
      }

      let [agent, chat] = await Promise.all([
        Agent.query().where('uid', agent_uid).andWhere('app_uid', app_uid).firstOrFail(),
        Chat.query()
          .where('external_id', whatsappChatId)
          .andWhere('app_uid', app_uid)
          .andWhere('agent_uid', agent_uid)
          .firstOrFail(),
      ])

      switch (true) {
        case userInput.includes('/reset'):
          await this.whatsappService.resetChat({
            agent_uid,
            chat_id: whatsappChatId,
            message_id: whatsappMessageId,
          })
          return response.status(200).json({ success: true })

        case userInput.includes('/help'):
          await this.whatsappService.sendHelp({
            agent_uid,
            chat_id: whatsappChatId,
            message_id: whatsappMessageId,
            message: helperMessage,
          })
          return response.status(200).json({ success: true })

        case userInput.includes('/pause'):
          await this.whatsappService.toggleAI({
            agent_uid,
            chat_id: whatsappChatId,
            message_id: whatsappMessageId,
            disable: true,
          })
          return response.status(200).json({ success: true })

        case userInput.includes('/resume'):
          await this.whatsappService.toggleAI({
            agent_uid,
            chat_id: whatsappChatId,
            message_id: whatsappMessageId,
            disable: false,
          })
          return response.status(200).json({ success: true })

        case userInput.includes('/ai'):
          await this.whatsappService.sendSeen({
            agent_uid,
            chat_id: whatsappChatId,
            message_id: whatsappMessageId,
          })

          await this.whatsappService.startTyping({
            agent_uid,
            chat_id: whatsappChatId,
          })
          break

        default:
          if (chat.is_ai_disabled) {
            return response.status(200).json({ success: true })
          } else {
            await this.whatsappService.sendSeen({
              agent_uid,
              chat_id: whatsappChatId,
              message_id: whatsappMessageId,
            })

            await this.whatsappService.startTyping({
              agent_uid,
              chat_id: whatsappChatId,
            })
          }
      }

      if (!chat?.external_id) {
        chat = await Chat.create({
          app_uid: agent?.app_uid,
          agent_uid: agent?.uid,
          external_id: whatsappChatId,
          is_whatsapp: true,
          label: senderName,
          metadata: {},
        })

        await this.whatsappService.stopTyping({
          agent_uid,
          chat_id: whatsappChatId,
        })

        await this.whatsappService.sendHelp({
          agent_uid,
          chat_id: whatsappChatId,
          message_id: whatsappMessageId,
          message: helperMessage,
        })

        await this.whatsappService.startTyping({
          agent_uid,
          chat_id: whatsappChatId,
        })
      }

      if (hasMedia) {
        if (mediaType === 'audio') {
          const filePath = await FileHelper.download(mediaUrl, 'audio.opus')

          if (!filePath) {
            await this.whatsappService.stopTyping({
              agent_uid,
              chat_id: whatsappChatId,
            })

            await this.whatsappService.sendText({
              agent_uid,
              chat_id: whatsappChatId,
              reply_to: whatsappMessageId,
              text: "🧠 Sorry, I can't process this audio.",
            })

            return response.status(200).json({ success: true })
          }

          userInput = await this.embedService.speechToText({
            chat_uid: chat.uid,
            file_path: filePath,
          })
        } else if (['image'].includes(mediaType)) {
          const filePath = await FileHelper.download(mediaUrl, 'file')

          if (!filePath) {
            await this.whatsappService.stopTyping({
              agent_uid,
              chat_id: whatsappChatId,
            })

            await this.whatsappService.sendText({
              agent_uid,
              chat_id: whatsappChatId,
              reply_to: whatsappMessageId,
              text: "🧠 Sorry, I can't process this image.",
            })

            return response.status(200).json({ success: true })
          }

          const fileToAdonisFormat = await FileHelper.convertFileToAdonisFormat(filePath)

          files = await this.embedService.createFiles({
            chat_uid: chat.uid,
            files: [fileToAdonisFormat],
          })
        } else {
          await this.whatsappService.stopTyping({
            agent_uid,
            chat_id: whatsappChatId,
          })

          await this.whatsappService.sendText({
            agent_uid,
            chat_id: whatsappChatId,
            reply_to: whatsappMessageId,
            text: "🧠 Sorry, I can't process this file(s) yet.",
          })

          return response.status(200).json({ success: true })
        }
      }

      request.updateBody({
        app_uid: agent.app_uid,
        agent_uid: agent.uid,
        chat_uid: chat.uid,
        files_uids: files?.map((file) => file?.uid),
        input: userInput,
        role: 'user',
        disable_stream: true,
        metadata: {},
      })

      // @ts-ignore
      const createMessageResponse = await new EmbedsController().createMessage({
        request,
        response,
        auth,
      })

      await this.whatsappService.stopTyping({ agent_uid, chat_id: whatsappChatId })

      await this.whatsappService.sendResponse({
        agent_uid,
        chat_id: whatsappChatId,
        message_id: whatsappMessageId,
        responseData: createMessageResponse,
      })

      return response.status(200).json({ success: true })
    } catch (error) {
      throw error
    }
  }
}
