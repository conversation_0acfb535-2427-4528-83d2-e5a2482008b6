import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, SubscriptionHelper } from '#app/helpers'
import env from '#start/env'
import Stripe from 'stripe'
import { HttpContext } from '@adonisjs/core/http'
import emitter from '@adonisjs/core/services/emitter'
import App from '#app/modules/apps/app_model'

export default class StripeHooksController {
  async handle({ request, response }: HttpContext) {
    try {
      const signature = request.header('stripe-signature')
      const body: string | Buffer = request.raw() || ''

      if (!body || !signature) {
        throw {
          message: 'Missing parameters',
          status: 400,
          code: 'missing-parameters',
        }
      }

      const stripe = await StripeHelper.getStripe()
      let event: Stripe.Event

      try {
        const secret =
          env.get('DB_DEFAULT_CONNECTION') === 'production'
            ? env.get('STRIPE_PRODUCTION_WEBHOOK_SECRET')
            : env.get('STRIPE_LOCAL_WEBHOOK_SECRET')

        event = stripe.webhooks.constructEvent(body, signature, secret)
      } catch (error) {
        throw error
      }

      const stripeEventType = event.type

      if (
        [
          'customer.subscription.created',
          'customer.subscription.deleted',
          'customer.subscription.paused',
          'customer.subscription.resumed',
          'customer.subscription.updated',
          'customer.subscription.trial_will_end',
        ].includes(stripeEventType)
      ) {
        const stripeReceivedPayload: Stripe.Subscription = event.data.object as Stripe.Subscription
        const stripeReceivedSubscriptionId = stripeReceivedPayload.id as string
        const stripeReceivedSubscription = await stripe.subscriptions.retrieve(
          stripeReceivedSubscriptionId
        )
        const stripeReceivedSubscriptionItem = stripeReceivedSubscription.items.data[0]
        const stripeReceivedCustomerId = stripeReceivedSubscription.customer as string
        const app = await App.findByOrFail('stripe_customer_id', stripeReceivedCustomerId)
        const appReceivedProduct = SubscriptionHelper.getProductByStripePriceId({
          stripePriceId: stripeReceivedSubscriptionItem.price.id,
        })

        if (!appReceivedProduct) {
          return response.json({ success: true })
        }

        if (stripeEventType === 'customer.subscription.created') {
          // Send event to Google
          const conversionValue = (stripeReceivedSubscriptionItem.price.unit_amount || 0) / 100

          emitter.emit('google_ads:create', {
            appUid: app?.uid,
            conversionName: app?.is_first_time_customer
              ? 'trial_started_7_days'
              : 'payment_finalized',
            conversionValue,
            conversionCurrency: 'USD',
          })
        }

        // Remove any old plans if the user just bought a new plan
        if (stripeEventType === 'customer.subscription.created') {
          const stripeExistingSubscriptions = await StripeHelper.getAllStripeSubscriptions({
            stripeCustomerId: stripeReceivedCustomerId,
          })

          for (const stripeExistingSubscription of stripeExistingSubscriptions) {
            // Ignore current purchased subscription
            if (stripeExistingSubscription.id === stripeReceivedSubscriptionId) {
              continue
            }

            const stripeExistingSubscriptionItem = stripeExistingSubscription.items.data[0]
            const appExistingProduct = SubscriptionHelper.getProductByStripePriceId({
              stripePriceId: stripeExistingSubscriptionItem.price.id,
            })

            if (!appExistingProduct) {
              continue
            }

            // Remove old plan if the user purchased a new plan
            if (appReceivedProduct.type === 'plan' && appExistingProduct?.type === 'plan') {
              await stripe.subscriptions.cancel(stripeExistingSubscription.id)
            }

            // Remove duplicate subscriptions
            if (appReceivedProduct.id === appExistingProduct.id) {
              await stripe.subscriptions.cancel(stripeExistingSubscription.id)
            }
          }
        }

        // Sync subscriptions
        await SubscriptionHelper.computeAppSubscriptions({ app, updateExisting: true })
      }

      if (['invoice.paid'].includes(stripeEventType)) {
        const stripeReceivedPayload: Stripe.Invoice = event.data.object as Stripe.Invoice
        const stripeReceivedSubscriptionId = stripeReceivedPayload.subscription as string
        const stripeReceivedSubscription = await stripe.subscriptions.retrieve(
          stripeReceivedSubscriptionId
        )
        const stripeReceivedSubscriptionItem = stripeReceivedSubscription.items.data[0]
        const stripeReceivedCustomerId = stripeReceivedSubscription.customer as string
        const app = await App.findByOrFail('stripe_customer_id', stripeReceivedCustomerId)
        const appReceivedProduct = SubscriptionHelper.getProductByStripePriceId({
          stripePriceId: stripeReceivedSubscriptionItem.price.id,
        })

        if (appReceivedProduct?.id === 'credits') {
          await app
            .merge({
              available_credits:
                app.available_credits + (stripeReceivedSubscriptionItem.quantity || 0),
            })
            .save()
        }
      }

      if (
        [
          'invoice.voided',
          'invoice.overdue',
          'invoice.payment_failed',
          'invoice.marked_uncollectible',
        ].includes(stripeEventType)
      ) {
        const stripeReceivedPayload: Stripe.Invoice = event.data.object as Stripe.Invoice
        const stripeReceivedSubscriptionId = stripeReceivedPayload.subscription as string
        const stripeReceivedSubscription = await stripe.subscriptions.retrieve(
          stripeReceivedSubscriptionId
        )
        const stripeReceivedSubscriptionItem = stripeReceivedSubscription.items.data[0]
        const stripeReceivedCustomerId = stripeReceivedSubscription.customer as string
        const app = await App.findByOrFail('stripe_customer_id', stripeReceivedCustomerId)
        const appReceivedProduct = SubscriptionHelper.getProductByStripePriceId({
          stripePriceId: stripeReceivedSubscriptionItem.price.id,
        })

        if (app && appReceivedProduct?.type === 'plan') {
          await app.merge({ available_credits: 0 }).save()
          await app.subscriptions?.merge({ plan: null }).save()

          // TODO: Remove domains from Cloudflare
        }
      }

      return response.json({ success: true })
    } catch (error) {
      throw error
    }
  }
}
