import { <PERSON>ailHelper } from '#app/helpers'
import env from '#start/env'
import { HttpContext } from '@adonisjs/core/http'

export default class NotifyHooksController {
  async handle({ request, response }: HttpContext) {
    const body = request.body() || {}
    const status = request.body()?.status || false

    try {
      await EmailHelper.sendSimpleEmail({
        to: [{ email: env.get('APP_EMAIL') }],
        cc: [],
        bcc: [],
        subject: `${status ? '✅' : '❌'} - Webhook Notification Received`,
        content: `<h2>Webhook Notification</h2><pre style="background: #f4f4f4; padding: 15px; border-radius: 5px;">${JSON.stringify(body, null, 2)}</pre>`,
      })

      return response
        .status(200)
        .json({ success: true, message: 'Notification sent successfully.' })
    } catch (error) {
      console.error('Error sending webhook notification email:', error)

      return response.status(500).json({
        success: false,
        message: 'Failed to send notification email.',
        error: error.message || error,
      })
    }
  }
}
