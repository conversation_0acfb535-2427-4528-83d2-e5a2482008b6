import vine from '@vinejs/vine'

const openTestSmtpSchema = vine.object({
  recipient: vine.string().email().normalizeEmail({ all_lowercase: true }).trim(),
  host: vine.string().trim(),
  port: vine.number().min(1).max(65535),
  user: vine.string().trim(),
  pass: vine.string().trim(),
  name: vine.string().trim(),
  email: vine.string().email().normalizeEmail({ all_lowercase: true }).trim(),
})

const openTestSmtpValidator = vine.compile(openTestSmtpSchema)

export { openTestSmtpValidator }
