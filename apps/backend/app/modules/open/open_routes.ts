import router from '@adonisjs/core/services/router'

export default () => {
  const ENDPOINT_PREFIX = '/open'
  const CONTROLLER_PATH = '#app/modules/open/open_controller'

  router
    .group(() => {
      router.get('/products', `${CONTROLLER_PATH}.products`)
      router.get('/pricing-table', `${CONTROLLER_PATH}.getFullPricingTable`)
      router.get('/writing', `${CONTROLLER_PATH}.getWriting`)
      router.get('/girlfriends', `${CONTROLLER_PATH}.getGirlfriends`)
      router.post('/test-smtp', `${CONTROLLER_PATH}.testSMTP`)
    })
    .prefix(`${ENDPOINT_PREFIX}`)
}
