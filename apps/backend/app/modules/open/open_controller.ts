import { BUSINESS_TOOLS, GIRLFRIENDS_TOOLS } from '#app/constants'
import { SubscriptionHelper } from '#app/helpers'
import { products } from '#config/products'
import { inject } from '@adonisjs/core'
import { HttpContext } from '@adonisjs/core/http'
import OpenService from '#app/modules/open/open_service'
import { openTestSmtpValidator } from '#app/modules/open/open_validation'

@inject()
export default class OpenController {
  constructor(private openService: OpenService) {}

  async getWriting({ response }: HttpContext) {
    return response.status(200).json(BUSINESS_TOOLS)
  }

  async getGirlfriends({ response }: HttpContext) {
    return response.status(200).json(GIRLFRIENDS_TOOLS)
  }

  async testSMTP({ request, response }: HttpContext) {
    try {
      const { recipient, host, port, user, pass, name, email } =
        await request.validateUsing(openTestSmtpValidator)

      const isValid = await this.openService.testSmtp({
        recipient,
        smtp: {
          host,
          port,
          auth: {
            user,
            pass,
          },
        },
        sender: {
          name,
          email,
        },
      })

      if (isValid) {
        return response.status(200).json({
          status: 200,
          message: 'SMTP connection successful.',
        })
      } else {
        throw {
          status: 400,
          code: 'smtp/connection-failed',
          message: 'Failed to establish SMTP connection. Please check your SMTP settings.',
        }
      }
    } catch (error) {
      if (error.message?.includes('Connection timeout')) {
        throw {
          status: 400,
          code: 'smtp/timeout',
          message: 'Timeout, verify your credentials.',
        }
      }

      if (error.message?.includes('Invalid login')) {
        throw {
          status: 400,
          code: 'smtp/timeout',
          message: 'Invalid login, verify your credentials.',
        }
      }

      throw {
        status: 500,
        code: 'server/error',
        message: error.message || 'An unexpected error occurred while testing SMTP connection.',
      }
    }
  }

  async products({ response }: HttpContext) {
    return response.status(200).json(products?.filter((product) => product?.is_visible))
  }

  async getFullPricingTable({ response }: HttpContext) {
    return response.status(200).json(SubscriptionHelper.getFullPricingTable())
  }
}
