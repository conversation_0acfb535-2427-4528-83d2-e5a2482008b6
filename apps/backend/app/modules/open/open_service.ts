import { EmailHelper } from '#app/helpers'
import { senderValidator, smtpValidator } from '#app/validators'
import { Infer } from '@vinejs/vine/types'
import { inject } from '@adonisjs/core'
import { HttpContext } from '@adonisjs/core/http'

@inject()
export default class OpenService {
  private ctx: HttpContext

  constructor(ctx: HttpContext) {
    this.ctx = ctx
  }

  async testSmtp(payload: {
    recipient?: string
    smtp: Infer<typeof smtpValidator>
    sender: Infer<typeof senderValidator>
  }) {
    const { recipient, sender, smtp } = payload

    const isValid = await EmailHelper.testSMTP({
      recipient,
      smtp,
      sender,
    })

    return isValid
  }
}
