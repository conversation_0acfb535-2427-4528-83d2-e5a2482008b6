import type { Infer } from '@vinejs/vine/types'
import { Filterable } from 'adonis-lucid-filter'
import { DateTime } from 'luxon'
import { v4 as uuid } from 'uuid'
import { compose } from '@adonisjs/core/helpers'
import { beforeCreate, belongsTo, column, SnakeCaseNamingStrategy } from '@adonisjs/lucid/orm'
import { BaseModel } from '@adonisjs/lucid/orm'
import type { BelongsTo } from '@adonisjs/lucid/types/relations'
import Agent from '#app/modules/agents/agent_model'
import App from '#app/modules/apps/app_model'
import Provider from '#app/modules/rag/providers/provider_model'
import { providerTypesValidator } from '#app/modules/rag/providers/provider_validator'
import {
  sourceDocumentValidator,
  sourceMediaValidator,
  sourceProductValidator,
  sourceQuizzValidator,
  sourceTextValidator,
  sourceUrlValidator,
  sourceYoutubeValidator,
} from '#app/modules/rag/sources/source_validator'
import BaseFilter from '#app/modules/base/base_filter'

export default class Source extends compose(BaseModel, Filterable) {
  serializeExtras = false
  static selfAssignPrimaryKey = true
  static namingStrategy = new SnakeCaseNamingStrategy()
  static $filter = () => BaseFilter

  @beforeCreate()
  static createUUID(model: Source) {
    model.uid = uuid()
  }

  @belongsTo(() => App, {
    localKey: 'uid',
    foreignKey: 'app_uid',
  })
  declare app: BelongsTo<typeof App>

  @belongsTo(() => Agent, {
    localKey: 'uid',
    foreignKey: 'agent_uid',
  })
  declare agent: BelongsTo<typeof Agent>

  @belongsTo(() => Provider, {
    localKey: 'uid',
    foreignKey: 'provider_uid',
  })
  declare provider: BelongsTo<typeof Provider>

  @column({
    isPrimary: true,
    meta: { searchable: true, type: 'string', validations: [{ name: 'uuid' }] },
  })
  declare uid: string

  @column({
    meta: {
      searchable: true,
      type: 'string',
      validations: [{ name: 'uuid' }],
    },
  })
  declare app_uid: string

  @column({
    meta: {
      searchable: true,
      type: 'string',
      validations: [{ name: 'uuid' }],
    },
  })
  declare agent_uid: string

  @column({
    meta: {
      searchable: true,
      type: 'string',
      validations: [{ name: 'uuid' }],
    },
  })
  declare provider_uid: string

  @column({ meta: { searchable: true, type: 'string' } })
  declare type: Infer<typeof providerTypesValidator>

  @column({ meta: { searchable: true, type: 'string' } })
  declare url: Infer<typeof sourceUrlValidator> | null

  @column({ meta: { searchable: true, type: 'string' } })
  declare youtube: Infer<typeof sourceYoutubeValidator> | null

  @column({ meta: { searchable: true, type: 'string' } })
  declare text: Infer<typeof sourceTextValidator> | null

  @column({ meta: { searchable: true, type: 'object' } })
  declare document: Infer<typeof sourceDocumentValidator> | null

  @column({ meta: { searchable: true, type: 'object' } })
  declare media: Infer<typeof sourceMediaValidator> | null

  @column({ meta: { searchable: true, type: 'object' } })
  declare quizz: Infer<typeof sourceQuizzValidator> | null

  @column({ meta: { searchable: true, type: 'object' } })
  declare product: Infer<typeof sourceProductValidator> | null

  @column({ serializeAs: null, meta: { searchable: true, type: 'string' } })
  declare content_original: string | null

  @column({ serializeAs: null, meta: { searchable: true, type: 'string' } })
  declare content_enhanced: string | null

  @column({ serializeAs: null, meta: { searchable: true, type: 'string' } })
  declare content_summary: string | null

  @column({ meta: { searchable: true, type: 'string' } })
  declare status: string

  @column({ meta: { searchable: true, type: 'object' } })
  declare metadata: { [key: string]: any } | null

  @column.dateTime({ autoCreate: true, meta: { searchable: true, type: 'date' } })
  declare created_at: DateTime

  @column.dateTime({
    autoCreate: true,
    autoUpdate: true,
    meta: { searchable: true, type: 'date' },
  })
  declare updated_at: DateTime

  @column({ serializeAs: null, meta: { searchable: true, type: 'date' } })
  declare deleted_at?: DateTime | null
}
