import { ModelPaginatorContract } from '@adonisjs/lucid/types/model'
import Source from '#app/modules/rag/sources/source_model'

export default class SourcePresenter {
  static serialize(item: Source) {
    return {
      uid: item.uid,
      app_uid: item.app_uid,
      agent_uid: item.agent_uid,
      provider_uid: item.provider_uid,
      type: item.type,
      url: item.url,
      youtube: item.youtube,
      text: item.text,
      document: item.document,
      media: item.media,
      quizz: item.quizz,
      product: item.product,
      status: item.status,
      metadata: item.metadata,
      created_at: item.created_at,
      updated_at: item.updated_at,
    }
  }

  static serializeMany(items: Source[]) {
    return items.map((item) => this.serialize(item))
  }

  static serializePaginated(items: ModelPaginatorContract<Source>) {
    return {
      page: items.currentPage,
      per_page: items.perPage,
      total: items.total,
      last_page: items.lastPage,
      data: this.serializeMany(items.all()),
    }
  }
}
