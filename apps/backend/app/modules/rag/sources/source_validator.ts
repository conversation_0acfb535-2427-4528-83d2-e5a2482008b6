import vine from '@vinejs/vine'

const sourceUrlSchema = vine
  .string()
  .trim()
  .url({ require_protocol: true, protocols: ['http', 'https'] })

const sourceYoutubeSchema = vine
  .string()
  .trim()
  .url({
    require_protocol: true,
    protocols: ['http', 'https'],
    host_whitelist: ['youtube.com', 'youtu.be'],
  })

const sourceTextSchema = vine.string().trim()

const sourceQuizzSchema = vine.object({
  question: vine.string().trim(),
  answer: vine.string().trim(),
})

const sourceDocumentSchema = vine.object({
  name: vine.string().trim(),
  type: vine.string().trim(),
  subtype: vine.string().trim(),
  extname: vine.string().trim(),
  size: vine.number().min(0),
  etag: vine.string().trim().optional().nullable(),
  location: vine.string().trim(),
  lang_iso_639_1: vine.string().trim().optional().nullable(),
  lang_iso_639_2: vine.string().trim().optional().nullable(),
  metadata: vine.object({}).allowUnknownProperties().optional().nullable(),
})

const sourceMediaSchema = vine.object({
  name: vine.string().trim(),
  type: vine.string().trim(),
  subtype: vine.string().trim(),
  extname: vine.string().trim(),
  size: vine.number().min(0),
  checksum: vine.string().trim(),
  etag: vine.string().trim(),
  location: vine.string().trim(),
  lang_iso_639_1: vine.string().trim().optional().nullable(),
  lang_iso_639_2: vine.string().trim().optional().nullable(),
  metadata: vine.object({}).allowUnknownProperties().optional().nullable(),
})

const sourceProductSchema = vine.object({
  name: vine.string().trim(),
  price: vine.number().min(0),
  url: vine
    .string()
    .trim()
    .url({ require_protocol: true, protocols: ['http', 'https'] }),
  description: vine.string().trim(),
  category: vine.string().trim().optional().nullable(),
  sku: vine.string().trim().optional().nullable(),
  size: vine.string().trim().optional().nullable(),
  color: vine.string().trim().optional().nullable(),
  tags: vine.array(vine.string().trim()).optional().nullable(),
  image_urls: vine.array(vine.string().trim()),
})

const sourceFileSchema = vine.object({
  name: vine.string().trim(),
  location: vine.string().trim(),
  size: vine.number().min(0),
  checksum: vine.string().trim(),
  etag: vine.string().trim(),
  url: vine
    .string()
    .trim()
    .url({ require_protocol: true, protocols: ['http', 'https'] }),
})

const sourceQuizzValidator = vine.compile(sourceQuizzSchema)
const sourceProductValidator = vine.compile(sourceProductSchema)
const sourceFileValidator = vine.compile(sourceFileSchema)
const sourceTextValidator = vine.compile(sourceTextSchema)
const sourceMediaValidator = vine.compile(sourceMediaSchema)
const sourceDocumentValidator = vine.compile(sourceDocumentSchema)
const sourceUrlValidator = vine.compile(sourceUrlSchema)
const sourceYoutubeValidator = vine.compile(sourceYoutubeSchema)

export {
  sourceDocumentSchema,
  sourceMediaSchema,
  sourceUrlSchema,
  sourceYoutubeSchema,
  sourceProductSchema,
  sourceQuizzSchema,
  sourceTextSchema,
  sourceQuizzValidator,
  sourceProductValidator,
  sourceFileValidator,
  sourceTextValidator,
  sourceMediaValidator,
  sourceDocumentValidator,
  sourceUrlValidator,
  sourceYoutubeValidator,
}
