import { requestFilterValidator } from '#app/validators'
import { inject } from '@adonisjs/core'
import { HttpContext } from '@adonisjs/core/http'
import SourceService from '#app/modules/rag/sources/source_service'
import SourcePresenter from '#app/modules/rag/sources/source_presenter'

@inject()
export default class SourcesController {
  constructor(private SourceService: SourceService) {}

  async index({ request, response }: HttpContext) {
    const payload = await request.validateUsing(requestFilterValidator)

    const sources = await this.SourceService.index(payload)

    return response.status(200).json(SourcePresenter.serializeMany(sources))
  }

  async show({ request, response }: HttpContext) {
    const uid = request.param('uid')

    const source = await this.SourceService.show(uid)

    return response.status(200).json(SourcePresenter.serialize(source))
  }

  async refresh({ request, response }: HttpContext) {
    const uid = request.param('uid')

    await this.SourceService.refresh(uid)

    return response.status(200).json({ success: true })
  }

  async destroy({ request, response }: HttpContext) {
    const uid = request.param('uid')

    this.SourceService.destroy(uid)

    return response.status(202).json({ success: true })
  }
}
