import { middleware } from '#start/kernel'
import router from '@adonisjs/core/services/router'

export default () => {
  const ENDPOINT_PREFIX = '/sources'
  const CONTROLLER_PATH = '#app/modules/rag/sources/sources_controller'

  router
    .group(() => {
      router.get('/', `${CONTROLLER_PATH}.index`)
      router.get('/:uid', `${CONTROLLER_PATH}.show`)
      router.get('/refresh/:uid', `${CONTROLLER_PATH}.refresh`)
      router.post('/', `${CONTROLLER_PATH}.store`).use(middleware.no_timeout())
      router.put('/:uid', `${CONTROLLER_PATH}.update`).use(middleware.no_timeout())
      router.delete('/:uid', `${CONTROLLER_PATH}.destroy`)
    })
    .prefix(`${ENDPOINT_PREFIX}`)
}
