import { requestFilterValidator } from '#app/validators'
import env from '#start/env'
import { Infer } from '@vinejs/vine/types'
import { inject } from '@adonisjs/core'
import { HttpContext } from '@adonisjs/core/http'

@inject()
export default class SourceService {
  private ctx: HttpContext

  constructor(ctx: HttpContext) {
    this.ctx = ctx
  }

  async index(filter: Infer<typeof requestFilterValidator>) {
    const page = filter.page || 1

    return await this.ctx.app
      .related('sources')
      .query()
      .filter(filter)
      .paginate(page, env.get('APP_PAGINATION_LIMIT'))
  }

  async show(uid: string) {
    return await this.ctx.app.related('sources').query().where('uid', uid).firstOrFail()
  }

  async refresh(uid: string) {
    const source = await this.ctx.app.related('sources').query().where('uid', uid).firstOrFail()

    return await source.merge({ status: 'training_processing' }).save()
  }

  async destroy(uid: string) {
    const source = await this.ctx.app.related('sources').query().where('uid', uid).firstOrFail()

    await source.delete()
  }

  async copyToProvider(params: { source_uid: string; target_provider_uid: string }) {
    const { source_uid, target_provider_uid } = params

    const source = await this.ctx.app
      .related('sources')
      .query()
      .where('uid', source_uid)
      .firstOrFail()

    const targetProvider = await this.ctx.app
      .related('providers')
      .query()
      .where('uid', target_provider_uid)
      .firstOrFail()

    const payload = source.serializeAttributes()

    delete payload?.uid

    return await targetProvider.related('sources').create(payload)
  }
}
