import env from '#start/env'
import { Infer } from '@vinejs/vine/types'
import axios from 'axios'
import { inject } from '@adonisjs/core'
import { sourceProductValidator } from '#app/modules/rag/sources/source_validator'
import {
  trainingIngestDocumentValidator,
  trainingIngestMediaValidator,
  trainingIngestResponseValidator,
  trainingIngestUrlValidator,
  trainingIngestYoutubeValidator,
} from '#app/modules/rag/training/training_validator'

@inject()
export default class TrainingService {
  private timeout = 60 * 15 * 1000

  constructor() {}

  async ingestUrl(
    params: Infer<typeof trainingIngestUrlValidator>
  ): Promise<Infer<typeof trainingIngestResponseValidator>> {
    const { url, improve } = params

    const { data }: { data: Infer<typeof trainingIngestResponseValidator> } = await axios({
      method: 'POST',
      url: `${env.get('TRAINING_API_URL')}/ingest/url`,
      data: {
        url,
        improve,
      },
      timeout: this.timeout,
    })

    return data
  }

  async ingestYoutube(
    params: Infer<typeof trainingIngestYoutubeValidator>
  ): Promise<Infer<typeof trainingIngestResponseValidator>> {
    const { url, improve, lang_iso_639_1, lang_iso_639_2 } = params

    const { data }: { data: Infer<typeof trainingIngestResponseValidator> } = await axios({
      method: 'POST',
      url: `${env.get('TRAINING_API_URL')}/ingest/youtube`,
      data: {
        url,
        improve,
        lang_iso_639_1,
        lang_iso_639_2,
      },
      timeout: this.timeout,
    })

    return data
  }

  async ingestDocument(
    params: Infer<typeof trainingIngestDocumentValidator>
  ): Promise<Infer<typeof trainingIngestResponseValidator>> {
    const {
      storage_path,
      lang_iso_639_1,
      lang_iso_639_2,
      improve,
      credentials,
    } = params

    const { data }: { data: Infer<typeof trainingIngestResponseValidator> } = await axios({
      method: 'POST',
      url: `${env.get('TRAINING_API_URL')}/ingest/document`,
      data: {
        storage_path,
        lang_iso_639_1,
        lang_iso_639_2,
        improve,
        credentials,
      },
      timeout: this.timeout,
    })

    return data
  }

  async ingestMedia(
    params: Infer<typeof trainingIngestMediaValidator>
  ): Promise<Infer<typeof trainingIngestResponseValidator>> {
    const {
      storage_path,
      lang_iso_639_1,
      lang_iso_639_2,
      improve,
      credentials,
    } = params

    const { data }: { data: Infer<typeof trainingIngestResponseValidator> } = await axios({
      method: 'POST',
      url: `${env.get('TRAINING_API_URL')}/ingest/media`,
      data: {
        storage_path,
        lang_iso_639_1,
        lang_iso_639_2,
        improve,
        credentials,
      },
      timeout: this.timeout,
    })

    return data
  }

  async ingestQuiz(params: {
    question: string
    answer: string
  }): Promise<Infer<typeof trainingIngestResponseValidator>> {
    const { question, answer } = params

    const contentOriginal = `# ${question}?\n- ${answer}`

    return {
      chunks: [contentOriginal],
      content_original: contentOriginal,
      content_enhanced: null,
      content_summary: null,
      metadata: {},
    }
  }

  async ingestText(params: {
    text: string
  }): Promise<Infer<typeof trainingIngestResponseValidator>> {
    const { text } = params

    return {
      chunks: [text || ''],
      content_original: text || null,
      content_enhanced: null,
      content_summary: null,
      metadata: null,
    }
  }

  async ingestProduct(params: {
    product: Infer<typeof sourceProductValidator> | null
  }): Promise<Infer<typeof trainingIngestResponseValidator>> {
    const { product } = params

    const contentOriginal = `# ${product?.name}\n- Price: ${product?.price || 'N/A'}\n- URL: ${product?.url || 'N/A'}\n- Description: ${product?.description || 'N/A'}\n- Category: ${product?.category || 'N/A'}\n- SKU: ${product?.sku || 'N/A'}\n- Size: ${product?.size || 'N/A'}\n- Color: ${product?.color || 'N/A'}\n- Tags: ${product?.tags?.join(', ') || 'N/A'}\n- Images: ${product?.image_urls?.join(', ') || 'N/A'}`

    return {
      chunks: [contentOriginal],
      content_original: contentOriginal,
      content_enhanced: null,
      content_summary: null,
      metadata: null,
    }
  }

  async fetchUrls(params: { url: string }): Promise<{ urls: string[] }> {
    const { url } = params

    const { data }: { data: { urls: string[] } } = await axios({
      method: 'POST',
      url: `${env.get('TRAINING_API_URL')}/fetch/urls`,
      data: {
        url,
      },
      timeout: this.timeout,
    })

    return data
  }

  async fetchSitemap(params: { url: string }): Promise<{ urls: string[] }> {
    const { url } = params

    const { data }: { data: { urls: string[] } } = await axios({
      method: 'POST',
      url: `${env.get('TRAINING_API_URL')}/fetch/sitemap`,
      data: {
        url,
      },
      timeout: this.timeout,
    })

    return data
  }
}
