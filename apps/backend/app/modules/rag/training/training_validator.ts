import vine from '@vinejs/vine'

const ingestDocumentSchema = vine.object({
  lang_iso_639_1: vine.string().optional().nullable(),
  lang_iso_639_2: vine.string().optional().nullable(),
  improve: vine.boolean(),
  storage_path: vine.string(),
  credentials: vine.object({
    access_key: vine.string().trim(),
    secret_key: vine.string().trim(),
    bucket: vine.string().trim(),
    region: vine.string().trim(),
    endpoint: vine.string().trim(),
  }),
})

const ingestMediaSchema = vine.object({
  lang_iso_639_1: vine.string().optional().nullable(),
  lang_iso_639_2: vine.string().optional().nullable(),
  improve: vine.boolean(),
  storage_path: vine.string(),
  credentials: vine.object({
    access_key: vine.string().trim(),
    secret_key: vine.string().trim(),
    bucket: vine.string().trim(),
    region: vine.string().trim(),
    endpoint: vine.string().trim(),
  }),
})

const ingestUrlSchema = vine.object({
  url: vine
    .string()
    .trim()
    .url({ require_protocol: true, protocols: ['http', 'https'] }),
  improve: vine.boolean(),
})

const ingestYoutubeSchema = vine.object({
  lang_iso_639_1: vine.string().optional().nullable(),
  lang_iso_639_2: vine.string().optional().nullable(),
  improve: vine.boolean(),
  url: vine
    .string()
    .trim()
    .url({ require_protocol: true, protocols: ['http', 'https'] }),
})

const ingestResponseSchema = vine.object({
  chunks: vine.array(vine.string()),
  content_original: vine.string().nullable(),
  content_enhanced: vine.string().nullable(),
  content_summary: vine.string().nullable(),
  metadata: vine.object({}).allowUnknownProperties().nullable(),
})

const crawlerResponseSchema = vine.object({
  crawl_id: vine.string(),
  urls: vine.array(vine.string()),
})

const searchSchema = vine.object({
  search_id: vine.string(),
  data: vine.any(),
})

const trainingIngestResponseValidator = vine.compile(ingestResponseSchema)
const trainingCrawlerResponseValidator = vine.compile(crawlerResponseSchema)
const trainingSearchValidator = vine.compile(searchSchema)
const trainingIngestDocumentValidator = vine.compile(ingestDocumentSchema)
const trainingIngestMediaValidator = vine.compile(ingestMediaSchema)
const trainingIngestUrlValidator = vine.compile(ingestUrlSchema)
const trainingIngestYoutubeValidator = vine.compile(ingestYoutubeSchema)

export {
  trainingIngestResponseValidator,
  trainingCrawlerResponseValidator,
  trainingSearchValidator,
  trainingIngestDocumentValidator,
  trainingIngestMediaValidator,
  trainingIngestUrlValidator,
  trainingIngestYoutubeValidator,
}
