import router from '@adonisjs/core/services/router'

export default () => {
  const ENDPOINT_PREFIX = '/providers'
  const CONTROLLER_PATH = '#app/modules/rag/providers/providers_controller'

  router
    .group(() => {
      router.get('/', `${CONTROLLER_PATH}.index`)
      router.get('/:uid', `${CONTROLLER_PATH}.show`)
      router.post('/', `${CONTROLLER_PATH}.store`)
      router.put('/:uid', `${CONTROLLER_PATH}.update`)
      router.delete('/:uid', `${CONTROLLER_PATH}.destroy`)
      router.put('/change-mode/:uid', `${CONTROLLER_PATH}.changeMode`)
      router.put('/ingest/urls/:uid', `${CONTROLLER_PATH}.ingestUrls`)
      router.put('/ingest/youtube/:uid', `${CONTROLLER_PATH}.ingestYoutube`)
      router.put('/ingest/documents/:uid', `${CONTROLLER_PATH}.ingestDocuments`)
      router.put('/ingest/medias/:uid', `${CONTROLLER_PATH}.ingestMedias`)
      router.put('/ingest/quizzes/:uid', `${CONTROLLER_PATH}.ingestQuizzes`)
      router.put('/ingest/texts/:uid', `${CONTROLLER_PATH}.ingestTexts`)
      router.put('/ingest/products/:uid', `${CONTROLLER_PATH}.ingestProducts`)
      router.put('/ingest/storage/:uid', `${CONTROLLER_PATH}.ingestStorage`)
      router.put('/fetch/urls/:uid', `${CONTROLLER_PATH}.fetchUrls`)
      router.put('/fetch/sitemap/:uid', `${CONTROLLER_PATH}.fetchSitemap`)
      router.get('/transmit/:uid', `${CONTROLLER_PATH}.transmit`)
    })
    .prefix(`${ENDPOINT_PREFIX}`)
}
