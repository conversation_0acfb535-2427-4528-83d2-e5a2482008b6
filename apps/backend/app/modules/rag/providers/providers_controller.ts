import { TRAINING_DOCUMENTS_EXTENSIONS, TRAINING_MEDIA_EXTENSIONS } from '#app/constants'
import { StorageHelper } from '#app/helpers'
import TransmitService from '#app/services/transmit_service'
import { requestFilterValidator } from '#app/validators'
import { inject } from '@adonisjs/core'
import { MultipartFile } from '@adonisjs/core/bodyparser'
import { HttpContext } from '@adonisjs/core/http'
import ProviderService from '#app/modules/rag/providers/provider_service'
import TrainingService from '#app/modules/rag/training/training_service'
import {
  providerChangeModeValidator,
  providerFetchSitemapValidator,
  providerFetchUrlsValidator,
  providerIngestProductsValidator,
  providerIngestQuizzesValidator,
  providerIngestStorageValidator,
  providerIngestTextsValidator,
  providerIngestUrlsValidator,
  providerIngestYoutubeUrlsValidator,
  providerStoreValidator,
  providerUpdateValidator,
} from '#app/modules/rag/providers/provider_validator'
import ProviderPresenter from '#app/modules/rag/providers/provider_presenter'
import SourcePresenter from '#app/modules/rag/sources/source_presenter'

@inject()
export default class ProvidersController {
  constructor(
    private providerService: ProviderService,
    private trainingService: TrainingService,
    private transmitService: TransmitService
  ) {}

  async index({ request, response }: HttpContext) {
    const payload = await request.validateUsing(requestFilterValidator)

    const providers = await this.providerService.index(payload)

    return response.status(200).json(ProviderPresenter.serializePaginated(providers))
  }

  async show({ request, response }: HttpContext) {
    const uid = request.param('uid')

    const provider = await this.providerService.show(uid)

    return response.status(200).json(ProviderPresenter.serialize(provider))
  }

  async store({ request, response }: HttpContext) {
    const payload = await request.validateUsing(providerStoreValidator)

    const provider = await this.providerService.store(payload)

    return response.status(201).json(ProviderPresenter.serialize(provider))
  }

  async update({ request, response }: HttpContext) {
    const uid = request.param('uid')

    const payload = await request.validateUsing(providerUpdateValidator)
    const provider = await this.providerService.update(uid, payload)

    return response.status(201).json(ProviderPresenter.serialize(provider))
  }

  async destroy({ request, response }: HttpContext) {
    const uid = request.param('uid')

    await this.providerService.destroy(uid)

    return response.status(202).json({ success: true })
  }

  async changeMode({ request, response }: HttpContext) {
    const payload = await request.validateUsing(providerChangeModeValidator)

    const provider = await this.providerService.changeMode(payload)

    return response.status(200).json(ProviderPresenter.serialize(provider))
  }

  async ingestUrls({ app, request, response }: HttpContext) {
    const payload = await request.validateUsing(providerIngestUrlsValidator)

    await app.canConsumeSources({
      sourceType: 'urls',
      throwError: true,
      toConsume: payload.urls.length,
    })

    const sources = await this.providerService.insertUrls(payload)

    return response.status(200).json(SourcePresenter.serializeMany(sources))
  }

  async ingestYoutube({ app, request, response }: HttpContext) {
    const payload = await request.validateUsing(providerIngestYoutubeUrlsValidator)

    await app.canConsumeSources({
      sourceType: 'youtube',
      throwError: true,
      toConsume: payload.youtube_urls.length,
    })

    const sources = await this.providerService.insertYoutubeUrls(payload)

    return response.status(200).json(SourcePresenter.serializeMany(sources))
  }

  async ingestDocuments({ app, planProduct, request, response }: HttpContext) {
    const uid = request.param('uid')

    request.multipart.onFile(
      'documents',
      {
        size: planProduct?.limitations?.max_file_size,
        extnames: TRAINING_DOCUMENTS_EXTENSIONS,
      },
      async (multipartStream, reporter) => {
        multipartStream.pause()
        multipartStream.on('data', reporter)

        const storageClient = new StorageHelper('knowledge')

        const { storagePath } = await storageClient.saveStream({
          multipartStream,
          basePath: `${app.uid}/${uid}`,
        })

        return { storagePath }
      }
    )

    await request.multipart.process()

    const documents = request.allFiles().documents as MultipartFile[] | MultipartFile
    const _documents = Array.isArray(documents) ? documents : [documents]

    const sources = await this.providerService.insertDocuments({
      uid,
      storage_paths: _documents.map((document) => document.meta.storage_path),
    })

    return response.status(200).json(SourcePresenter.serializeMany(sources))
  }

  async ingestMedias({ app, planProduct, request, response }: HttpContext) {
    const uid = request.param('uid')

    /*
      await app.canConsumeSources({
        source_type: 'medias',
        throw_error: true,
        to_consume: payload.direct_urls.length,
      })
      */

    request.multipart.onFile(
      'medias',
      {
        size: planProduct?.limitations?.max_file_size,
        extnames: TRAINING_MEDIA_EXTENSIONS,
      },
      async (multipartStream, reporter) => {
        multipartStream.pause()
        multipartStream.on('data', reporter)

        const storageClient = new StorageHelper('knowledge')

        const { storagePath } = await storageClient.saveStream({
          multipartStream,
          basePath: `${app.uid}/${uid}`,
        })

        return { storagePath }
      }
    )

    await request.multipart.process()

    const medias = request.allFiles().medias as MultipartFile[] | MultipartFile
    const _medias = Array.isArray(medias) ? medias : [medias]

    const sources = await this.providerService.insertMedias({
      uid,
      storage_paths: _medias.map((media) => media.meta.storage_path),
    })

    return response.status(200).json(SourcePresenter.serializeMany(sources))
  }

  async ingestQuizzes({ request, response }: HttpContext) {
    const payload = await request.validateUsing(providerIngestQuizzesValidator)

    const sources = await this.providerService.insertQuizzes(payload)

    return response.status(200).json(SourcePresenter.serializeMany(sources))
  }

  async ingestTexts({ request, response }: HttpContext) {
    const payload = await request.validateUsing(providerIngestTextsValidator)

    const sources = await this.providerService.insertTexts(payload)

    return response.status(200).json(SourcePresenter.serializeMany(sources))
  }

  async ingestProducts({ request, response }: HttpContext) {
    const payload = await request.validateUsing(providerIngestProductsValidator)

    const sources = await this.providerService.insertProducts(payload)

    return response.status(200).json(SourcePresenter.serializeMany(sources))
  }

  async ingestStorage({ request, response }: HttpContext) {
    const payload = await request.validateUsing(providerIngestStorageValidator)

    const provider = await this.providerService.insertStorage(payload)

    return response.status(200).json(ProviderPresenter.serialize(provider))
  }

  async fetchUrls({ app, request, response }: HttpContext) {
    const uid = request.param('uid')
    const payload = await request.validateUsing(providerFetchUrlsValidator)
    const { url, auto_insert = true } = payload

    const { urls } = await this.trainingService.fetchUrls({ url })

    if (!auto_insert) {
      return response.status(200).json({ urls })
    }

    await app.canConsumeSources({
      sourceType: 'urls',
      throwError: true,
      toConsume: urls.length,
    })

    return response.status(200).json({ urls })
  }

  async fetchSitemap({ app, request, response }: HttpContext) {
    const uid = request.param('uid')
    const payload = await request.validateUsing(providerFetchSitemapValidator)
    const { url, auto_insert = true } = payload

    const { urls } = await this.trainingService.fetchSitemap({ url })

    if (!auto_insert) {
      return response.status(200).json({ urls })
    }

    await app.canConsumeSources({
      sourceType: 'urls',
      throwError: true,
      toConsume: urls.length,
    })

    return response.status(200).json({ urls })
  }

  async transmit({ app, request, response }: HttpContext) {
    const uid = request.param('uid')

    // Get the provider and its sources
    const provider = await app.related('providers').query().where('uid', uid).firstOrFail()

    const sources = await provider
      .related('sources')
      .query()
      .select('uid', 'status', 'content_original', 'content_enhanced', 'content_summary')
      .orderBy('created_at', 'desc')

    // Broadcast the current state
    await this.transmitService.sendBroadcast(`app:${app.uid}:provider:${uid}:sources`, {
      sources,
    })

    return response.status(200).json(SourcePresenter.serializeMany(sources))
  }
}
