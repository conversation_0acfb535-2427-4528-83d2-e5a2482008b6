import { StorageHelper } from '#app/helpers'
import { requestFilterValidator } from '#app/validators'
import env from '#start/env'
import { Infer } from '@vinejs/vine/types'
import { inject } from '@adonisjs/core'
import { HttpContext } from '@adonisjs/core/http'
import db from '@adonisjs/lucid/services/db'
import Provider from '#app/modules/rag/providers/provider_model'
import {
  providerChangeModeValidator,
  providerIngestProductsValidator,
  providerIngestQuizzesValidator,
  providerIngestStorageValidator,
  providerIngestTextsValidator,
  providerIngestUrlsValidator,
  providerIngestYoutubeUrlsValidator,
  providerInsertDocumentsValidator,
  providerInsertMediasValidator,
  providerStoreValidator,
  providerUpdateValidator,
} from '#app/modules/rag/providers/provider_validator'

@inject()
export default class ProviderService {
  private ctx: HttpContext | undefined

  constructor(ctx?: HttpContext) {
    this.ctx = ctx
  }

  async index(filter: Infer<typeof requestFilterValidator>) {
    const page = filter.page || 1

    return await this.ctx!.app.related('providers')
      .query()
      .filter(filter)
      .paginate(page, env.get('APP_PAGINATION_LIMIT'))
  }

  async show(uid: string) {
    return await this.ctx!.app.related('providers').query().where('uid', uid).firstOrFail()
  }

  async indexByType(payload: { agent_uid: string; type: string }) {
    const { agent_uid, type } = payload

    return await this.ctx!.app.related('providers')
      .query()
      .where('agent_uid', agent_uid)
      .andWhere('type', type)
  }

  async store(payload: Infer<typeof providerStoreValidator>) {
    return await this.ctx!.app.related('providers').create(payload)
  }

  async update(uid: string, payload: Infer<typeof providerUpdateValidator>) {
    const provider = await this.ctx!.app.related('providers')
      .query()
      .where('uid', uid)
      .firstOrFail()

    return await provider.merge(payload).save()
  }

  async destroy(uid: string) {
    const [provider, sources] = await Promise.all([
      this.ctx!.app.related('providers')
        .query()
        .select('uid', 'agent_uid')
        .preload('agent')
        .where('uid', uid)
        .firstOrFail(),
      this.ctx!.app.related('sources').query().select('uid').where('provider_uid', uid),
    ])

    const embeddingTable = provider!.agent.getEmbeddingTableName()
    const sourcesUids = sources.map((source) => source.uid)

    await db.transaction(async (trx) => {
      await Promise.all([
        trx.rawQuery(
          `DELETE FROM embeddings.${embeddingTable} WHERE source_uid = ANY(:sources_uids)`,
          { sources_uids: sourcesUids }
        ),
        trx.from('sources').whereIn('uid', sourcesUids).delete(),
        trx.from('providers').where('uid', uid).delete(),
      ])
    })

    return true
  }

  async copyToAgent(payload: { uid: string; target_agent_uid: string }) {
    const { uid, target_agent_uid } = payload

    const provider = await this.ctx!.app.related('providers')
      .query()
      .where('uid', uid)
      .preload('sources')
      .firstOrFail()

    const targetAgent = await this.ctx!.app.related('agents')
      .query()
      .where('uid', target_agent_uid)
      .firstOrFail()

    const newProvider = provider.serializeAttributes()

    delete newProvider?.uid

    return await targetAgent.related('providers').create(newProvider)
  }

  async changeMode(payload: Infer<typeof providerChangeModeValidator>) {
    const { uid, mode } = payload

    const provider = await this.ctx!.app.related('providers')
      .query()
      .where('uid', uid)
      .firstOrFail()

    return await provider.merge({ mode }).save()
  }

  async insertUrls(payload: Infer<typeof providerIngestUrlsValidator>) {
    const { uid, urls } = payload

    const provider = await this.ctx!.app.related('providers')
      .query()
      .where('uid', uid)
      .firstOrFail()

    await provider.load('agent')

    const { agent } = provider

    const readySources = (
      await Promise.all(
        urls.map(async (url) => {
          const isUnique = await agent!.isSourceUnique({ key: 'url', value: url })

          if (!isUnique) {
            return null
          }

          return {
            app_uid: provider.app_uid,
            agent_uid: provider.agent_uid,
            status: 'training_processing',
            url,
          }
        })
      )
    ).filter((source): source is Exclude<typeof source, null> => source !== null)

    return await provider.related('sources').createMany(readySources)
  }

  async insertYoutubeUrls(payload: Infer<typeof providerIngestYoutubeUrlsValidator>) {
    const { uid, youtube_urls } = payload

    const provider = await this.ctx!.app.related('providers')
      .query()
      .where('uid', uid)
      .firstOrFail()

    await provider.load('agent')

    const { agent } = provider

    const readySources = (
      await Promise.all(
        youtube_urls.map(async (youtubeUrl) => {
          const isUnique = await agent!.isSourceUnique({ key: 'url', value: youtubeUrl })

          if (!isUnique) {
            return null
          }

          return {
            app_uid: provider.app_uid,
            agent_uid: provider.agent_uid,
            status: 'training_processing',
            url: youtubeUrl,
          }
        })
      )
    ).filter((source): source is Exclude<typeof source, null> => source !== null)

    return await provider.related('sources').createMany(readySources)
  }

  async insertDocuments(payload: Infer<typeof providerInsertDocumentsValidator>) {
    const { uid, storage_paths } = payload

    const provider = await this.ctx!.app.related('providers')
      .query()
      .where('uid', uid)
      .firstOrFail()

    await provider.load('agent')

    const readySources = (
      await Promise.all(
        storage_paths.map(async (storagePath) => {
          const storageClient = new StorageHelper('knowledge')
          const metadata = await storageClient.getFileMetadata(storagePath)

          if (!metadata?.etag) {
            return null
          }

          const isUnique = await provider.agent!.isSourceUnique({
            key: 'document->>etag',
            value: metadata?.etag,
          })

          if (!isUnique) {
            return null
          }

          return {
            app_uid: provider.app_uid,
            agent_uid: provider.agent_uid,
            status: 'training_processing',
            document: { ...metadata, location: storagePath },
          }
        })
      )
    ).filter((source): source is Exclude<typeof source, null> => source !== null)

    return await provider.related('sources').createMany(readySources)
  }

  async insertMedias(payload: Infer<typeof providerInsertMediasValidator>) {
    const { uid, storage_paths } = payload

    const provider = await this.ctx!.app.related('providers')
      .query()
      .where('uid', uid)
      .firstOrFail()

    await provider.load('agent')

    const readySources = (
      await Promise.all(
        storage_paths.map(async (storagePath) => {
          const storageClient = new StorageHelper('knowledge')
          const metadata = await storageClient.getFileMetadata(storagePath)

          if (
            !metadata?.etag ||
            !metadata.name ||
            !metadata.extname ||
            !metadata.size ||
            !metadata.type ||
            !metadata.subtype
          ) {
            return null
          }

          const isUnique = await provider.agent!.isSourceUnique({
            key: 'media->>etag',
            value: metadata.etag,
          })

          if (!isUnique) {
            return null
          }

          return {
            app_uid: provider.app_uid,
            agent_uid: provider.agent_uid,
            status: 'training_processing',
            media: {
              name: metadata.name,
              type: metadata.type,
              size: metadata.size,
              location: storagePath,
              checksum: metadata.etag,
              etag: metadata.etag,
              subtype: metadata.subtype,
              extname: metadata.extname,
            },
          }
        })
      )
    ).filter((source): source is Exclude<typeof source, null> => source !== null)

    return await provider.related('sources').createMany(readySources)
  }

  async insertQuizzes(payload: Infer<typeof providerIngestQuizzesValidator>) {
    const { uid, quizzes } = payload

    const provider = await this.ctx!.app.related('providers')
      .query()
      .where('uid', uid)
      .firstOrFail()

    const readySources = quizzes
      .filter((quizz) => quizz.answer && quizz.question)
      .map((quizz) => ({
        app_uid: provider.app_uid,
        agent_uid: provider.agent_uid,
        status: 'training_processing',
        quizz,
      }))

    return await provider.related('sources').createMany(readySources)
  }

  async insertTexts(payload: Infer<typeof providerIngestTextsValidator>) {
    const { uid, texts } = payload

    const provider = await this.ctx!.app.related('providers')
      .query()
      .where('uid', uid)
      .firstOrFail()

    const readySources = texts
      .filter((text) => text)
      .map((text) => ({
        app_uid: provider.app_uid,
        agent_uid: provider.agent_uid,
        status: 'training_processing',
        text,
      }))

    return await provider.related('sources').createMany(readySources)
  }

  async insertProducts(payload: Infer<typeof providerIngestProductsValidator>) {
    const { uid, products } = payload

    const provider = await this.ctx!.app.related('providers')
      .query()
      .where('uid', uid)
      .firstOrFail()

    const readySources = products.map((product) => ({
      app_uid: provider.app_uid,
      agent_uid: provider.agent_uid,
      status: 'training_processing',
      product,
    }))

    return await provider.related('sources').createMany(readySources)
  }

  async insertStorage(payload: Infer<typeof providerIngestStorageValidator>) {
    const { uid, storage_config } = payload

    const provider = await this.ctx!.app.related('providers')
      .query()
      .where('uid', uid)
      .firstOrFail()

    return await provider.merge({ storage_config }).save()
  }
}
