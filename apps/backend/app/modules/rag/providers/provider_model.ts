import { LANGUAGES } from '#app/constants'
import { StorageHelper } from '#app/helpers'
import type { Infer } from '@vinejs/vine/types'
import { Filterable } from 'adonis-lucid-filter'
import { DateTime } from 'luxon'
import { v4 as uuid } from 'uuid'
import { compose } from '@adonisjs/core/helpers'
import {
  beforeCreate,
  beforeSave,
  belongsTo,
  column,
  computed,
  hasMany,
  SnakeCaseNamingStrategy,
} from '@adonisjs/lucid/orm'
import { BaseModel } from '@adonisjs/lucid/orm'
import type { BelongsTo, HasMany } from '@adonisjs/lucid/types/relations'
import Agent from '#app/modules/agents/agent_model'
import App from '#app/modules/apps/app_model'
import Base from '#app/modules/base/base_model'
import Source from '#app/modules/rag/sources/source_model'
import {
  providerStorageConfigValidator,
  providerTypesValidator,
} from '#app/modules/rag/providers/provider_validator'
import BaseFilter from '#app/modules/base/base_filter'

export default class Provider extends compose(BaseModel, Filterable) {
  serializeExtras = false
  static selfAssignPrimaryKey = true
  static namingStrategy = new SnakeCaseNamingStrategy()
  static $filter = () => BaseFilter

  @beforeCreate()
  static createUUID(model: Provider) {
    model.uid = uuid()
  }

  @belongsTo(() => App, {
    localKey: 'uid',
    foreignKey: 'app_uid',
  })
  declare app: BelongsTo<typeof App>

  @belongsTo(() => Agent, {
    localKey: 'uid',
    foreignKey: 'agent_uid',
  })
  declare agent: BelongsTo<typeof Agent>

  @hasMany(() => Source, {
    localKey: 'uid',
    foreignKey: 'provider_uid',
  })
  declare sources: HasMany<typeof Source>

  @column({
    isPrimary: true,
    meta: { searchable: true, type: 'string', validations: [{ name: 'uuid' }] },
  })
  declare uid: string

  @column({
    meta: {
      searchable: true,
      type: 'string',
    },
  })
  declare app_uid: string

  @column({
    meta: {
      searchable: true,
      validations: [{ name: 'uuid' }],
    },
  })
  declare agent_uid: string

  @column({ meta: { searchable: true, type: 'string' } })
  declare label: string

  @column({ meta: { searchable: true, type: 'string' } })
  declare type: Infer<typeof providerTypesValidator>

  @column({ meta: { searchable: true, type: 'string' } })
  declare language: string

  @column({ meta: { searchable: true, type: 'number' } })
  declare fetch_every_hours: number

  @column({
    meta: {
      searchable: true,
      type: 'object',
      validations: [{ name: 'noEscape' }],
    },
  })
  declare storage_config: Infer<typeof providerStorageConfigValidator>

  @column({ meta: { searchable: true, type: 'boolean' } })
  declare mode: boolean

  @column({ meta: { searchable: true, type: 'string' } })
  declare status: string

  @column({ meta: { searchable: true, type: 'object' } })
  declare metadata: { [key: string]: any } | null

  @column({
    meta: { searchable: true, type: 'date' },
  })
  declare last_synch_at?: DateTime | null

  @column.dateTime({ autoCreate: true, meta: { searchable: true, type: 'date' } })
  declare created_at: DateTime

  @column.dateTime({
    autoCreate: true,
    autoUpdate: true,
    meta: { searchable: true, type: 'date' },
  })
  declare updated_at: DateTime

  @column({ serializeAs: null, meta: { searchable: true, type: 'date' } })
  declare deleted_at?: DateTime | null

  @computed()
  get language_iso_639_1(): string {
    const { iso_639_1 } = LANGUAGES.find((language) => this.language === language.unique_key) || {}

    return iso_639_1 || 'en'
  }

  @computed()
  get language_iso_639_2(): string {
    const { iso_639_2 } = LANGUAGES.find((language) => this.language === language.unique_key) || {}

    return iso_639_2 || 'en'
  }
}
