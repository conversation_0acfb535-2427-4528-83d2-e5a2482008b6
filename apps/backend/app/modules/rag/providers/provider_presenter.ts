import { ModelPaginatorContract } from '@adonisjs/lucid/types/model'
import Provider from '#app/modules/rag/providers/provider_model'

export default class ProviderPresenter {
  static serialize(item: Provider) {
    return {
      uid: item.uid,
      app_uid: item.app_uid,
      agent_uid: item.agent_uid,
      label: item.label,
      type: item.type,
      language: item.language,
      fetch_every_hours: item.fetch_every_hours,
      storage_config: item.storage_config,
      mode: item.mode,
      status: item.status,
      metadata: item.metadata,
      last_synch_at: item.last_synch_at,
      created_at: item.created_at,
      updated_at: item.updated_at,
      language_iso_639_1: item.language_iso_639_1,
      language_iso_639_2: item.language_iso_639_2,
    }
  }

  static serializeMany(items: Provider[]) {
    return items.map((item) => this.serialize(item))
  }

  static serializePaginated(items: ModelPaginatorContract<Provider>) {
    return {
      page: items.currentPage,
      per_page: items.perPage,
      total: items.total,
      last_page: items.lastPage,
      data: this.serializeMany(items.all()),
    }
  }
}
