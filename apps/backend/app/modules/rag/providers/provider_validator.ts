import { LANGUAGES_KEYS } from '#app/constants'
import vine from '@vinejs/vine'
import {
  sourceProductSchema,
  sourceQuizzSchema,
  sourceTextSchema,
} from '#app/modules/rag/sources/source_validator'

const providerTypesSchema = vine.enum([
  'urls',
  'youtube',
  'documents',
  'medias',
  'quizzes',
  'texts',
  'products',
  'storage',
])

const providerStorageConfigSchema = vine.object({
  access_key: vine.string().trim(),
  secret_key: vine.string().trim(),
  bucket_region: vine.string().trim(),
  bucket_name: vine.string().trim(),
  bucket_endpoint: vine
    .string()
    .trim()
    .url({ require_protocol: true, protocols: ['http', 'https'] }),
})

const providerStoreSchema = vine.object({
  agent_uid: vine.string().uuid(),
  label: vine.string().trim(),
  language: vine.string().trim(),
  type: providerTypesSchema,
  fetch_every_hours: vine.number().min(0).optional(),
  storage_config: providerStorageConfigSchema.optional().requiredWhen('type', '=', 'storage'),
})

const providerUpdateSchema = vine.object({
  label: vine.string().trim(),
  language: vine.enum(LANGUAGES_KEYS),
  fetch_every_hours: vine.number().min(0),
  storage_config: providerStorageConfigSchema.optional(),
})

const providerChangeModeSchema = vine.object({
  uid: vine.string().uuid(),
  mode: vine.boolean(),
})

const providerIngestUrlsSchema = vine.object({
  uid: vine.string().uuid(),
  urls: vine.array(
    vine
      .string()
      .trim()
      .url({ require_protocol: true, protocols: ['http', 'https'] })
  ),
})

const providerIngestYoutubeUrlsSchema = vine.object({
  uid: vine.string().uuid(),
  youtube_urls: vine.array(
    vine
      .string()
      .trim()
      .url({
        require_protocol: true,
        protocols: ['http', 'https'],
        host_whitelist: ['youtube.com', 'youtu.be', 'www.youtube.com', 'www.youtu.be'],
      })
  ),
})

const providerIngestQuizzesSchema = vine.object({
  uid: vine.string().uuid(),
  quizzes: vine.array(sourceQuizzSchema),
})

const providerIngestTextsSchema = vine.object({
  uid: vine.string().uuid(),
  texts: vine.array(sourceTextSchema),
})

const providerIngestProductsSchema = vine.object({
  uid: vine.string().uuid(),
  products: vine.array(sourceProductSchema),
})

const providerIngestStorageSchema = vine.object({
  uid: vine.string().uuid(),
  storage_config: providerStorageConfigSchema,
})

const providerInsertDocumentsSchema = vine.object({
  uid: vine.string().uuid(),
  storage_paths: vine.array(vine.string().trim()),
})

const providerInsertMediasSchema = vine.object({
  uid: vine.string().uuid(),
  storage_paths: vine.array(vine.string().trim()),
})

const providerFetchUrlsSchema = vine.object({
  url: vine
    .string()
    .trim()
    .url({ require_protocol: true, protocols: ['http', 'https'] }),
  auto_insert: vine.boolean().optional(),
})

const providerFetchSitemapSchema = vine.object({
  url: vine
    .string()
    .trim()
    .url({ require_protocol: true, protocols: ['http', 'https'] }),
  auto_insert: vine.boolean().optional(),
})

const providerTypesValidator = vine.compile(providerTypesSchema)
const providerStoreValidator = vine.compile(providerStoreSchema)
const providerUpdateValidator = vine.compile(providerUpdateSchema)
const providerChangeModeValidator = vine.compile(providerChangeModeSchema)
const providerStorageConfigValidator = vine.compile(providerStorageConfigSchema)
const providerIngestUrlsValidator = vine.compile(providerIngestUrlsSchema)
const providerIngestYoutubeUrlsValidator = vine.compile(providerIngestYoutubeUrlsSchema)
const providerIngestQuizzesValidator = vine.compile(providerIngestQuizzesSchema)
const providerIngestTextsValidator = vine.compile(providerIngestTextsSchema)
const providerIngestProductsValidator = vine.compile(providerIngestProductsSchema)
const providerIngestStorageValidator = vine.compile(providerIngestStorageSchema)
const providerInsertDocumentsValidator = vine.compile(providerInsertDocumentsSchema)
const providerInsertMediasValidator = vine.compile(providerInsertMediasSchema)
const providerFetchUrlsValidator = vine.compile(providerFetchUrlsSchema)
const providerFetchSitemapValidator = vine.compile(providerFetchSitemapSchema)

export {
  providerTypesSchema,
  providerTypesValidator,
  providerStoreValidator,
  providerUpdateValidator,
  providerChangeModeValidator,
  providerStorageConfigValidator,
  providerIngestUrlsValidator,
  providerIngestYoutubeUrlsValidator,
  providerIngestQuizzesValidator,
  providerIngestTextsValidator,
  providerIngestProductsValidator,
  providerIngestStorageValidator,
  providerInsertDocumentsValidator,
  providerInsertMediasValidator,
  providerFetchUrlsValidator,
  providerFetchSitemapValidator,
}
