import { BaseModel, belongsTo, column } from '@adonisjs/lucid/orm'
import type { BelongsTo } from '@adonisjs/lucid/types/relations'
import Source from '#app/modules/rag/sources/source_model'

export default class Embedding extends BaseModel {
  @belongsTo(() => Source, {
    localKey: 'uid',
    foreignKey: 'source_uid',
  })
  declare source: BelongsTo<typeof Source>

  @column({
    meta: {
      searchable: true,
      validations: [{ name: 'uuid' }],
    },
  })
  declare source_uid: string

  @column()
  declare embedding: number[]

  @column({ meta: { searchable: true, type: 'object' } })
  declare metadata: { [key: string]: any } | null
}
