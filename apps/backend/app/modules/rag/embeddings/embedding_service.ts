import { Infer } from '@vinejs/vine/types'
import { inject } from '@adonisjs/core'
import db from '@adonisjs/lucid/services/db'
import Embedding from '#app/modules/rag/embeddings/embedding_model'
import { embeddingValidator } from '#app/modules/rag/embeddings/embedding_validator'

@inject()
export default class EmbeddingService {
  private readonly EMBEDDING_SCHEMA = 'embeddings'
  private readonly MAX_RETRIES = 3

  constructor() {}

  async createTable(table: string) {
    for (let attempt = 1; attempt <= this.MAX_RETRIES; attempt++) {
      try {
        await db.transaction(async (trx) => {
          const tableResults = await trx.rawQuery(
            `SELECT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = '${this.EMBEDDING_SCHEMA}' AND table_name = '${table}') AS table_exists`
          )

          const tableExists = tableResults?.rows[0]?.table_exists

          if (!tableExists) {
            await trx.rawQuery(`CREATE TABLE IF NOT EXISTS ${this.EMBEDDING_SCHEMA}.${table} (
              "uid" uuid PRIMARY KEY,
              "source_uid" uuid NOT NULL,
              "embedding" vector(1536),
              "metadata" jsonb,
              "created_at" timestamptz(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
              "updated_at" timestamptz(6),
              "deleted_at" timestamptz(6)
            )`)

            await trx.rawQuery(
              `ALTER TABLE ${this.EMBEDDING_SCHEMA}.${table} ADD CONSTRAINT "fk_embeddings_${table}_source_uid" FOREIGN KEY ("source_uid") REFERENCES "public"."sources" ("uid") ON DELETE CASCADE ON UPDATE NO ACTION`
            )
          }
        })

        break
      } catch (error) {
        console.error(error)

        if (attempt === this.MAX_RETRIES) {
          throw {
            message: 'Error creating your AI agent, contact the support team!',
            status: 500,
          }
        }
      }
    }

    try {
      await db.rawQuery(
        `CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_hnsw_${table}_embedding ON ${this.EMBEDDING_SCHEMA}.${table} USING hnsw (embedding vector_ip_ops)`
      )
    } catch (error) {
      console.error(error)
    }

    try {
      await db.rawQuery(
        `CREATE INDEX CONCURRENTLY IF NOT EXISTS "idx_${table}_source_uid" ON ${this.EMBEDDING_SCHEMA}.${table} USING btree ("source_uid")`
      )
    } catch (error) {
      console.error(error)
    }
  }

  async createIndex(params: { table: string; vacuum: boolean }) {
    const { table, vacuum } = params

    try {
      await db.rawQuery(
        `CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_hnsw_${table}_embedding ON ${this.EMBEDDING_SCHEMA}.${table} USING hnsw (embedding vector_ip_ops)`
      )
    } catch (error) {
      console.error(error)
    }

    try {
      await db.rawQuery(
        `CREATE INDEX CONCURRENTLY IF NOT EXISTS "idx_${table}_source_uid" ON ${this.EMBEDDING_SCHEMA}.${table} USING btree ("source_uid")`
      )
    } catch (error) {
      console.error(error)
    }

    if (vacuum) {
      try {
        await db.rawQuery(`VACUUM ANALYZE ${this.EMBEDDING_SCHEMA}.${table}`)
      } catch (error) {
        console.error(error)
      }
    }
  }

  async deleteTable(table: string) {
    for (let attempt = 1; attempt <= this.MAX_RETRIES; attempt++) {
      try {
        await db.transaction(async (trx) => {
          await trx.rawQuery(`DROP TABLE IF EXISTS ${this.EMBEDDING_SCHEMA}.${table}`)
        })

        break
      } catch (error) {
        console.error(error)

        if (attempt === this.MAX_RETRIES) {
          throw {
            message: 'Error creating your AI agent, contact the support team!',
            status: 500,
          }
        }
      }
    }
  }

  async getAllBySource(params: { table: string; source_uid: string }): Promise<Embedding[]> {
    const { table, source_uid } = params

    const { rows } = await db.rawQuery(
      `SELECT * FROM ${this.EMBEDDING_SCHEMA}.${table} WHERE source_uid = :source_uid`,
      { source_uid }
    )

    return rows || []
  }

  async multiInsert(params: { table: string; embeddings: Infer<typeof embeddingValidator>[] }) {
    const { table, embeddings } = params

    await db.transaction(async (trx) => {
      await trx.table(`${this.EMBEDDING_SCHEMA}.${table}`).multiInsert(embeddings)
    })
  }

  async deleteEmbeddingsBySources(params: { table: string; source_uids: string[] }) {
    const { table, source_uids } = params

    await db.transaction(async (trx) => {
      await trx.rawQuery(
        `DELETE FROM ${this.EMBEDDING_SCHEMA}.${table} WHERE source_uid IN (${source_uids.map(() => '?').join(',')})`,
        source_uids
      )
    })
  }

  /*
  async createEmbeddings(params: {
    app: App
    agent: Agent
    chunks: string[]
    embedding_model: string
    dimensions: number
  }): Promise<{ index: number; embedding: number[]; chunk: string }[]> {
    const { app, agent, chunks, embedding_model, dimensions } = params

    const client = new OpenAI({
      apiKey: env.get('GATEWAY_API_KEY'),
      baseURL: env.get('GATEWAY_API_URL'),
      organization: app?.uid,
      project: agent?.uid,
    })

    const { data } = await client.embeddings.create({
      input: chunks,
      model: embedding_model,
      dimensions: dimensions,
      user: app?.uid,
    })

    return data.map((embedding, index) => ({
      index,
      embedding: embedding.embedding,
      chunk: chunks[index],
    }))
  }
  */
}
