import { ModelPaginatorContract } from '@adonisjs/lucid/types/model'
import Embedding from '#app/modules/rag/embeddings/embedding_model'

export default class EmbeddingPresenter {
  static serialize(item: Embedding) {
    return {
      source_uid: item.source_uid,
      embedding: item.embedding,
      metadata: item.metadata,
    }
  }

  static serializeMany(items: Embedding[]) {
    return items.map((item) => this.serialize(item))
  }

  static serializePaginated(items: ModelPaginatorContract<Embedding>) {
    return {
      page: items.currentPage,
      per_page: items.perPage,
      total: items.total,
      last_page: items.lastPage,
      data: this.serializeMany(items.all()),
    }
  }
}
