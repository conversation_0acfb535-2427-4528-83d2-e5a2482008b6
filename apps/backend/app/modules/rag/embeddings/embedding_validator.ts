import vine from '@vinejs/vine'
import { providerTypesSchema } from '#app/modules/rag/providers/provider_validator'
import {
  sourceDocumentSchema,
  sourceMediaSchema,
  sourceProductSchema,
  sourceQuizzSchema,
  sourceUrlSchema,
  sourceYoutubeSchema,
} from '#app/modules/rag/sources/source_validator'

const embeddingSchema = vine.object({
  uid: vine.string().uuid(),
  source_uid: vine.string().uuid(),
  embedding: vine.string().trim(),
  score: vine.string().optional().nullable(),
  metadata: vine
    .object({
      chunk_id: vine.number().min(0),
      content: vine.string().trim(),
      app_uid: vine.string().uuid(),
      agent_uid: vine.string().uuid(),
      provider_uid: vine.string().uuid(),
      type: providerTypesSchema,
      language: vine.string().trim(),
      url: sourceUrlSchema.optional().nullable(),
      youtube: sourceYoutubeSchema.optional().nullable(),
      document: sourceDocumentSchema.optional().nullable(),
      media: sourceMediaSchema.optional().nullable(),
      quizz: sourceQuizzSchema.optional().nullable(),
      product: sourceProductSchema.optional().nullable(),
      model_embedding: vine.string().trim(),
      model_dimensions: vine.number().min(0),
    })
    .allowUnknownProperties(),
})

const embeddingValidator = vine.compile(embeddingSchema)

export { embeddingSchema, embeddingValidator }
