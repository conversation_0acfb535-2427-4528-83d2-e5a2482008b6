import { inject } from '@adonisjs/core'
import { HttpContext } from '@adonisjs/core/http'
import App from '#app/modules/apps/app_model'
import AppService from '#app/modules/apps/app_service'
import { appAutoTopupValidator } from '#app/modules/apps/app_validator'

@inject()
export default class AppsController {
  protected model = App

  constructor(private appService: AppService) {}

  async updateAutoTopup({ request, response }: HttpContext) {
    const { queries_auto_topup, queries_below, queries_purchase } =
      await request.validateUsing(appAutoTopupValidator)

    const response_auto_topup = await this.appService.saveAutoTopup({
      queries_auto_topup,
      queries_below,
      queries_purchase,
    })

    return response.status(200).json(response_auto_topup)
  }
}
