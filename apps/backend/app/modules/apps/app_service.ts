import { Infer } from '@vinejs/vine/types'
import { inject } from '@adonisjs/core'
import { HttpContext } from '@adonisjs/core/http'
import { appAutoTopupValidator, appCompanyValidator } from '#app/modules/apps/app_validator'

@inject()
export default class AppService {
  private ctx: HttpContext

  constructor(ctx: HttpContext) {
    this.ctx = ctx
  }

  async saveImage(params: { key: 'logo' | 'favicon'; url: string }) {
    const { key, url } = params

    const savedImages = await this.ctx.app
      .merge({
        [key]: url,
      })
      .save()

    return savedImages
  }

  async saveCompany(payload: Infer<typeof appCompanyValidator>) {
    const { page_title, company_name } = payload

    return await this.ctx.app
      .merge({
        page_title,
        company_name,
      })
      .save()
  }

  async saveAutoTopup(payload: Infer<typeof appAutoTopupValidator>) {
    const { queries_auto_topup, queries_below, queries_purchase } = payload

    return await this.ctx.app
      .merge({
        queries_auto_topup,
        queries_below,
        queries_purchase,
      })
      .save()
  }
}
