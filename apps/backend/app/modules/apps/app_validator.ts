import { TRAINING_IMAGE_EXTENSIONS } from '#app/constants'
import vine from '@vinejs/vine'

const updateImageKeySchema = vine.enum(['logo', 'favicon'])

const autoTopupSchema = vine.object({
  queries_auto_topup: vine.boolean(),
  queries_below: vine.number(),
  queries_purchase: vine.number(),
})

const smtpSchema = vine.object({
  host: vine.string(),
  port: vine.number(),
  username: vine.string(),
  password: vine.string(),
  name: vine.string(),
  email: vine.string().email().normalizeEmail({ all_lowercase: true }).trim(),
})

const domainsSchema = vine.array(
  vine.string().url({ require_protocol: true, protocols: ['http', 'https'] })
)

const companySchema = vine.object({
  page_title: vine.string().optional(),
  company_name: vine.string().optional(),
})

const onboardingSchema = vine.object({
  status: vine.boolean(),
  key: vine.enum(['created_message', 'created_training', 'change_interface', 'change_settings']),
})

const appUpdateImageKeyValidator = vine.compile(updateImageKeySchema)
const appAutoTopupValidator = vine.compile(autoTopupSchema)
const appSmtpValidator = vine.compile(smtpSchema)
const appDomainsValidator = vine.compile(domainsSchema)
const appCompanyValidator = vine.compile(companySchema)
const appOnboardingValidator = vine.compile(onboardingSchema)

export {
  appUpdateImageKeyValidator,
  appAutoTopupValidator,
  appSmtpValidator,
  appDomainsValidator,
  appCompanyValidator,
  appOnboardingValidator,
}
