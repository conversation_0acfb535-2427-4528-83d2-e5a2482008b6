import { ModelPaginatorContract } from '@adonisjs/lucid/types/model'
import App from '#app/modules/apps/app_model'

export default class AppPresenter {
  static serialize(item: App) {
    return {
      uid: item.uid,
      available_credits: item.available_credits,
      logo: item.logo,
      favicon: item.favicon,
      is_banned: item.is_banned,
      is_first_time_customer: item.is_first_time_customer,
      is_first_time_churning: item.is_first_time_churning,
      appsumo_license: item.appsumo_license,
      stripe_customer_id: item.stripe_customer_id,
      created_at: item.created_at,
      updated_at: item.updated_at,
      computed_files_path: item.computed_files_path,
    }
  }

  static serializeMany(items: App[]) {
    return items.map((item) => this.serialize(item))
  }

  static serializePaginated(items: ModelPaginatorContract<App>) {
    return {
      page: items.currentPage,
      per_page: items.perPage,
      total: items.total,
      last_page: items.lastPage,
      data: this.serializeMany(items.all()),
    }
  }
}
