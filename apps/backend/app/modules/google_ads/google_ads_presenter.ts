import { ModelPaginatorContract } from '@adonisjs/lucid/types/model'
import GoogleAds from '#app/modules/google_ads/google_ads_model'

export default class GoogleAdsPresenter {
  static serialize(item: GoogleAds) {
    return {
      uid: item.uid,
      app_uid: item.app_uid,
      user_uid: item.user_uid,
      gclid: item.gclid,
      stripe_customer_id: item.stripe_customer_id,
      email: item.email,
      conversionName: item.conversionName,
      conversion_time: item.conversion_time,
      conversionValue: item.conversionValue,
      conversionCurrency: item.conversionCurrency,
      metadata: item.metadata,
      created_at: item.created_at,
      updated_at: item.updated_at,
    }
  }

  static serializeMany(items: GoogleAds[]) {
    return items.map((item) => this.serialize(item))
  }

  static serializePaginated(items: ModelPaginatorContract<GoogleAds>) {
    return {
      page: items.currentPage,
      per_page: items.perPage,
      total: items.total,
      last_page: items.lastPage,
      data: this.serializeMany(items.all()),
    }
  }
}
