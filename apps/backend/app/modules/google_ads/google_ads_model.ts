import { Filterable } from 'adonis-lucid-filter'
import { DateTime } from 'luxon'
import { v4 as uuid } from 'uuid'
import { compose } from '@adonisjs/core/helpers'
import { beforeCreate, belongsTo, column, SnakeCaseNamingStrategy } from '@adonisjs/lucid/orm'
import { BaseModel } from '@adonisjs/lucid/orm'
import type { BelongsTo } from '@adonisjs/lucid/types/relations'
import App from '#app/modules/apps/app_model'
import User from '#app/modules/users/user_model'
import BaseFilter from '#app/modules/base/base_filter'

export default class GoogleAds extends compose(BaseModel, Filterable) {
  serializeExtras = false
  static selfAssignPrimaryKey = true
  static namingStrategy = new SnakeCaseNamingStrategy()
  static $filter = () => BaseFilter
  static table = 'google_ads'

  @beforeCreate()
  static createUUID(model: GoogleAds) {
    model.uid = uuid()
  }

  @belongsTo(() => App, {
    localKey: 'uid',
    foreignKey: 'app_uid',
  })
  declare app: BelongsTo<typeof App>

  @belongsTo(() => User, {
    localKey: 'uid',
    foreignKey: 'user_uid',
  })
  declare user: BelongsTo<typeof User>

  @column({
    isPrimary: true,
    meta: { searchable: true, type: 'string', validations: [{ name: 'uuid' }] },
  })
  declare uid: string

  @column({
    meta: {
      searchable: true,
      type: 'string',
    },
  })
  declare app_uid: string

  @column({
    meta: {
      searchable: true,
      validations: [{ name: 'uuid' }],
    },
  })
  declare user_uid: string

  @column({ meta: { searchable: true, type: 'string' } })
  declare gclid: string

  @column({ meta: { searchable: true, type: 'string' } })
  declare stripe_customer_id: string

  @column({
    meta: { searchable: true, type: 'string', validations: [{ name: 'email' }] },
  })
  declare email: string

  @column({ meta: { searchable: true, type: 'string' } })
  declare conversionName: string

  @column({ meta: { searchable: true, type: 'date' } })
  declare conversion_time: DateTime

  @column({ meta: { searchable: true, type: 'number' } })
  declare conversionValue: number

  @column({ meta: { searchable: true, type: 'string' } })
  declare conversionCurrency: string

  @column({ meta: { searchable: true, type: 'object' } })
  declare metadata: { [key: string]: any } | null

  @column.dateTime({ autoCreate: true, meta: { searchable: true, type: 'date' } })
  declare created_at: DateTime

  @column.dateTime({
    autoCreate: true,
    autoUpdate: true,
    meta: { searchable: true, type: 'date' },
  })
  declare updated_at: DateTime

  @column({ serializeAs: null, meta: { searchable: true, type: 'date' } })
  declare deleted_at?: DateTime | null
}
