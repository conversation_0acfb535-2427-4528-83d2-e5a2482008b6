import router from '@adonisjs/core/services/router'

export default () => {
  const CONTROLLER_PATH = '#app/modules/whitelabel/domains/domains_controller'

  router.get('/domains', `${CONTROLLER_PATH}.index`)
  router.post('/domains', `${CONTROLLER_PATH}.store`)
  router.get('/domains/:uid', `${CONTROLLER_PATH}.show`)
  router.put('/domains/:uid', `${CONTROLLER_PATH}.update`)
  router.delete('/domains/:uid', `${CONTROLLER_PATH}.destroy`)
  router.post('/domains/assign/:uid', `${CONTROLLER_PATH}.assign`)
  router.get('/domains/unassign/:uid', `${CONTROLLER_PATH}.unassign`)
  router.get('/domains/primary/:uid', `${CONTROLLER_PATH}.setPrimary`)
  router.get('/domains/verify/:uid', `${CONTROLLER_PATH}.verify`)
  router.put('/domains/update-image/:uid', `${CONTROLLER_PATH}.updateImage`)
}
