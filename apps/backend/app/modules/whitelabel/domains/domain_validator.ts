import vine from '@vinejs/vine'

const storeSchema = vine.object({
  domain_name: vine.string().trim(),
  company_name: vine.string().trim(),
  primary_color: vine.string().trim(),
  logo_url: vine.string().trim(),
  favicon_url: vine.string().trim(),
  is_verified: vine.boolean(),
  is_primary: vine.boolean(),
})

const assignSchema = vine.object({
  agent_uid: vine.string().uuid(),
})

const updateImageKeySchema = vine.enum(['logo', 'favicon'])

const domainStoreValidator = vine.compile(storeSchema)
const domainAssignValidator = vine.compile(assignSchema)
const domainUpdateImageKeyValidator = vine.compile(updateImageKeySchema)

export { domainStoreValidator, domainAssignValidator, domainUpdateImageKeyValidator }
