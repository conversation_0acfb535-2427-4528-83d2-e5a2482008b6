import { TRAINING_IMAGE_EXTENSIONS } from '#app/constants'
import { StorageHelper } from '#app/helpers'
import { requestFilterValidator } from '#app/validators'
import { Infer } from '@vinejs/vine/types'
import { inject } from '@adonisjs/core'
import { MultipartFile } from '@adonisjs/core/bodyparser'
import { HttpContext } from '@adonisjs/core/http'
import DomainService from '#app/modules/whitelabel/domains/domain_service'
import {
  domainAssignValidator,
  domainStoreValidator,
  domainUpdateImageKeyValidator,
} from '#app/modules/whitelabel/domains/domain_validator'
import DomainPresenter from '#app/modules/whitelabel/domains/domain_presenter'

@inject()
export default class DomainsController {
  constructor(private DomainService: DomainService) {}

  async index({ response, request }: HttpContext) {
    const payload = await request.validateUsing(requestFilterValidator)

    const domains = await this.DomainService.index(payload)

    return response.status(200).json(DomainPresenter.serializePaginated(domains))
  }

  async show({ request, response }: HttpContext) {
    const uid = request.param('uid')

    const domain = await this.DomainService.show(uid)

    return response.status(200).json(domain)
  }

  async store({ request, response }: HttpContext) {
    const payload = await request.validateUsing(domainStoreValidator)
    const domain = await this.DomainService.store(payload)

    return response.status(201).json(DomainPresenter.serialize(domain))
  }

  async update({ request, response }: HttpContext) {
    const uid = request.param('uid')

    const payload = await request.validateUsing(domainStoreValidator)
    const domain = await this.DomainService.update(uid, payload)

    return response.status(200).json(DomainPresenter.serialize(domain))
  }

  async updateImage({ app, request, response }: HttpContext) {
    const uid = request.param('uid')
    let key: Infer<typeof domainUpdateImageKeyValidator> = 'logo'

    request.multipart.onFile(
      '*',
      {
        size: '30mb',
        extnames: TRAINING_IMAGE_EXTENSIONS,
      },
      async (multipartStream, reporter) => {
        key = multipartStream.name as Infer<typeof domainUpdateImageKeyValidator>

        multipartStream.pause()
        multipartStream.on('data', reporter)

        const storageClient = new StorageHelper('apps')

        const { directUrl } = await storageClient.saveStream({
          multipartStream,
          basePath: `${app.uid}/${uid}`,
        })

        return { directUrl }
      }
    )

    await request.multipart.process()

    const imageMultipart = request.file(key) as MultipartFile
    const directUrl: string = imageMultipart.meta.direct_url

    if (key && directUrl) {
      const domain = await this.DomainService.updateImage({
        uid,
        key,
        url: directUrl,
      })

      return response.status(201).json(DomainPresenter.serialize(domain))
    }
  }

  async destroy({ request, response }: HttpContext) {
    const uid = request.param('uid')

    await this.DomainService.destroy(uid)

    return response.status(202).json({ success: true })
  }

  async assign({ request, response }: HttpContext) {
    const domain_uid = request.param('uid')

    const { agent_uid } = await request.validateUsing(domainAssignValidator)

    await this.DomainService.assign({ agent_uid, domain_uid })

    return response.status(200).json({ success: true })
  }

  async unassign({ request, response }: HttpContext) {
    const domain_uid = request.param('uid')

    await this.DomainService.unassign(domain_uid)

    return response.status(200).json({ success: true })
  }

  async setPrimary({ request, response }: HttpContext) {
    const domain_uid = request.param('uid')

    await this.DomainService.setPrimary(domain_uid)

    return response.status(200).json({ success: true })
  }

  async verify({ request, response }: HttpContext) {
    const domain_uid = request.param('uid')

    await this.DomainService.verify(domain_uid)

    return response.status(200).json({ success: true })
  }
}
