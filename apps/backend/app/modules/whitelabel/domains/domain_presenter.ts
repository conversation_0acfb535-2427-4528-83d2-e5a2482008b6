import { ModelPaginatorContract } from '@adonisjs/lucid/types/model'
import Domain from '#app/modules/whitelabel/domains/domain_model'

export default class DomainPresenter {
  static serialize(item: Domain) {
    return {
      uid: item.uid,
      app_uid: item.app_uid,
      agent_uid: item.agent_uid,
      domain_name: item.domain_name,
      company_name: item.company_name,
      logo_url: item.logo_url,
      favicon_url: item.favicon_url,
      primary_color: item.primary_color,
      is_verified: item.is_verified,
      is_primary: item.is_primary,
      metadata: item.metadata,
      created_at: item.created_at,
      updated_at: item.updated_at,
    }
  }

  static serializeMany(items: Domain[]) {
    return items.map((item) => this.serialize(item))
  }

  static serializePaginated(items: ModelPaginatorContract<Domain>) {
    return {
      page: items.currentPage,
      per_page: items.perPage,
      total: items.total,
      last_page: items.lastPage,
      data: this.serializeMany(items.all()),
    }
  }
}
