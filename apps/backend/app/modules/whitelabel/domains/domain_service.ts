import { requestFilterValidator } from '#app/validators'
import env from '#start/env'
import { Infer } from '@vinejs/vine/types'
import { inject } from '@adonisjs/core'
import { HttpContext } from '@adonisjs/core/http'
import Domain from '#app/modules/whitelabel/domains/domain_model'
import {
  domainStoreValidator,
  domainUpdateImageKeyValidator,
} from '#app/modules/whitelabel/domains/domain_validator'

@inject()
export default class DomainService {
  private ctx: HttpContext

  constructor(ctx: HttpContext) {
    this.ctx = ctx
  }

  async index(filter: Infer<typeof requestFilterValidator>) {
    const page = filter.page || 1
    return this.ctx.app
      .related('domains')
      .query()
      .filter(filter)
      .paginate(page, env.get('APP_PAGINATION_LIMIT'))
  }

  async show(uid: string) {
    return this.ctx.app.related('domains').query().where('uid', uid).firstOrFail()
  }

  async store(payload: Infer<typeof domainStoreValidator>) {
    return this.ctx.app.related('domains').create(payload)
  }

  async update(uid: string, payload: Infer<typeof domainStoreValidator>) {
    const domain = await this.ctx.app.related('domains').query().where('uid', uid).firstOrFail()

    return await domain.merge(payload).save()
  }

  async updateImage(params: {
    uid: string
    key: Infer<typeof domainUpdateImageKeyValidator>
    url: string
  }) {
    const { uid, key, url } = params

    const domain = await this.ctx.app.related('domains').query().where('uid', uid).firstOrFail()

    if (key === 'logo') {
      await domain.merge({ logo_url: url }).save()
    } else if (key === 'favicon') {
      await domain.merge({ favicon_url: url }).save()
    }

    return domain
  }

  async destroy(uid: string) {
    const domain = await this.ctx.app.related('domains').query().where('uid', uid).firstOrFail()

    return await domain.delete()
  }

  async setPrimary(uid: string) {
    const domain = await this.ctx.app.related('domains').query().where('uid', uid).firstOrFail()

    await this.ctx.app
      .related('domains')
      .query()
      .where('uid', '!=', uid)
      .update({ is_primary: false })

    return await domain.merge({ is_primary: true }).save()
  }

  async assign(payload: { agent_uid: string; domain_uid: string }) {
    const { agent_uid, domain_uid } = payload

    const domain = await this.ctx.app
      .related('domains')
      .query()
      .where('uid', domain_uid)
      .firstOrFail()

    return await domain.merge({ agent_uid }).save()
  }

  async unassign(agent_uid: string) {
    const domain = await this.ctx.app
      .related('domains')
      .query()
      .where('agent_uid', agent_uid)
      .firstOrFail()

    return await domain.merge({ agent_uid: null }).save()
  }

  async verify(domain_uid: string) {
    return await this.ctx.app.related('domains').query().where('uid', domain_uid).firstOrFail()
  }
}
