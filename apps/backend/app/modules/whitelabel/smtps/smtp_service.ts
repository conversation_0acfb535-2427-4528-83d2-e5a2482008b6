import { EmailHelper } from '#app/helpers'
import { requestFilterValidator } from '#app/validators'
import env from '#start/env'
import { Infer } from '@vinejs/vine/types'
import { inject } from '@adonisjs/core'
import { HttpContext } from '@adonisjs/core/http'
import { smtpStoreValidator } from '#app/modules/whitelabel/smtps/smtp_validator'

@inject()
export default class SmtpService {
  private ctx: HttpContext

  constructor(ctx: HttpContext) {
    this.ctx = ctx
  }

  async index(filter: Infer<typeof requestFilterValidator>) {
    const page = filter.page || 1
    return this.ctx.app
      .related('smtps')
      .query()
      .filter(filter)
      .paginate(page, env.get('APP_PAGINATION_LIMIT'))
  }

  async show(uid: string) {
    return this.ctx.app.related('smtps').query().where('uid', uid).firstOrFail()
  }

  async store(payload: Infer<typeof smtpStoreValidator>) {
    return this.ctx.app.related('smtps').create(payload)
  }

  async update(uid: string, payload: Infer<typeof smtpStoreValidator>) {
    const smtp = await this.ctx.app.related('smtps').query().where('uid', uid).firstOrFail()

    return await smtp.merge(payload).save()
  }

  async destroy(uid: string) {
    const smtp = await this.ctx.app.related('smtps').query().where('uid', uid).firstOrFail()

    return await smtp.delete()
  }

  async setPrimary(uid: string) {
    const smtp = await this.ctx.app.related('smtps').query().where('uid', uid).firstOrFail()

    await this.ctx.app
      .related('smtps')
      .query()
      .where('uid', '!=', uid)
      .update({ is_primary: false })

    return await smtp.merge({ is_primary: true }).save()
  }

  async assign(payload: { agent_uid: string; smtp_uid: string }) {
    const { agent_uid, smtp_uid } = payload

    const smtp = await this.ctx.app.related('smtps').query().where('uid', smtp_uid).firstOrFail()

    return await smtp.merge({ agent_uid }).save()
  }

  async unassign(agent_uid: string) {
    const smtp = await this.ctx.app.related('smtps').query().where('uid', agent_uid).firstOrFail()

    return await smtp.merge({ agent_uid: null }).save()
  }

  async verify(payload: { smtp_uid: string; recipient: string }) {
    const { smtp_uid, recipient } = payload
    const smtp = await this.ctx.app.related('smtps').query().where('uid', smtp_uid).firstOrFail()

    const isValid = await EmailHelper.testSMTP({
      recipient,
      smtp: {
        host: smtp.host,
        port: smtp.port,
        secure: smtp.secure,
        auth: {
          user: smtp.username,
          pass: smtp.password,
        },
      },
      sender: {
        name: smtp.from_name,
        email: smtp.from_email,
      },
    })

    return isValid ? true : false
  }
}
