import { requestFilterValidator } from '#app/validators'
import { inject } from '@adonisjs/core'
import { HttpContext } from '@adonisjs/core/http'
import SmtpService from '#app/modules/whitelabel/smtps/smtp_service'
import {
  smtpAssignValidator,
  smtpStoreValidator,
} from '#app/modules/whitelabel/smtps/smtp_validator'
import SmtpPresenter from '#app/modules/whitelabel/smtps/smtp_presenter'

@inject()
export default class SmtpsController {
  constructor(private SmtpService: SmtpService) {}

  async index({ response, request }: HttpContext) {
    const payload = await request.validateUsing(requestFilterValidator)

    const smtps = await this.SmtpService.index(payload)

    return response.status(200).json(SmtpPresenter.serializePaginated(smtps))
  }

  async show({ request, response }: HttpContext) {
    const uid = request.param('uid')

    const smtp = await this.SmtpService.show(uid)

    return response.status(200).json(SmtpPresenter.serialize(smtp))
  }

  async store({ request, response }: HttpContext) {
    const payload = await request.validateUsing(smtpStoreValidator)
    const smtp = await this.SmtpService.store(payload)

    return response.status(201).json(SmtpPresenter.serialize(smtp))
  }

  async update({ request, response }: HttpContext) {
    const uid = request.param('uid')

    const payload = await request.validateUsing(smtpStoreValidator)
    const smtp = await this.SmtpService.update(uid, payload)

    return response.status(200).json(SmtpPresenter.serialize(smtp))
  }

  async destroy({ request, response }: HttpContext) {
    const uid = request.param('uid')

    await this.SmtpService.destroy(uid)

    return response.status(202).json({ success: true })
  }

  async assign({ request, response }: HttpContext) {
    const smtp_uid = request.param('uid')

    const { agent_uid } = await request.validateUsing(smtpAssignValidator)

    await this.SmtpService.assign({ agent_uid, smtp_uid })

    return response.status(200).json({ success: true })
  }

  async unassign({ request, response }: HttpContext) {
    const smtpUid = request.param('uid')

    await this.SmtpService.unassign(smtpUid)

    return response.status(200).json({ success: true })
  }

  async setPrimary({ request, response }: HttpContext) {
    const smtpUid = request.param('uid')

    await this.SmtpService.setPrimary(smtpUid)

    return response.status(200).json({ success: true })
  }

  async verify({ request, response, user }: HttpContext) {
    const smtpUid = request.param('uid')
    const recipient = user?.email

    if (!recipient) {
      throw { message: 'Recipient not found' }
    }

    const success = await this.SmtpService.verify({ smtpUid, recipient })

    return response.status(200).json({ success })
  }
}
