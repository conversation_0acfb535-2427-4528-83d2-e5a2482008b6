import { Filterable } from 'adonis-lucid-filter'
import { DateTime } from 'luxon'
import { v4 as uuid } from 'uuid'
import { compose } from '@adonisjs/core/helpers'
import { beforeCreate, column, hasOne, SnakeCaseNamingStrategy } from '@adonisjs/lucid/orm'
import { BaseModel } from '@adonisjs/lucid/orm'
import type { HasOne } from '@adonisjs/lucid/types/relations'
import Agent from '#app/modules/agents/agent_model'
import BaseFilter from '#app/modules/base/base_filter'

export default class SMTP extends compose(BaseModel, Filterable) {
  serializeExtras = false
  static selfAssignPrimaryKey = true
  static namingStrategy = new SnakeCaseNamingStrategy()
  static $filter = () => BaseFilter

  @beforeCreate()
  static createUUID(model: SMTP) {
    model.uid = uuid()
  }

  @hasOne(() => Agent, {
    foreignKey: 'uid',
    localKey: 'agent_uid',
  })
  declare agent: HasOne<typeof Agent>

  @column({
    isPrimary: true,
    meta: { searchable: true, type: 'string', validations: [{ name: 'uuid' }] },
  })
  declare uid: string

  @column({
    meta: {
      searchable: true,
      type: 'string',
      validations: [{ name: 'uuid' }],
    },
  })
  declare app_uid: string

  @column({
    meta: {
      searchable: true,
      type: 'string',
      validations: [{ name: 'uuid' }],
    },
  })
  declare agent_uid: string

  @column({ meta: { searchable: true, type: 'string' } })
  declare host: string

  @column({ meta: { searchable: true, type: 'number' } })
  declare port: number

  @column({ meta: { searchable: true, type: 'string' } })
  declare username: string

  @column({ meta: { searchable: true, type: 'string' } })
  declare password: string

  @column({ meta: { searchable: true, type: 'boolean' } })
  declare secure: boolean

  @column({ meta: { searchable: true, type: 'string' } })
  declare from_email: string

  @column({ meta: { searchable: true, type: 'string' } })
  declare from_name: string

  @column({ meta: { searchable: true, type: 'boolean' } })
  declare is_verified: boolean

  @column({ meta: { searchable: true, type: 'boolean' } })
  declare is_primary: boolean

  @column({ meta: { searchable: true, type: 'object' } })
  declare metadata: { [key: string]: any } | null

  @column.dateTime({ autoCreate: true, meta: { searchable: true, type: 'date' } })
  declare created_at: DateTime

  @column.dateTime({
    autoCreate: true,
    autoUpdate: true,
    meta: { searchable: true, type: 'date' },
  })
  declare updated_at: DateTime

  @column({ serializeAs: null, meta: { searchable: true, type: 'date' } })
  declare deleted_at?: DateTime | null
}
