import router from '@adonisjs/core/services/router'

export default () => {
  const CONTROLLER_PATH = '#app/modules/whitelabel/smtps/smtps_controller'

  router.get('/smtps', `${CONTROLLER_PATH}.index`)
  router.post('/smtps', `${CONTROLLER_PATH}.store`)
  router.get('/smtps/:uid', `${CONTROLLER_PATH}.show`)
  router.put('/smtps/:uid', `${CONTROLLER_PATH}.update`)
  router.delete('/smtps/:uid', `${CONTROLLER_PATH}.destroy`)
  router.post('/smtps/assign/:uid', `${CONTROLLER_PATH}.assign`)
  router.get('/smtps/unassign/:uid', `${CONTROLLER_PATH}.unassign`)
  router.get('/smtps/primary/:uid', `${CONTROLLER_PATH}.setPrimary`)
  router.get('/smtps/verify/:uid', `${CONTROLLER_PATH}.verify`)
}
