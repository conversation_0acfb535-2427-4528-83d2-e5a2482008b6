import { ModelPaginatorContract } from '@adonisjs/lucid/types/model'
import SMTP from '#app/modules/whitelabel/smtps/smtp_model'

export default class SMTPPresenter {
  static serialize(item: SMTP) {
    return {
      uid: item.uid,
      app_uid: item.app_uid,
      agent_uid: item.agent_uid,
      host: item.host,
      port: item.port,
      username: item.username,
      password: item.password,
      secure: item.secure,
      from_email: item.from_email,
      from_name: item.from_name,
      is_verified: item.is_verified,
      is_primary: item.is_primary,
      metadata: item.metadata,
      created_at: item.created_at,
      updated_at: item.updated_at,
    }
  }

  static serializeMany(items: SMTP[]) {
    return items.map((item) => this.serialize(item))
  }

  static serializePaginated(items: ModelPaginatorContract<SMTP>) {
    return {
      page: items.currentPage,
      per_page: items.perPage,
      total: items.total,
      last_page: items.lastPage,
      data: this.serializeMany(items.all()),
    }
  }
}
