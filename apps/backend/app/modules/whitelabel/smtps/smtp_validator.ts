import vine from '@vinejs/vine'

const smtpStoreSchema = vine.object({
  host: vine.string().trim(),
  port: vine.number(),
  username: vine.string().trim(),
  password: vine.string().trim(),
  secure: vine.boolean(),
  from_email: vine.string().trim().email(),
  from_name: vine.string().trim(),
  is_verified: vine.boolean(),
  is_primary: vine.boolean(),
})

const smtpAssignSchema = vine.object({
  agent_uid: vine.string().uuid(),
})

const smtpStoreValidator = vine.compile(smtpStoreSchema)
const smtpAssignValidator = vine.compile(smtpAssignSchema)

export { smtpStoreValidator, smtpAssignValidator }
