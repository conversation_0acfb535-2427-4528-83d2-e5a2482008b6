import router from '@adonisjs/core/services/router'

export default () => {
  const CONTROLLER_PATH = '#app/modules/whitelabel/keys/keys_controller'

  router.get('/keys', `${CONTROLLER_PATH}.index`)
  router.post('/keys', `${CONTROLLER_PATH}.store`)
  router.get('/keys/:uid', `${CONTROLLER_PATH}.show`)
  router.put('/keys/:uid', `${CONTROLLER_PATH}.update`)
  router.delete('/keys/:uid', `${CONTROLLER_PATH}.destroy`)
  router.post('/keys/assign/:uid', `${CONTROLLER_PATH}.assign`)
  router.get('/keys/unassign/:uid', `${CONTROLLER_PATH}.unassign`)
  router.get('/keys/primary/:uid', `${CONTROLLER_PATH}.setPrimary`)
  router.get('/keys/verify/:uid', `${CONTROLLER_PATH}.verify`)
  router.get('/keys/test-all/:uid', `${CONTROLLER_PATH}.testAll`)
  router.post('/keys/test', `${CONTROLLER_PATH}.test`)
}
