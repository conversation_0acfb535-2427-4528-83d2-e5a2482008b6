import { requestFilterValidator } from '#app/validators'
import env from '#start/env'
import { Infer } from '@vinejs/vine/types'
import axios from 'axios'
import { inject } from '@adonisjs/core'
import { HttpContext } from '@adonisjs/core/http'
import Key from '#app/modules/whitelabel/keys/key_model'
import { keyStoreValidator } from '#app/modules/whitelabel/keys/key_validator'

@inject()
export default class KeyService {
  private ctx: HttpContext

  constructor(ctx: HttpContext) {
    this.ctx = ctx
  }

  async index(filter: Infer<typeof requestFilterValidator>) {
    const page = filter.page || 1
    return this.ctx.app
      .related('keys')
      .query()
      .filter(filter)
      .paginate(page, env.get('APP_PAGINATION_LIMIT'))
  }

  async show(uid: string) {
    return this.ctx.app.related('keys').query().where('uid', uid).firstOrFail()
  }

  async store(payload: Infer<typeof keyStoreValidator>) {
    return this.ctx.app.related('keys').create(payload)
  }

  async update(uid: string, payload: Infer<typeof keyStoreValidator>) {
    const key = await this.ctx.app.related('keys').query().where('uid', uid).firstOrFail()

    return await key.merge(payload).save()
  }

  async destroy(uid: string) {
    const key = await this.ctx.app.related('keys').query().where('uid', uid).firstOrFail()

    return await key.delete()
  }

  async setPrimary(uid: string) {
    const key = await this.ctx.app.related('keys').query().where('uid', uid).firstOrFail()

    await this.ctx.app.related('keys').query().where('uid', '!=', uid).update({ is_primary: false })

    return await key.merge({ is_primary: true }).save()
  }

  async assign(payload: { agent_uid: string; key_uid: string }) {
    const { agent_uid, key_uid } = payload

    const key = await this.ctx.app.related('keys').query().where('uid', key_uid).firstOrFail()

    return await key.merge({ agent_uid }).save()
  }

  async unassign(agent_uid: string) {
    const key = await this.ctx.app.related('keys').query().where('uid', agent_uid).firstOrFail()

    return await key.merge({ agent_uid: null }).save()
  }

  async verify(key_uid: string) {
    const key = await this.ctx.app.related('keys').query().where('uid', key_uid).firstOrFail()

    const result = await this.testKey(key.provider, key.key)

    await key.merge({ is_verified: result.success }).save()

    return result
  }

  async testKey(provider: string, key: string) {
    try {
      switch (provider) {
        case 'openai':
          return await this.testOpenAI(key)
        case 'mistral':
          return await this.testMistral(key)
        case 'cohere':
          return await this.testCohere(key)
        case 'anthropic':
          return await this.testAnthropic(key)
        case 'groq':
          return await this.testGroq(key)
        case 'openrouter':
          return await this.testOpenRouter(key)
        default:
          return { success: false, message: 'Unsupported provider' }
      }
    } catch (error) {
      return { success: false, message: error.message || 'Unknown error occurred' }
    }
  }

  private async testOpenAI(key: string) {
    try {
      const response = await axios.get('https://api.openai.com/v1/models', {
        headers: {
          'Authorization': `Bearer ${key}`,
          'Content-Type': 'application/json',
        },
      })

      return { success: response.status === 200, message: 'OpenAI key is valid' }
    } catch (error) {
      return {
        success: false,
        message: error.response?.data?.error?.message || 'Invalid OpenAI key',
      }
    }
  }

  private async testMistral(key: string) {
    try {
      const response = await axios.get('https://api.mistral.ai/v1/models', {
        headers: {
          'Authorization': `Bearer ${key}`,
          'Content-Type': 'application/json',
        },
      })

      return { success: response.status === 200, message: 'Mistral key is valid' }
    } catch (error) {
      return {
        success: false,
        message: error.response?.data?.error?.message || 'Invalid Mistral key',
      }
    }
  }

  private async testCohere(key: string) {
    try {
      const response = await axios.get('https://api.cohere.ai/v1/models', {
        headers: {
          'Authorization': `Bearer ${key}`,
          'Content-Type': 'application/json',
        },
      })

      return { success: response.status === 200, message: 'Cohere key is valid' }
    } catch (error) {
      return { success: false, message: error.response?.data?.message || 'Invalid Cohere key' }
    }
  }

  private async testAnthropic(key: string) {
    try {
      const response = await axios.get('https://api.anthropic.com/v1/models', {
        headers: {
          'x-api-key': key,
          'anthropic-version': '2023-06-01',
          'Content-Type': 'application/json',
        },
      })

      return { success: response.status === 200, message: 'Anthropic key is valid' }
    } catch (error) {
      return {
        success: false,
        message: error.response?.data?.error?.message || 'Invalid Anthropic key',
      }
    }
  }

  private async testGroq(key: string) {
    try {
      const response = await axios.get('https://api.groq.com/v1/models', {
        headers: {
          'Authorization': `Bearer ${key}`,
          'Content-Type': 'application/json',
        },
      })

      return { success: response.status === 200, message: 'Groq key is valid' }
    } catch (error) {
      return { success: false, message: error.response?.data?.error?.message || 'Invalid Groq key' }
    }
  }

  private async testOpenRouter(key: string) {
    try {
      const response = await axios.get('https://openrouter.ai/api/v1/models', {
        headers: {
          'Authorization': `Bearer ${key}`,
          'HTTP-Referer': env.get('APP_URL', 'https://insertchat.com'),
          'X-Title': 'InsertChat',
          'Content-Type': 'application/json',
        },
      })

      return { success: response.status === 200, message: 'OpenRouter key is valid' }
    } catch (error) {
      return {
        success: false,
        message: error.response?.data?.error?.message || 'Invalid OpenRouter key',
      }
    }
  }
}
