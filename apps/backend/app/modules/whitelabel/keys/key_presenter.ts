import { ModelPaginatorContract } from '@adonisjs/lucid/types/model'
import Key from '#app/modules/whitelabel/keys/key_model'

export default class KeyPresenter {
  static serialize(item: Key) {
    return {
      uid: item.uid,
      app_uid: item.app_uid,
      agent_uid: item.agent_uid,
      provider: item.provider,
      key: item.key,
      max_messages_per_month: item.max_messages_per_month,
      is_verified: item.is_verified,
      is_primary: item.is_primary,
      metadata: item.metadata,
      created_at: item.created_at,
      updated_at: item.updated_at,
    }
  }

  static serializeMany(items: Key[]) {
    return items.map((item) => this.serialize(item))
  }

  static serializePaginated(items: ModelPaginatorContract<Key>) {
    return {
      page: items.currentPage,
      per_page: items.perPage,
      total: items.total,
      last_page: items.lastPage,
      data: this.serializeMany(items.all()),
    }
  }
}
