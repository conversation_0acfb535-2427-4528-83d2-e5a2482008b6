import { requestFilterValidator } from '#app/validators'
import { inject } from '@adonisjs/core'
import { HttpContext } from '@adonisjs/core/http'
import KeyService from '#app/modules/whitelabel/keys/key_service'
import {
  keyAssignValidator,
  keyStoreValidator,
  keyTestValidator,
} from '#app/modules/whitelabel/keys/key_validator'
import KeyPresenter from '#app/modules/whitelabel/keys/key_presenter'

@inject()
export default class KeysController {
  constructor(private KeyService: KeyService) {}

  async index({ response, request }: HttpContext) {
    const payload = await request.validateUsing(requestFilterValidator)

    const keys = await this.KeyService.index(payload)

    return response.status(200).json(KeyPresenter.serializePaginated(keys))
  }

  async show({ request, response }: HttpContext) {
    const uid = request.param('uid')

    const key = await this.KeyService.show(uid)

    return response.status(200).json(KeyPresenter.serialize(key))
  }

  async store({ request, response }: HttpContext) {
    const payload = await request.validateUsing(keyStoreValidator)
    const key = await this.KeyService.store(payload)

    return response.status(201).json(KeyPresenter.serialize(key))
  }

  async update({ request, response }: HttpContext) {
    const uid = request.param('uid')

    const payload = await request.validateUsing(keyStoreValidator)
    const key = await this.KeyService.update(uid, payload)

    return response.status(200).json(KeyPresenter.serialize(key))
  }

  async destroy({ request, response }: HttpContext) {
    const uid = request.param('uid')

    await this.KeyService.destroy(uid)

    return response.status(202).json({ success: true })
  }

  async assign({ request, response }: HttpContext) {
    const key_uid = request.param('uid')

    const { agent_uid } = await request.validateUsing(keyAssignValidator)

    await this.KeyService.assign({ agent_uid, key_uid })

    return response.status(200).json({ success: true })
  }

  async unassign({ request, response }: HttpContext) {
    const key_uid = request.param('uid')

    await this.KeyService.unassign(key_uid)

    return response.status(200).json({ success: true })
  }

  async setPrimary({ request, response }: HttpContext) {
    const key_uid = request.param('uid')

    await this.KeyService.setPrimary(key_uid)

    return response.status(200).json({ success: true })
  }

  async verify({ request, response }: HttpContext) {
    const key_uid = request.param('uid')

    const success = await this.KeyService.verify(key_uid)

    return response.status(200).json({ success })
  }

  async test({ request, response }: HttpContext) {
    const { provider, key } = await request.validateUsing(keyTestValidator)

    const result = await this.KeyService.testKey(provider, key)

    return response.status(200).json(result)
  }
}
