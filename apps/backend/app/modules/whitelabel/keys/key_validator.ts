import vine from '@vinejs/vine'

const providersArray = ['openai', 'mistral', 'cohere', 'anthropic', 'groq', 'openrouter']

const storeSchema = vine.object({
  provider: vine.enum(providersArray),
  secret: vine.string().trim(),
  max_messages_per_month: vine.number().nullable(),
  is_verified: vine.boolean(),
  is_primary: vine.boolean(),
})

const assignSchema = vine.object({
  agent_uid: vine.string().uuid(),
})

const testSchema = vine.object({
  provider: vine.string().trim().in(providersArray),
  key: vine.string().trim(),
})

const keyStoreValidator = vine.compile(storeSchema)
const keyAssignValidator = vine.compile(assignSchema)
const keyTestValidator = vine.compile(testSchema)

export { keyStoreValidator, keyAssignValidator, keyTestValidator }
