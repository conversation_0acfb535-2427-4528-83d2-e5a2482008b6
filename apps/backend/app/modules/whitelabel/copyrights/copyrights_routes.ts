import router from '@adonisjs/core/services/router'

export default () => {
  const CONTROLLER_PATH = '#app/modules/whitelabel/copyrights/copyrights_controller'

  router.get('/copyrights', `${CONTROLLER_PATH}.index`)
  router.post('/copyrights', `${CONTROLLER_PATH}.store`)
  router.get('/copyrights/:uid', `${CONTROLLER_PATH}.show`)
  router.put('/copyrights/:uid', `${CONTROLLER_PATH}.update`)
  router.delete('/copyrights/:uid', `${CONTROLLER_PATH}.destroy`)
  router.post('/copyrights/assign/:uid', `${CONTROLLER_PATH}.assign`)
  router.get('/copyrights/unassign/:uid', `${CONTROLLER_PATH}.unassign`)
  router.get('/copyrights/primary/:uid', `${CONTROLLER_PATH}.setPrimary`)
}
