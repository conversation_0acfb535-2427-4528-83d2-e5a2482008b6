import { requestFilterValidator } from '#app/validators'
import env from '#start/env'
import { Infer } from '@vinejs/vine/types'
import { inject } from '@adonisjs/core'
import { HttpContext } from '@adonisjs/core/http'
import { copyrightStoreValidator } from '#app/modules/whitelabel/copyrights/copyright_validator'

@inject()
export default class CopyrightService {
  private ctx: HttpContext

  constructor(ctx: HttpContext) {
    this.ctx = ctx
  }

  async index(filter: Infer<typeof requestFilterValidator>) {
    const page = filter.page || 1
    return this.ctx.app
      .related('copyrights')
      .query()
      .filter(filter)
      .paginate(page, env.get('APP_PAGINATION_LIMIT'))
  }

  async show(uid: string) {
    return this.ctx.app.related('copyrights').query().where('uid', uid).firstOrFail()
  }

  async store(data: Infer<typeof copyrightStoreValidator>) {
    return this.ctx.app.related('copyrights').create(data)
  }

  async update(uid: string, payload: Infer<typeof copyrightStoreValidator>) {
    const copyright = await this.ctx.app
      .related('copyrights')
      .query()
      .where('uid', uid)
      .firstOrFail()

    return await copyright.merge(payload).save()
  }

  async destroy(uid: string) {
    const copyright = await this.ctx.app
      .related('copyrights')
      .query()
      .where('uid', uid)
      .firstOrFail()

    return await copyright.delete()
  }

  async setPrimary(uid: string) {
    const copyright = await this.ctx.app
      .related('copyrights')
      .query()
      .where('uid', uid)
      .firstOrFail()

    await this.ctx.app
      .related('copyrights')
      .query()
      .where('uid', '!=', uid)
      .update({ is_primary: false })

    return await copyright.merge({ is_primary: true }).save()
  }

  async assign(payload: { agent_uid: string; copyright_uid: string }) {
    const { agent_uid, copyright_uid } = payload

    const copyright = await this.ctx.app
      .related('copyrights')
      .query()
      .where('uid', copyright_uid)
      .firstOrFail()

    return await copyright.merge({ agent_uid }).save()
  }

  async unassign(agent_uid: string) {
    const copyright = await this.ctx.app
      .related('copyrights')
      .query()
      .where('agent_uid', agent_uid)
      .firstOrFail()

    return await copyright.merge({ agent_uid: null }).save()
  }
}
