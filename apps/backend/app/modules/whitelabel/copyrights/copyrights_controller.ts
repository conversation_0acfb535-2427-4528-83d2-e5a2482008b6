import { requestFilterValidator } from '#app/validators'
import { inject } from '@adonisjs/core'
import { HttpContext } from '@adonisjs/core/http'
import CopyrightService from '#app/modules/whitelabel/copyrights/copyright_service'
import {
  copyrightAssignValidator,
  copyrightStoreValidator,
} from '#app/modules/whitelabel/copyrights/copyright_validator'
import CopyrightPresenter from '#app/modules/whitelabel/copyrights/copyright_presenter'

@inject()
export default class CopyrightsController {
  constructor(private CopyrightService: CopyrightService) {}

  async index({ response, request }: HttpContext) {
    const payload = await request.validateUsing(requestFilterValidator)

    const copyrights = await this.CopyrightService.index(payload)

    return response.status(200).json(CopyrightPresenter.serializePaginated(copyrights))
  }

  async show({ request, response }: HttpContext) {
    const uid = request.param('uid')

    const copyright = await this.CopyrightService.show(uid)

    return response.status(200).json(CopyrightPresenter.serialize(copyright))
  }

  async store({ request, response }: HttpContext) {
    const payload = await request.validateUsing(copyrightStoreValidator)
    const copyright = await this.CopyrightService.store(payload)

    return response.status(201).json(CopyrightPresenter.serialize(copyright))
  }

  async update({ request, response }: HttpContext) {
    const uid = request.param('uid')

    const payload = await request.validateUsing(copyrightStoreValidator)
    const copyright = await this.CopyrightService.update(uid, payload)

    return response.status(200).json(CopyrightPresenter.serialize(copyright))
  }

  async destroy({ request, response }: HttpContext) {
    const uid = request.param('uid')

    await this.CopyrightService.destroy(uid)

    return response.status(202).json({ success: true })
  }

  async assign({ request, response }: HttpContext) {
    const copyright_uid = request.param('uid')

    const { agent_uid } = await request.validateUsing(copyrightAssignValidator)

    await this.CopyrightService.assign({ agent_uid, copyright_uid })

    return response.status(200).json({ success: true })
  }

  async unassign({ request, response }: HttpContext) {
    const copyright_uid = request.param('uid')

    await this.CopyrightService.unassign(copyright_uid)

    return response.status(200).json({ success: true })
  }

  async setPrimary({ request, response }: HttpContext) {
    const copyright_uid = request.param('uid')

    await this.CopyrightService.setPrimary(copyright_uid)

    return response.status(200).json({ success: true })
  }
}
