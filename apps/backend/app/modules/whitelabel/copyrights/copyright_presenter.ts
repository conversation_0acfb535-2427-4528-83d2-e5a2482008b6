import { ModelPaginatorContract } from '@adonisjs/lucid/types/model'
import Copyright from '#app/modules/whitelabel/copyrights/copyright_model'

export default class CopyrightPresenter {
  static serialize(item: Copyright) {
    return {
      uid: item.uid,
      app_uid: item.app_uid,
      agent_uid: item.agent_uid,
      text: item.text,
      url: item.url,
      is_primary: item.is_primary,
      metadata: item.metadata,
      created_at: item.created_at,
      updated_at: item.updated_at,
    }
  }

  static serializeMany(items: Copyright[]) {
    return items.map((item) => this.serialize(item))
  }

  static serializePaginated(items: ModelPaginatorContract<Copyright>) {
    return {
      page: items.currentPage,
      per_page: items.perPage,
      total: items.total,
      last_page: items.lastPage,
      data: this.serializeMany(items.all()),
    }
  }
}
