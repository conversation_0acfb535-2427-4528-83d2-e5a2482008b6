import vine from '@vinejs/vine'

const storeSchema = vine.object({
  text: vine.string().trim(),
  url: vine.string().trim(),
  is_primary: vine.boolean(),
})

const assignSchema = vine.object({
  agent_uid: vine.string().uuid(),
})

const copyrightStoreValidator = vine.compile(storeSchema)
const copyrightAssignValidator = vine.compile(assignSchema)

export { copyrightStoreValidator, copyrightAssignValidator }
