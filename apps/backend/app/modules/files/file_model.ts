import { StorageHelper } from '#app/helpers/storage_helper'
import { bucketTypeValidator } from '#app/validators'
import type { Infer } from '@vinejs/vine/types'
import { Filterable } from 'adonis-lucid-filter'
import { DateTime } from 'luxon'
import { v4 as uuid } from 'uuid'
import { compose } from '@adonisjs/core/helpers'
import { beforeCreate, belongsTo, column, SnakeCaseNamingStrategy } from '@adonisjs/lucid/orm'
import { BaseModel } from '@adonisjs/lucid/orm'
import type { BelongsTo } from '@adonisjs/lucid/types/relations'
import Agent from '#app/modules/agents/agent_model'
import App from '#app/modules/apps/app_model'
import Chat from '#app/modules/chats/chat_model'
import Message from '#app/modules/messages/message_model'
import BaseFilter from '#app/modules/base/base_filter'

export default class File extends compose(BaseModel, Filterable) {
  serializeExtras = false
  static selfAssignPrimaryKey = true
  static namingStrategy = new SnakeCaseNamingStrategy()
  static $filter = () => BaseFilter

  @beforeCreate()
  static createUUID(model: File) {
    model.uid = uuid()
  }

  @belongsTo(() => App, {
    localKey: 'uid',
    foreignKey: 'app_uid',
  })
  declare app: BelongsTo<typeof App>

  @belongsTo(() => Agent, {
    localKey: 'uid',
    foreignKey: 'agent_uid',
  })
  declare agent: BelongsTo<typeof Agent>

  @belongsTo(() => Chat, {
    localKey: 'uid',
    foreignKey: 'chat_uid',
  })
  declare chat: BelongsTo<typeof Chat>

  @belongsTo(() => Message, {
    localKey: 'uid',
    foreignKey: 'message_uid',
  })
  declare message: BelongsTo<typeof Message>

  @column({
    isPrimary: true,
    meta: { searchable: true, type: 'string', validations: [{ name: 'uuid' }] },
  })
  declare uid: string

  @column({
    meta: {
      searchable: true,
      type: 'string',
      validations: [{ name: 'uuid' }],
    },
  })
  declare app_uid: string

  @column({
    meta: {
      searchable: true,
      type: 'string',
      validations: [{ name: 'uuid' }],
    },
  })
  declare agent_uid: string

  @column({
    meta: {
      searchable: true,
      type: 'string',
      validations: [{ name: 'uuid' }],
    },
  })
  declare chat_uid: string

  @column({
    meta: {
      searchable: true,
      type: 'string',
      validations: [{ name: 'uuid' }],
    },
  })
  declare message_uid: string | null

  @column({ meta: { searchable: true, type: 'string' } })
  declare type: Infer<typeof bucketTypeValidator>

  @column({ meta: { searchable: true, type: 'string' } })
  declare name: string

  @column({ meta: { searchable: true, type: 'string' } })
  declare extname: string

  @column({ meta: { searchable: true, type: 'string' } })
  declare location: string

  @column({ meta: { searchable: true, type: 'number' } })
  declare size: number

  @column({ meta: { searchable: true, type: 'string' } })
  declare checksum: string

  @column({ meta: { searchable: true, type: 'string' } })
  declare transcription: string | null

  @column({ meta: { searchable: true, type: 'string' } })
  declare status: string

  @column({ serializeAs: null, meta: { searchable: true, type: 'object' } })
  declare metadata: { [key: string]: any } | null

  @column.dateTime({ autoCreate: true, meta: { searchable: true, type: 'date' } })
  declare created_at: DateTime

  @column.dateTime({
    autoCreate: true,
    autoUpdate: true,
    meta: { searchable: true, type: 'date' },
  })
  declare updated_at: DateTime

  @column({ serializeAs: null, meta: { searchable: true, type: 'date' } })
  declare deleted_at?: DateTime | null

  async getUrl() {
    const storageClient = new StorageHelper(this.type)

    return await storageClient.getSignedUrl(this.location)
  }
}
