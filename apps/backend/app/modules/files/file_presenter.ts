import { ModelPaginatorContract } from '@adonisjs/lucid/types/model'
import File from '#app/modules/files/file_model'

export default class FilePresenter {
  static serialize(item: File) {
    return {
      uid: item.uid,
      app_uid: item.app_uid,
      agent_uid: item.agent_uid,
      chat_uid: item.chat_uid,
      message_uid: item.message_uid,
      type: item.type,
      name: item.name,
      extname: item.extname,
      location: item.location,
      size: item.size,
      checksum: item.checksum,
      transcription: item.transcription,
      status: item.status,
      created_at: item.created_at,
      updated_at: item.updated_at,
    }
  }

  static serializeMany(items: File[]) {
    return items.map((item) => this.serialize(item))
  }

  static serializePaginated(items: ModelPaginatorContract<File>) {
    return {
      page: items.currentPage,
      per_page: items.perPage,
      total: items.total,
      last_page: items.lastPage,
      data: this.serializeMany(items.all()),
    }
  }
}
