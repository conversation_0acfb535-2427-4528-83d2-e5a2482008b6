import { inject } from '@adonisjs/core'
import { HttpContext } from '@adonisjs/core/http'
import FirebaseService from '#app/modules/firebase/firebase_service'
import {
  firebaseSendNotificationValidator,
  firebaseSubscribeValidator,
} from '#app/modules/firebase/firebase_validator'

@inject()
export default class FirebaseController {
  constructor(protected firebaseService: FirebaseService) {}

  async subscribe({ request, response }: HttpContext) {
    const payload = await request.validateUsing(firebaseSubscribeValidator)

    await this.firebaseService.subscribeToTopic(payload)

    return response.status(200).json({ message: 'Subscribed successfully.' })
  }

  async sendNotification({ request, response }: HttpContext) {
    const payload = await request.validateUsing(firebaseSendNotificationValidator)

    await this.firebaseService.sendNotification(payload)

    return response.status(200).json({ message: 'Notification sent successfully.' })
  }
}
