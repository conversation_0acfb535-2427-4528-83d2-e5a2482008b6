import admin from 'firebase-admin'
import { inject } from '@adonisjs/core'
import { HttpContext } from '@adonisjs/core/http'
import adonisApp from '@adonisjs/core/services/app'

@inject()
export default class FirebaseService {
  private ctx: HttpContext

  constructor(ctx: HttpContext) {
    this.ctx = ctx
  }

  async getFirebaseInstance() {
    return await adonisApp.container.make('firebase')
  }

  async subscribeToTopic(params: { token: string; topic: string }) {
    const { token, topic } = params

    const firebaseInstance = await this.getFirebaseInstance()

    return await firebaseInstance.subscribeToTopic(token, topic)
  }

  async sendNotification(params: { title: string; body: string; topic: string }) {
    const { title, body, topic } = params

    const firebaseInstance = await this.getFirebaseInstance()

    await firebaseInstance.send({
      notification: {
        title,
        body,
      },
      topic,
    })
  }
}
