import { ModelPaginatorContract } from '@adonisjs/lucid/types/model'
import Firebase from '#app/modules/firebase/firebase_model'

export default class FirebasePresenter {
  static serialize(item: Firebase) {
    return {}
  }

  static serializeMany(items: Firebase[]) {
    return items.map((item) => this.serialize(item))
  }

  static serializePaginated(items: ModelPaginatorContract<Firebase>) {
    return {
      page: items.currentPage,
      per_page: items.perPage,
      total: items.total,
      last_page: items.lastPage,
      data: this.serializeMany(items.all()),
    }
  }
}
