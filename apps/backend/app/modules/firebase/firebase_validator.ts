import vine from '@vinejs/vine'

const sendNotificationSchema = vine.object({
  topic: vine.string().trim(),
  title: vine.string().trim(),
  body: vine.string().trim(),
})

const subscribeSchema = vine.object({
  token: vine.string().trim(),
  topic: vine.string().trim(),
})

const updateSchema = vine.object({
  title: vine.string().trim().minLength(6),
  description: vine.string().trim().escape(),
})

const firebaseSendNotificationValidator = vine.compile(sendNotificationSchema)
const firebaseSubscribeValidator = vine.compile(subscribeSchema)
const firebaseUpdateValidator = vine.compile(updateSchema)

export {
  firebaseSendNotificationValidator,
  firebaseSubscribeValidator,
  firebaseUpdateValidator,
}
