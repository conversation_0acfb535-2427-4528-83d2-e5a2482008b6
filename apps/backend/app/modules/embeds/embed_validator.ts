import vine from '@vinejs/vine'

const feedbackSchema = vine.object({
  status: vine.enum(['thumb_up', 'thumb_down']),
})

const verifyPasswordSchema = vine.object({
  password: vine.string().trim(),
})

const createChatSchema = vine.object({
  agent_uid: vine.string().uuid(),
  label: vine.string().trim(),
  loaded_variables: vine.object({}).allowUnknownProperties(),
})

const createMessageSchema = vine.object({
  app_uid: vine.string().uuid(),
  agent_uid: vine.string().uuid(),
  chat_uid: vine.string().uuid(),
  model_uid: vine.string().uuid(),
  input: vine.string().trim(),
  role: vine.enum(['user', 'assistant']),
  images_urls: vine.array(vine.string().trim()).optional(),
  metadata: vine.object({}).allowUnknownProperties().optional(),
})

const embedVerifyPasswordValidator = vine.compile(verifyPasswordSchema)
const embedCreateChatValidator = vine.compile(createChatSchema)
const embedCreateMessageValidator = vine.compile(createMessageSchema)
const embedFeedbackValidator = vine.compile(feedbackSchema)

export {
  embedVerifyPasswordValidator,
  embedCreateChatValidator,
  embedCreateMessageValidator,
  embedFeedbackValidator,
}
