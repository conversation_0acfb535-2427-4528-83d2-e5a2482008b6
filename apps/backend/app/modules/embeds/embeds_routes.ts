import { middleware } from '#start/kernel'
import router from '@adonisjs/core/services/router'

export default () => {
  const ENDPOINT_PREFIX = '/embeds'
  const CONTROLLER_PATH = '#app/modules/embeds/embeds_controller'

  router
    .group(() => {
      // Agents
      router.get('/agents/:agent_uid', `${CONTROLLER_PATH}.getAgent`)
      // Chats
      router.post('/chats', `${CONTROLLER_PATH}.createChat`)
      router.get('/chats/:chat_uid', `${CONTROLLER_PATH}.getChat`)
      // Feedbacks
      router.post('/feedbacks/:message_uid', `${CONTROLLER_PATH}.createFeedback`)
      // Messages
      router.post('/messages', `${CONTROLLER_PATH}.createMessage`).use(middleware.no_timeout())
      // Password
      router.post('/verify-password/:agent_uid', `${CONTROLLER_PATH}.verifyPassword`)
      // Files
      router.post('/store-files/:chat_uid', `${CONTROLLER_PATH}.storeFiles`)
      // Audio
      router.post('/audio/stt/:chat_uid', `${CONTROLLER_PATH}.stt`).use(middleware.no_timeout())
      router.get('/audio/tts/:message_uid', `${CONTROLLER_PATH}.tts`).use(middleware.no_timeout())
    })
    .prefix(`${ENDPOINT_PREFIX}`)
}
