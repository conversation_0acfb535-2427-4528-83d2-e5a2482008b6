import {
  APP_TMP_FOLDER_PATH,
  TRAINING_DOCUMENTS_EXTENSIONS,
  TRAINING_IMAGE_EXTENSIONS,
  TRAINING_MEDIA_EXTENSIONS,
} from '#app/constants'
import { <PERSON><PERSON><PERSON>per, StorageHelper } from '#app/helpers'
import { createWriteStream } from 'node:fs'
import { pipeline } from 'node:stream/promises'
import { v4 as uuid } from 'uuid'
import { inject } from '@adonisjs/core'
import { MultipartFile } from '@adonisjs/core/bodyparser'
import { cuid } from '@adonisjs/core/helpers'
import { HttpContext } from '@adonisjs/core/http'
import Agent from '#app/modules/agents/agent_model'
import EmbedService from '#app/modules/embeds/embed_service'
import {
  embedCreateChatValidator,
  embedCreateMessageValidator,
  embedFeedbackValidator,
  embedVerifyPasswordValidator,
} from '#app/modules/embeds/embed_validator'
import Agent<PERSON>resenter from '#app/modules/agents/agent_presenter'
import Chat<PERSON>resenter from '#app/modules/chats/chat_presenter'
import Feedback<PERSON>resenter from '#app/modules/feedbacks/feedback_presenter'
import MessagePresenter from '#app/modules/messages/message_presenter'

@inject()
export default class EmbedsController {
  constructor(private embedService: EmbedService) {}

  /*
   * TTS
   */
  async tts({ request, response }: HttpContext) {
    const message_uid = request.param('message_uid')

    const ttsResponse = await this.embedService.textToSpeech(message_uid)

    // response.header('Content-Type', 'audio/opus')
    response.header('Content-Type', 'audio/mpeg')
    response.header('Connection', 'keep-alive')
    response.header('Content-Disposition', 'inline; filename="speech.mpeg"')

    return response.stream(ttsResponse)
  }

  /*
   * STT
   */
  async stt({ request, response }: HttpContext) {
    const chat_uid = request.param('chat_uid')

    request.multipart.onFile(
      'file',
      {
        size: '10mb',
        extnames: TRAINING_MEDIA_EXTENSIONS,
      },
      async (multipartStream, reporter) => {
        multipartStream.pause()
        multipartStream.on('data', reporter)

        const extname = FileHelper.getFileExtension(multipartStream.file.clientName)
        const filePath = `${APP_TMP_FOLDER_PATH}/${cuid()}_${uuid()}.${extname}`

        await pipeline(multipartStream, createWriteStream(filePath))

        return { filePath }
      }
    )

    await request.multipart.process()

    const file = request.file('file') as MultipartFile

    if (!file.meta.filePath) {
      throw { message: 'File not found' }
    }

    const text = await this.embedService.speechToText({
      chat_uid,
      file_path: file.meta.filePath,
    })

    return response.json({ text })
  }

  /*
   * Chats
   */
  async getChat({ request, response }: HttpContext) {
    const chat_uid = request.param('chat_uid')

    const { chat, messages } = await this.embedService.getChat(chat_uid)

    return response.status(200).json({
      chat: ChatPresenter.serialize(chat),
      messages: MessagePresenter.serializeMany(messages),
    })
  }

  async createChat({ request, response }: HttpContext) {
    const { agent_uid, label, loaded_variables } =
      await request.validateUsing(embedCreateChatValidator)

    const chat = await this.embedService.createChat({ agent_uid, label, loaded_variables })

    return response.status(201).json(ChatPresenter.serialize(chat))
  }

  /*
   * Agents
   */
  async getAgent({ request, response }: HttpContext) {
    const agent_uid = request.param('agent_uid')

    const agent = await this.embedService.getAgent(agent_uid)

    return response.status(200).json(AgentPresenter.serialize(agent))
  }

  /*
   * Files
   */
  async storeFiles({ request, response }: HttpContext) {
    const chat_uid = request.param('chat_uid')

    request.multipart.onFile(
      'files',
      {
        size: '30mb',
        extnames: [
          ...TRAINING_IMAGE_EXTENSIONS,
          ...TRAINING_DOCUMENTS_EXTENSIONS,
          ...TRAINING_MEDIA_EXTENSIONS,
        ],
      },
      async (multipartStream, reporter) => {
        multipartStream.pause()
        multipartStream.on('data', reporter)

        const storageClient = new StorageHelper('chats')

        const { directUrl, storagePath, extname, size } = await storageClient.saveStream({
          multipartStream,
          basePath: `${chat_uid}`,
        })

        return { directUrl, storagePath, extname, size }
      }
    )

    await request.multipart.process()

    const files = request.allFiles().files as MultipartFile[] | MultipartFile
    const _files = Array.isArray(files) ? files : [files]

    await this.embedService.createFiles({
      chat_uid,
      files: _files,
    })

    const directUrls: string[] = _files.map((file) => file.meta.directUrl)

    return response.status(201).json(directUrls)
  }

  /*
   * Messages
   */
  async createMessage({ request, response }: HttpContext) {
    const { app_uid, agent_uid, chat_uid, model_uid, input, role, images_urls, metadata } =
      await request.validateUsing(embedCreateMessageValidator)

    const stream = await this.embedService.createMessage({
      app_uid,
      agent_uid,
      chat_uid,
      model_uid,
      input,
      role,
      images_urls,
      metadata,
    })

    // No timeout
    request.request.socket.setTimeout(0)
    response.request.socket.setNoDelay(true)
    response.request.socket.setKeepAlive(true, 0)
    response.header('Content-Type', 'text/event-stream')
    response.header('Cache-Control', 'no-cache')
    response.header('Connection', 'keep-alive')
    response.stream(stream, () => {
      const message = 'Unable to serve the stream. Try again!'
      const status = 400

      return [message, status]
    })
  }

  /*
   * Feedback
   */
  async createFeedback({ request, response }: HttpContext) {
    const message_uid = request.param('message_uid')

    const { status } = await request.validateUsing(embedFeedbackValidator)
    const feedback = await this.embedService.createFeedback(message_uid, { status })

    return response.status(201).json(FeedbackPresenter.serialize(feedback))
  }

  /*
   * Password
   */
  async verifyPassword({ request, response }: HttpContext) {
    const agent_uid = request.param('agent_uid')

    const { password } = await request.validateUsing(embedVerifyPasswordValidator)
    const agent = await Agent.findByOrFail('uid', agent_uid)

    if (agent.password === password) {
      return response.status(201).json({ status: true })
    }

    return response.status(201).json({ status: false })
  }
}
