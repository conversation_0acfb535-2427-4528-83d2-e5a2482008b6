import { requestFilterValidator } from '#app/validators'
import env from '#start/env'
import { Infer } from '@vinejs/vine/types'
import { inject } from '@adonisjs/core'
import { HttpContext } from '@adonisjs/core/http'

@inject()
export default class FeedbackService {
  private ctx: HttpContext

  constructor(ctx: HttpContext) {
    this.ctx = ctx
  }

  async index(filter: Infer<typeof requestFilterValidator>) {
    const page = filter.page || 1

    return await this.ctx.app
      .related('feedbacks')
      .query()
      .filter(filter)
      .paginate(page, env.get('APP_PAGINATION_LIMIT'))
  }

  async show(uid: string) {
    return await this.ctx.app.related('feedbacks').query().where('uid', uid).firstOrFail()
  }

  async destroy(uid: string) {
    const feedback = await this.ctx.app.related('feedbacks').query().where('uid', uid).firstOrFail()

    return await feedback.delete()
  }

  async markAsTrained(uid: string) {
    const feedback = await this.ctx.app.related('feedbacks').query().where('uid', uid).firstOrFail()

    return await feedback.merge({ is_trained: true }).save()
  }
}
