import { ModelPaginatorContract } from '@adonisjs/lucid/types/model'
import Feedback from '#app/modules/feedbacks/feedback_model'

export default class FeedbackPresenter {
  static serialize(item: Feedback) {
    return {
      uid: item.uid,
      app_uid: item.app_uid,
      agent_uid: item.agent_uid,
      chat_uid: item.chat_uid,
      message_uid: item.message_uid,
      user_message: item.user_message,
      ai_message: item.ai_message,
      status: item.status,
      is_trained: item.is_trained,
      created_at: item.created_at,
      updated_at: item.updated_at,
    }
  }

  static serializeMany(items: Feedback[]) {
    return items.map((item) => this.serialize(item))
  }

  static serializePaginated(items: ModelPaginatorContract<Feedback>) {
    return {
      page: items.currentPage,
      per_page: items.perPage,
      total: items.total,
      last_page: items.lastPage,
      data: this.serializeMany(items.all()),
    }
  }
}
