import { requestFilterValidator } from '#app/validators'
import { inject } from '@adonisjs/core'
import { HttpContext } from '@adonisjs/core/http'
import FeedbackService from '#app/modules/feedbacks/feedback_service'
import FeedbackPresenter from '#app/modules/feedbacks/feedback_presenter'

@inject()
export default class FeedbacksController {
  constructor(private feedbackService: FeedbackService) {}

  async index({ request, response }: HttpContext) {
    const payload = await request.validateUsing(requestFilterValidator)

    const feedbacks = await this.feedbackService.index(payload)

    return response.status(200).json(FeedbackPresenter.serializePaginated(feedbacks))
  }

  async show({ request, response }: HttpContext) {
    const uid = request.param('uid')

    const feedback = await this.feedbackService.show(uid)

    return response.status(200).json(FeedbackPresenter.serialize(feedback))
  }

  async destroy({ request, response }: HttpContext) {
    const uid = request.param('uid')

    await this.feedbackService.destroy(uid)

    return response.status(202).json({ success: true })
  }

  async markAsTrained({ request, response }: HttpContext) {
    const uid = request.param('uid')

    const feedback = await this.feedbackService.markAsTrained(uid)

    return response.status(200).json(FeedbackPresenter.serialize(feedback))
  }
}
