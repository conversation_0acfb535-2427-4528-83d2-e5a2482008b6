import { ModelPaginatorContract } from '@adonisjs/lucid/types/model'
import Contact from '#app/modules/contacts/contact_model'

export default class ContactPresenter {
  static serialize(item: Contact) {
    return {
      uid: item.uid,
      app_uid: item.app_uid,
      agent_uid: item.agent_uid,
      chat_uid: item.chat_uid,
      external_id: item.external_id,
      first_name: item.first_name,
      last_name: item.last_name,
      email: item.email,
      phone: item.phone,
      address: item.address,
      website: item.website,
      gender: item.gender,
      company: item.company,
      job_title: item.job_title,
      job_role: item.job_role,
      message: item.message,
      metadata: item.metadata,
      created_at: item.created_at,
      updated_at: item.updated_at,
      designation: item.designation,
      initials: item.initials,
    }
  }

  static serializeMany(items: Contact[]) {
    return items.map((item) => this.serialize(item))
  }

  static serializePaginated(items: ModelPaginatorContract<Contact>) {
    return {
      page: items.currentPage,
      per_page: items.perPage,
      total: items.total,
      last_page: items.lastPage,
      data: this.serializeMany(items.all()),
    }
  }
}
