import { Filterable } from 'adonis-lucid-filter'
import { DateTime } from 'luxon'
import { v4 as uuid } from 'uuid'
import { compose } from '@adonisjs/core/helpers'
import string from '@adonisjs/core/helpers/string'
import {
  BaseModel,
  beforeCreate,
  belongsTo,
  column,
  computed,
  SnakeCaseNamingStrategy,
} from '@adonisjs/lucid/orm'
import { LucidRow } from '@adonisjs/lucid/types/model'
import type { BelongsTo } from '@adonisjs/lucid/types/relations'
import Agent from '#app/modules/agents/agent_model'
import App from '#app/modules/apps/app_model'
import Chat from '#app/modules/chats/chat_model'
import BaseFilter from '#app/modules/base/base_filter'

export default class Contact extends compose(BaseModel, Filterable) {
  serializeExtras = false
  static selfAssignPrimaryKey = true
  static namingStrategy = new SnakeCaseNamingStrategy()
  static $filter = () => BaseFilter

  @beforeCreate()
  static createUUID(model: Contact) {
    model.uid = uuid()
  }

  @belongsTo(() => App, {
    localKey: 'uid',
    foreignKey: 'app_uid',
  })
  declare app: BelongsTo<typeof App>

  @belongsTo(() => Agent, {
    localKey: 'uid',
    foreignKey: 'agent_uid',
  })
  declare agent: BelongsTo<typeof Agent>

  @belongsTo(() => Chat, {
    localKey: 'uid',
    foreignKey: 'chat_uid',
  })
  declare chat: BelongsTo<typeof Chat>

  @column({
    isPrimary: true,
    meta: { searchable: true, type: 'string', validations: [{ name: 'uuid' }] },
  })
  declare uid: string

  @column({
    meta: {
      searchable: true,
      type: 'string',
      validations: [{ name: 'uuid' }],
    },
  })
  declare app_uid: string

  @column({
    meta: {
      searchable: true,
      type: 'string',
      validations: [{ name: 'uuid' }],
    },
  })
  declare agent_uid: string

  @column({
    meta: {
      searchable: true,
      type: 'string',
      validations: [{ name: 'uuid' }],
    },
  })
  declare chat_uid: string

  @column({
    meta: { searchable: true, type: 'string', validations: [] },
  })
  declare external_id: string | null

  @column({
    prepare: (value: string) => (value ? string.sentenceCase(value)?.trim() : null),
    consume: (value: string) => (value ? string.sentenceCase(value)?.trim() : null),
    meta: { searchable: true, type: 'string', validations: [] },
  })
  declare first_name: string | null

  @column({
    prepare: (value: string) => (value ? string.sentenceCase(value)?.trim() : null),
    consume: (value: string) => (value ? string.sentenceCase(value)?.trim() : null),
    meta: { searchable: true, type: 'string', validations: [] },
  })
  declare last_name: string | null

  @column({
    prepare: (value: string) => value?.toLowerCase()?.trim(),
    consume: (value: string) => value?.toLowerCase()?.trim(),
    meta: {
      searchable: true,
      type: 'string',
      validations: [{ name: 'email' }],
    },
  })
  declare email: string | null

  @column({
    meta: { searchable: true, type: 'string', validations: [] },
  })
  declare phone: string | null

  @column({
    meta: { searchable: true, type: 'string', validations: [] },
  })
  declare address: string

  @column({
    meta: { searchable: true, type: 'string', validations: [] },
  })
  declare website: string

  @column({
    meta: { searchable: true, type: 'string', validations: [] },
  })
  declare gender: string

  @column({
    meta: { searchable: true, type: 'string', validations: [] },
  })
  declare company: string

  @column({
    meta: { searchable: true, type: 'string', validations: [] },
  })
  declare job_title: string

  @column({
    meta: { searchable: true, type: 'string', validations: [] },
  })
  declare job_role: string

  @column({
    meta: { searchable: true, type: 'string', validations: [] },
  })
  declare message: string

  @column({ meta: { searchable: true, type: 'object' } })
  declare metadata: { [key: string]: any } | null

  @column.dateTime({ autoCreate: true, meta: { searchable: true, type: 'date' } })
  declare created_at: DateTime

  @column.dateTime({
    autoCreate: true,
    autoUpdate: true,
    meta: { searchable: true, type: 'date' },
  })
  declare updated_at: DateTime

  @column({ serializeAs: null, meta: { searchable: true, type: 'date' } })
  declare deleted_at?: DateTime | null

  @computed()
  get designation(): string {
    return `${this.first_name ? this.first_name : ''} ${this.last_name ? this.last_name : ''}`.trim()
  }

  @computed()
  get initials(): string {
    return `${this.first_name ? this.first_name.charAt(0) : ''}${this.last_name ? this.last_name.charAt(0) : ''}`
  }
}
