import vine from '@vinejs/vine'

const storeSchema = vine.object({
  agent_uid: vine.string().uuid(),
  chat_uid: vine.string().uuid(),
  email: vine.string().email().optional(),
  phone: vine.string().optional(),
  first_name: vine.string().optional(),
  last_name: vine.string().optional(),
  address: vine.string().optional(),
  website: vine.string().optional(),
  gender: vine.string().optional(),
  company: vine.string().optional(),
  job_title: vine.string().optional(),
  job_role: vine.string().optional(),
  message: vine.string().optional(),
  external_id: vine.string().optional(),
})

const updateSchema = vine.object({
  ...storeSchema.getProperties(),
})

const contactStoreValidator = vine.compile(storeSchema)
const contactUpdateValidator = vine.compile(updateSchema)

export { contactStoreValidator, contactUpdateValidator }
