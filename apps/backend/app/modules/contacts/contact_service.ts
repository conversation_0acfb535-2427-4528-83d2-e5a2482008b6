import { requestFilterValidator } from '#app/validators'
import env from '#start/env'
import type { Infer } from '@vinejs/vine/types'
import { inject } from '@adonisjs/core'
import { HttpContext } from '@adonisjs/core/http'
import Contact from '#app/modules/contacts/contact_model'
import {
  contactStoreValidator,
  contactUpdateValidator,
} from '#app/modules/contacts/contact_validator'

@inject()
export default class ContactService {
  private ctx: HttpContext

  constructor(ctx: HttpContext) {
    this.ctx = ctx
  }

  async index(filter: Infer<typeof requestFilterValidator>) {
    const page = filter.page || 1

    return await this.ctx.app
      .related('contacts')
      .query()
      .filter(filter)
      .paginate(page, env.get('APP_PAGINATION_LIMIT'))
  }

  async show(uid: string) {
    return await this.ctx.app.related('contacts').query().where('uid', uid).firstOrFail()
  }

  async store(payload: Infer<typeof contactStoreValidator>) {
    return await this.ctx.app.related('contacts').create(payload)
  }

  async update(uid: string, payload: Infer<typeof contactUpdateValidator>) {
    const contact = await this.ctx.app.related('contacts').query().where('uid', uid).firstOrFail()

    return await contact.merge(payload).save()
  }

  async destroy(uid: string) {
    const contact = await this.ctx.app.related('contacts').query().where('uid', uid).firstOrFail()

    return await contact.delete()
  }
}
