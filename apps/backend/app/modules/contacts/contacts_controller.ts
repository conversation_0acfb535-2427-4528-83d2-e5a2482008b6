import { requestFilterValidator } from '#app/validators'
import { inject } from '@adonisjs/core'
import { HttpContext } from '@adonisjs/core/http'
import ContactService from '#app/modules/contacts/contact_service'
import {
  contactStoreValidator,
  contactUpdateValidator,
} from '#app/modules/contacts/contact_validator'
import ContactPresenter from '#app/modules/contacts/contact_presenter'

@inject()
export default class ContactsController {
  constructor(private contactService: ContactService) {}

  async index({ request, response }: HttpContext) {
    const payload = await request.validateUsing(requestFilterValidator)

    const contacts = await this.contactService.index(payload)

    return response.status(200).json(ContactPresenter.serializePaginated(contacts))
  }

  async show({ request, response }: HttpContext) {
    const uid = request.param('uid')

    const contact = await this.contactService.show(uid)

    return response.status(200).json(ContactPresenter.serialize(contact))
  }

  async store({ request, response }: HttpContext) {
    const payload = await request.validateUsing(contactStoreValidator)
    const contact = await this.contactService.store(payload)

    return response.status(201).json(ContactPresenter.serialize(contact))
  }

  async update({ request, response }: HttpContext) {
    const uid = request.param('uid')

    const payload = await request.validateUsing(contactUpdateValidator)
    const contact = await this.contactService.update(uid, payload)

    return response.status(200).json(ContactPresenter.serialize(contact))
  }

  async destroy({ request, response }: HttpContext) {
    const contactUid = request.param('uid')

    await this.contactService.destroy(contactUid)

    return response.status(202).json({ success: true })
  }
}
