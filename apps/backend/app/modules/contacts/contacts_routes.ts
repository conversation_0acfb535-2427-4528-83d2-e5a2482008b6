import router from '@adonisjs/core/services/router'

export default () => {
  const ENDPOINT_PREFIX = '/contacts'
  const CONTROLLER_PATH = '#app/modules/contacts/contacts_controller'

  router
    .group(() => {
      router.get('/', `${CONTROLLER_PATH}.index`)
      router.get('/:uid', `${CONTROLLER_PATH}.show`)
      router.post('/', `${CONTROLLER_PATH}.store`)
      router.put('/:uid', `${CONTROLLER_PATH}.update`)
      router.delete('/:uid', `${CONTROLLER_PATH}.destroy`)
    })
    .prefix(`${ENDPOINT_PREFIX}`)
}
