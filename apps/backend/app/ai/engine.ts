import Li<PERSON><PERSON><PERSON><PERSON><PERSON>per from '#app/ai/helpers/lite_llm_helper'
import Too<PERSON><PERSON><PERSON>ler from '#app/ai/tool_handler'
import ToolService from '#app/ai/tools/tool_service'
import { Readable } from 'node:stream'
import {
  ChatCompletionChunk,
  ChatCompletionContentPart,
  ChatCompletionMessageParam,
} from 'openai/resources/index.mjs'
import { v4 as uuid } from 'uuid'
import { inject } from '@adonisjs/core'
import emitter from '@adonisjs/core/services/emitter'
import Agent from '#app/modules/agents/agent_model'
import App from '#app/modules/apps/app_model'
import Chat from '#app/modules/chats/chat_model'
import Message from '#app/modules/messages/message_model'
import Model from '#app/modules/models/model_model'

@inject()
export default class AiEngine {
  private app: App
  private agent: Agent
  private model: Model
  private chat: Chat
  private stream: Readable
  private toolHandler: ToolHandler

  constructor(params: { app: App; agent: Agent; model: Model; chat: Chat; stream: Readable }) {
    this.stream = params.stream
    this.app = params.app
    this.agent = params.agent
    this.model = params.model
    this.chat = params.chat

    this.toolHandler = new ToolHandler(params.agent, params.chat, this.stream)
  }

  async execute(params: {
    input: string
    role: 'user' | 'assistant'
    imagesUrls: string[]
    metadata: Record<string, any>
  }) {
    const { input, role, imagesUrls, metadata } = params

    // Save the input message
    await Message.create({
      uid: uuid(),
      app_uid: this.app.uid,
      agent_uid: this.agent.uid,
      chat_uid: this.chat.uid,
      model_uid: this.model.uid,
      role,
      output_text: input,
      credits: this.model.credits,
      metadata,
    })

    const tools = await ToolService.getActiveToolsAiSchemas(this.agent.tools)

    let messages = await this.chat.getChatMessagesOpenAiFormat()

    // Add the system prompt
    if (this.agent.prompt_system) {
      messages.unshift({
        role: 'developer',
        content: this.agent.prompt_system,
      })
    }

    // Add the message
    if (role === 'user') {
      let content: ChatCompletionContentPart[] = []

      if (input) {
        content.push({
          type: 'text',
          text: input,
        })
      }

      if (imagesUrls?.length) {
        for (const url of imagesUrls) {
          content.push({
            type: 'image_url',
            image_url: {
              url,
              detail: 'low',
            },
          })
        }
      }

      messages.push({
        role: 'user',
        name: 'user',
        content,
      })
    } else if (role === 'assistant') {
      messages.push({
        role: 'assistant',
        name: 'assistant',
        content: input,
      })
    }

    while (true) {
      const finalToolCalls: Record<number, ChatCompletionChunk.Choice.Delta.ToolCall> = {}
      const finalMessage: ChatCompletionMessageParam = {
        role: 'assistant',
        name: 'assistant',
        content: '',
      }

      const stream = await LiteLlmHelper.createStreamingCompletion({
        app: this.app,
        messages,
        modelName: this.model.model_name,
        temperature: this.agent.temperature,
        tools: tools || undefined,
      })

      for await (const chunk of stream) {
        if (!chunk.choices || !chunk.choices[0]?.delta) {
          continue
        }

        const toolCalls = chunk.choices[0].delta.tool_calls || []
        const content = chunk.choices[0].delta.content || ''

        if (content) {
          finalMessage.content += content

          this.stream.push(JSON.stringify({ content, o: 'append' }))
        }

        for (const toolCall of toolCalls) {
          const { index } = toolCall

          if (!finalToolCalls[index]) {
            finalToolCalls[index] = toolCall
          }

          if (toolCall.function?.arguments) {
            finalToolCalls[index].function!.arguments += toolCall.function.arguments
          }
        }
      }

      if (finalMessage) {
        messages.push(finalMessage)
      }

      if (!Object.keys(finalToolCalls).length) {
        break
      } else {
        messages = await this.toolHandler.handleToolCalls(finalToolCalls, messages)
      }
    }

    emitter.emit('credits:subtract', {
      app: this.app,
      account: null,
      credits: this.model.credits,
    })

    // Save the output message
    await Message.create({
      uid: uuid(),
      app_uid: this.app.uid,
      agent_uid: this.agent.uid,
      chat_uid: this.chat.uid,
      model_uid: this.model.uid,
      role: 'assistant',
      output_text: messages[messages.length - 1].content as string,
      credits: this.model.credits,
      metadata,
    })

    this.stream.push(null)

    return messages
  }
}
