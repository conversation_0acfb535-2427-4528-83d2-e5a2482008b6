import Lite<PERSON><PERSON><PERSON>elper from '#app/ai/helpers/lite_llm_helper'
import { z } from 'zod'
import App from '#app/modules/apps/app_model'

export default class QuestionsCompletion {
  static async generate(params: {
    app: App
    systemPrompt: string
    historyString: string
    lastMessage: string
  }) {
    const { app, historyString, lastMessage } = params

    const text = `Given the following user input <USER_INPUT> and conversation history <CONVERSATION_HISTORY>, analyze and process the input to enhance search and understanding capabilities.

      # 1. Step-Back Analysis (step_back_question_1, step_back_question_2, step_back_question_3):
      - Create three diverse, broader, more general question that addresses the core concept behind the user's specific query.
      - The step-back question should be easier to answer while maintaining relevance.
      - Examples:
        Specific: "What was <PERSON>'s income in 1784?"
        Step-back: "What were <PERSON>'s main sources of income throughout his career?"

        Specific: "Who directed Star Wars in 1977?"
        Step-back: "Who has been involved in directing Star Wars films?"

      # 2. Question/Answer Variations (variation_1, variation_2, variation_3):
      - Create three semantically equivalent but linguistically diverse versions.
      - Maintain the original intent and meaning while varying vocabulary and structure.
      - Ensure variations are contextually appropriate given the conversation history.
      - For statements or expressions that don't require rephrasing, use 'N/A'.
      - Each variation should optimize for different potential search vectors.

      <CONVERSATION_HISTORY>
      ${historyString}
      </CONVERSATION_HISTORY>

      <USER_INPUT>
      ${lastMessage}
      </USER_INPUT>`

    const questionsResponse = await LiteLlmHelper.createStructuredCompletion({
      app,
      text,
      zodSchema: z.object({
        step_back_question_1: z.string().default('N/A'),
        step_back_question_2: z.string().default('N/A'),
        step_back_question_3: z.string().default('N/A'),
        variation_1: z.string().default('N/A'),
        variation_2: z.string().default('N/A'),
        variation_3: z.string().default('N/A'),
      }),
    })

    return questionsResponse
  }
}
