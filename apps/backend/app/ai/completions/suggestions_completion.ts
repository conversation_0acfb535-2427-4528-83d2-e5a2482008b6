import Lite<PERSON><PERSON><PERSON>elper from '#app/ai/helpers/lite_llm_helper'
import { z } from 'zod'
import App from '#app/modules/apps/app_model'

export default class SuggestionsCompletion {
  static async generate(params: {
    app: App
    systemPrompt: string
    historyString: string
    lastMessage: string
  }) {
    const { app, systemPrompt, historyString, lastMessage } = params

    const text = `You are a follow-up question generator. Generate 3 diverse and relevant follow-up questions from the user's perspective.

      Instructions:
      - Act as the user interacting with the AI assistant
      - Create 3 distinct questions in response to the AI's last message in <LAST_AI_ANSWER>
      - Keep questions concise (under 100 characters)
      - Make each question unique in topic/perspective
      - Match the conversation's language style (default to English if unclear)
      - Consider the context provided in <SYSTEM_PROMPT>
      - Focus on questions that progress the conversation meaningfully
      - Avoid simple clarifications or repetitive questions
      - Questions should be practical and actionable

      Remember: Questions should feel natural and help achieve the user's goals based on the conversation history.

      <SYSTEM_PROMPT>
      ${systemPrompt}
      </SYSTEM_PROMPT>

      <CONVERSATION_HISTORY>
      ${historyString}
      </CONVERSATION_HISTORY>

      <LAST_AI_ANSWER>
      ${lastMessage}
      </LAST_AI_ANSWER>`

    const suggestionsResponse = await LiteLlmHelper.createStructuredCompletion({
      app,
      text,
      zodSchema: z.object({
        suggestion_1: z.string().max(100),
        suggestion_2: z.string().max(100),
        suggestion_3: z.string().max(100),
      }),
    })

    return suggestionsResponse
  }
}
