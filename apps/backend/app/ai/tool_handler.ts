import ToolService from '#app/ai/tools/tool_service'
import { Readable } from 'node:stream'
import { ChatCompletionChunk, ChatCompletionMessageParam } from 'openai/resources/index.mjs'
import Agent from '#app/modules/agents/agent_model'
import Chat from '#app/modules/chats/chat_model'

export default class ToolHandler {
  constructor(
    private agent: Agent,
    private chat: Chat,
    private stream: Readable
  ) {}

  async handleToolCalls(
    toolCalls: Record<number, ChatCompletionChunk.Choice.Delta.ToolCall>,
    messages: ChatCompletionMessageParam[]
  ) {
    const updatedMessages = [...messages]

    for (const toolCall of Object.values(toolCalls)) {
      try {
        if (!toolCall.function?.name || !toolCall?.id) {
          continue
        }

        const toolResponse = await ToolService.executeTool({
          composed_name: toolCall.function.name,
          args: JSON.parse(toolCall.function?.arguments || '{}'),
          context: {
            agent: this.agent,
            chat: this.chat,
          },
        })

        updatedMessages.push({
          tool_call_id: toolCall.id,
          role: 'tool',
          content: JSON.stringify(toolResponse),
        })
      } catch (error) {
        console.error(error)

        this.stream.push({
          error: {
            message: error instanceof Error ? error.message : 'An unknown error occurred',
            tool: toolCall?.function?.name || '',
          },
        })
      }
    }

    return updatedMessages
  }
}
