import env from '#start/env'
import axios, { AxiosInstance } from 'axios'
import fs from 'node:fs'
import { Readable } from 'node:stream'
import OpenAI from 'openai'
import { zodResponseFormat } from 'openai/helpers/zod'
import { ChatCompletionMessageParam, ChatCompletionTool } from 'openai/resources/index.mjs'
import { z } from 'zod'
import App from '#app/modules/apps/app_model'

export default class LiteLlmHelper {
  static getOpenAiClient(app: App): OpenAI {
    return new OpenAI({
      apiKey: env.get('GATEWAY_API_KEY'),
      baseURL: env.get('GATEWAY_API_URL'),
      organization: app?.uid,
      project: app?.uid,
    })
  }

  static getAxiosClient(app: App): AxiosInstance {
    return axios.create({
      baseURL: env.get('GATEWAY_API_URL'),
      headers: {
        'Authorization': `Bearer ${env.get('GATEWAY_API_KEY')}`,
        'Content-Type': 'application/json',
      },
    })
  }

  static async createCompletion(params: {
    app: App
    modelName: string
    text: string
    isJson: boolean
    kwargs: { [key: string]: any }
  }): Promise<object | string> {
    const { app, modelName, text, isJson, kwargs } = params

    const client = LiteLlmHelper.getOpenAiClient(app)

    let _kwargs = { ...kwargs }

    if (isJson) {
      _kwargs.response_format = { type: 'json_object' }
    }

    const completion = await client.completions.create({
      prompt: text,
      model: modelName || env.get('GATEWAY_DEFAULT_MODEL'),
      stream: false,
      user: app?.uid,
      ..._kwargs,
    })

    const content = completion?.choices?.[0]?.text

    return isJson ? JSON.parse(content) : content
  }

  static async createStructuredCompletion(params: {
    app: App
    modelName?: string
    text: string
    zodSchema: z.ZodTypeAny
  }): Promise<object> {
    const { app, modelName, text, zodSchema } = params

    const client = LiteLlmHelper.getOpenAiClient(app)

    const completion = await client.beta.chat.completions.parse({
      model: modelName || env.get('GATEWAY_DEFAULT_MODEL'),
      messages: [{ role: 'user', content: text }],
      stream: false,
      user: app?.uid,
      response_format: zodResponseFormat(zodSchema, 'data'),
    })

    const content = completion?.choices?.[0]?.message.parsed

    return typeof content === 'string' ? JSON.parse(content) : content
  }

  static async createStreamingCompletion(params: {
    app: App
    messages: ChatCompletionMessageParam[]
    modelName: string
    temperature: number
    tools: ChatCompletionTool[] | undefined
    kwargs?: { [key: string]: any }
  }) {
    const { app, messages, modelName, temperature, tools, kwargs } = params
    const client = LiteLlmHelper.getOpenAiClient(app)
    const _kwargs = { ...kwargs }

    const stream = await client.chat.completions.create({
      messages,
      model: modelName || env.get('GATEWAY_DEFAULT_MODEL'),
      temperature,
      tools,
      stream: true,
      store: true,
      user: app?.uid,
      ..._kwargs,
    })

    // return stream.toReadableStream()
    return stream
  }

  static async createEmbeddings(params: {
    app: App
    texts: string[]
    modelName: string
  }): Promise<{ index: number; embedding: number[]; chunk: string }[]> {
    const { texts, modelName, app } = params

    const client = LiteLlmHelper.getOpenAiClient(app)

    const embeddingsResponse = await client.embeddings.create({
      input: texts,
      model: modelName,
      dimensions: 1536,
      user: app?.uid,
    })

    return embeddingsResponse.data.map((embedding, index) => ({
      index,
      embedding: embedding.embedding,
      chunk: texts[index],
    }))
  }

  static async rerank(params: {
    app: App
    query: string
    documents: string[]
    topN: number
  }): Promise<string> {
    const { app, query, documents, topN } = params

    const client = LiteLlmHelper.getAxiosClient(app)

    const rerankResponse = await client.post('/v2/rerank', {
      query,
      model: env.get('GATEWAY_RERANK_MODEL'),
      topN,
      returnDocuments: false,
      documents: documents,
    })

    return rerankResponse.data
  }

  static async textToSpeech(params: {
    app: App
    input: string
    voice: 'alloy' | 'ash' | 'coral' | 'echo' | 'fable' | 'onyx' | 'nova' | 'sage' | 'shimmer'
  }): Promise<Readable> {
    const { input, app, voice } = params

    const client = LiteLlmHelper.getOpenAiClient(app)

    const speechResponse = await client.audio.speech.create({
      model: env.get('GATEWAY_TTS_MODEL'),
      input,
      voice,
      speed: 1.15,
      response_format: 'opus',
    })

    return speechResponse.body as Readable
  }

  static async speechToText(params: { app: App; filePath: string }) {
    const { app, filePath } = params

    if (!filePath) {
      throw { message: 'File not found' }
    }

    const client = LiteLlmHelper.getOpenAiClient(app)

    const transcriptionResponse = await client.audio.transcriptions.create({
      file: fs.createReadStream(filePath),
      model: env.get('GATEWAY_STT_MODEL'),
    })

    return transcriptionResponse.text
  }
}
