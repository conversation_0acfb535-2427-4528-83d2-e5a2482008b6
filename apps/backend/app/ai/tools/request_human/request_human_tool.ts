import { RequestHumanService } from '#app/ai/tools/request_human/request_human_service'
import { z } from 'zod'
import Agent from '#app/modules/agents/agent_model'
import Chat from '#app/modules/chats/chat_model'
import Tool from '#app/modules/tools/tool_model'

export const requestHuman = {
  request_human: {
    description: 'Request human assistance when the AI cannot help or the issue is too complex',
    triggers: [
      'I need to speak with a human',
      'Can I talk to a real person?',
      'This is too complex for AI',
      'I want to speak with customer service',
    ],
    schema: z.object({
      reason: z.string().describe('Reason for requesting human assistance'),
    }),
    execute: async (context: { agent: Agent; chat: Chat; tool: Tool }, args: any) => {
      const requestHumanService = new RequestHumanService(context)

      return await requestHumanService.requestHuman(args)
    },
  },
}
