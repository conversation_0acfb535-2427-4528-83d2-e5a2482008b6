import ToolService from '#app/ai/tools/tool_service'
import Agent from '#app/modules/agents/agent_model'
import Chat from '#app/modules/chats/chat_model'
import Tool from '#app/modules/tools/tool_model'
import BaseToolService from '#app/modules/base/base_tool_service'

export class RequestHumanService extends BaseToolService {
  constructor(context: { agent: Agent; chat: Chat; tool: Tool }) {
    super(context)
  }

  async requestHuman(args: { reason: string }) {
    try {
      return 'A human agent will assist you shortly.'
    } catch (error) {
      console.error(error)

      return ToolService.messages.error
    }
  }
}
