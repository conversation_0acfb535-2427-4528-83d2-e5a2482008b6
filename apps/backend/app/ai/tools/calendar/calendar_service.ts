import Agent from '#app/modules/agents/agent_model'
import Chat from '#app/modules/chats/chat_model'
import Tool from '#app/modules/tools/tool_model'
import BaseToolService from '#app/modules/base/base_tool_service'

export class CalendarService extends BaseToolService {
  private url: string

  constructor(context: { agent: Agent; chat: Chat; tool: Tool }) {
    super(context)

    const { agent, chat, tool } = context

    this.url = tool?.config?.url as string
  }

  getCalendar() {
    return `<iframe src="${this.url}" width="100%" height="700" allowtransparency="true" frameborder="0" allow="camera; microphone; autoplay; encrypted-media;" sandbox="allow-popups allow-popups-to-escape-sandbox allow-same-origin allow-scripts"></iframe>`
  }
}
