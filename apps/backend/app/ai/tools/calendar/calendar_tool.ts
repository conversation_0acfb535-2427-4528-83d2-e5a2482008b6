import { CalendarService } from '#app/ai/tools/calendar/calendar_service'
import { z } from 'zod'
import Agent from '#app/modules/agents/agent_model'
import Chat from '#app/modules/chats/chat_model'
import Tool from '#app/modules/tools/tool_model'

export const calendar = {
  get_calendar: {
    description: 'Get the calendar booking form',
    triggers: [
      'I want to book a meeting.',
      'Can I schedule a meeting for next Friday at 10 AM?',
      'I need to set up a time to discuss this project',
    ],
    schema: z.object({}),
    execute: async (context: { agent: Agent; chat: Chat; tool: Tool }, args: any) => {
      const calendarService = new CalendarService(context)

      return calendarService.getCalendar()
    },
  },
}
