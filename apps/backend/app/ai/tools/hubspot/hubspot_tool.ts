import { HubspotService } from '#app/ai/tools/hubspot/hubspot_service'
import { z } from 'zod'
import Agent from '#app/modules/agents/agent_model'
import Chat from '#app/modules/chats/chat_model'
import Tool from '#app/modules/tools/tool_model'

export const hubspot = {
  capture_lead: {
    description:
      'Capture contact information when the user wants to be contacted later or follow up at another time.',
    triggers: [
      "I'll reach out later if I need more help.",
      'How can I contact support?',
      'Can I contact someone later?',
    ],
    schema: z.object({
      email: z.string().email('Invalid email address').describe('Contact email address'),
      firstName: z.string().min(1, 'First name is required').describe('Contact first name'),
      lastName: z.string().optional().nullable().describe('Contact last name (optional)'),
      phone: z.string().optional().nullable().describe('Contact phone number (optional)'),
      company: z.string().optional().nullable().describe('Contact company name (optional)'),
    }),
    execute: async (context: { agent: Agent; chat: Chat; tool: Tool }, args: any) => {
      const hubspotService = new HubspotService(context)

      return await hubspotService.captureLead(args)
    },
  },
  create_ticket: {
    description:
      "Create a support ticket when the issue needs human assistance, such as complex problems that can't be resolved by AI, issues requiring follow-up, requests needing specialized expertise.",
    triggers: [
      'This problem is more complex than I thought. Can someone look into it?',
      'I need more specialized help with this issue.',
      'Can you create a ticket for this problem so someone can follow up?',
    ],
    schema: z.object({
      subject: z.string().min(1, 'Subject is required').describe('Title or summary of the ticket'),
      description: z
        .string()
        .min(1, 'Description is required')
        .describe('Detailed explanation of the issue or request'),
      email: z.string().email('Invalid email address').describe('Contact email address'),
      firstName: z
        .string()
        .min(1, 'First name is required')
        .describe('First name of the requester'),
      lastName: z.string().optional().nullable().describe('Last name of the requester (optional)'),
      phone: z.string().optional().nullable().describe('Contact phone number (optional)'),
      company: z.string().optional().nullable().describe('Company name (optional)'),
    }),
    execute: async (context: { agent: Agent; chat: Chat; tool: Tool }, args: any) => {
      const hubspotService = new HubspotService(context)

      return await hubspotService.createTicket(args)
    },
  },
}
