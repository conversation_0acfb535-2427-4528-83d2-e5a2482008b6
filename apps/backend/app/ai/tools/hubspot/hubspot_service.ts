import ToolService from '#app/ai/tools/tool_service'
import axios from 'axios'
import string from '@adonisjs/core/helpers/string'
import emitter from '@adonisjs/core/services/emitter'
import Agent from '#app/modules/agents/agent_model'
import Chat from '#app/modules/chats/chat_model'
import Tool from '#app/modules/tools/tool_model'
import BaseToolService from '#app/modules/base/base_tool_service'

export class HubspotService extends BaseToolService {
  private apiKey: string
  private apiUrl: string
  private emails: string[]

  constructor(context: { agent: Agent; chat: Chat; tool: Tool }) {
    super(context)

    this.apiKey = this.tool?.config?.api_key as string
    this.apiUrl = this.tool?.config?.api_url as string
    this.emails = this.tool?.config?.emails as string[]
  }

  async captureLead(args: {
    email: string
    first_name: string
    last_name?: string | null
    phone?: string | null
    company?: string | null
  }) {
    try {
      const { email, first_name, last_name, phone, company } = args
      const { uid, app_uid } = this.chat
      const headers = {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json',
      }
      const associations = []

      // Create contact
      try {
        const contactResponse = await axios.get(
          `https://api.hubapi.com/crm/v3/objects/contacts/${email}?idProperty=email`,
          {
            headers,
          }
        )

        if (contactResponse?.data?.id) {
          return ToolService.messages.emailed
        }
      } catch (error) {
        console.error(error)
      }

      // Create company
      try {
        if (company) {
          let companyId = null

          const companiesResponse = await axios.get(
            `https://api.hubapi.com/crm/v3/objects/companies`,
            {
              headers,
            }
          )

          const companyMatch = (companiesResponse.data?.results || []).find(
            (c: any) => c.properties.name?.toLowerCase() === company.toLowerCase()
          )

          if (companyMatch?.id) {
            companyId = companyMatch?.id
          } else {
            const companyResponse = await axios.post(
              'https://api.hubapi.com/crm/v3/objects/companies',
              {
                properties: {
                  name: string.sentenceCase(company),
                  domain: null,
                },
              },
              { headers }
            )

            companyId = companyResponse.data.id
          }

          if (companyId) {
            associations.push({
              to: {
                id: companyId,
              },
              types: [
                {
                  associationCategory: 'HUBSPOT_DEFINED',
                  associationTypeId: 279, // Contact to company
                },
              ],
            })
          }
        }
      } catch (error) {
        console.error(error)
      }

      // Create contact
      await axios.post(
        `https://api.hubapi.com/crm/v3/objects/contacts`,
        {
          properties: {
            contact: {
              email,
              firstname: first_name,
              lastname: last_name || undefined,
              phone: phone || undefined,
              company: company || undefined,
            },
          },
          associations,
        },
        { headers }
      )

      await this.chat.load('widget')

      const [savedContact] = await Promise.all([
        this.chat.related('contacts').create({
          email,
          first_name,
          last_name: last_name || undefined,
          phone: phone || undefined,
          company: company || undefined,
          chat_uid: uid,
          app_uid,
        }),
        this.chat
          .merge({
            label: `${first_name} ${last_name || ''}`,
            has_contact: true,
          })
          .save(),
      ])

      if (this.emails && this.emails.length) {
        emitter.emit('email:send', {
          templateName: 'new_lead',
          to: this.emails.map((item) => ({ email: item })),
          cc: [],
          cci: [],
          subject: '🧠 New lead',
          appUid: this.chat?.app_uid,
          agentUid: this.chat?.agent_uid,
          variables: {
            agentName: this.chat?.agent?.label || '',
            chatLabel: this.chat?.label || '',
            contact: savedContact,
          },
        })
      }

      return ToolService.messages.emailed
    } catch (error) {
      console.error(error)
      return ToolService.messages.error
    }
  }

  async createTicket(args: {
    subject: string
    description: string
    email: string
    first_name: string
    last_name?: string | null
    phone?: string | null
    company?: string | null
  }) {
    try {
      const { email, first_name, last_name, phone, company, subject, description } = args
      const { uid, app_uid } = this.chat
      const associations = []
      const headers = {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json',
      }

      // Create contact
      try {
        const contactResponse = await axios
          .get(`https://api.hubapi.com/crm/v3/objects/contacts/${email}?idProperty=email`, {
            headers,
          })
          .catch(() =>
            axios.post(
              `https://api.hubapi.com/crm/v3/objects/contacts`,
              {
                properties: {
                  email,
                  firstname: first_name,
                  lastname: last_name || undefined,
                  phone: phone || undefined,
                  company: company || undefined,
                },
              },
              { headers }
            )
          )

        if (contactResponse?.data?.id) {
          associations.push({
            to: { id: contactResponse.data.id },
            types: [
              {
                associationCategory: 'HUBSPOT_DEFINED',
                associationTypeId: 16, // Ticket to contact
              },
            ],
          })
        }
      } catch (error) {
        console.error(error)
      }

      // Create company
      try {
        if (company) {
          let companyId = null

          const companiesResponse = await axios.get(
            `https://api.hubapi.com/crm/v3/objects/companies`,
            {
              headers,
            }
          )

          const companyMatch = (companiesResponse.data?.results || []).find(
            (c: any) => c.properties.name?.toLowerCase() === company.toLowerCase()
          )

          if (companyMatch?.id) {
            companyId = companyMatch?.id
          } else {
            const companyResponse = await axios.post(
              'https://api.hubapi.com/crm/v3/objects/companies',
              {
                properties: {
                  name: string.sentenceCase(company),
                  domain: null,
                },
              },
              { headers }
            )

            companyId = companyResponse.data.id
          }

          if (companyId) {
            associations.push({
              to: {
                id: companyId,
              },
              types: [
                {
                  associationCategory: 'HUBSPOT_DEFINED',
                  associationTypeId: 339, // Ticket to company
                },
              ],
            })
          }
        }
      } catch (error) {
        console.error(error)
      }

      await axios.post(
        `https://api.hubapi.com/crm/v3/objects/tickets`,
        {
          properties: {
            subject,
            content: description,
            hs_pipeline: 0,
            hs_pipeline_stage: 1,
          },
          associations,
        },
        {
          headers,
        }
      )

      await this.chat.load('widget')

      const [savedContact] = await Promise.all([
        this.chat.related('contacts').create({
          email,
          first_name,
          last_name: last_name || undefined,
          phone: phone || undefined,
          company: company || undefined,
          chat_uid: uid,
          app_uid,
        }),
        this.chat
          .merge({
            label: `${first_name} ${last_name || ''}`,
            has_contact: true,
          })
          .save(),
      ])

      // Send email
      emitter.emit('email:send', {
        templateName: 'new_ticket',
        to: this.emails.map((item) => ({ email: item })) || [],
        cc: [],
        cci: [],
        subject: '🧠 New ticket',
        appUid: this.chat?.app_uid,
        agentUid: this.chat?.agent_uid,
        variables: {
          agentName: this.chat?.agent?.label || '',
          chatLabel: this.chat?.label || '',
          contact: savedContact,
        },
      })

      return ToolService.messages.emailed
    } catch (error) {
      console.error(error)
      return ToolService.messages.error
    }
  }
}
