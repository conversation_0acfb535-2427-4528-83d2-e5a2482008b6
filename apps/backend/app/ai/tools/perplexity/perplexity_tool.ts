import { PerplexityService } from '#app/ai/tools/perplexity/perplexity_service'
import { z } from 'zod'
import Agent from '#app/modules/agents/agent_model'
import Chat from '#app/modules/chats/chat_model'
import Tool from '#app/modules/tools/tool_model'

export const perplexity = {
  search: {
    description: 'Search for accurate and up-to-date information using Perplexity AI',
    triggers: [
      'What is the latest information about',
      'Can you find accurate details about',
      'Search for current information on',
      'What are the facts about',
    ],
    schema: z.object({
      query: z.string().describe('The search query to find accurate information'),
    }),
    execute: async (context: { agent: Agent; chat: Chat; tool: Tool }, args: any) => {
      const perplexityService = new PerplexityService(context)

      return await perplexityService.search(args)
    },
  },
}
