import Lite<PERSON><PERSON><PERSON>elper from '#app/ai/helpers/lite_llm_helper'
import ToolService from '#app/ai/tools/tool_service'
import Agent from '#app/modules/agents/agent_model'
import Chat from '#app/modules/chats/chat_model'
import Tool from '#app/modules/tools/tool_model'
import BaseToolService from '#app/modules/base/base_tool_service'

export class PerplexityService extends BaseToolService {
  private apiKey: string
  private apiUrl: string

  constructor(context: { agent: Agent; chat: Chat; tool: Tool }) {
    super(context)

    this.apiKey = this.tool?.config?.api_key as string
    this.apiUrl = this.tool?.config?.api_url as string
  }

  async search(args: { query: string }) {
    try {
      const { query } = args

      await this.chat.load('app')
      const { app } = this.chat

      const completionResponse = await LiteLlmHelper.createCompletion({
        app,
        text: query,
        modelName: 'llama-3.1-sonar-large-128k-online',
        isJson: false,
        kwargs: {},
      })

      return completionResponse
    } catch (error) {
      console.error(error)

      return ToolService.messages.error
    }
  }
}
