import { ShopifyService } from '#app/ai/tools/shopify/shopifyService'
import { z } from 'zod'
import Agent from '#app/modules/agents/agent_model'
import Chat from '#app/modules/chats/chat_model'
import Tool from '#app/modules/tools/tool_model'

export const shopify = {
  check_order_status: {
    description:
      'Check order status using customer email and order number when they ask about their order or want to track a package.',
    triggers: [
      "What's the status of my order?",
      "Where's my package?",
      'I have my order number; how do I check it?',
      'Can I check my order status with my email?',
    ],
    schema: z.object({
      email: z
        .string()
        .email('Invalid email address')
        .describe('Email address associated with the order'),
      order_number: z
        .string()
        .min(1, 'Order number is required')
        .describe('Order number to check the status'),
    }),
    execute: async (context: { agent: Agent; chat: Chat; tool: Tool }, args: any) => {
      const shopifyService = new ShopifyService(context)

      return await shopifyService.checkOrderStatus(args)
    },
  },
  track_shipment: {
    description:
      'Track a shipment using a tracking code when users ask about shipment status, provide a tracking number, or request package location information.',
    triggers: [
      'Can I track my shipment?',
      'How can I check where my package is?',
      'I have a tracking number; how do I use it?',
      'Where is my package right now?',
    ],
    schema: z.object({
      tracking_code: z
        .string()
        .min(1, 'Tracking code is required')
        .describe('The tracking code provided by the shipping carrier to track your package'),
    }),
    execute: async (context: { agent: Agent; chat: Chat; tool: Tool }, args: any) => {
      const shopifyService = new ShopifyService(context)

      return await shopifyService.trackShipment(args)
    },
  },
  view_product_details: {
    description:
      'View detailed product information when users ask about specific products, their features, prices, or availability.',
    triggers: [
      'Can I get more info on this product?',
      'What are the details for [product name]?',
      "What's the price of [product name]?",
    ],
    schema: z.object({
      product_identifier: z
        .string()
        .min(1, 'Product identifier is required')
        .describe('Product ID or name'),
    }),
    execute: async (context: { agent: Agent; chat: Chat; tool: Tool }, args: any) => {
      const shopifyService = new ShopifyService(context)

      return await shopifyService.viewProductDetails(args)
    },
  },
  list_products: {
    description:
      'Lists available products in the Shopify store when users ask about products, specific categories, or show shopping intent.',
    triggers: [
      'What products do you have?',
      'What electronics do you have in stock?',
      "I'm looking to buy something; what do you have?",
    ],
    schema: z.object({}),
    execute: async (context: { agent: Agent; chat: Chat; tool: Tool }, args: any) => {
      const shopifyService = new ShopifyService(context)
      return await shopifyService.listProducts()
    },
  },
  check_refund_status: {
    description:
      'Check refund status for an order using customer email and order number. Only use when customer explicitly asks about a refund status and is willing to provide their order details.',
    triggers: [
      "What's the status of my refund?",
      'When should I expect my refund?',
      'I have my order number; how do I check my refund?',
    ],
    schema: z.object({
      email: z
        .string()
        .email('Invalid email address')
        .describe('Email address associated with the order'),
      order_number: z
        .string()
        .min(1, 'Order number is required')
        .describe('Order number to check refund status'),
    }),
    execute: async (context: { agent: Agent; chat: Chat; tool: Tool }, args: any) => {
      const shopifyService = new ShopifyService(context)

      return await shopifyService.checkRefundStatus(args)
    },
  },
  update_customer_profile: {
    description:
      'Update customer profile information when they request profile changes or verify updates',
    triggers: [
      'I want to update my profile.',
      'I need to change my email.',
      'How can I edit my account info?',
      'Did my last change go through?',
    ],
    schema: z.object({
      email: z
        .string()
        .email('Invalid email address')
        .describe('Email address associated with your customer profile'),
      first_name: z.string().min(1, 'First name is required').describe('Your first name'),
      last_name: z.string().min(1, 'Last name is required').describe('Your last name'),
      phone: z.string().optional().nullable().describe('Your phone number'),
    }),
    execute: async (context: { agent: Agent; chat: Chat; tool: Tool }, args: any) => {
      const shopifyService = new ShopifyService(context)

      return await shopifyService.updateCustomerProfile(args)
    },
  },
  request_return: {
    description:
      'Handle return requests when customers ask to return an order, provide an order number, or inquire about return policies.',
    triggers: [
      'I want to return my order.',
      'I need to return order [order number].',
      'Can I return this item?',
    ],
    schema: z.object({
      email: z
        .string()
        .email('Invalid email address')
        .describe('Email address associated with the order'),
      order_number: z
        .string()
        .min(1, 'Order number is required')
        .describe('Order number for the item you want to return'),
      reason: z
        .string()
        .min(1, 'Return reason is required')
        .describe('Reason for requesting the return'),
    }),
    execute: async (context: { agent: Agent; chat: Chat; tool: Tool }, args: any) => {
      const shopifyService = new ShopifyService(context)

      return await shopifyService.requestReturn(args)
    },
  },
  view_store_policies: {
    description:
      'Retrieve store policies (return, shipping, privacy) when users ask about policies, returns, shipping details, or privacy information.',
    triggers: [
      'What are your return policies?',
      'How do I return an item?',
      'What are your shipping options?',
    ],
    schema: z.object({
      policy_type: z
        .enum(['return', 'shipping', 'privacy'])
        .describe(
          'The type of policy to view - return policy for refund information, shipping policy for delivery details, or privacy policy for data handling practices'
        ),
    }),
    execute: async (context: { agent: Agent; chat: Chat; tool: Tool }, args: any) => {
      const shopifyService = new ShopifyService(context)

      return await shopifyService.viewStorePolicies(args)
    },
  },
}
