import axios from 'axios'
import Agent from '#app/modules/agents/agent_model'
import Chat from '#app/modules/chats/chat_model'
import Tool from '#app/modules/tools/tool_model'
import BaseToolService from '#app/modules/base/base_tool_service'

export class ShopifyService extends BaseToolService {
  private accessToken: string
  private shopName: string

  constructor(context: { agent: Agent; chat: Chat; tool: Tool }) {
    super(context)

    this.accessToken = this.tool?.config?.access_token as string
    this.shopName = this.tool?.config?.shop_name as string
  }

  async checkOrderStatus(args: { email: string; order_number: string }) {
    try {
      const { email, order_number } = args

      if (!email || !order_number) {
        throw new Error('Email or order number is missing.')
      }

      const apiResponse = await axios.get(
        `https://${this.shopName}.myshopify.com/admin/api/2023-10/orders.json`,
        {
          headers: {
            'X-Shopify-Access-Token': this.accessToken,
            'Content-Type': 'application/json',
          },
          params: {
            email,
            name: `#${order_number}`,
            status: 'any',
          },
        }
      )

      const orders =
        apiResponse.data?.orders?.map((order: any) => ({
          orderId: order.id,
          order_number: order.order_number,
          createdAt: order.created_at,
          totalPrice: order.total_price,
          financialStatus: order.financial_status,
          fulfillmentStatus: order.fulfillment_status,
        })) || []

      if (orders.length === 0) {
        return `No orders found for email **${email}** with order number **#${order_number}**. Please check your details and try again.`
      }

      return JSON.stringify(orders, null, 4)
    } catch (error) {
      console.error(error)

      return 'An error occurred while checking the order status. Please try again later.'
    }
  }

  async trackShipment(args: { tracking_code: string }) {
    try {
      const { tracking_code } = args

      if (!tracking_code) {
        throw new Error('Tracking code is missing.')
      }

      const apiResponse = await axios.get(
        `https://${this.shopName}.myshopify.com/admin/api/2023-10/fulfillments.json`,
        {
          headers: {
            'X-Shopify-Access-Token': this.accessToken,
            'Content-Type': 'application/json',
          },
          params: {
            tracking_number: tracking_code,
          },
        }
      )

      const fulfillments =
        apiResponse.data?.fulfillments?.map((fulfillment: any) => ({
          fulfillmentId: fulfillment.id,
          orderId: fulfillment.order_id,
          status: fulfillment.status,
          trackingNumbers: fulfillment.tracking_numbers || [],
          trackingUrls: fulfillment.tracking_urls || [],
          createdAt: fulfillment.created_at,
          updatedAt: fulfillment.updated_at,
        })) || []

      if (fulfillments.length === 0) {
        return `No shipments found with tracking code **${tracking_code}**. Please check the code and try again.`
      }

      return JSON.stringify(fulfillments, null, 4)
    } catch (error) {
      console.error(error)

      return 'An error occurred while tracking the shipment. Please try again later.'
    }
  }

  async viewProductDetails(args: { product_identifier: string }) {
    try {
      const { product_identifier } = args

      if (!product_identifier) {
        throw new Error('Product identifier is missing.')
      }

      let endpoint = `https://${this.shopName}.myshopify.com/admin/api/2023-10/products.json`
      let params_query: object = {}

      if (/^\d+$/.test(product_identifier)) {
        endpoint = `https://${this.shopName}.myshopify.com/admin/api/2023-10/products/${product_identifier}.json`
      } else {
        params_query = { title: product_identifier }
      }

      const apiResponse = await axios.get(endpoint, {
        headers: {
          'X-Shopify-Access-Token': this.accessToken,
          'Content-Type': 'application/json',
        },
        params: params_query,
      })

      const products =
        apiResponse.data?.products?.map((product: any) => ({
          productId: product.id,
          title: product.title,
          description: product.body_html || '',
          vendor: product.vendor || '',
          productType: product.product_type || '',
          variants: (product.variants || []).map((variant: any) => ({
            variantId: variant.id,
            title: variant.title,
            price: variant.price,
            available: variant.inventory_quantity > 0,
          })),
          images: (product.images || []).map((image: any) => image.src),
          createdAt: product.created_at,
          updatedAt: product.updated_at,
        })) || []

      if (products.length === 0) {
        return `No products found with identifier **${product_identifier}**. Please check the details and try again.`
      }

      return JSON.stringify(products, null, 4)
    } catch (error) {
      console.error(error)
      return 'An error occurred while fetching product details. Please try again later.'
    }
  }

  async listProducts() {
    try {
      const apiResponse = await axios.get(
        `https://${this.shopName}.myshopify.com/admin/api/2023-10/products.json`,
        {
          headers: {
            'X-Shopify-Access-Token': this.accessToken,
            'Content-Type': 'application/json',
          },
          params: {
            limit: 20,
            fields: 'id,title,variants,images',
          },
        }
      )

      const products =
        apiResponse.data?.products?.map((product: any) => ({
          productId: product.id,
          title: product.title,
          variants: (product.variants || []).map((variant: any) => ({
            variantId: variant.id,
            title: variant.title,
            price: variant.price,
          })),
          images: (product.images || []).map((image: any) => image.src),
        })) || []

      if (products.length === 0) {
        return 'No products are currently available in the store.'
      }

      return JSON.stringify(products, null, 4)
    } catch (error) {
      console.error(error)
      return 'An error occurred while fetching products. Please try again later.'
    }
  }

  async checkRefundStatus(args: { email: string; order_number: string }) {
    try {
      const { email, order_number } = args

      if (!email || !order_number) {
        throw new Error('Email or order number is missing.')
      }

      const ordersResponse = await axios.get(
        `https://${this.shopName}.myshopify.com/admin/api/2023-10/orders.json`,
        {
          headers: {
            'X-Shopify-Access-Token': this.accessToken,
            'Content-Type': 'application/json',
          },
          params: {
            email,
            name: `#${order_number}`,
            status: 'any',
          },
        }
      )

      const orders = ordersResponse.data?.orders || []

      if (orders.length === 0) {
        return `No orders found for email **${email}** with order number **#${order_number}**. Please check your details and try again.`
      }

      const order = orders[0]

      const apiResponse = await axios.get(
        `https://${this.shopName}.myshopify.com/admin/api/2023-10/orders/${order.id}/refunds.json`,
        {
          headers: {
            'X-Shopify-Access-Token': this.accessToken,
            'Content-Type': 'application/json',
          },
        }
      )

      const refunds =
        apiResponse.data?.refunds?.map((refund: any) => ({
          refundId: refund.id,
          orderId: refund.order_id,
          createdAt: refund.created_at,
          status: refund.status,
          refundLineItems: (refund.refund_line_items || []).map((item: any) => ({
            lineItemId: item.line_item_id,
            quantity: item.quantity,
            restockType: item.restock_type,
          })),
          transactions: (refund.transactions || []).map((tx: any) => ({
            transactionId: tx.id,
            kind: tx.kind,
            status: tx.status,
            amount: tx.amount,
            currency: tx.currency,
          })),
        })) || []

      if (refunds.length === 0) {
        return `No refunds found for order number **#${order_number}**. If you believe this is an error, please contact our support team.`
      }

      return JSON.stringify(refunds, null, 4)
    } catch (error) {
      console.error(error)
      return 'An error occurred while checking refund status. Please try again later.'
    }
  }

  async updateCustomerProfile(args: {
    email: string
    first_name: string
    last_name: string
    phone?: string | null
  }) {
    try {
      const { email, first_name, last_name, phone } = args

      if (!email) {
        throw new Error('Email is missing.')
      }

      const customersResponse = await axios.get(
        `https://${this.shopName}.myshopify.com/admin/api/2023-10/customers.json`,
        {
          headers: {
            'X-Shopify-Access-Token': this.accessToken,
            'Content-Type': 'application/json',
          },
          params: {
            email,
          },
        }
      )

      const customers = customersResponse.data?.customers || []

      if (customers.length === 0) {
        return `No customer found with email **${email}**. Please check your email and try again.`
      }

      const customer = customers[0]
      const updateData: Record<string, string> = {}

      if (first_name) {
        updateData.first_name = first_name
      }

      if (last_name) {
        updateData.last_name = last_name
      }

      if (phone) {
        updateData.phone = phone
      }

      if (Object.keys(updateData).length === 0) {
        return 'No updates provided. Please specify the information you want to update.'
      }

      const apiResponse = await axios.put(
        `https://${this.shopName}.myshopify.com/admin/api/2023-10/customers/${customer.id}.json`,
        { customer: updateData },
        {
          headers: {
            'X-Shopify-Access-Token': this.accessToken,
            'Content-Type': 'application/json',
          },
        }
      )

      return JSON.stringify(apiResponse.data?.customer, null, 4)
    } catch (error) {
      console.error(error)
      return 'An error occurred while updating customer profile. Please try again later.'
    }
  }

  async requestReturn(args: { email: string; order_number: string; reason: string }) {
    try {
      const { email, order_number, reason } = args

      if (!email || !order_number || !reason) {
        throw new Error('Email, order number, or reason is missing.')
      }

      const ordersResponse = await axios.get(
        `https://${this.shopName}.myshopify.com/admin/api/2023-10/orders.json`,
        {
          headers: {
            'X-Shopify-Access-Token': this.accessToken,
            'Content-Type': 'application/json',
          },
          params: {
            email,
            name: `#${order_number}`,
            status: 'any',
          },
        }
      )

      const orders = ordersResponse.data?.orders || []

      if (orders.length === 0) {
        return `No orders found for email **${email}** with order number **#${order_number}**. Please check your details and try again.`
      }

      const order = orders[0]

      await axios.post(
        `https://${this.shopName}.myshopify.com/admin/api/2023-10/orders/${order.id}/metafields.json`,
        {
          metafield: {
            namespace: 'return_requests',
            key: 'requested',
            value: reason,
            type: 'single_line_text_field',
          },
        },
        {
          headers: {
            'X-Shopify-Access-Token': this.accessToken,
            'Content-Type': 'application/json',
          },
        }
      )

      return `Your return request for order **#${order_number}** has been submitted successfully. Our support team will contact you shortly regarding the next steps.`
    } catch (error) {
      console.error(error)
      return 'An error occurred while requesting return. Please try again later.'
    }
  }

  async viewStorePolicies(args: { policy_type: string }) {
    try {
      const { policy_type } = args

      if (!policy_type) {
        throw new Error('Policy type is missing.')
      }

      const apiResponse = await axios.get(
        `https://${this.shopName}.myshopify.com/admin/api/2023-10/shop.json`,
        {
          headers: {
            'X-Shopify-Access-Token': this.accessToken,
            'Content-Type': 'application/json',
          },
        }
      )

      const policies = apiResponse.data?.shop || {}

      let policyContent = ''

      switch (policy_type) {
        case 'return':
          policyContent = policies?.return_policy || 'No returns policy found.'
          break
        case 'shipping':
          policyContent = policies?.shipping_policy || 'No shipping policy found.'
          break
        case 'privacy':
          policyContent = policies?.privacy_policy || 'No privacy policy found.'
          break
        default:
          policyContent = 'Invalid policy type requested.'
      }

      return policyContent
    } catch (error) {
      console.error(error)
      return 'An error occurred while fetching store policies. Please try again later.'
    }
  }
}
