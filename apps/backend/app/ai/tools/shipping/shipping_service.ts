import ToolService from '#app/ai/tools/tool_service'
import axios from 'axios'
import Agent from '#app/modules/agents/agent_model'
import Chat from '#app/modules/chats/chat_model'
import Tool from '#app/modules/tools/tool_model'
import BaseToolService from '#app/modules/base/base_tool_service'

export class ShippingService extends BaseToolService {
  private apiKey: string

  constructor(context: { agent: Agent; chat: Chat; tool: Tool }) {
    super(context)

    this.apiKey = this.tool?.config?.api_key as string
  }

  async trackShipment(args: { tracking_codes: string[] }) {
    try {
      const { tracking_codes } = args

      if (!tracking_codes || !Array.isArray(tracking_codes) || tracking_codes?.length === 0) {
        throw new Error('No tracking codes provided.')
      }

      const status: Record<string, any> = {}
      const timeout = 60 * 10 * 1000
      const data = tracking_codes?.map((code) => ({
        number: code,
        auto_detection: true,
      }))
      const headers = {
        'Content-Type': 'application/json',
        '17token': this.apiKey,
      }

      try {
        await axios({
          url: 'https://api.17track.net/track/v2.2/register',
          method: 'POST',
          data,
          headers,
          timeout,
        })
      } catch (error) {
        console.error(error)
      }

      const { data: trackInfo } = await axios({
        url: 'https://api.17track.net/track/v2.2/gettrackinfo',
        method: 'POST',
        data,
        headers,
        timeout,
      })

      const { accepted: acceptedInfo, rejected: rejectedInfo } = trackInfo?.data || {}

      acceptedInfo?.forEach((item: any) => {
        status[item?.number] = {
          message: item?.track_info || 'No information available.',
          status: 'success',
        }
      })

      rejectedInfo?.forEach((item: any) => {
        status[item?.number] = {
          message: item?.error?.message?.includes('register first')
            ? 'Carrier cannot be detected.'
            : item?.error?.message || 'Unknown error.',
          status: 'failure',
        }
      })

      return JSON.stringify(status, null, 4)
    } catch (error) {
      console.error(error)

      return ToolService.messages.error
    }
  }
}
