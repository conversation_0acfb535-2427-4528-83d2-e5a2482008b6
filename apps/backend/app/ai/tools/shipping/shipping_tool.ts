import { ShippingService } from '#app/ai/tools/shipping/shipping_service'
import { z } from 'zod'
import Agent from '#app/modules/agents/agent_model'
import Chat from '#app/modules/chats/chat_model'
import Tool from '#app/modules/tools/tool_model'

export const shipping = {
  track_shipment: {
    description: 'Track shipments and get delivery status updates',
    triggers: [
      'Where is my package?',
      'Track my order',
      "What's the status of my shipment?",
      'When will my package arrive?',
    ],
    schema: z.object({
      tracking_codes: z.array(z.string()).describe('List of tracking numbers to check'),
    }),
    execute: async (context: { agent: Agent; chat: Chat; tool: Tool }, args: any) => {
      const shippingService = new ShippingService(context)

      return await shippingService.trackShipment(args)
    },
  },
}
