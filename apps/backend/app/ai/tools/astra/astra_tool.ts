import { AstraService } from '#app/ai/tools/astra/astra_service'
import { z } from 'zod'
import Agent from '#app/modules/agents/agent_model'
import Chat from '#app/modules/chats/chat_model'
import Tool from '#app/modules/tools/tool_model'

export const astra = {
  search_boats: {
    description:
      'Search for available boats when users want to find boats, check boat charters, or verify availability',
    triggers: [
      'I want to find a boat for charter',
      'What boats are available?',
      'Show me boat options',
    ],
    schema: z.object({
      date: z
        .string()
        .optional()
        .nullable()
        .describe('Date for checking boat availability (format: YYYY-MM-DD)'),
      charter_type: z
        .enum(['daily', 'weekly'])
        .optional()
        .nullable()
        .describe('Type of charter duration. Possible values: daily, weekly'),
      sale_or_charter: z
        .enum(['sale', 'charter'])
        .optional()
        .nullable()
        .describe('Whether looking to buy or charter. Possible values: sale, charter'),
    }),
    execute: async (context: { agent: Agent; chat: Chat; tool: Tool }, args: any) => {
      const astraService = new AstraService(context)

      return await astraService.searchBoats(args)
    },
  },
  get_boat_by_id: {
    description:
      'Get detailed information about a specific boat by its ID, including availability and specifications',
    triggers: [
      'Can you tell me more about boat 123?',
      'What are the details for yacht ID 456?',
      'Is boat 789 available for charter?',
    ],
    schema: z.object({
      boat_id: z.string().describe('The unique identifier of the boat'),
      date: z
        .string()
        .optional()
        .nullable()
        .describe('The date for the charter in YYYY-MM-DD format'),
      charter_type: z
        .enum(['daily', 'weekly'])
        .optional()
        .nullable()
        .describe('The type of charter - Options: daily, weekly'),
    }),
    execute: async (context: { agent: Agent; chat: Chat; tool: Tool }, args: any) => {
      const astraService = new AstraService(context)

      return await astraService.getBoatById(args)
    },
  },
  get_boat_price: {
    description:
      'Get pricing information for a specific boat, including daily and weekly charter rates',
    triggers: [
      'How much does it cost to charter boat 123?',
      'What are the daily rates for yacht 456?',
      'Can you tell me the weekly price for boat 789?',
    ],
    schema: z.object({
      boat_id: z.string().describe('ID of the boat to get price for'),
      date: z.string().describe('Date for checking boat price (format: YYYY-MM-DD)'),
      guests: z.number().optional().nullable().describe('Number of guests (optional)'),
      coupon: z.string().optional().nullable().describe('Coupon code for discounts (optional)'),
    }),
    execute: async (context: { agent: Agent; chat: Chat; tool: Tool }, args: any) => {
      const astraService = new AstraService(context)

      return await astraService.getBoatPrice(args)
    },
  },
}
