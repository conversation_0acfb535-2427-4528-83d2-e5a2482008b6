import ToolService from '#app/ai/tools/tool_service'
import axios from 'axios'
import crypto from 'node:crypto'
import Agent from '#app/modules/agents/agent_model'
import Chat from '#app/modules/chats/chat_model'
import Tool from '#app/modules/tools/tool_model'
import BaseToolService from '#app/modules/base/base_tool_service'

export class AstraService extends BaseToolService {
  private apiUrl: string
  private apiToken: string
  private headers = {
    'Content-Type': 'application/json',
  }

  constructor(context: { agent: Agent; chat: Chat; tool: Tool }) {
    super(context)

    this.apiUrl = this.tool?.config?.api_url as string
    this.apiToken = this.tool?.config?.api_token as string
  }

  async searchBoats(args: {
    date?: string | null
    charter_type?: 'daily' | 'weekly' | null
    sale_or_charter?: 'sale' | 'charter' | null
  }) {
    try {
      const { date, charter_type, sale_or_charter } = args
      const call = 'getBoats'
      const petition =
        new Date().toISOString().slice(0, 10) +
        ' ' +
        new Date().toLocaleTimeString('en-US', { hour12: false }).slice(0, 5)
      const sign = crypto
        .createHash('sha1')
        .update(call + petition + this.apiToken)
        .digest('hex')

      const response = await axios.post(
        this.apiUrl,
        {
          call,
          petition,
          sign,
          date,
          charter_type,
          sale_or_charter,
        },
        {
          headers: this.headers,
        }
      )

      return `- Return the maximum amount of information in your final response.
        - Always include the ID of the boat in your final response.
        - Always return images URLs in your final response.
        - List of all available boats:\n
        ${JSON.stringify(response.data, null, 4)}`
    } catch (error) {
      console.error(error)

      return ToolService.messages.error
    }
  }

  async getBoatById(args: {
    boat_id: string
    date?: string | null
    charter_type?: 'daily' | 'weekly' | null
  }) {
    try {
      const { boat_id, date, charter_type } = args
      const call = 'getBoat'
      const petition = new Date().toISOString()
      const sign = crypto
        .createHash('sha1')
        .update(call + petition + this.apiToken)
        .digest('hex')

      const response = await axios.post(
        this.apiUrl,
        {
          call,
          petition,
          sign,
          boat: boat_id,
          date,
          charter_type,
        },
        {
          headers: this.headers,
        }
      )

      return `- Return the maximum amount of information in your final response.
        - Always include the ID of the boat in your final response.
        - Always return images URLs in your final response.
        - Information about the boat your asked for:\n
        ${JSON.stringify(response.data, null, 4)}`
    } catch (error) {
      console.error(error)

      return ToolService.messages.error
    }
  }

  async getBoatPrice(args: {
    boat_id: string
    date?: string | null
    guests?: number | null
    coupon?: string | null
  }) {
    try {
      const { boat_id, date, guests, coupon } = args
      const call = 'getPrice'
      const petition = new Date().toISOString()
      const sign = crypto
        .createHash('sha1')
        .update(call + petition + this.apiToken)
        .digest('hex')

      const response = await axios.post(
        this.apiUrl,
        {
          call,
          petition,
          sign,
          boat: boat_id,
          date,
          guests,
          coupon,
        },
        {
          headers: this.headers,
        }
      )

      return `- Return the maximum amount of information in your final response.
        - Always include the ID of the boat in your final response.
        - Always return images URLs in your final response.
        - Information about the price of the boat your asked for:\n
        ${JSON.stringify(response.data, null, 4)}`
    } catch (error) {
      console.error(error)

      return ToolService.messages.error
    }
  }
}
