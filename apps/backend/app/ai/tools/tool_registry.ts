import { astra } from '#app/ai/tools/astra/astra_tool'
import { calendar } from '#app/ai/tools/calendar/calendar_tool'
import { hubspot } from '#app/ai/tools/hubspot/hubspot_tool'
import { image } from '#app/ai/tools/image/image_tool'
import { knowledge } from '#app/ai/tools/knowledge/knowledge_tool'
import { lead } from '#app/ai/tools/lead/lead_tool'
import { perplexity } from '#app/ai/tools/perplexity/perplexity_tool'
import { requestHuman } from '#app/ai/tools/request_human/request_human_tool'
import { resolved } from '#app/ai/tools/resolved/resolved_tool'
import { search } from '#app/ai/tools/search/search_tool'
import { shipping } from '#app/ai/tools/shipping/shipping_tool'
import { shopify } from '#app/ai/tools/shopify/shopify_tool'
import { transcribe } from '#app/ai/tools/transcribe/transcribe_tool'
import { woocommerce } from '#app/ai/tools/woocommerce/woocommerce_tool'
import { zendesk } from '#app/ai/tools/zendesk/zendesk_tool'
import { Infer } from '@vinejs/vine/types'
import { ChatCompletionTool } from 'openai/resources/chat/completions.mjs'
import { z } from 'zod'
import { zodToJsonSchema } from 'zod-to-json-schema'
import Tool from '#app/modules/tools/tool_model'
import { toolHttpConfigValidator, toolInputsValidator } from '#app/modules/tools/tool_validator'

export type ToolFunction = {
  description: string
  triggers: string[]
  schema: z.ZodObject<any>
  execute: Function
}

export type ToolDefinition = Record<string, ToolFunction>

export const NATIVE_TOOLS: Record<string, ToolDefinition> = {
  search,
  astra,
  calendar,
  lead,
  transcribe,
  requestHuman,
  resolved,
  hubspot,
  shopify,
  woocommerce,
  zendesk,
  image,
  perplexity,
  shipping,
  knowledge,
} as const

export const getToolByName = (name: string): ToolDefinition | undefined => {
  return NATIVE_TOOLS[name]
}

export const getToolFunction = (
  toolName: string,
  functionName: string
): ToolFunction | undefined => {
  const tool = getToolByName(toolName)
  return tool?.[functionName]
}

export const convertToAiSchema = (tool: Tool): ChatCompletionTool[] => {
  if (!tool.status) return []

  if (tool.type === 'native') {
    return nativeToolToAiSchema(tool)
  } else {
    return httpToolToAiSchema(tool)
  }
}

const nativeToolToAiSchema = (tool: Tool): ChatCompletionTool[] => {
  const toolName = tool.name
  const toolFns = NATIVE_TOOLS[toolName as keyof typeof NATIVE_TOOLS] as Record<
    string,
    ToolFunction
  >

  if (!toolFns) return []

  return Object.entries(toolFns).map(([fnName, fn]) => ({
    type: 'function',
    function: {
      name: `${toolName}.${fnName}`,
      description: fn.description,
      parameters: zodToJsonSchema(fn.schema),
    },
  }))
}

const httpToolToAiSchema = (tool: Tool): ChatCompletionTool[] => {
  if (!tool.config) return []

  const { name, description, inputs } = tool.config as Infer<typeof toolHttpConfigValidator>

  const zodSchema = createZodSchema(inputs)

  return [
    {
      type: 'function',
      function: {
        name,
        description,
        parameters: zodToJsonSchema(zodSchema),
      },
    },
  ]
}

const createZodSchema = (inputs: Infer<typeof toolInputsValidator>) => {
  const shape = inputs.reduce(
    (acc, input) => {
      if (!input.status) return acc
      acc[input.name] = input.type === 'url' ? z.string().url() : z.string()
      return acc
    },
    {} as Record<string, z.ZodTypeAny>
  )

  return z.object(shape)
}
