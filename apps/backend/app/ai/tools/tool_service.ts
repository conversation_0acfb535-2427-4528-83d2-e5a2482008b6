import { convertToAiSchema, getToolFunction } from '#app/ai/tools/tool_registry'
import { Infer } from '@vinejs/vine/types'
import axios from 'axios'
import { ChatCompletionTool } from 'openai/resources/chat/completions.mjs'
import Agent from '#app/modules/agents/agent_model'
import Chat from '#app/modules/chats/chat_model'
import Tool from '#app/modules/tools/tool_model'
import { toolHttpConfigValidator } from '#app/modules/tools/tool_validator'

export default class ToolService {
  static readonly messages = {
    emailed: 'Thank you! Please keep an eye on your inbox!',
    error: 'An error occurred, please try again.',
  }

  static getActiveToolsAiSchemas(dbTools: Tool[]): ChatCompletionTool[] | undefined {
    return (
      dbTools?.filter((tool) => tool.status).flatMap((tool) => convertToAiSchema(tool)) || undefined
    )
  }

  static async executeTool(params: {
    composed_name: string
    args: Record<string, any>
    context: {
      agent: Agent
      chat: Chat
    }
  }) {
    const { composed_name, args, context } = params
    const { agent } = context

    // Parse the tool name and function name
    const [toolName, functionName] = composed_name.split('.')

    if (toolName && functionName) {
      // Try to execute native tool
      const toolFunction = getToolFunction(toolName, functionName)

      if (toolFunction) {
        return await toolFunction.execute({
          context,
          args: this.cleanParams(args),
        })
      }

      // If not a native tool, check for HTTP tools
      const httpTool = agent.tools.find(
        (tool: Tool) => tool.type === 'http' && tool.config?.name === composed_name
      )

      if (httpTool && httpTool.config) {
        return await this.executeHttpTool({ tool: httpTool, args })
      }
    }

    throw new Error(`Tool not found: ${composed_name}`)
  }

  private static async executeHttpTool(params: { tool: Tool; args: { [key: string]: any } }) {
    const { tool, args } = params
    const { url, method, headers } = tool.config as Infer<typeof toolHttpConfigValidator>
    const cleanParams = this.cleanParams(args)
    const customHeaders: Record<string, string> = {}

    // Process headers
    headers?.forEach((header) => {
      if (header.key && header.value) {
        customHeaders[header.key] = header.value
      }
    })

    const response = await axios({
      url,
      method,
      headers: customHeaders,
      params: method === 'GET' ? cleanParams : {},
      data: method === 'POST' ? cleanParams : {},
      timeout: 60 * 1000,
    })

    return response.data
  }

  private static cleanParams(data: Record<string, any>) {
    return Object.fromEntries(
      Object.entries(data).map(([key, value]) => [
        key,
        typeof value === 'string'
          ? value.trim() || null
          : Array.isArray(value)
            ? [...new Set(value)].filter(Boolean)
            : value,
      ])
    )
  }
}
