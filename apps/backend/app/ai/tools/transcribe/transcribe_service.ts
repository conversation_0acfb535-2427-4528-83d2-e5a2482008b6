import ToolService from '#app/ai/tools/tool_service'
import emitter from '@adonisjs/core/services/emitter'
import Agent from '#app/modules/agents/agent_model'
import Chat from '#app/modules/chats/chat_model'
import Contact from '#app/modules/contacts/contact_model'
import Tool from '#app/modules/tools/tool_model'
import BaseToolService from '#app/modules/base/base_tool_service'

export class TranscribeService extends BaseToolService {
  private emailTitle: string
  private inputs: string[]

  constructor(context: { agent: Agent; chat: Chat; tool: Tool }) {
    super(context)

    this.emailTitle = this.tool?.config?.email_title as string
    this.inputs = this.tool?.config?.inputs as string[]
  }

  async transcribe(args: { [key: string]: string }) {
    try {
      const cleanParams = args
      const { uid, app_uid, agent_uid } = this.chat
      const { first_name, last_name, email } = cleanParams || {}

      const [messages] = await Promise.all([
        this.chat.getChatMessagesStringFormat(),
        Contact.create({
          chat_uid: uid,
          app_uid,
          agent_uid,
          email,
          first_name,
          last_name,
        }),
        this.chat.load('agent'),
      ])

      if (!messages) {
        throw new Error('Failed to retrieve computed messages.')
      }

      emitter.emit('email:send', {
        templateName: 'chat_transcribe',
        to: [{ email, name: first_name }],
        cc: [],
        cci: [],
        subject: this.emailTitle,
        appUid: this.chat?.app_uid,
        agentUid: this.chat?.agent_uid,
        variables: {
          firstName: first_name,
          lastName: last_name,
          messages,
        },
      })

      return ToolService.messages.emailed
    } catch (error) {
      console.error(error)

      return ToolService.messages.error
    }
  }
}
