import { TranscribeService } from '#app/ai/tools/transcribe/transcribe_service'
import { z } from 'zod'
import Agent from '#app/modules/agents/agent_model'
import Chat from '#app/modules/chats/chat_model'
import Tool from '#app/modules/tools/tool_model'

export const transcribe = {
  transcribe: {
    description: 'Transcribe audio messages to text',
    triggers: [
      'I sent a voice message',
      'Can you transcribe this audio?',
      'What does this voice note say?',
      'Convert this audio to text',
    ],
    schema: z.object({
      url: z.string().url().describe('URL of the audio file to transcribe'),
    }),
    execute: async (context: { agent: Agent; chat: Chat; tool: Tool }, args: any) => {
      const transcribeService = new TranscribeService(context)

      return await transcribeService.transcribe(args)
    },
  },
}
