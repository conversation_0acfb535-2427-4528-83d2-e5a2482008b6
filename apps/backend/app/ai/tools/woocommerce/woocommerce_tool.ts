import { WooCommerceService } from '#app/ai/tools/woocommerce/woocommerceService'
import { z } from 'zod'
import Agent from '#app/modules/agents/agent_model'
import Chat from '#app/modules/chats/chat_model'
import Tool from '#app/modules/tools/tool_model'

export const woocommerce = {
  check_order_status: {
    description:
      'Check order status using customer email and order number. Only offer when user asks about order status, tracking, or delivery.',
    triggers: [
      "What's the status of my order?",
      "Where's my package?",
      'I have my order number; how do I check it?',
      'Can I check my order status with my email?',
    ],
    schema: z.object({
      email: z.string().email().describe('Customer email address'),
      orderNumber: z.string().describe('Order number'),
    }),
    execute: async (context: { agent: Agent; chat: Chat; tool: Tool }, args: any) => {
      const woocommerceService = new WooCommerceService(context)

      return await woocommerceService.checkOrderStatus(args)
    },
  },
  view_product_details: {
    description:
      'View detailed product information when users ask about specific products, their features, prices, or availability. Only use for product-specific inquiries.',
    triggers: [
      'Can I get more info on this product?',
      'What are the details for [product name]?',
      "What's the price of [product name]?",
    ],
    schema: z.object({
      productName: z.string().describe('Product name'),
    }),
    execute: async (context: { agent: Agent; chat: Chat; tool: Tool }, args: any) => {
      const woocommerceService = new WooCommerceService(context)

      return await woocommerceService.viewProductDetails(args)
    },
  },
  list_products: {
    description: 'List all available products in the store.',
    triggers: [
      'What products do you have?',
      'What electronics do you have in stock?',
      "I'm looking to buy something; what do you have?",
    ],
    schema: z.object({}),
    execute: async (context: { agent: Agent; chat: Chat; tool: Tool }, args: any) => {
      const woocommerceService = new WooCommerceService(context)

      return await woocommerceService.listProducts()
    },
  },
  check_refund_status: {
    description: "Check the refund status using the user's email and order number.",
    triggers: [
      "What's the status of my refund?",
      'Can you check my refund?',
      'When will my refund be processed?',
      'Where is my refund?',
      'I have my order number, can you check my refund status?',
    ],
    schema: z.object({
      email: z.string().email().describe('Customer email address'),
      orderNumber: z.string().describe('Order number'),
    }),
    execute: async (context: { agent: Agent; chat: Chat; tool: Tool }, args: any) => {
      const woocommerceService = new WooCommerceService(context)

      return await woocommerceService.checkRefundStatus(args)
    },
  },
  get_coupons: {
    description:
      'Retrieve a coupon details when the user asks about a specific coupon, wants to check the validity of a coupon code or seeks information on the discount or terms of a coupon.',
    triggers: [
      'Tell me about coupon X',
      'What does coupon X offer?',
      'Is coupon X still active?',
      'Can I use coupon X?',
    ],
    schema: z.object({
      couponCode: z.string().optional().nullable().describe('Coupon code'),
    }),
    execute: async (context: { agent: Agent; chat: Chat; tool: Tool }, args: any) => {
      const woocommerceService = new WooCommerceService(context)

      return await woocommerceService.getCoupons(args)
    },
  },
  check_customer_downloads: {
    description:
      'Offer the option to retrieve customer downloads using their email when the user asks for their downloadable products or the user confirms a recent purchase and requests access to downloads',
    triggers: [
      'Show me my downloads',
      'I want to see my purchased files.',
      'I bought a course, can I access the materials?',
    ],
    schema: z.object({
      email: z.string().email().describe('Customer email address'),
    }),
    execute: async (context: { agent: Agent; chat: Chat; tool: Tool }, args: any) => {
      const woocommerceService = new WooCommerceService(context)

      return await woocommerceService.checkCustomerDownloads(args)
    },
  },
}
