// @ts-ignore
import WooCommerceRestApi from '@woocommerce/woocommerce-rest-api'
import Agent from '#app/modules/agents/agent_model'
import Chat from '#app/modules/chats/chat_model'
import Tool from '#app/modules/tools/tool_model'
import BaseToolService from '#app/modules/base/base_tool_service'

export class WooCommerceService extends BaseToolService {
  private api: any
  private static PER_PAGE = 30

  constructor(context: { agent: Agent; chat: Chat; tool: Tool }) {
    super(context)

    this.api = new WooCommerceRestApi({
      url: this.tool.config?.url as string,
      consumerKey: this.tool.config?.consumer_key as string,
      consumerSecret: this.tool.config?.consumer_secret as string,
      version: 'wc/v3',
    })
  }

  async checkOrderStatus(args: { email: string; orderNumber: string }) {
    try {
      const { email, orderNumber } = args

      if (!email || !orderNumber) {
        throw new Error('Email or order number is missing.')
      }

      const { data: order } = await this.api.get(`orders/${orderNumber}`)

      if (!order) {
        return `No orders found for email ${email} with order number ${orderNumber}.`
      }

      if (order?.billing?.email !== email) {
        return 'Email does not match order.'
      }

      return JSON.stringify({ status: 'success', orderStatus: order?.status, order }, null, 4)
    } catch (error) {
      console.error(error)
      return 'An error occurred while checking the order status. Please try again later.'
    }
  }

  async viewProductDetails(args: { productName: string }) {
    try {
      const { productName } = args

      if (!productName) {
        return 'Product name is required.'
      }

      const [searchResults, skuResults] = await Promise.all([
        this.api.get('products', {
          search: productName,
          per_page: WooCommerceService.PER_PAGE,
        }),
        this.api.get('products', {
          sku: productName,
          per_page: WooCommerceService.PER_PAGE,
        }),
      ])

      const products = [...(searchResults?.data ?? []), ...(skuResults?.data ?? [])]

      if (!products || !products?.length) {
        return 'No product found.'
      }

      await Promise.all(
        products.map(async (product) => {
          const { data: variations } =
            (await this.api.get(`products/${product?.id}/variations`, {
              per_page: WooCommerceService.PER_PAGE,
            })) || []

          product.variations = variations
        })
      )

      return JSON.stringify({ status: 'success', product: products[0] }, null, 4)
    } catch (error) {
      console.error(error)
      return 'An error occurred while fetching product details. Please try again later.'
    }
  }

  async listProducts() {
    try {
      const { data: products } = await this.api.get('products', {
        per_page: WooCommerceService.PER_PAGE,
      })

      if (!products || !products?.length) {
        return 'No products found.'
      }

      return JSON.stringify({ status: 'success', products }, null, 4)
    } catch (error) {
      console.error(error)
      return 'An error occurred while fetching products. Please try again later.'
    }
  }

  async checkRefundStatus(args: { email: string; orderNumber: string }) {
    try {
      const { email, orderNumber } = args

      if (!email || !orderNumber) {
        throw new Error('Email and order number are required.')
      }

      const { data: order } = await this.api.get(`orders/${orderNumber}`)

      if (!order) {
        return `No orders found for email ${email} with order number ${orderNumber}.`
      }

      if (order?.billing?.email !== email) {
        return 'Email does not match order.'
      }

      const { data: refunds } = await this.api.get(`orders/${orderNumber}/refunds`)

      if (!refunds || !refunds?.length) {
        return 'No refunds found.'
      }

      return JSON.stringify(refunds, null, 4)
    } catch (error) {
      console.error(error)
      return 'An error occurred while checking refund status. Please try again later.'
    }
  }

  async getCoupons(args: { couponCode?: string | null }) {
    try {
      const { couponCode } = args

      const { data: coupons } = await this.api.get(
        'coupons',
        couponCode
          ? { code: couponCode, per_page: WooCommerceService.PER_PAGE }
          : { per_page: WooCommerceService.PER_PAGE }
      )

      if (!coupons || !coupons.length) {
        return 'No coupons found.'
      }

      return JSON.stringify(coupons, null, 4)
    } catch (error) {
      console.error(error)
      return 'An error occurred while fetching coupons. Please try again later.'
    }
  }

  async checkCustomerDownloads(args: { email: string }) {
    try {
      const { email } = args

      if (!email) {
        throw new Error('Email is required.')
      }

      const { data: customers } = await this.api.get('customers', { email })

      if (!customers || !customers.length) {
        return `No customer found with email **${email}**. Please check the email and try again.`
      }

      const customerId = customers[0].id

      const { data: downloads } = await this.api.get(`customers/${customerId}/downloads`, {
        per_page: WooCommerceService.PER_PAGE,
      })

      if (!downloads || !downloads.length) {
        return `No downloads found for customer with email **${email}**.`
      }

      return JSON.stringify(downloads, null, 4)
    } catch (error) {
      console.error(error)
      return 'An error occurred while fetching customer downloads. Please try again later.'
    }
  }
}
