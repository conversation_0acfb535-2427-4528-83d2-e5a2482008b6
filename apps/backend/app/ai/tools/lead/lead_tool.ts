import { LeadService } from '#app/ai/tools/lead/lead_service'
import { z } from 'zod'
import Agent from '#app/modules/agents/agent_model'
import Chat from '#app/modules/chats/chat_model'
import Tool from '#app/modules/tools/tool_model'

export const lead = {
  capture: {
    description: 'Capture lead information when users express interest or provide contact details',
    triggers: [
      'I want more information',
      'Can someone contact me?',
      "I'd like to learn more",
      'Please have someone reach out to me',
    ],
    schema: z.object({
      firstName: z.string().min(1, 'First name is required').describe('First name of the lead'),
      lastName: z.string().min(1, 'Last name is required').describe('Last name of the lead'),
      email: z.string().email('Invalid email address').describe('Email address of the lead'),
      phone: z.string().optional().describe('Phone number of the lead'),
      company: z.string().optional().describe('Company name of the lead'),
      message: z.string().optional().describe('Additional message or notes from the lead'),
    }),
    execute: async (context: { agent: Agent; chat: Chat; tool: Tool }, args: any) => {
      const leadService = new LeadService(context)

      return await leadService.captureLead(args)
    },
  },
}
