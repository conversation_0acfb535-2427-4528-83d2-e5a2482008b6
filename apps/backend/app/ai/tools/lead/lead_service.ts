import ToolService from '#app/ai/tools/tool_service'
import emitter from '@adonisjs/core/services/emitter'
import Agent from '#app/modules/agents/agent_model'
import Chat from '#app/modules/chats/chat_model'
import Tool from '#app/modules/tools/tool_model'
import BaseToolService from '#app/modules/base/base_tool_service'

export class LeadService extends BaseToolService {
  private emails: string[]
  private inputs: string[]

  constructor(context: { agent: Agent; chat: Chat; tool: Tool }) {
    super(context)

    this.emails = this.tool?.config?.emails as string[]
    this.inputs = this.tool?.config?.inputs as string[]
  }

  async captureLead(args: {
    first_name: string
    last_name: string
    email: string
    phone?: string
    company?: string
    message?: string
  }) {
    try {
      const { uid, app_uid, agent_uid } = this.chat
      const cleanParams = args

      await this.chat.load('agent')
      const { first_name, last_name } = cleanParams

      const [savedContact] = await Promise.all([
        this.agent.related('contacts').create({
          ...cleanParams,
          first_name,
          last_name,
          chat_uid: uid,
          agent_uid: agent_uid,
          app_uid: app_uid,
        }),
        this.chat
          .merge({
            label: `${first_name} ${last_name}`,
            has_contact: true,
          })
          .save(),
      ])

      emitter.emit('email:send', {
        templateName: 'new_lead',
        to: this.emails.map((email) => ({ email })) || [],
        cc: [],
        cci: [],
        subject: '🧠 New lead',
        appUid: this.chat?.app_uid,
        agentUid: this.chat?.agent_uid,
        variables: {
          agentName: this.chat?.agent?.label || '',
          chatLabel: this.chat?.label || '',
          contact: savedContact,
        },
      })

      return ToolService.messages.emailed
    } catch (error) {
      console.error(error)

      return ToolService.messages.error
    }
  }
}
