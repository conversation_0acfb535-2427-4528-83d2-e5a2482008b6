import { ImageService } from '#app/ai/tools/image/image_service'
import { z } from 'zod'
import Agent from '#app/modules/agents/agent_model'
import Chat from '#app/modules/chats/chat_model'
import Tool from '#app/modules/tools/tool_model'

export const image = {
  generate: {
    description: 'Generate high-quality images using AI',
    triggers: [
      'Generate an image of',
      'Create an image of',
      'Generate a picture of',
      'Create a picture of',
    ],
    schema: z.object({
      prompt: z.string().describe('Prompt for the image generation'),
    }),
    execute: async (context: { agent: Agent; chat: Chat; tool: Tool }, args: any) => {
      const imageService = new ImageService(context)

      return await imageService.generate(args)
    },
  },
}
