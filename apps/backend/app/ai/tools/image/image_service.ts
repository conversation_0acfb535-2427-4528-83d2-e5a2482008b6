import ToolService from '#app/ai/tools/tool_service'
import env from '#start/env'
import { fal } from '@fal-ai/client'
import Agent from '#app/modules/agents/agent_model'
import Chat from '#app/modules/chats/chat_model'
import Tool from '#app/modules/tools/tool_model'
import BaseToolService from '#app/modules/base/base_tool_service'

export class ImageService extends BaseToolService {
  constructor(context: { agent: Agent; chat: Chat; tool: Tool }) {
    super(context)
  }

  async generate(args: { prompt: string }) {
    try {
      const { prompt } = args

      fal.config({
        credentials: env.get('FAL_API_KEY'),
      })

      const submitResponse = await fal.queue.submit('fal-ai/flux/schnell', {
        input: {
          prompt,
          image_size: 'landscape_4_3',
          num_inference_steps: 4,
          seed: 6252023,
          sync_mode: false,
          num_images: 1,
          enable_safety_checker: false,
        },
      })

      const resultResponse = await fal.queue.result('fal-ai/flux/schnell', {
        requestId: submitResponse.request_id,
      })

      return resultResponse
    } catch (error) {
      console.error(error)

      return ToolService.messages.error
    }
  }
}
