import { SearchService } from '#app/ai/tools/search/search_service'
import { z } from 'zod'
import { ToolContext } from '#app/modules/base/base_tool_service'

export const search = {
  search: {
    description: 'Search the web for real-time information and current events',
    triggers: [
      'What is the latest news about',
      'Can you search for',
      'Find information about',
      'What do you know about',
    ],
    schema: z.object({
      query: z.string().describe('The search query to find information on the web'),
    }),
    execute: async (params: { context: ToolContext; args: { query: string } }) => {
      const { context, args } = params

      const searchService = new SearchService(context)

      return await searchService.search(args)
    },
  },
}
