import env from '#start/env'
import axios from 'axios'
import BaseToolService, { ToolContext } from '#app/modules/base/base_tool_service'

export class SearchService extends BaseToolService {
  constructor(context: ToolContext) {
    super(context)
  }

  async search(args: { query: string }) {
    try {
      const { query } = args
      const cleanQuery = this.cleanParams({ query }).query

      const { data } = await axios({
        method: 'POST',
        url: env.get('TRAINING_API_URL') + '/search/google',
        data: {
          q: cleanQuery,
        },
      })

      return data
    } catch (error) {
      console.error(error)
      return this.formatError('An error occurred during search. Please try again.')
    }
  }
}
