import LiteLlmHelper from '#app/ai/helpers/lite_llm_helper'
import { StorageHelper } from '#app/helpers'
import env from '#start/env'
import { Infer } from '@vinejs/vine/types'
import { CohereClient } from 'cohere-ai'
import { decodeHTML } from 'entities'
import { removeStopwords } from 'stopword'
import db from '@adonisjs/lucid/services/db'
import type { ISourceDetails } from '#app/interfaces'
import Agent from '#app/modules/agents/agent_model'
import Chat from '#app/modules/chats/chat_model'
import Tool from '#app/modules/tools/tool_model'
import BaseToolService from '#app/modules/base/base_tool_service'
import { embeddingValidator } from '#app/modules/rag/embeddings/embedding_validator'

export default class KnowledgeService extends BaseToolService {
  private readonly LIMIT = 8

  constructor(context: { agent: Agent; chat: Chat; tool: Tool }) {
    super(context)
  }

  async search(args: { query: string }) {
    const { query } = args

    const embeddingsTable = this.agent.getEmbeddingTableName()
    const threshold = this.agent?.model_embedding === 'text-embedding-ada-002' ? 0.75 : 0.3
    const sourceDetails: ISourceDetails = {
      urls: [],
      files: [],
    }

    // SET hnsw.ef_search = 200;
    // SET hnsw.iterative_scan = relaxed_order
    // SET hnsw.scan_mem_multiplier = 2;

    const { rows: inactiveProviderSources } = await db.rawQuery(
      'SELECT s.uid AS source_uid FROM sources s INNER JOIN providers p ON s.provider_uid = p.uid WHERE p.mode = false'
    )

    const sqlQuery = `SELECT  uid, source_uid, metadata, (embedding <#> :query_embedding) AS score
        FROM embeddings.${embeddingsTable}
        WHERE
          (embedding <#> :query_embedding) <= -${threshold}
          ${inactiveProviderSources.length > 0 ? `AND source_uid NOT IN (${inactiveProviderSources.join(', ')})` : ''}
        ORDER BY score
        LIMIT ${this.LIMIT}`

    const tokens: string[] = [query].flatMap((question) =>
      removeStopwords(question.replace(/[?!.,]/g, '').split(' ')).filter(
        (token) => token.length >= 5
      )
    )

    const texts: string[] = [...new Set([query, ...tokens].filter((text) => text))]

    await this.agent.load('app')

    const embeddings = await LiteLlmHelper.createEmbeddings({
      app: this.agent.app,
      modelName: this.agent.model_embedding,
      texts,
    })

    const chunksArrays = await Promise.all(
      embeddings.map(async (embedding) => {
        const { rows: chunks } = await db.rawQuery(sqlQuery, {
          query_embedding: JSON.stringify(embedding),
        })

        return chunks as Infer<typeof embeddingValidator>[]
      })
    )

    // Grouping & Scoring
    const chunksMap = new Map<string, { uid: string; score: number; metadata: any }>()

    for (const chunksArray of chunksArrays) {
      for (const chunk of chunksArray) {
        const { uid, score, metadata } = chunk

        const scoreValue = Number.parseFloat(score ?? '0') * -1

        if (!chunksMap.has(uid)) {
          chunksMap.set(uid, { uid, score: scoreValue, metadata })
        } else {
          const existingChunk = chunksMap.get(uid)

          if (existingChunk) {
            existingChunk.score += scoreValue
          }
        }
      }
    }

    // Ranking
    const chunks = Array.from(chunksMap.values())
    let topChunks: { uid: string; score: number; metadata: Record<string, any> }[] = chunks

    if (chunks.length > this.LIMIT) {
      const cohere = new CohereClient({
        token: env.get('COHERE_API_KEY'),
      })

      const rankedChunks = await cohere.rerank({
        query: query,
        model: 'rerank-v3.5',
        topN: this.LIMIT,
        returnDocuments: false,
        documents: chunks.map((chunk) => decodeHTML(chunk.metadata.content)),
      })

      topChunks = rankedChunks?.results?.map((doc) => chunks[doc.index])
    }

    // Sort by score
    topChunks.sort((a, b) => b.score - a.score).slice(0, this.LIMIT)

    const urls = new Set<string>()
    const files = new Map<string, string>()

    // Process all sources in parallel
    const filePromises = []

    for (const source of Object.values(topChunks)) {
      const { metadata } = source
      const { url, document, media, file_location: fileLocation } = metadata || {}

      if (url) {
        urls.add(url)
      }

      const location = fileLocation || document?.location || media?.location

      if (location && !files.has(location)) {
        filePromises.push(
          (async () => {
            const storageClient = new StorageHelper('knowledge')
            const fileUrl = await storageClient.getSignedUrl(location)

            files.set(
              location,
              JSON.stringify({
                name: metadata?.file_name,
                location,
                fileUrl,
              })
            )
          })()
        )
      }
    }

    // Wait for all file URL retrievals to complete
    await Promise.all(filePromises)

    // Convert sets and map to arrays
    sourceDetails.urls = Array.from(urls)
    sourceDetails.files = Array.from(files.values()).map((file) => JSON.parse(file))

    const contents: string[] = topChunks.map((source) => decodeHTML(source.metadata.content))

    return { sourceDetails, contents }
  }
}
