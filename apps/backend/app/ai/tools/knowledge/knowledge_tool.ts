import KnowledgeService from '#app/ai/tools/knowledge/knowledge_service'
import { z } from 'zod'
import Agent from '#app/modules/agents/agent_model'
import Chat from '#app/modules/chats/chat_model'
import Tool from '#app/modules/tools/tool_model'

export const knowledge = {
  search: {
    description: 'Search through the knowledge base for relevant information',
    triggers: [
      'What do you know about',
      'Can you tell me about',
      'I need information on',
      'What are your thoughts on',
    ],
    schema: z.object({
      query: z.string().describe('The search query to find relevant information'),
    }),
    execute: async (context: { agent: Agent; chat: Chat; tool: Tool }, args: any) => {
      const knowledgeService = new KnowledgeService(context)

      return await knowledgeService.search(args)
    },
  },
}
