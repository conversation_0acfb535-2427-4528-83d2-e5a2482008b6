import { ResolvedService } from '#app/ai/tools/resolved/resolved_service'
import { z } from 'zod'
import Agent from '#app/modules/agents/agent_model'
import Chat from '#app/modules/chats/chat_model'
import Tool from '#app/modules/tools/tool_model'

export const resolved = {
  mark_as_resolved: {
    description: 'Mark the chat as resolved when the user confirms the issue is fixed',
    triggers: [
      'That fixes it',
      'This works for me',
      "Great, that's what I needed",
      'Thanks a lot',
      'Thank you, that was helpful',
    ],
    schema: z.object({}),
    execute: async (context: { agent: Agent; chat: Chat; tool: Tool }, args: any) => {
      const resolvedService = new ResolvedService(context)

      return await resolvedService.markAsResolved()
    },
  },
}
