import { ZendeskService } from '#app/ai/tools/zendesk/zendesk_service'
import { z } from 'zod'
import Agent from '#app/modules/agents/agent_model'
import Chat from '#app/modules/chats/chat_model'
import Tool from '#app/modules/tools/tool_model'

export const zendesk = {
  create_ticket: {
    description: 'Create a support ticket in Zendesk',
    triggers: [
      'I need help with something',
      'Can I speak to support?',
      'I have a problem',
      'I want to report an issue',
    ],
    schema: z.object({
      subject: z.string().describe('The subject of the ticket'),
      description: z.string().describe('The description of the ticket'),
      email: z.string().email().describe('Email address of the requester'),
      firstName: z.string().describe('First name of the requester'),
      lastName: z.string().describe('Last name of the requester'),
      phone: z.string().optional().describe('Phone number of the requester'),
      company: z.string().optional().describe('Company name of the requester'),
      type: z.enum(['question', 'incident', 'problem', 'task']).describe('Type of the ticket'),
      priority: z.enum(['urgent', 'high', 'normal', 'low']).describe('Priority of the ticket'),
    }),
    execute: async (context: { agent: Agent; chat: Chat; tool: Tool }, args: any) => {
      const zendeskTool = new ZendeskService(context)

      return await zendeskTool.createTicket(args)
    },
  },
  check_ticket_status: {
    description: 'Check the status of a Zendesk ticket',
    triggers: [
      "What's the status of my ticket?",
      'Where is my ticket?',
      'Has my ticket been resolved?',
      'When will my ticket be resolved?',
    ],
    schema: z.object({
      ticketId: z.string().describe('The ticket ID to check'),
      email: z.string().email().describe('Email address used for the ticket'),
    }),
    execute: async (context: { agent: Agent; chat: Chat; tool: Tool }, args: any) => {
      const zendeskTool = new ZendeskService(context)

      return await zendeskTool.checkTicketStatus(args)
    },
  },
}
