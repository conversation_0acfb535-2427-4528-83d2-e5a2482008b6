import axios from 'axios'
import emitter from '@adonisjs/core/services/emitter'
import Agent from '#app/modules/agents/agent_model'
import Chat from '#app/modules/chats/chat_model'
import Tool from '#app/modules/tools/tool_model'

export class ZendeskService {
  private agent: Agent
  private chat: Chat
  private tool: Tool
  private apiKey: string
  private apiEmail: string
  private subdomain: string
  private emails: string[]

  constructor(context: { agent: Agent; chat: Chat; tool: Tool }) {
    this.agent = context.agent
    this.chat = context.chat
    this.tool = context.tool

    const config = this.tool.config as {
      api_key: string
      api_email: string
      subdomain: string
      emails: string[]
    }
    this.apiKey = config.api_key
    this.apiEmail = config.api_email
    this.subdomain = config.subdomain
    this.emails = config.emails
  }

  private get baseUrl() {
    return `https://${this.subdomain}.zendesk.com/api/v2`
  }

  private get headers() {
    return {
      'Content-Type': 'application/json',
      'Authorization': `Basic ${Buffer.from(`${this.apiEmail}/token:${this.apiKey}`).toString('base64')}`,
    }
  }

  private async findOrCreateOrganization(company: string) {
    const searchResponse = await axios({
      method: 'GET',
      url: `${this.baseUrl}/organizations/search.json`,
      headers: this.headers,
      params: {
        name: company || undefined,
      },
    })

    if (searchResponse.data?.organizations?.length > 0) {
      return searchResponse.data.organizations[0].id
    }

    const createOrgResponse = await axios({
      method: 'POST',
      url: `${this.baseUrl}/organizations.json`,
      headers: this.headers,
      data: {
        organization: {
          name: company,
        },
      },
    })

    return createOrgResponse?.data?.organization?.id
  }

  private async findOrCreateUser(params: {
    email: string
    firstName: string
    lastName?: string | null
    phone?: string | null
    organizationId?: number | null
  }) {
    const { email, firstName, lastName, phone, organizationId } = params

    const searchResponse = await axios({
      method: 'GET',
      url: `${this.baseUrl}/users/search.json`,
      headers: this.headers,
      params: {
        query: email,
      },
    })

    if (searchResponse.data?.users?.length > 0) {
      return searchResponse.data.users[0].id
    }

    const createUserResponse = await axios({
      method: 'POST',
      url: `${this.baseUrl}/users.json`,
      headers: this.headers,
      data: {
        user: {
          role: 'end-user',
          name: `${firstName} ${lastName}`.trim(),
          organization_id: organizationId,
          email,
          phone,
        },
      },
    })

    return createUserResponse.data?.user?.id
  }

  async createTicket(args: {
    email: string
    firstName: string
    lastName?: string | null
    phone?: string | null
    company?: string | null
    subject: string
    description: string
    type?: string | null
    priority?: string | null
  }) {
    try {
      const { email, firstName, lastName, phone, company, subject, description, type, priority } =
        args

      // Create organization if company is provided
      const organizationId = company ? await this.findOrCreateOrganization(company) : null

      // Create or find user
      const userId = await this.findOrCreateUser({
        email,
        firstName,
        lastName,
        phone,
        organizationId,
      })

      await this.chat.load('agent')
      await this.chat.agent.load('model')

      const { agent } = this.chat
      const { model } = agent

      // Create ticket
      const ticketResponse = await axios({
        method: 'POST',
        url: `${this.baseUrl}/tickets.json?async=true`,
        data: {
          ticket: {
            subject,
            priority,
            type,
            external_id: this.chat.uid,
            requester_id: userId,
            organization_id: organizationId,
            comment: {
              body: description,
            },
            metadata: {
              chat_uid: this.chat.uid,
              chatbot_uid: agent.uid,
              model_uid: model.uid,
              chat_name: this.chat.label,
              chatbot_name: agent.label,
              model_name: model.model_name,
              email,
              phone,
              company,
            },
          },
        },
        headers: this.headers,
      })

      // Create contact
      const [savedContact] = await Promise.all([
        agent.related('contacts').create({
          email,
          first_name: firstName,
          last_name: lastName || undefined,
          phone: phone || undefined,
          company: company || undefined,
          chat_uid: this.chat.uid,
          app_uid: this.chat.app_uid,
          agent_uid: agent.uid,
        }),
        this.chat
          .merge({
            label: `${firstName} ${lastName}`,
            has_contact: true,
          })
          .save(),
      ])

      // Send email
      if (email && this.emails.length) {
        emitter.emit('email:send', {
          templateName: 'new_ticket',
          to: this.emails.map((item) => ({ email: item })) || [],
          cc: [],
          cci: [],
          subject: '🧠 New ticket',
          appUid: this.chat.app_uid,
          agentUid: agent.uid,
          variables: {
            agentName: this.chat?.agent?.label || '',
            chatLabel: this.chat?.label || '',
            contact: savedContact,
          },
        })
      }

      return `Your ticket ID: ${ticketResponse?.data?.ticket?.id}\nWe'll get back to you shortly via email.`
    } catch (error) {
      console.error(error)

      return 'An error occurred while creating the ticket. Please try again later.'
    }
  }

  async checkTicketStatus(args: { ticketId: string; email: string }) {
    try {
      const { ticketId, email } = args

      if (!ticketId || !email) {
        throw new Error('Ticket ID and email are required.')
      }

      const ticketResponse = await axios({
        method: 'GET',
        url: `${this.baseUrl}/tickets/${ticketId}.json`,
        headers: this.headers,
      })

      const ticket = ticketResponse.data?.ticket

      if (!ticket) {
        return `No ticket found with ID **${ticketId}**. Please check your ticket ID and try again.`
      }

      const requesterResponse = await axios({
        method: 'GET',
        url: `${this.baseUrl}/users/${ticket.requester_id}.json`,
        headers: this.headers,
      })

      const requester = requesterResponse.data?.user

      if (requester?.email !== email) {
        return 'The provided email does not match the ticket requester.'
      }

      const ticketDetails = {
        ticket_id: ticket.id,
        subject: ticket.subject,
        status: ticket.status,
        priority: ticket.priority,
        type: ticket.type,
        created_at: ticket.created_at,
        updated_at: ticket.updated_at,
      }

      return JSON.stringify(ticketDetails, null, 4)
    } catch (error) {
      console.error(error)

      return 'An error occurred while checking the ticket status. Please try again later.'
    }
  }
}
