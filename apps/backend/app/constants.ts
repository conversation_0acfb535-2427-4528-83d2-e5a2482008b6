import env from '#start/env'
import { Infer } from '@vinejs/vine/types'
import fs from 'node:fs'
import adonisApp from '@adonisjs/core/services/app'
import { agentSettingsValidator } from '#app/modules/agents/agent_validator'

// Env
export const IS_PRODUCTION = adonisApp.inProduction

// App
export const APP_ROOT_FOLDER_PATH = adonisApp.appRoot.pathname
export const APP_TMP_FOLDER_PATH = adonisApp.tmpPath()

// SMTP
export const APP_SMTP = {
  host: env.get('SMTP_HOST'),
  port: env.get('SMTP_PORT'),
  secure: env.get('SMTP_PORT') === 465,
  auth: {
    user: env.get('SMTP_USERNAME'),
    pass: env.get('SMTP_PASSWORD'),
  },
}

export const APP_SENDER = {
  name: env.get('SENDER_NAME'),
  email: env.get('SENDER_EMAIL'),
}

// Training
export const TRAINING_IMAGE_EXTENSIONS = ['jpg', 'jpeg', 'png', 'ico', 'webp', 'svg']

export const TRAINING_DOCUMENTS_EXTENSIONS = [
  // Spreadsheet
  'csv',
  'xls',
  'xlsx',
  'ods',
  // Document
  'pdf',
  'doc',
  'docx',
  'odt',
  'epub',
  'rtf',
  // Plain Text
  'txt',
  'md',
  'xml',
  // Presentation
  'ppt',
  'pptx',
  'odp',
  // Other
  'html',
  'json',
]

export const TRAINING_MEDIA_EXTENSIONS = [
  // Audio
  'mp3',
  'mp4',
  'mpeg',
  'mpga',
  'm4a',
  'wav',
  'webm',
  'opus',
]

// Storage
export const STORAGE_DOCUMENTS_FOLDER_PATH = `${APP_ROOT_FOLDER_PATH}app/resources/files/storage/documents`
export const STORAGE_IMAGES_FOLDER_PATH = `${APP_ROOT_FOLDER_PATH}app/resources/files/storage/images`
export const STORAGE_MEDIA_FOLDER_PATH = `${APP_ROOT_FOLDER_PATH}app/resources/files/storage/media`

// Tools
export const DISPOSABLE_EMAIL_DOMAINS: string[] = JSON.parse(
  fs.readFileSync(`${APP_ROOT_FOLDER_PATH}node_modules/disposable-email-domains/index.json`, 'utf8')
)

export const BUSINESS_TOOLS: string[] = JSON.parse(
  fs.readFileSync(`${APP_ROOT_FOLDER_PATH}app/resources/files/business_tools.json`, 'utf8')
)

export const GIRLFRIENDS_TOOLS: string[] = JSON.parse(
  fs.readFileSync(`${APP_ROOT_FOLDER_PATH}app/resources/files/girlfriends_tools.json`, 'utf8')
)

export const DEFAULT_AGENTS_TOOLS = JSON.parse(
  fs.readFileSync(`${APP_ROOT_FOLDER_PATH}app/resources/files/agent_tools.json`, 'utf8')
)

export const DEFAULT_AGENT_SETTINGS: Infer<typeof agentSettingsValidator> = JSON.parse(
  fs.readFileSync(`${APP_ROOT_FOLDER_PATH}app/resources/files/agent_settings.json`, 'utf8')
)

export const MODELS_BY_KEY = [
  'gpt-4o',
  'gpt-4o-mini',
  'o1-mini',
  'o1-preview',
  'llama-405b',
  'llama-70b',
  'llama-8b',
  'claude-sonnet',
  'claude-opus',
  'claude-haiku',
  'perplexity-huge-online',
  'perplexity-large-online',
  'perplexity-small-online',
  'gemini-pro',
  'mistral-large',
  'mistral-medium',
  'mistral-small',
  'command-r-plus',
  'command-r',
]

export const ADDONS_OPTIONS = [
  // Standard
  'branding',
  // Pro
  'credits',
  // Agency
  'urls',
  'files',
  'chatbots',
  'domain',
  // Business
  'mobile_app',
  'seats',
]

export const AGENTS_BASIC_PLAN = [
  'web_search',
  'perplexity_search',
  'cards',
  'question_answer',
  'buttons',
  'smart_suggestions',
  'transcribe',
  'calculator',
]

export const AGENTS_STANDARD_PLAN = [
  ...AGENTS_BASIC_PLAN,
  'mark_as_resolved',
  'whatsapp',
  'request_human',
  'sequences',
  'calendar_meeting',
  'capture_lead',
  'hubspot',
  'zendesk',
  'dynamics',
]

export const AGENTS_BUSINESS_PLAN = [
  ...AGENTS_STANDARD_PLAN,
  'shopify',
  'woocommerce',
  'shipping_tracking',
  'image_generator',
  'accounts',
  'wolframalpha',
  'ifttt',
  'dynamic_training',
]

export const AGENTS_ENTERPRISE_PLAN = [...AGENTS_BUSINESS_PLAN]

export const LANGUAGES_KEYS = [
  'en_eng',
  'es_spa',
  'pt_por',
  'ru_rus',
  'de_deu',
  'fr_fra',
  'hi_hin',
  'ja_jpn',
  'it_ita',
  'id_ind',
  'ko_kor',
  'vi_vie',
  'tr_tur',
  'pl_pol',
  'th_tha',
  'nl_nld',
  'fil_tgl',
  'ar_ara',
  'bn_ben',
  'ms_msa',
  'ro_ron',
  'uk_ukr',
  'sv_swe',
  'hu_hun',
  'cs_ces',
  'da_dan',
  'no_nor',
  'fi_fin',
  'el_ell',
  'sr_srp',
  'sk_slk',
  'bg_bul',
  'my_mya',
  'sw_swa',
  'tl_tgl',
  'jw_jav',
  'la_lat',
  'gu_guj',
  'ka_kat',
  'am_amh',
  'ne_nep',
  'kn_kan',
  'ml_mal',
  'ta_tam',
  'mr_mar',
  'ug_uzb',
  'hy_hye',
  'pa_pan',
  'az_aze',
  'yi_yid',
  'cy_cym',
  'gl_glg',
  'km_khm',
  'ga_gle',
  'ht_hat',
  'sa_san',
  'te_tel',
  'ps_pus',
]

export const LANGUAGES = [
  { unique_key: 'en_eng', name: 'English', iso_639_1: 'en', iso_639_2: 'eng' },
  { unique_key: 'es_spa', name: 'Spanish', iso_639_1: 'es', iso_639_2: 'spa' },
  { unique_key: 'pt_por', name: 'Portuguese', iso_639_1: 'pt', iso_639_2: 'por' },
  { unique_key: 'ru_rus', name: 'Russian', iso_639_1: 'ru', iso_639_2: 'rus' },
  { unique_key: 'de_deu', name: 'German', iso_639_1: 'de', iso_639_2: 'deu' },
  { unique_key: 'fr_fra', name: 'French', iso_639_1: 'fr', iso_639_2: 'fra' },
  { unique_key: 'hi_hin', name: 'Hindi', iso_639_1: 'hi', iso_639_2: 'hin' },
  { unique_key: 'ja_jpn', name: 'Japanese', iso_639_1: 'ja', iso_639_2: 'jpn' },
  { unique_key: 'it_ita', name: 'Italian', iso_639_1: 'it', iso_639_2: 'ita' },
  { unique_key: 'id_ind', name: 'Indonesian', iso_639_1: 'id', iso_639_2: 'ind' },
  { unique_key: 'ko_kor', name: 'Korean', iso_639_1: 'ko', iso_639_2: 'kor' },
  { unique_key: 'vi_vie', name: 'Vietnamese', iso_639_1: 'vi', iso_639_2: 'vie' },
  { unique_key: 'tr_tur', name: 'Turkish', iso_639_1: 'tr', iso_639_2: 'tur' },
  { unique_key: 'pl_pol', name: 'Polish', iso_639_1: 'pl', iso_639_2: 'pol' },
  { unique_key: 'th_tha', name: 'Thai', iso_639_1: 'th', iso_639_2: 'tha' },
  { unique_key: 'nl_nld', name: 'Dutch', iso_639_1: 'nl', iso_639_2: 'nld' },
  { unique_key: 'fil_tgl', name: 'Filipino', iso_639_1: 'fil', iso_639_2: 'tgl' },
  { unique_key: 'ar_ara', name: 'Arabic', iso_639_1: 'ar', iso_639_2: 'ara' },
  { unique_key: 'bn_ben', name: 'Bengali', iso_639_1: 'bn', iso_639_2: 'ben' },
  { unique_key: 'ms_msa', name: 'Malay', iso_639_1: 'ms', iso_639_2: 'msa' },
  { unique_key: 'ro_ron', name: 'Romanian', iso_639_1: 'ro', iso_639_2: 'ron' },
  { unique_key: 'uk_ukr', name: 'Ukrainian', iso_639_1: 'uk', iso_639_2: 'ukr' },
  { unique_key: 'sv_swe', name: 'Swedish', iso_639_1: 'sv', iso_639_2: 'swe' },
  { unique_key: 'hu_hun', name: 'Hungarian', iso_639_1: 'hu', iso_639_2: 'hun' },
  { unique_key: 'cs_ces', name: 'Czech', iso_639_1: 'cs', iso_639_2: 'ces' },
  { unique_key: 'da_dan', name: 'Danish', iso_639_1: 'da', iso_639_2: 'dan' },
  { unique_key: 'no_nor', name: 'Norwegian', iso_639_1: 'no', iso_639_2: 'nor' },
  { unique_key: 'fi_fin', name: 'Finnish', iso_639_1: 'fi', iso_639_2: 'fin' },
  { unique_key: 'el_ell', name: 'Greek', iso_639_1: 'el', iso_639_2: 'ell' },
  { unique_key: 'sr_srp', name: 'Serbian', iso_639_1: 'sr', iso_639_2: 'srp' },
  { unique_key: 'sk_slk', name: 'Slovak', iso_639_1: 'sk', iso_639_2: 'slk' },
  { unique_key: 'bg_bul', name: 'Bulgarian', iso_639_1: 'bg', iso_639_2: 'bul' },
  { unique_key: 'my_mya', name: 'Burmese', iso_639_1: 'my', iso_639_2: 'mya' },
  { unique_key: 'sw_swa', name: 'Swahili', iso_639_1: 'sw', iso_639_2: 'swa' },
  { unique_key: 'tl_tgl', name: 'Tagalog', iso_639_1: 'tl', iso_639_2: 'tgl' },
  { unique_key: 'jw_jav', name: 'Javanese', iso_639_1: 'jw', iso_639_2: 'jav' },
  { unique_key: 'la_lat', name: 'Latin', iso_639_1: 'la', iso_639_2: 'lat' },
  { unique_key: 'gu_guj', name: 'Gujarati', iso_639_1: 'gu', iso_639_2: 'guj' },
  { unique_key: 'ka_kat', name: 'Georgian', iso_639_1: 'ka', iso_639_2: 'kat' },
  { unique_key: 'am_amh', name: 'Amharic', iso_639_1: 'am', iso_639_2: 'amh' },
  { unique_key: 'ne_nep', name: 'Nepali', iso_639_1: 'ne', iso_639_2: 'nep' },
  { unique_key: 'kn_kan', name: 'Kannada', iso_639_1: 'kn', iso_639_2: 'kan' },
  { unique_key: 'ml_mal', name: 'Malayalam', iso_639_1: 'ml', iso_639_2: 'mal' },
  { unique_key: 'ta_tam', name: 'Tamil', iso_639_1: 'ta', iso_639_2: 'tam' },
  { unique_key: 'mr_mar', name: 'Marathi', iso_639_1: 'mr', iso_639_2: 'mar' },
  { unique_key: 'ug_uzb', name: 'Uzbek', iso_639_1: 'ug', iso_639_2: 'uzb' },
  { unique_key: 'hy_hye', name: 'Armenian', iso_639_1: 'hy', iso_639_2: 'hye' },
  { unique_key: 'pa_pan', name: 'Punjabi', iso_639_1: 'pa', iso_639_2: 'pan' },
  { unique_key: 'az_aze', name: 'Azerbaijani', iso_639_1: 'az', iso_639_2: 'aze' },
  { unique_key: 'yi_yid', name: 'Yiddish', iso_639_1: 'yi', iso_639_2: 'yid' },
  { unique_key: 'cy_cym', name: 'Welsh', iso_639_1: 'cy', iso_639_2: 'cym' },
  { unique_key: 'gl_glg', name: 'Galician', iso_639_1: 'gl', iso_639_2: 'glg' },
  { unique_key: 'km_khm', name: 'Khmer', iso_639_1: 'km', iso_639_2: 'khm' },
  { unique_key: 'ga_gle', name: 'Irish', iso_639_1: 'ga', iso_639_2: 'gle' },
  { unique_key: 'ht_hat', name: 'Haitian Creole', iso_639_1: 'ht', iso_639_2: 'hat' },
  { unique_key: 'sa_san', name: 'Sanskrit', iso_639_1: 'sa', iso_639_2: 'san' },
  { unique_key: 'te_tel', name: 'Telugu', iso_639_1: 'te', iso_639_2: 'tel' },
  { unique_key: 'ps_pus', name: 'Pashto', iso_639_1: 'ps', iso_639_2: 'pus' },
]
