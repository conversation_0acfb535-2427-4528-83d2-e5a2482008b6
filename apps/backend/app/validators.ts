import vine from '@vinejs/vine'

const requestFilterSchema = vine.object({
  page: vine.number().optional(),
  search: vine.string().trim().optional(),
  filters: vine
    .array(
      vine.object({
        key: vine.string().trim(),
        value: vine.string().trim(),
        operator: vine.enum([
          '=',
          '!=',
          '>',
          '<',
          '>=',
          '<=',
          'like',
          'ilike',
          'in',
          'not_in',
          'is null',
        ]),
      })
    )
    .optional(),
  orderBy: vine
    .array(
      vine.object({
        key: vine.string().trim(),
        direction: vine.enum(['asc', 'desc']),
      })
    )
    .optional(),
  expand: vine.array(vine.string()).optional(),
  groupBy: vine.string().trim().optional(),
})

const bucketTypeSchema = vine.enum(['knowledge', 'agents', 'apps', 'chats', 'custom'] as const)

const bucketConfigSchema = vine.object({
  bucket_name: vine.string().trim(),
  is_public: vine.boolean(),
})

const storageSchema = vine.object({
  access_key: vine.string().trim(),
  secret_key: vine.string().trim(),
  bucket_region: vine.string().trim(),
  bucket_name: vine.string().trim(),
  bucket_endpoint: vine
    .string()
    .trim()
    .url({ require_protocol: true, protocols: ['http', 'https'] }),
})

const simpleEmailSchema = vine.object({
  subject: vine.string().trim(),
  to: vine.array(
    vine.object({
      name: vine.string().trim().optional(),
      email: vine.string().email(),
    })
  ),
  cc: vine
    .array(
      vine.object({
        name: vine.string().trim().optional(),
        email: vine.string().email(),
      })
    )
    .optional(),
  bcc: vine
    .array(
      vine.object({
        name: vine.string().trim().optional(),
        email: vine.string().email(),
      })
    )
    .optional(),
  content: vine.string().trim(),
})

const transactionalEmailSchema = vine.object({
  ...simpleEmailSchema.getProperties(),
  template_name: vine.string().trim(),
  app_uid: vine.string().uuid(),
  agent_uid: vine.string().uuid().optional(),
  variables: vine.object({}).optional(),
})

const smtpSchema = vine.object({
  host: vine.string().trim(),
  port: vine.number(),
  secure: vine.boolean().optional(),
  auth: vine.object({
    user: vine.string().trim(),
    pass: vine.string().trim(),
  }),
})

const senderSchema = vine.object({
  name: vine.string().trim(),
  email: vine.string().email(),
})

const bucketTypeValidator = vine.compile(bucketTypeSchema)
const bucketConfigValidator = vine.compile(bucketConfigSchema)
const storageValidator = vine.compile(storageSchema)
const simpleEmailValidator = vine.compile(simpleEmailSchema)
const transactionalEmailValidator = vine.compile(transactionalEmailSchema)
const smtpValidator = vine.compile(smtpSchema)
const senderValidator = vine.compile(senderSchema)
const requestFilterValidator = vine.compile(requestFilterSchema)

export {
  bucketTypeValidator,
  bucketConfigValidator,
  storageValidator,
  simpleEmailValidator,
  transactionalEmailValidator,
  smtpValidator,
  senderValidator,
  requestFilterValidator,
}

export * from '#app/modules/agents/agent_validator'
export * from '#app/modules/users/user_validator'
export * from '#app/modules/chats/chat_validator'
export * from '#app/modules/contacts/contact_validator'
export * from '#app/modules/tools/tool_validator'
export * from '#app/modules/subscriptions/subscription_validator'
export * from '#app/modules/whitelabel/keys/key_validator'
export * from '#app/modules/whitelabel/smtps/smtp_validator'
export * from '#app/modules/whitelabel/domains/domain_validator'
export * from '#app/modules/whitelabel/copyrights/copyright_validator'
export * from '#app/modules/auth/auth_validator'
export * from '#app/modules/apps/app_validator'
export * from '#app/modules/rag/providers/provider_validator'
export * from '#app/modules/rag/sources/source_validator'
export * from '#app/modules/rag/embeddings/embedding_validator'
export * from '#app/modules/rag/training/training_validator'
