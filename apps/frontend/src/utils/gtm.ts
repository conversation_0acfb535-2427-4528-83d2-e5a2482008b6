export const loadGtmScript = (gtmId: string): void => {
  if (!gtmId || typeof window === 'undefined' || document.getElementById(`gtm-${gtmId}`)) {
    console.log('GTM script loading skipped:', {
      gtmId,
      typeofWindow: typeof window,
      elementExists: !!document.getElementById(`gtm-${gtmId}`),
    })

    return
  }

  console.log(`Loading GTM script for ID: ${gtmId}`)

  // GTM Data Layer initialization
  ;(window as any).dataLayer = (window as any).dataLayer || []
  ;(window as any).dataLayer.push({
    'gtm.start': new Date().getTime(),
    'event': 'gtm.js',
  })

  // Create and append the GTM script tag
  const gtmScript = document.createElement('script')
  gtmScript.id = `gtm-${gtmId}` // Add an ID to prevent duplicates
  gtmScript.async = true
  gtmScript.src = `https://www.googletagmanager.com/gtm.js?id=${gtmId}`

  const headElement = document.head || document.getElementsByTagName('head')[0]
  headElement.appendChild(gtmScript)

  // Optional: Add noscript fallback
  const noScript = document.createElement('noscript')
  const gtmIframe = document.createElement('iframe')
  gtmIframe.src = `https://www.googletagmanager.com/ns.html?id=${gtmId}`
  gtmIframe.height = '0'
  gtmIframe.width = '0'
  gtmIframe.style.display = 'none'
  gtmIframe.style.visibility = 'hidden'
  noScript.appendChild(gtmIframe)
  // Insert noscript preferably right after the opening body tag if possible,
  // but inserting before the first child is a common practice in SPAs.
  if (document.body) {
    document.body.insertBefore(noScript, document.body.firstChild)
  } else {
    // Fallback if body isn't ready yet (less likely in onMounted)
    document.addEventListener('DOMContentLoaded', () => {
      document.body.insertBefore(noScript, document.body.firstChild)
    })
  }
}
