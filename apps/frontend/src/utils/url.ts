/**
 * Extracts the domain with subdomain from a URL
 * Removes protocol, paths, query parameters, fragments, and trailing slashes
 * @param url - The URL to extract domain from
 * @returns Clean domain with subdomain (e.g., "api.dev.app.com")
 */
export function extractDomain(url: string): string {
  try {
    // Use URL API for reliable parsing
    const urlObj = new URL(url)
    return urlObj.hostname
  } catch {
    // Fallback regex for malformed URLs
    const match = url.match(/^(?:https?:\/\/)?([^\/\?#]+)/i)
    return match ? match[1] : ''
  }
}
