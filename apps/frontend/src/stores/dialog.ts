import { ref, markRaw, type Component } from 'vue'
import { defineStore } from 'pinia'
import { defineAsyncComponent } from 'vue'

export interface DialogInstance {
  id: string
  key: string
  isOpen: boolean
  component: Component | null
  props?: Record<string, any>
}

export interface DialogConfig<TProps = any> {
  key: string
  component: Component
  allowMultiple?: boolean
  propsSchema?: (props: TProps) => boolean
}

/**
 * DIALOG PROPS SYSTEM
 *
 * This system automatically extracts prop types from dialog components.
 *
 * HOW IT WORKS:
 * 1. Each dialog component defines its props using defineProps<T>()
 * 2. We import the component types and extract their props
 * 3. We merge with BaseDialogProps (dialogId) automatically
 *
 * ADDING NEW DIALOGS:
 * 1. Create your dialog component with defineProps<YourProps>()
 * 2. Add the component import below
 * 3. Add the prop type extraction
 * 4. Add to DialogPropsMap
 * 5. Add to dialogConfigs
 *
 * EXAMPLE COMPONENT:
 * ```vue
 * <script setup lang="ts">
 * defineProps<{
 *   userId?: string
 *   mode: 'create' | 'edit'
 *   dialogId: string  // This will be merged automatically
 * }>()
 * </script>
 * ```
 */

// Import dialog component types for prop extraction
import type AgentInstallComponent from '@/components/agent/install/AgentInstall.vue'
import type AgentDesignComponent from '@/components/agent/design/AgentDesign.vue'
import type AgentSettingsComponent from '@/components/agent/settings/AgentSettings.vue'
import type AgentKnowledgeComponent from '@/components/agent/knowledge/AgentKnowledge.vue'
import type AgentToolsComponent from '@/components/agent/tools/AgentTools.vue'
import type AgentInboxComponent from '@/components/agent/inbox/AgentInbox.vue'
import type AgentIntegrationComponent from '@/components/agent/integration/AgentIntegration.vue'
import type AgentFeedbacksComponent from '@/components/agent/feedbacks/AgentFeedbacks.vue'
import type AgentFeedbacksFormComponent from '@/components/agent/feedbacks/AgentFeedbacksForm.vue'
import type AgentLeadsFormComponent from '@/components/agent/leads/AgentLeadsForm.vue'
import type AccountComponent from '@/components/account/Account.vue'
import type UsersComponent from '@/components/users/Users.vue'
import type UsersFormComponent from '@/components/users/UsersForm.vue'
import type SubscriptionsComponent from '@/components/subscriptions/Subscriptions.vue'
import type SupportComponent from '@/components/support/Support.vue'
import type WhitelabelComponent from '@/components/whitelabel/Whitelabel.vue'
import type WhitelabelCopyrightsFormComponent from '@/components/whitelabel/copyrights/WhitelabelCopyrightsForm.vue'
import type WhitelabelDomainsFormComponent from '@/components/whitelabel/domains/WhitelabelDomainsForm.vue'
import type WhitelabelKeysFormComponent from '@/components/whitelabel/keys/WhitelabelKeysForm.vue'
import type WhitelabelSmtpsFormComponent from '@/components/whitelabel/smtps/WhitelabelSmtpsForm.vue'

// Base props that all dialogs receive from the dialog store
export interface BaseDialogProps {
  dialogId: string
}

// Utility type to extract props from Vue components
type ExtractComponentProps<T> = T extends new (...args: any[]) => { $props: infer P }
  ? P
  : Record<string, never>

// Extract prop types from dialog components and merge with base props
export type AgentInstallProps = ExtractComponentProps<typeof AgentInstallComponent> &
  BaseDialogProps
export type AgentDesignProps = ExtractComponentProps<typeof AgentDesignComponent> & BaseDialogProps
export type AgentSettingsProps = ExtractComponentProps<typeof AgentSettingsComponent> &
  BaseDialogProps
export type AgentKnowledgeProps = ExtractComponentProps<typeof AgentKnowledgeComponent> &
  BaseDialogProps
export type AgentToolsProps = ExtractComponentProps<typeof AgentToolsComponent> & BaseDialogProps
export type AgentInboxProps = ExtractComponentProps<typeof AgentInboxComponent> & BaseDialogProps
export type AgentIntegrationProps = ExtractComponentProps<typeof AgentIntegrationComponent> &
  BaseDialogProps
export type AgentFeedbacksProps = ExtractComponentProps<typeof AgentFeedbacksComponent> &
  BaseDialogProps
export type AgentFeedbacksFormProps = ExtractComponentProps<typeof AgentFeedbacksFormComponent> &
  BaseDialogProps
export type AgentLeadsFormProps = ExtractComponentProps<typeof AgentLeadsFormComponent> &
  BaseDialogProps
export type AccountProps = ExtractComponentProps<typeof AccountComponent> & BaseDialogProps
export type UsersProps = ExtractComponentProps<typeof UsersComponent> & BaseDialogProps
export type UsersFormProps = ExtractComponentProps<typeof UsersFormComponent> & BaseDialogProps
export type SubscriptionsProps = ExtractComponentProps<typeof SubscriptionsComponent> &
  BaseDialogProps
export type SupportProps = ExtractComponentProps<typeof SupportComponent> & BaseDialogProps
export type WhitelabelProps = ExtractComponentProps<typeof WhitelabelComponent> & BaseDialogProps
export type WhitelabelCopyrightsFormProps = ExtractComponentProps<
  typeof WhitelabelCopyrightsFormComponent
> &
  BaseDialogProps
export type WhitelabelDomainsFormProps = ExtractComponentProps<
  typeof WhitelabelDomainsFormComponent
> &
  BaseDialogProps
export type WhitelabelKeysFormProps = ExtractComponentProps<typeof WhitelabelKeysFormComponent> &
  BaseDialogProps
export type WhitelabelSmtpsFormProps = ExtractComponentProps<typeof WhitelabelSmtpsFormComponent> &
  BaseDialogProps

// Map dialog keys to their automatically extracted prop types
export interface DialogPropsMap {
  'agent.install': AgentInstallProps
  'agent.design': AgentDesignProps
  'agent.settings': AgentSettingsProps
  'agent.knowledge': AgentKnowledgeProps
  'agent.tools': AgentToolsProps
  'agent.inbox': AgentInboxProps
  'agent.integration': AgentIntegrationProps
  'agent.feedbacks': AgentFeedbacksProps
  'agent.feedbacks.form': AgentFeedbacksFormProps
  'agent.leads.form': AgentLeadsFormProps
  'app.account': AccountProps
  'app.users': UsersProps
  'app.users.form': UsersFormProps
  'app.subscriptions': SubscriptionsProps
  'app.support': SupportProps
  'app.whitelabel': WhitelabelProps
  'app.whitelabel.copyrights.form': WhitelabelCopyrightsFormProps
  'app.whitelabel.domains.form': WhitelabelDomainsFormProps
  'app.whitelabel.keys.form': WhitelabelKeysFormProps
  'app.whitelabel.smtps.form': WhitelabelSmtpsFormProps
}

// Centralized dialog registry with unique keys
export const DIALOG_KEYS = {
  // Agent dialogs
  AGENT_INSTALL: 'agent.install',
  AGENT_DESIGN: 'agent.design',
  AGENT_SETTINGS: 'agent.settings',
  AGENT_KNOWLEDGE: 'agent.knowledge',
  AGENT_TOOLS: 'agent.tools',
  AGENT_INBOX: 'agent.inbox',
  AGENT_INTEGRATION: 'agent.integration',
  AGENT_FEEDBACKS: 'agent.feedbacks',
  AGENT_FEEDBACKS_FORM: 'agent.feedbacks.form',
  AGENT_LEADS_FORM: 'agent.leads.form',

  // App dialogs
  ACCOUNT: 'app.account',
  USERS: 'app.users',
  USERS_FORM: 'app.users.form',
  SUBSCRIPTIONS: 'app.subscriptions',
  SUPPORT: 'app.support',

  // Whitelabel dialogs
  WHITELABEL: 'app.whitelabel',
  WHITELABEL_COPYRIGHTS_FORM: 'app.whitelabel.copyrights.form',
  WHITELABEL_DOMAINS_FORM: 'app.whitelabel.domains.form',
  WHITELABEL_KEYS_FORM: 'app.whitelabel.keys.form',
  WHITELABEL_SMTPS_FORM: 'app.whitelabel.smtps.form',
} as const

export type DialogKey = (typeof DIALOG_KEYS)[keyof typeof DIALOG_KEYS]

// Dialog configurations
export const dialogConfigs: Record<DialogKey, DialogConfig> = {
  [DIALOG_KEYS.AGENT_INSTALL]: {
    key: DIALOG_KEYS.AGENT_INSTALL,
    component: defineAsyncComponent(() => import('@/components/agent/install/AgentInstall.vue')),
    allowMultiple: false,
  },
  [DIALOG_KEYS.AGENT_DESIGN]: {
    key: DIALOG_KEYS.AGENT_DESIGN,
    component: defineAsyncComponent(() => import('@/components/agent/design/AgentDesign.vue')),
    allowMultiple: false,
  },
  [DIALOG_KEYS.AGENT_SETTINGS]: {
    key: DIALOG_KEYS.AGENT_SETTINGS,
    component: defineAsyncComponent(() => import('@/components/agent/settings/AgentSettings.vue')),
    allowMultiple: false,
  },
  [DIALOG_KEYS.AGENT_KNOWLEDGE]: {
    key: DIALOG_KEYS.AGENT_KNOWLEDGE,
    component: defineAsyncComponent(
      () => import('@/components/agent/knowledge/AgentKnowledge.vue')
    ),
    allowMultiple: false,
  },
  [DIALOG_KEYS.AGENT_TOOLS]: {
    key: DIALOG_KEYS.AGENT_TOOLS,
    component: defineAsyncComponent(() => import('@/components/agent/tools/AgentTools.vue')),
    allowMultiple: false,
  },
  [DIALOG_KEYS.AGENT_INBOX]: {
    key: DIALOG_KEYS.AGENT_INBOX,
    component: defineAsyncComponent(() => import('@/components/agent/inbox/AgentInbox.vue')),
    allowMultiple: false,
  },
  [DIALOG_KEYS.AGENT_INTEGRATION]: {
    key: DIALOG_KEYS.AGENT_INTEGRATION,
    component: defineAsyncComponent(
      () => import('@/components/agent/integration/AgentIntegration.vue')
    ),
    allowMultiple: false,
  },
  [DIALOG_KEYS.AGENT_FEEDBACKS]: {
    key: DIALOG_KEYS.AGENT_FEEDBACKS,
    component: defineAsyncComponent(
      () => import('@/components/agent/feedbacks/AgentFeedbacks.vue')
    ),
    allowMultiple: false,
  },
  [DIALOG_KEYS.AGENT_FEEDBACKS_FORM]: {
    key: DIALOG_KEYS.AGENT_FEEDBACKS_FORM,
    component: defineAsyncComponent(
      () => import('@/components/agent/feedbacks/AgentFeedbacksForm.vue')
    ),
    allowMultiple: true,
  },
  [DIALOG_KEYS.AGENT_LEADS_FORM]: {
    key: DIALOG_KEYS.AGENT_LEADS_FORM,
    component: defineAsyncComponent(() => import('@/components/agent/leads/AgentLeadsForm.vue')),
    allowMultiple: true,
  },

  // App dialogs
  [DIALOG_KEYS.ACCOUNT]: {
    key: DIALOG_KEYS.ACCOUNT,
    component: defineAsyncComponent(() => import('@/components/account/Account.vue')),
    allowMultiple: false,
  },
  [DIALOG_KEYS.USERS]: {
    key: DIALOG_KEYS.USERS,
    component: defineAsyncComponent(() => import('@/components/users/Users.vue')),
    allowMultiple: false,
  },
  [DIALOG_KEYS.USERS_FORM]: {
    key: DIALOG_KEYS.USERS_FORM,
    component: defineAsyncComponent(() => import('@/components/users/UsersForm.vue')),
    allowMultiple: true,
  },
  [DIALOG_KEYS.SUBSCRIPTIONS]: {
    key: DIALOG_KEYS.SUBSCRIPTIONS,
    component: defineAsyncComponent(() => import('@/components/subscriptions/Subscriptions.vue')),
    allowMultiple: false,
  },
  [DIALOG_KEYS.SUPPORT]: {
    key: DIALOG_KEYS.SUPPORT,
    component: defineAsyncComponent(() => import('@/components/support/Support.vue')),
    allowMultiple: false,
  },

  // Whitelabel dialogs
  [DIALOG_KEYS.WHITELABEL]: {
    key: DIALOG_KEYS.WHITELABEL,
    component: defineAsyncComponent(() => import('@/components/whitelabel/Whitelabel.vue')),
    allowMultiple: false,
  },
  [DIALOG_KEYS.WHITELABEL_COPYRIGHTS_FORM]: {
    key: DIALOG_KEYS.WHITELABEL_COPYRIGHTS_FORM,
    component: defineAsyncComponent(
      () => import('@/components/whitelabel/copyrights/WhitelabelCopyrightsForm.vue')
    ),
    allowMultiple: true,
  },
  [DIALOG_KEYS.WHITELABEL_DOMAINS_FORM]: {
    key: DIALOG_KEYS.WHITELABEL_DOMAINS_FORM,
    component: defineAsyncComponent(
      () => import('@/components/whitelabel/domains/WhitelabelDomainsForm.vue')
    ),
    allowMultiple: true,
  },
  [DIALOG_KEYS.WHITELABEL_KEYS_FORM]: {
    key: DIALOG_KEYS.WHITELABEL_KEYS_FORM,
    component: defineAsyncComponent(
      () => import('@/components/whitelabel/keys/WhitelabelKeysForm.vue')
    ),
    allowMultiple: true,
  },
  [DIALOG_KEYS.WHITELABEL_SMTPS_FORM]: {
    key: DIALOG_KEYS.WHITELABEL_SMTPS_FORM,
    component: defineAsyncComponent(
      () => import('@/components/whitelabel/smtps/WhitelabelSmtpsForm.vue')
    ),
    allowMultiple: true,
  },
}

export const useDialogStore = defineStore('dialog', () => {
  // Dialog instances state
  const dialogs_instances = ref<Map<string, DialogInstance>>(new Map())

  // Generate unique instance ID
  function generateInstanceId(key: DialogKey): string {
    return `${key}-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`
  }

  // Type-safe dialog opening with overloads for each dialog type
  function openDialog<K extends DialogKey>(
    key: K,
    props?: Omit<DialogPropsMap[K], 'dialogId'>
  ): string {
    const config = dialogConfigs[key]

    if (!config) {
      console.error(`Dialog with key "${key}" not found`)
      return ''
    }

    // Check if dialog allows multiple instances
    if (!config.allowMultiple) {
      // Close existing instance of this dialog type
      const existingInstance = Array.from(dialogs_instances.value.values()).find(
        (instance) => instance.key === key
      )

      if (existingInstance) {
        closeDialog(existingInstance.id)
      }
    }

    // Generate unique instance ID
    const instanceId = generateInstanceId(key)

    // Add dialog instance ID and close handler to props
    const dialogProps = {
      ...props,
      dialogId: instanceId,
    } as DialogPropsMap[K]

    // Create dialog instance
    const instance: DialogInstance = {
      id: instanceId,
      key,
      isOpen: true,
      component: markRaw(config.component),
      props: dialogProps,
    }

    dialogs_instances.value.set(instanceId, instance)

    return instanceId
  }

  // Close dialog by instance ID
  function closeDialog(instanceId: string) {
    const instance = dialogs_instances.value.get(instanceId)
    if (instance) {
      instance.isOpen = false

      setTimeout(() => {
        dialogs_instances.value.delete(instanceId)
      }, 100)
    }
  }

  // Close dialog by key (closes all instances of this dialog type)
  function closeDialogByKey(key: DialogKey) {
    const instancesToClose = Array.from(dialogs_instances.value.values()).filter(
      (instance) => instance.key === key
    )

    instancesToClose.forEach((instance) => {
      closeDialog(instance.id)
    })
  }

  // Get dialog instance by ID
  function getDialog(instanceId: string) {
    return dialogs_instances.value.get(instanceId)
  }

  // Check if dialog is open by instance ID
  function isDialogOpen(instanceId: string) {
    const instance = dialogs_instances.value.get(instanceId)
    return instance?.isOpen || false
  }

  // Check if dialog type is open by key
  function isDialogTypeOpen(key: DialogKey) {
    return Array.from(dialogs_instances.value.values()).some(
      (instance) => instance.key === key && instance.isOpen
    )
  }

  // Get all instances of a dialog type
  function getDialogInstances(key: DialogKey) {
    return Array.from(dialogs_instances.value.values()).filter((instance) => instance.key === key)
  }

  // Close all dialogs
  function closeAllDialogs() {
    dialogs_instances.value.forEach((instance) => {
      instance.isOpen = false
    })

    setTimeout(() => {
      dialogs_instances.value.clear()
    }, 100)
  }

  return {
    // State
    dialogs_instances,

    // Core functions
    openDialog,
    closeDialog,
    closeDialogByKey,

    // Query functions
    getDialog,
    isDialogOpen,
    isDialogTypeOpen,
    getDialogInstances,

    // Utility functions
    closeAllDialogs,
  }
})
