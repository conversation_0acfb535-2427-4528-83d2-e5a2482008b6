import { defineStore } from 'pinia'
import { useDark, useToggle, usePreferredDark } from '@vueuse/core'

export const useThemeStore = defineStore('theme', () => {
  const isDark = useDark({
    selector: 'html',
    attribute: 'class',
    valueDark: 'dark',
    valueLight: '',
  })

  const toggleDark = useToggle(isDark)

  function setColorMode(mode: 'dark' | 'light' | 'auto') {
    if (mode === 'auto') {
      // When setting to auto, we'll use the system preference
      isDark.value = usePreferredDark().value
    } else {
      isDark.value = mode === 'dark'
    }
  }

  return {
    isDark,
    toggleDark,
    setColorMode,
  }
})
