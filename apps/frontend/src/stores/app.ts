import { defineStore } from 'pinia'
import { ref } from 'vue'
import { useLocalStorage } from '@vueuse/core'

export const useAppStore = defineStore('app', () => {
  const currentAppUid = useLocalStorage<string | null>('current_app_uid', null)
  const currentAgentUid = useLocalStorage<string | null>('current_agent_uid', null)
  const currentDomain = useLocalStorage<string>('current_domain', 'app.insertchat.com')
  const authToken = useLocalStorage<string>('auth_token', '')
  const isNavigating = ref(false)
  const sidebarItemState = useLocalStorage<string | null>('sidebar:item-state', null)

  function setCurrentAppUid(uid: string) {
    currentAppUid.value = uid
  }

  function setCurrentAgentUid(uid: string) {
    currentAgentUid.value = uid
  }

  function setCurrentDomain(domain: string) {
    currentDomain.value = domain
  }

  function setNavigating(value: boolean) {
    isNavigating.value = value
  }

  function resetNavigating() {
    isNavigating.value = false
  }

  function setSidebarItemState(state: string | null) {
    sidebarItemState.value = state
  }

  function reset() {
    currentAppUid.value = null
    currentAgentUid.value = null
    currentDomain.value = ''
    isNavigating.value = false
    authToken.value = ''
    sidebarItemState.value = null
  }

  return {
    currentAppUid,
    setCurrentAppUid,
    currentAgentUid,
    setCurrentAgentUid,
    currentDomain,
    setCurrentDomain,
    isNavigating,
    setNavigating,
    resetNavigating,
    authToken,
    sidebarItemState,
    setSidebarItemState,
    reset,
  }
})
