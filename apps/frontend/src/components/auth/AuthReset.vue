<script setup lang="ts">
import { useForm } from 'vee-validate'
import { toTypedSchema } from '@vee-validate/zod'
import * as z from 'zod'
import { useRouter } from 'vue-router'
import {
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormDescription,
  FormMessage,
} from '@/shadcn/ui/form'
import { mutateAuthResetPassword, ensureAuthDomain, queryAuthDomain } from '@/services/auth_service'

const router = useRouter()
const { isLoading, data: domain } = queryAuthDomain('https://api.dev.app.com')
const mutation = mutateAuthResetPassword()
const { handleSubmit, isSubmitting } = useForm({
  validationSchema: toTypedSchema(
    z.object({
      email: z.string().email({ message: 'Invalid email address' }),
    })
  ),
})

// Functions
const onSubmit = handleSubmit(async (values) => {
  try {
    await mutation.mutateAsync({ email: values.email })

    toast({
      title: 'Reset Link Sent',
      description: 'Please check your email for the reset link.',
      variant: 'default',
    })

    router.push('/auth/login')
  } catch (error: any) {
    toast({
      title: 'Reset Error',
      description: error?.message || 'Failed to send reset link',
      variant: 'destructive',
    })
  }
})
</script>

<template>
  <div class="flex items-center justify-center min-h-screen">
    <div class="flex flex-col items-center w-full">
      <template v-if="isLoading">
        <Skeleton class="w-[200px] h-[40px] mb-6" />
      </template>
      <template v-else>
        <img
          v-if="domain?.logo_url"
          :src="domain?.logo_url"
          :alt="domain?.company_name || 'Logo'"
          class="w-[200px] h-auto mb-6"
        />
      </template>

      <BaseCard title="Reset Password" description="Enter your email to reset your password">
        <form @submit="onSubmit" class="grid gap-4">
          <FormField name="email" v-slot="{ field: componentField }">
            <FormItem>
              <FormLabel>Email</FormLabel>
              <FormControl>
                <Input type="email" placeholder="<EMAIL>" v-bind="componentField" />
              </FormControl>
              <FormDescription>Enter your email address.</FormDescription>
              <FormMessage />
            </FormItem>
          </FormField>

          <BaseButton type="submit" class="w-full" :isDisabled="false" :isSubmitting="isSubmitting">
            Send reset link
          </BaseButton>
        </form>

        <template #footer>
          <div class="mt-4 text-sm text-center">
            Remember your password?
            <a @click.prevent="router.push('/auth/login')" class="underline cursor-pointer">
              Login
            </a>
          </div>
        </template>
      </BaseCard>
    </div>
  </div>
</template>
