<script setup lang="ts">
import { useForm } from 'vee-validate'
import { toTypedSchema } from '@vee-validate/zod'
import * as z from 'zod'
import { useRouter } from 'vue-router'
import {
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormDescription,
  FormMessage,
} from '@/shadcn/ui/form'
import {
  mutateAuthRegister,
  mutateAuthAccessGoogle,
  ensureAuthDomain,
  queryAuthDomain,
} from '@/services/auth_service'

// Composables
const router = useRouter()
const { isLoading, data: domain } = queryAuthDomain('https://api.dev.app.com')
const mutation = mutateAuthRegister()
const { handleSubmit, isSubmitting } = useForm({
  validationSchema: toTypedSchema(
    z.object({
      firstName: z.string().min(2, { message: 'First name must be at least 2 characters' }),
      email: z.string().email({ message: 'Invalid email address' }),
      password: z.string().min(6, { message: 'Password must be at least 6 characters' }),
    })
  ),
})

// Functions
const onSubmit = handleSubmit(async (values) => {
  try {
    await mutation.mutateAsync({
      email: values.email,
      password: values.password,
    })

    toast({
      title: 'Registration Successful',
      description: 'Your account has been created. Please log in.',
      variant: 'default',
    })

    router.push('/auth/login')
  } catch (error: any) {
    toast({
      title: 'Registration Error',
      description: error?.message || 'Registration failed',
      variant: 'destructive',
    })
  }
})

async function handleGoogle() {
  try {
    await mutateAuthAccessGoogle().mutateAsync({
      token: 'GOOGLE_OAUTH_TOKEN',
      gclid: '',
      utm: {},
      qs: {},
    })

    router.push('/')
  } catch (error: any) {
    toast({
      title: 'Google Login Error',
      description: error?.message || 'Google login failed',
      variant: 'destructive',
    })
  }
}
</script>

<template>
  <div class="flex items-center justify-center min-h-screen">
    <div class="flex flex-col items-center w-full">
      <template v-if="isLoading">
        <Skeleton class="w-[200px] h-[40px] mb-6" />
      </template>
      <template v-else>
        <img
          v-if="domain?.logo_url"
          :src="domain?.logo_url"
          :alt="domain?.company_name || 'Logo'"
          class="w-[200px] h-auto mb-6"
        />
      </template>

      <BaseCard title="Register" description="Enter your details below to create your account">
        <form @submit="onSubmit" class="grid gap-4">
          <FormField name="firstName" v-slot="{ field: componentField }">
            <FormItem>
              <FormLabel>First name</FormLabel>
              <FormControl>
                <Input type="text" placeholder="John" v-bind="componentField" />
              </FormControl>
              <FormDescription>Enter your first name.</FormDescription>
              <FormMessage />
            </FormItem>
          </FormField>

          <FormField name="email" v-slot="{ field: componentField }">
            <FormItem>
              <FormLabel>Email</FormLabel>
              <FormControl>
                <Input type="email" placeholder="<EMAIL>" v-bind="componentField" />
              </FormControl>
              <FormDescription>Enter your email address.</FormDescription>
              <FormMessage />
            </FormItem>
          </FormField>

          <FormField name="password" v-slot="{ field: componentField }">
            <FormItem>
              <FormLabel>Password</FormLabel>
              <FormControl>
                <Input type="password" placeholder="••••••••" v-bind="componentField" />
              </FormControl>
              <FormDescription>Enter your password (at least 6 characters).</FormDescription>
              <FormMessage />
            </FormItem>
          </FormField>

          <BaseButton type="submit" class="w-full" :isSubmitting="isSubmitting">
            Create an account
          </BaseButton>

          <Button variant="outline" class="w-full" :disabled="isSubmitting" @click="handleGoogle">
            Sign up with Google
          </Button>
        </form>

        <template #footer>
          <div class="mt-4 text-sm text-center">
            Already have an account?
            <a @click.prevent="router.push('/auth/login')" class="underline cursor-pointer">
              Sign in
            </a>
          </div>
        </template>
      </BaseCard>
    </div>
  </div>
</template>
