<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useForm } from 'vee-validate'
import { toTypedSchema } from '@vee-validate/zod'
import * as z from 'zod'
import { useRouter } from 'vue-router'
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/shadcn/ui/form'
import {
  mutateAuthLogin,
  mutateAuthAccessGoogle,
  ensureAuthDomain,
  queryAuthDomain,
} from '@/services/auth_service'
import { useAppStore } from '@/stores/app'

const router = useRouter()
const appStore = useAppStore()
const mutation = mutateAuthLogin()
const { isLoading, data: domain } = queryAuthDomain('https://api.dev.app.com')
const { handleSubmit, isSubmitting } = useForm({
  validationSchema: toTypedSchema(
    z.object({
      email: z.string().email({ message: 'Invalid email address' }),
      password: z.string().min(6, { message: 'Password must be at least 6 characters' }),
    })
  ),
})

const onSubmit = handleSubmit(async (values) => {
  try {
    const foo = await mutation.mutateAsync({
      email: values.email,
      password: values.password,
    })

    console.log('foo', foo)

    const { token, user } = foo

    console.log('token', token)
    console.log('user', user)

    if (!token || !user) {
      throw new Error('Login failed')
    }

    appStore.authToken = token

    router.push('/')
    toast({
      title: 'Login Successful',
      description: 'Welcome back!',
      variant: 'default',
    })
  } catch (error: any) {
    toast({
      title: 'Login Error',
      description: error?.message || 'Login failed',
      variant: 'destructive',
    })
  }
})

async function handleGoogle() {
  try {
    await mutateAuthAccessGoogle().mutateAsync({
      token: 'GOOGLE_OAUTH_TOKEN',
      gclid: '',
      utm: {},
      qs: {},
    })

    router.push('/')
    toast({
      title: 'Google Login Successful',
      description: 'Welcome back!',
      variant: 'default',
    })
  } catch (error: any) {
    toast({
      title: 'Google Login Error',
      description: error?.message || 'Google login failed',
      variant: 'destructive',
    })
  }
}
</script>

<template>
  <div class="flex items-center justify-center min-h-screen">
    <div class="flex flex-col items-center w-full px-4">
      <template v-if="isLoading">
        <Skeleton class="w-[200px] h-[40px] mb-6" />
      </template>
      <template v-else>
        <img
          v-if="domain?.logo_url"
          :src="domain?.logo_url"
          :alt="domain?.company_name || 'Logo'"
          class="w-[200px] h-auto mb-6"
        />
      </template>

      <BaseCard title="Login" description="Enter your email below to login to your account">
        <form @submit="onSubmit" class="grid gap-4">
          <FormField name="email" v-slot="{ componentField }">
            <FormItem>
              <FormLabel>Email</FormLabel>
              <FormControl>
                <Input type="email" placeholder="<EMAIL>" v-bind="componentField" />
              </FormControl>
              <FormDescription>Enter your email address.</FormDescription>
              <FormMessage />
            </FormItem>
          </FormField>

          <FormField name="password" v-slot="{ componentField }">
            <FormItem>
              <FormLabel>
                Password
                <a @click.prevent="router.push('/auth/reset')" class="ml-auto text-sm underline">
                  Forgot your password?
                </a>
              </FormLabel>
              <FormControl>
                <Input type="password" placeholder="••••••••" v-bind="componentField" />
              </FormControl>
              <FormDescription>Enter your password.</FormDescription>
              <FormMessage />
            </FormItem>
          </FormField>

          <BaseButton type="submit" class="w-full" :isDisabled="false" :isSubmitting="isSubmitting">
            Login
          </BaseButton>

          <BaseButton
            variant="outline"
            class="w-full"
            :isDisabled="false"
            :isSubmitting="false"
            @click="handleGoogle"
          >
            Login with Google
          </BaseButton>
        </form>

        <template #footer>
          <div class="mt-4 text-sm text-center">
            Don't have an account?
            <a @click.prevent="router.push('/auth/register')" class="underline">Sign up</a>
          </div>
        </template>
      </BaseCard>
    </div>
  </div>
</template>
