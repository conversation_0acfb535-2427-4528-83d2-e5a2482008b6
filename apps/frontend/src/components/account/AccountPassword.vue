<script setup lang="ts">
import { useForm } from 'vee-validate'
import { toTypedSchema } from '@vee-validate/zod'
import * as z from 'zod'
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/shadcn/ui/form'
import { mutateAccountPassword } from '@/services/account_service'

// Composables
const mutation = mutateAccountPassword()
const { handleSubmit, isSubmitting } = useForm({
  validationSchema: toTypedSchema(
    z
      .object({
        current_password: z.string().min(6, { message: 'Current password is required' }),
        password: z.string().min(6, { message: 'New password must be at least 6 characters' }),
        password_confirm: z.string().min(6, { message: 'Confirm password is required' }),
      })
      .refine((data) => data.password === data.password_confirm, {
        message: 'Passwords do not match',
        path: ['password_confirm'],
      })
  ),
})

// Functions
const onSubmit = handleSubmit(async (values) => {
  try {
    await mutation.mutateAsync({
      password: values.password,
      password_confirm: values.password_confirm,
    })

    toast({ title: 'Password updated successfully' })
  } catch (error: any) {
    toast({
      title: 'Failed to update password',
      description: error?.message || 'Password update failed',
      variant: 'destructive',
    })
  }
})
</script>

<template>
  <BaseForm :isLoading="false" :isSubmitting="isSubmitting" :inputs="3" @submit="onSubmit">
    <FormField name="current_password" v-slot="{ componentField }">
      <FormItem>
        <FormLabel>Current Password</FormLabel>
        <FormControl>
          <Input type="password" placeholder="Current Password" v-bind="componentField" />
        </FormControl>
        <FormDescription>Enter your current password.</FormDescription>
        <FormMessage />
      </FormItem>
    </FormField>

    <FormField name="password" v-slot="{ componentField }">
      <FormItem>
        <FormLabel>New Password</FormLabel>
        <FormControl>
          <Input type="password" placeholder="New Password" v-bind="componentField" />
        </FormControl>
        <FormDescription>Enter your new password (at least 6 characters).</FormDescription>
        <FormMessage />
      </FormItem>
    </FormField>

    <FormField name="password_confirm" v-slot="{ componentField }">
      <FormItem>
        <FormLabel>Confirm Password</FormLabel>
        <FormControl>
          <Input type="password" placeholder="Confirm Password" v-bind="componentField" />
        </FormControl>
        <FormDescription>Re-enter your new password to confirm.</FormDescription>
        <FormMessage />
      </FormItem>
    </FormField>

    <BaseButton type="submit" :isDisabled="false" :isSubmitting="isSubmitting"> Save </BaseButton>
  </BaseForm>
</template>
