<script setup lang="ts">
import { useForm } from 'vee-validate'
import { toTypedSchema } from '@vee-validate/zod'
import * as z from 'zod'
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/shadcn/ui/form'
import { ensureAuthMe } from '@/services/auth_service'
import { mutateAccountEmail } from '@/services/account_service'

// Composables
const mutation = mutateAccountEmail()
const { handleSubmit, isSubmitting, resetForm } = useForm({
  initialValues: {
    current_email: '',
  },
  validationSchema: toTypedSchema(
    z.object({
      current_email: z.string().email({ message: 'Invalid email address' }).readonly(),
      email: z.string().email({ message: 'Invalid email address' }),
      password: z.string().min(6, { message: 'Password is required' }),
    })
  ),
})

onMounted(async () => {
  const { user } = await ensureAuthMe()

  resetForm({ values: { current_email: user.email ?? '' } })
})

// Functions
const onSubmit = handleSubmit(async (values) => {
  try {
    await mutation.mutateAsync({ password: values.password, new_email: values.email })

    toast({ title: 'Email updated successfully' })
  } catch (error: any) {
    toast({
      title: 'Failed to update email',
      description: error?.message || 'Email update failed',
      variant: 'destructive',
    })
  }
})
</script>

<template>
  <BaseForm :isLoading="false" :isSubmitting="isSubmitting" :inputs="2" @submit="onSubmit">
    <FormField name="current_email" v-slot="{ componentField }">
      <FormItem>
        <FormLabel>Current Email</FormLabel>
        <FormControl>
          <Input
            type="email"
            placeholder="Current Email"
            :disabled="true"
            v-bind="componentField"
          />
        </FormControl>
        <FormDescription>Your current email address.</FormDescription>
        <FormMessage />
      </FormItem>
    </FormField>

    <FormField name="email" v-slot="{ componentField }">
      <FormItem>
        <FormLabel>New Email</FormLabel>
        <FormControl>
          <Input type="email" placeholder="Email" v-bind="componentField" />
        </FormControl>
        <FormDescription>Enter your new email address.</FormDescription>
        <FormMessage />
      </FormItem>
    </FormField>

    <FormField name="password" v-slot="{ componentField }">
      <FormItem>
        <FormLabel>Current Password</FormLabel>
        <FormControl>
          <Input
            type="password"
            placeholder="Enter your current password"
            v-bind="componentField"
          />
        </FormControl>
        <FormDescription> Please enter your current password. </FormDescription>
        <FormMessage />
      </FormItem>
    </FormField>

    <BaseButton type="submit" :isDisabled="false" :isSubmitting="isSubmitting"> Save </BaseButton>
  </BaseForm>
</template>
