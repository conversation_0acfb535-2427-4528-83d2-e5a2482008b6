<script setup lang="ts">
import { useForm } from 'vee-validate'
import { toTypedSchema } from '@vee-validate/zod'
import * as z from 'zod'
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/shadcn/ui/form'
import { ensureAuthMe } from '@/services/auth_service'
import { mutateAccountDetails } from '@/services/account_service'
import { onMounted } from 'vue'

// Composables
const mutation = mutateAccountDetails()
const { handleSubmit, isSubmitting, resetForm } = useForm({
  initialValues: {
    first_name: '',
    last_name: '',
  },
  validationSchema: toTypedSchema(
    z.object({
      first_name: z.string().min(2, { message: 'First name is required' }),
      last_name: z.string().min(2, { message: 'Last name is required' }),
    })
  ),
})

onMounted(async () => {
  const { user } = await ensureAuthMe()

  resetForm({ values: { first_name: user.first_name ?? '', last_name: user.last_name ?? '' } })
})

// Functions
const onSubmit = handleSubmit(async (values) => {
  try {
    await mutation.mutateAsync({
      first_name: values.first_name,
      last_name: values.last_name,
    })

    toast({ title: 'Profile updated successfully' })
  } catch (error: any) {
    toast({
      title: 'Failed to update profile',
      description: error?.message || 'Profile update failed',
      variant: 'destructive',
    })
  }
})
</script>

<template>
  <BaseForm :isLoading="false" :isSubmitting="isSubmitting" :inputs="2" @submit="onSubmit">
    <FormField name="first_name" v-slot="{ componentField }">
      <FormItem>
        <FormLabel>First Name</FormLabel>
        <FormControl>
          <Input type="text" placeholder="First Name" v-bind="componentField" />
        </FormControl>
        <FormDescription>Enter your first name.</FormDescription>
        <FormMessage />
      </FormItem>
    </FormField>

    <FormField name="last_name" v-slot="{ componentField }">
      <FormItem>
        <FormLabel>Last Name</FormLabel>
        <FormControl>
          <Input type="text" placeholder="Last Name" v-bind="componentField" />
        </FormControl>
        <FormDescription>Enter your last name.</FormDescription>
        <FormMessage />
      </FormItem>
    </FormField>

    <BaseButton type="submit" :isDisabled="false" :isSubmitting="isSubmitting"> Save </BaseButton>
  </BaseForm>
</template>
