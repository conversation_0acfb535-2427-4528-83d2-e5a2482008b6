<script setup lang="ts">
import { useThemeStore } from '@/stores/theme'

const themeStore = useThemeStore()
</script>

<template>
  <DropdownMenu>
    <DropdownMenuTrigger as-child>
      <Button variant="outline" size="icon">
        <Sun
          class="h-[1.2rem] w-[1.2rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0"
        />
        <Moon
          class="absolute h-[1.2rem] w-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100"
        />
        <span class="sr-only">Toggle theme</span>
      </Button>
    </DropdownMenuTrigger>
    <DropdownMenuContent align="end">
      <DropdownMenuItem @click="themeStore.setColorMode('light')">
        <Sun class="w-4 h-4 mr-2" />
        <span>Light</span>
      </DropdownMenuItem>
      <DropdownMenuItem @click="themeStore.setColorMode('dark')">
        <Moon class="w-4 h-4 mr-2" />
        <span>Dark</span>
      </DropdownMenuItem>
      <DropdownMenuItem @click="themeStore.setColorMode('auto')">
        <Laptop class="w-4 h-4 mr-2" />
        <span>System</span>
      </DropdownMenuItem>
    </DropdownMenuContent>
  </DropdownMenu>
</template>
