<template>
  <div
    class="flex flex-col w-full px-2 py-2 space-y-2 transition-colors duration-200 border rounded-md border-input bg-background"
    :class="{ 'ring-2 ring-primary ring-offset-2 ring-offset-background': isDragging }"
    @dragover.prevent="handleDragOver"
    @dragleave.prevent="handleDragLeave"
    @drop.prevent="handleDrop"
    @paste="handlePaste"
  >
    <!-- Uploaded files preview -->
    <div
      v-if="uploadedFiles.length > 0"
      class="flex flex-wrap gap-2 px-2 pb-2 border-b border-border/50"
    >
      <ChatTextareaFiles
        v-for="(file, index) in uploadedFiles"
        :key="index"
        :file="file"
        @remove="removeFile(index)"
      />
    </div>

    <!-- Message textarea -->
    <BaseTextarea
      ref="baseTextareaRef"
      v-model="message"
      :rows="1"
      placeholder="Ask anything"
      class="block w-full min-h-[40px] max-h-[300px] m-0 rounded-none bg-transparent border-0 shadow-none resize-none focus-visible:ring-0 focus-visible:outline-none placeholder:text-muted-foreground"
      @keydown.enter.prevent="handleSend"
    />

    <!-- Action buttons -->
    <div class="flex items-center justify-between flex-shrink-0">
      <!-- Left-aligned buttons -->
      <div class="flex items-center gap-1">
        <Button variant="ghost" size="icon" @click="openFilePicker">
          <BaseIcon name="Paperclip" />
        </Button>
        <Button variant="ghost" size="icon" @click="handleSearch">
          <BaseIcon name="Search" />
        </Button>
        <Button variant="ghost" size="icon" @click="handleMicClick">
          <BaseIcon name="Mic" />
        </Button>
      </div>

      <!-- Right-aligned send button -->
      <Button
        size="icon"
        :disabled="!message.trim() && uploadedFiles.length === 0"
        @click="handleSend"
        class="rounded-full"
      >
        <BaseIcon name="Send" />
      </Button>
    </div>

    <!-- Hidden file input -->
    <input ref="fileInput" type="file" multiple class="sr-only" @change="handleFileUpload" />
  </div>
</template>

<script setup lang="ts">
// Define type for the exposed methods of BaseTextarea
interface BaseTextareaInstance {
  resetHeight: () => void
}

// Define the payload for the send event
interface SendPayload {
  text: string
  files: File[]
}

const emit = defineEmits<{
  (e: 'send', payload: SendPayload): void // Updated send event payload
  // (e: 'upload', payload: File): void // Removed upload event, handled locally
  (e: 'search'): void
  (e: 'mic-click'): void
}>()

// Message being composed by the user
const message = ref('')
// List of uploaded files
const uploadedFiles = ref<File[]>([])
// Ref to the BaseTextarea component instance
const baseTextareaRef = ref<BaseTextareaInstance | null>(null)
// Hidden file input reference
const fileInput = ref<HTMLInputElement | null>(null)

// State for drag-and-drop visual feedback
const isDragging = ref(false)

// Send the message and files, then clear
function handleSend() {
  const textToSend = message.value.trim()
  const filesToSend = [...uploadedFiles.value] // Clone the array

  if (!textToSend && filesToSend.length === 0) return // Prevent sending empty messages

  emit('send', { text: textToSend, files: filesToSend })

  // Clear inputs after sending
  message.value = ''
  uploadedFiles.value = []

  // Use nextTick to ensure the textarea value is cleared before resetting height
  nextTick(() => {
    baseTextareaRef.value?.resetHeight()
  })
}

// Trigger the hidden file input
function openFilePicker() {
  fileInput.value?.click()
}

// Add selected files to the list
function handleFileUpload(event: Event) {
  const target = event.target as HTMLInputElement
  const files = target.files
  if (files) {
    // Add newly selected files to the existing list
    uploadedFiles.value.push(...Array.from(files))
  }
  // Reset the input so the same file can be uploaded again if needed
  target.value = ''
}

// Remove a file from the list by index
function removeFile(index: number) {
  uploadedFiles.value.splice(index, 1)
}

// Placeholder for search functionality
function handleSearch() {
  console.log('Search button clicked')
  emit('search')
}

// Placeholder for Mic button click
function handleMicClick() {
  console.log('Mic button clicked')
  emit('mic-click')
}

// --- Drag and Drop Handlers ---
function handleDragOver(event: DragEvent) {
  console.log('Drag over event detected.', event)
  // Optional: Add checks here if needed, e.g., event.dataTransfer.types
  isDragging.value = true
}

function handleDragLeave(event: DragEvent) {
  // Basic leave detection
  isDragging.value = false
}

function handleDrop(event: DragEvent) {
  isDragging.value = false // Reset visual state
  const files = event.dataTransfer?.files
  console.log('Dropped files:', files)
  if (files && files.length > 0) {
    console.log('Files dropped, attempting to add.')
    console.log('uploadedFiles before push:', uploadedFiles.value)
    uploadedFiles.value.push(...Array.from(files))
    console.log('uploadedFiles after push:', uploadedFiles.value)
  } else {
    console.log('No files found in drop event.')
  }
}

// --- Paste Handler ---
function handlePaste(event: ClipboardEvent) {
  const clipboardData = event.clipboardData
  const files = clipboardData?.files

  console.log('Paste event detected.')
  console.log('Pasted files:', files)
  console.log('Pasted text:', clipboardData?.getData('text/plain'))

  if (files && files.length > 0) {
    console.log('Files detected in paste event, preventing default.')
    event.preventDefault() // Prevent default only when handling files
    console.log('uploadedFiles before paste:', uploadedFiles.value)
    uploadedFiles.value.push(...Array.from(files))
    console.log('uploadedFiles after paste:', uploadedFiles.value)
  } else {
    console.log('No files in paste event, allowing default text paste.')
    // No preventDefault() here - let the browser handle text pasting
  }
}
</script>
