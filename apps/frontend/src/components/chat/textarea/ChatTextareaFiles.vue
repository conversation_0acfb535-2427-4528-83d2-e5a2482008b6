<template>
  <div
    class="relative inline-flex items-center gap-2 p-2 pr-8 text-sm border rounded-md bg-muted/50 border-border/50"
  >
    <!-- Icon based on file type -->
    <BaseIcon :name="fileIcon" class="flex-shrink-0 text-primary" />

    <!-- File details -->
    <div class="flex flex-col overflow-hidden">
      <span class="font-medium truncate">{{ file.name }}</span>
      <span class="text-xs text-muted-foreground">{{ fileTypeDescription }}</span>
    </div>

    <!-- Remove button -->
    <Button
      variant="ghost"
      size="icon"
      class="absolute w-5 h-5 top-1 right-1 text-muted-foreground hover:bg-destructive/20 hover:text-destructive"
      @click="emit('remove')"
    >
      <BaseIcon name="X" />
    </Button>
  </div>
</template>

<script setup lang="ts">
const props = defineProps<{
  file: File
}>()
const emit = defineEmits<{
  (e: 'remove'): void
}>()

// Determine icon based on file type
const fileIcon = computed(() => {
  const type = props.file.type
  if (type.startsWith('image/')) return FileImage
  if (type === 'application/pdf') return FileText
  if (type === 'application/zip' || type === 'application/x-zip-compressed') return FileArchive
  // Add more types as needed
  return File // Default icon
})

// Determine file type description
const fileTypeDescription = computed(() => {
  const type = props.file.type
  if (type.startsWith('image/')) return 'Image'
  if (type === 'application/pdf') return 'PDF'
  if (type === 'application/zip' || type === 'application/x-zip-compressed') return 'Zip Archive'
  // Basic fallback
  return type.split('/')[0] || 'File'
})
</script>
