<script setup lang="ts">
import { queryAgents } from '@/services/agent_service'
import type { Agent } from '@/types'
import { SidebarMenuSkeleton } from '@/shadcn/ui/sidebar'

const { data: agents, isLoading } = queryAgents()

const activeAgent = ref<Agent | null>(null)

// Set the first agent as active when agents are loaded
watchEffect(() => {
  if (agents.value?.data && agents.value.data.length > 0 && !activeAgent.value) {
    activeAgent.value = agents.value.data[0]
  }
})

function setActiveAgent(agent: Agent) {
  activeAgent.value = agent
}

// Helper function to get agent display name
function getAgentDisplayName(agent: Agent): string {
  return agent.label || 'Unnamed Agent'
}

// Helper function to get agent icon
function getAgentIcon(agent: Agent): string {
  // Use a default icon based on agent purpose or fallback to a generic one
  const purposeIcons: Record<string, string> = {
    support: 'Headphones',
    sales: 'TrendingUp',
    ecommerce: 'ShoppingCart',
    marketing: 'Megaphone',
    education: 'GraduationCap',
    healthcare: 'Heart',
    real_estate: 'Home',
    personal_assistant: 'User',
    character: 'Smile',
    other: 'Bot',
  }
  return purposeIcons[agent.purpose] || 'Bot'
}
</script>

<template>
  <SidebarMenuItem>
    <!-- Loading State -->
    <template v-if="isLoading">
      <SidebarMenuSkeleton show-icon />
    </template>

    <!-- No Agents State -->
    <template v-else-if="!agents?.data || agents.data.length === 0">
      <SidebarMenuButton size="lg" disabled>
        <div
          class="flex items-center justify-center rounded-lg aspect-square size-8 bg-sidebar-primary text-sidebar-primary-foreground"
        >
          <BaseIcon name="Bot" class="size-4" />
        </div>
        <div class="grid flex-1 text-sm leading-tight text-left">
          <span class="font-semibold truncate">No Agents</span>
        </div>
      </SidebarMenuButton>
    </template>

    <!-- Agents Available -->
    <template v-else>
      <DropdownMenu>
        <DropdownMenuTrigger as-child>
          <SidebarMenuButton
            size="lg"
            class="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
          >
            <div
              class="flex items-center justify-center rounded-lg aspect-square size-8 bg-sidebar-primary text-sidebar-primary-foreground"
            >
              <BaseIcon :name="activeAgent ? getAgentIcon(activeAgent) : 'Bot'" class="size-4" />
            </div>
            <div class="grid flex-1 text-sm leading-tight text-left">
              <span class="font-semibold truncate">
                {{ activeAgent ? getAgentDisplayName(activeAgent) : 'Select Agent' }}
              </span>
            </div>
            <BaseIcon name="ChevronDown" class="ml-auto" />
          </SidebarMenuButton>
        </DropdownMenuTrigger>

        <DropdownMenuContent
          class="w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg"
          align="start"
          side="bottom"
          :side-offset="4"
        >
          <DropdownMenuLabel class="text-xs text-muted-foreground"> Agents </DropdownMenuLabel>
          <DropdownMenuItem
            v-for="agent in agents.data"
            :key="agent.uid"
            class="gap-2 p-2"
            @click="setActiveAgent(agent)"
          >
            <div class="flex items-center justify-center border rounded-sm size-6">
              <BaseIcon :name="getAgentIcon(agent)" class="size-4 shrink-0" />
            </div>
            {{ getAgentDisplayName(agent) }}
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuItem class="gap-2 p-2">
            <div class="flex items-center justify-center border rounded-md size-6">
              <BaseIcon name="Plus" />
            </div>
            <div class="font-medium text-muted-foreground">Add agent</div>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </template>
  </SidebarMenuItem>
</template>
