<script setup lang="ts">
const agents = [
  {
    name: 'Customer Support Agent',
    logo: 'GalleryVerticalEnd',
    status: 'active',
  },
  {
    name: 'Sales Assistant',
    logo: 'AudioWaveform',
    status: 'active',
  },
  {
    name: 'Technical Support',
    logo: 'Command',
    status: 'inactive',
  },
]

const activeAgent = ref(agents[0])

function setactiveAgent(agent: (typeof agents)[number]) {
  activeAgent.value = agent
}
</script>

<template>
  <SidebarMenuItem>
    <DropdownMenu>
      <DropdownMenuTrigger as-child>
        <SidebarMenuButton
          size="lg"
          class="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
        >
          <div
            class="flex items-center justify-center rounded-lg aspect-square size-8 bg-sidebar-primary text-sidebar-primary-foreground"
          >
            <BaseIcon :name="activeAgent.logo" class="size-4" />
          </div>
          <div class="grid flex-1 text-sm leading-tight text-left">
            <span class="font-semibold truncate">{{ activeAgent.name }}</span>
            <!--<span class="text-xs truncate">{{ activeAgent.status }}</span>-->
          </div>
          <BaseIcon name="ChevronDown" class="ml-auto" />
        </SidebarMenuButton>
      </DropdownMenuTrigger>

      <DropdownMenuContent
        class="w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg"
        align="start"
        side="bottom"
        :side-offset="4"
      >
        <DropdownMenuLabel class="text-xs text-muted-foreground"> Agents </DropdownMenuLabel>
        <DropdownMenuItem
          v-for="(agent, index) in agents"
          :key="agent.name"
          class="gap-2 p-2"
          @click="setactiveAgent(agent)"
        >
          <div class="flex items-center justify-center border rounded-sm size-6">
            <BaseIcon :name="agent.logo" class="size-4 shrink-0" />
          </div>
          {{ agent.name }}
        </DropdownMenuItem>
        <DropdownMenuSeparator />
        <DropdownMenuItem class="gap-2 p-2">
          <div class="flex items-center justify-center border rounded-md size-6">
            <BaseIcon name="Plus" />
          </div>
          <div class="font-medium text-muted-foreground">Add agent</div>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  </SidebarMenuItem>
</template>
