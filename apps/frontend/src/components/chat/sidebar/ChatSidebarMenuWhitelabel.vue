<script setup lang="ts">
import { useDialogStore, DIALOG_KEYS } from '@/stores/dialog'
import { ICONS } from '@/constants'

const { isMobile } = useSidebar()
const dialogStore = useDialogStore()

// Helper function to open dialogs using the new key system
const openDialog = (key: string) => {
  // Map old string keys to new DIALOG_KEYS
  const keyMap: Record<string, any> = {
    whitelabel: DIALOG_KEYS.WHITELABEL,
  }

  const dialogKey = keyMap[key]
  if (dialogKey) {
    dialogStore.openDialog(dialogKey)
  }
}
</script>

<template>
  <SidebarMenuItem>
    <SidebarMenuButton as-child size="sm" @click="openDialog('whitelabel')">
      <a href="#">
        <BaseIcon :name="ICONS.WHITELABEL" />
        <span>White-Label Settings</span>
      </a>
    </SidebarMenuButton>

    <DropdownMenu>
      <DropdownMenuTrigger as-child>
        <SidebarMenuAction show-on-hover>
          <BaseIcon :name="ICONS.SETTINGS" />
          <span class="sr-only">White-Label Settings</span>
        </SidebarMenuAction>
      </DropdownMenuTrigger>

      <DropdownMenuContent
        class="rounded-lg w-60"
        :side="isMobile ? 'bottom' : 'right'"
        align="end"
      >
        <DropdownMenuItem @click="openDialog('install')">
          <BaseIcon name="Code2" />
          <span>SMTP</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  </SidebarMenuItem>
</template>
