<script setup lang="ts">
import { queryChatHistory } from '@/services/chat_service'
import type { ChatHistoryItem } from '@/types'
import { SidebarMenuSkeleton } from '@/shadcn/ui/sidebar'

const { isMobile } = useSidebar()
const appStore = useAppStore()

// Watch for current agent changes and load chat history
const currentAgentUid = computed(() => appStore.currentAgentUid || undefined)
const { data: chatHistory, isLoading } = queryChatHistory(currentAgentUid)

// Group chats by time periods
const groupedChats = computed(() => {
  if (!chatHistory.value?.data) return []

  const now = new Date()
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
  const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000)
  const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000)

  const groups = {
    today: [] as ChatHistoryItem[],
    yesterday: [] as ChatHistoryItem[],
    thisWeek: [] as ChatHistoryItem[],
    older: [] as ChatHistoryItem[],
  }

  chatHistory.value.data.forEach((chat: ChatHistoryItem) => {
    const chatDate = new Date(chat.created_at.toString())

    if (chatDate >= today) {
      groups.today.push(chat)
    } else if (chatDate >= yesterday) {
      groups.yesterday.push(chat)
    } else if (chatDate >= weekAgo) {
      groups.thisWeek.push(chat)
    } else {
      groups.older.push(chat)
    }
  })

  const result = []

  if (groups.today.length > 0) {
    result.push({
      title: 'Today',
      icon: 'Clock',
      isActive: true,
      items: groups.today.map((chat) => ({
        title: getFirstMessage(chat) || 'New Chat',
        url: `#chat-${chat.uid}`,
        uid: chat.uid,
      })),
    })
  }

  if (groups.yesterday.length > 0) {
    result.push({
      title: 'Yesterday',
      icon: 'Calendar',
      isActive: false,
      items: groups.yesterday.map((chat) => ({
        title: getFirstMessage(chat) || 'New Chat',
        url: `#chat-${chat.uid}`,
        uid: chat.uid,
      })),
    })
  }

  if (groups.thisWeek.length > 0) {
    result.push({
      title: 'This Week',
      icon: 'CalendarDays',
      isActive: false,
      items: groups.thisWeek.map((chat) => ({
        title: getFirstMessage(chat) || 'New Chat',
        url: `#chat-${chat.uid}`,
        uid: chat.uid,
      })),
    })
  }

  if (groups.older.length > 0) {
    result.push({
      title: 'Older',
      icon: 'History',
      isActive: false,
      items: groups.older.map((chat) => ({
        title: getFirstMessage(chat) || 'New Chat',
        url: `#chat-${chat.uid}`,
        uid: chat.uid,
      })),
    })
  }

  return result
})

// Helper function to get first message or fallback
function getFirstMessage(chat: ChatHistoryItem): string {
  // Try to get from metadata or external_id, or use a truncated version
  if (chat.external_id && chat.external_id !== chat.uid) {
    return chat.external_id.length > 50
      ? chat.external_id.substring(0, 50) + '...'
      : chat.external_id
  }

  // Fallback to a generic title with timestamp
  const date = new Date(chat.created_at.toString())
  return `Chat ${date.toLocaleDateString()}`
}

// Fallback data for when no agent is selected or loading
const fallbackData = [
  {
    title: 'Pinned',
    url: '#',
    icon: 'Pin',
    isActive: false,
    items: [
      {
        title: 'Customer Support Workflow',
        url: '#',
      },
      {
        title: 'Marketing Campaign Ideas',
        url: '#',
      },
      {
        title: 'Marketing Campaign Ideas',
        url: '#',
      },
      {
        title: 'Marketing Campaign Ideas',
        url: '#',
      },
      {
        title: 'Marketing Campaign Ideas',
        url: '#',
      },
      {
        title: 'Marketing Campaign Ideas',
        url: '#',
      },
      {
        title: 'Marketing Campaign Ideas',
        url: '#',
      },
      {
        title: 'Marketing Campaign Ideas',
        url: '#',
      },
      {
        title: 'Marketing Campaign Ideas',
        url: '#',
      },
      {
        title: 'Marketing Campaign Ideas',
        url: '#',
      },
      {
        title: 'Marketing Campaign Ideas',
        url: '#',
      },
      {
        title: 'Marketing Campaign Ideas',
        url: '#',
      },
      {
        title: 'Marketing Campaign Ideas',
        url: '#',
      },
    ],
  },
  {
    title: 'Today',
    url: '#',
    icon: 'Bot',
    isActive: true,
    items: [
      {
        title: 'Website Redesign Discussion',
        url: '#',
      },
      {
        title: 'Product Feature Planning',
        url: '#',
      },
      {
        title: 'Product Feature Planning',
        url: '#',
      },
      {
        title: 'Product Feature Planning',
        url: '#',
      },
      {
        title: 'Product Feature Planning',
        url: '#',
      },
      {
        title: 'Product Feature Planning',
        url: '#',
      },
      {
        title: 'Product Feature Planning',
        url: '#',
      },
      {
        title: 'Product Feature Planning',
        url: '#',
      },
      {
        title: 'Product Feature Planning',
        url: '#',
      },
      {
        title: 'Product Feature Planning',
        url: '#',
      },
      {
        title: 'Product Feature Planning',
        url: '#',
      },
      {
        title: 'Product Feature Planning',
        url: '#',
      },
      {
        title: 'Product Feature Planning',
        url: '#',
      },
    ],
  },
  {
    title: 'History',
    url: '#',
    icon: 'History',
    isActive: false,
    items: [
      {
        title: 'Team Meeting Notes',
        url: '#',
      },
      {
        title: 'Project Timeline Review',
        url: '#',
      },
      {
        title: 'Project Timeline Review',
        url: '#',
      },
      {
        title: 'Project Timeline Review',
        url: '#',
      },
      {
        title: 'Project Timeline Review',
        url: '#',
      },
      {
        title: 'Project Timeline Review',
        url: '#',
      },
      {
        title: 'Project Timeline Review',
        url: '#',
      },
      {
        title: 'Project Timeline Review',
        url: '#',
      },
      {
        title: 'Project Timeline Review',
        url: '#',
      },
      {
        title: 'Project Timeline Review',
        url: '#',
      },
      {
        title: 'Project Timeline Review',
        url: '#',
      },
      {
        title: 'Project Timeline Review',
        url: '#',
      },
    ],
  },
]

// Use the real data when available, fallback when loading or no agent selected
const displayData = computed(() => {
  if (!currentAgentUid.value || isLoading.value) {
    return fallbackData
  }

  if (groupedChats.value.length === 0) {
    return [
      {
        title: 'No Chats',
        icon: 'MessageSquare',
        isActive: false,
        items: [
          {
            title: 'No chat history found',
            url: '#',
            uid: 'empty',
          },
        ],
      },
    ]
  }

  return groupedChats.value
})

const initialActiveTitle = displayData.value.find((d) => d.isActive)?.title ?? null

// Use centralized store state with fallback to initial active title
const openItemTitle = computed({
  get: () => appStore.sidebarItemState ?? initialActiveTitle,
  set: (value) => appStore.setSidebarItemState(value),
})

function handleToggle(itemTitle: string) {
  const newValue = openItemTitle.value === itemTitle ? null : itemTitle
  appStore.setSidebarItemState(newValue)
}
</script>

<template>
  <!-- Loading State -->
  <template v-if="isLoading && currentAgentUid">
    <SidebarGroup>
      <SidebarGroupLabel>
        <SidebarMenuSkeleton show-icon />
      </SidebarGroupLabel>
      <SidebarMenu>
        <SidebarMenuSkeleton v-for="i in 3" :key="i" />
      </SidebarMenu>
    </SidebarGroup>
  </template>

  <!-- Chat History -->
  <template v-else>
    <Collapsible
      v-for="item in displayData"
      :key="item.title"
      :open="openItemTitle === item.title"
      class="group/collapsible"
    >
      <SidebarGroup>
        <SidebarGroupLabel asChild>
          <CollapsibleTrigger @click="handleToggle(item.title)">
            <BaseIcon :name="item.icon" class="mr-1" />
            {{ item.title }}

            <BaseIcon
              name="ChevronDown"
              class="ml-auto transition-transform group-data-[state=open]/collapsible:rotate-180"
            />
          </CollapsibleTrigger>
        </SidebarGroupLabel>

        <CollapsibleContent>
          <SidebarMenu>
            <SidebarMenuItem
              v-for="subItem in item.items"
              :key="subItem.title"
              data-sidebar="menu-item"
              class="relative group/menu-item"
            >
              <SidebarMenuButton as-child>
                <a :href="subItem.url">
                  <span>{{ subItem.title }}</span>
                </a>
              </SidebarMenuButton>

              <DropdownMenu>
                <DropdownMenuTrigger as-child>
                  <SidebarMenuAction show-on-hover>
                    <BaseIcon name="MoreHorizontal" />
                    <span class="sr-only">More</span>
                  </SidebarMenuAction>
                </DropdownMenuTrigger>
                <DropdownMenuContent
                  class="w-32 rounded-lg"
                  :side="isMobile ? 'bottom' : 'right'"
                  :align="isMobile ? 'end' : 'start'"
                >
                  <DropdownMenuItem>
                    <BaseIcon name="Pin" class="text-muted-foreground" />
                    <span>Pin</span>
                  </DropdownMenuItem>

                  <DropdownMenuItem>
                    <BaseIcon name="Trash2" class="text-muted-foreground" />
                    <span>Delete</span>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </SidebarMenuItem>
          </SidebarMenu>
        </CollapsibleContent>
      </SidebarGroup>
    </Collapsible>
  </template>
</template>
