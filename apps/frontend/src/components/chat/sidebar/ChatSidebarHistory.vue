<script setup lang="ts">
const { isMobile } = useSidebar()
const appStore = useAppStore()

const data = [
  {
    title: 'Pinned',
    url: '#',
    icon: 'Pin',
    isActive: false,
    items: [
      {
        title: 'Customer Support Workflow',
        url: '#',
      },
      {
        title: 'Marketing Campaign Ideas',
        url: '#',
      },
      {
        title: 'Marketing Campaign Ideas',
        url: '#',
      },
      {
        title: 'Marketing Campaign Ideas',
        url: '#',
      },
      {
        title: 'Marketing Campaign Ideas',
        url: '#',
      },
      {
        title: 'Marketing Campaign Ideas',
        url: '#',
      },
      {
        title: 'Marketing Campaign Ideas',
        url: '#',
      },
      {
        title: 'Marketing Campaign Ideas',
        url: '#',
      },
      {
        title: 'Marketing Campaign Ideas',
        url: '#',
      },
      {
        title: 'Marketing Campaign Ideas',
        url: '#',
      },
      {
        title: 'Marketing Campaign Ideas',
        url: '#',
      },
      {
        title: 'Marketing Campaign Ideas',
        url: '#',
      },
      {
        title: 'Marketing Campaign Ideas',
        url: '#',
      },
    ],
  },
  {
    title: 'Today',
    url: '#',
    icon: 'Bot',
    isActive: true,
    items: [
      {
        title: 'Website Redesign Discussion',
        url: '#',
      },
      {
        title: 'Product Feature Planning',
        url: '#',
      },
      {
        title: 'Product Feature Planning',
        url: '#',
      },
      {
        title: 'Product Feature Planning',
        url: '#',
      },
      {
        title: 'Product Feature Planning',
        url: '#',
      },
      {
        title: 'Product Feature Planning',
        url: '#',
      },
      {
        title: 'Product Feature Planning',
        url: '#',
      },
      {
        title: 'Product Feature Planning',
        url: '#',
      },
      {
        title: 'Product Feature Planning',
        url: '#',
      },
      {
        title: 'Product Feature Planning',
        url: '#',
      },
      {
        title: 'Product Feature Planning',
        url: '#',
      },
      {
        title: 'Product Feature Planning',
        url: '#',
      },
      {
        title: 'Product Feature Planning',
        url: '#',
      },
    ],
  },
  {
    title: 'History',
    url: '#',
    icon: 'History',
    isActive: false,
    items: [
      {
        title: 'Team Meeting Notes',
        url: '#',
      },
      {
        title: 'Project Timeline Review',
        url: '#',
      },
      {
        title: 'Project Timeline Review',
        url: '#',
      },
      {
        title: 'Project Timeline Review',
        url: '#',
      },
      {
        title: 'Project Timeline Review',
        url: '#',
      },
      {
        title: 'Project Timeline Review',
        url: '#',
      },
      {
        title: 'Project Timeline Review',
        url: '#',
      },
      {
        title: 'Project Timeline Review',
        url: '#',
      },
      {
        title: 'Project Timeline Review',
        url: '#',
      },
      {
        title: 'Project Timeline Review',
        url: '#',
      },
      {
        title: 'Project Timeline Review',
        url: '#',
      },
      {
        title: 'Project Timeline Review',
        url: '#',
      },
    ],
  },
]

const initialActiveTitle = data.find((d) => d.isActive)?.title ?? null

// Use centralized store state with fallback to initial active title
const openItemTitle = computed({
  get: () => appStore.sidebarItemState ?? initialActiveTitle,
  set: (value) => appStore.setSidebarItemState(value)
})

function handleToggle(itemTitle: string) {
  const newValue = openItemTitle.value === itemTitle ? null : itemTitle
  appStore.setSidebarItemState(newValue)
}
</script>

<template>
  <Collapsible
    v-for="item in data"
    :key="item.title"
    :open="openItemTitle === item.title"
    class="group/collapsible"
  >
    <SidebarGroup>
      <SidebarGroupLabel asChild>
        <CollapsibleTrigger @click="handleToggle(item.title)">
          <BaseIcon :name="item.icon" class="mr-1" />
          {{ item.title }}

          <BaseIcon
            name="ChevronDown"
            class="ml-auto transition-transform group-data-[state=open]/collapsible:rotate-180"
          />
        </CollapsibleTrigger>
      </SidebarGroupLabel>

      <CollapsibleContent>
        <SidebarMenu>
          <SidebarMenuItem
            v-for="subItem in item.items"
            :key="subItem.title"
            data-sidebar="menu-item"
            class="relative group/menu-item"
          >
            <SidebarMenuButton as-child>
              <a :href="subItem.url">
                <span>{{ subItem.title }}</span>
              </a>
            </SidebarMenuButton>

            <DropdownMenu>
              <DropdownMenuTrigger as-child>
                <SidebarMenuAction show-on-hover>
                  <BaseIcon name="MoreHorizontal" />
                  <span class="sr-only">More</span>
                </SidebarMenuAction>
              </DropdownMenuTrigger>
              <DropdownMenuContent
                class="w-32 rounded-lg"
                :side="isMobile ? 'bottom' : 'right'"
                :align="isMobile ? 'end' : 'start'"
              >
                <DropdownMenuItem>
                  <BaseIcon name="Pin" class="text-muted-foreground" />
                  <span>Pin</span>
                </DropdownMenuItem>

                <DropdownMenuItem>
                  <BaseIcon name="Trash2" class="text-muted-foreground" />
                  <span>Delete</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </SidebarMenuItem>
        </SidebarMenu>
      </CollapsibleContent>
    </SidebarGroup>
  </Collapsible>
</template>
