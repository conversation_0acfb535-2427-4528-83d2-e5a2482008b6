<template>
  <div class="flex px-2 mb-4" :class="[isAssistant ? 'justify-start' : 'justify-end']">
    <!-- Assistant Message -->
    <div
      v-if="isAssistant"
      class="rounded-2xl pt-4 px-4 pb-2 max-w-[80%]"
      :style="{ backgroundColor: assistantBg, color: assistantTextColor }"
    >
      <div class="flex items-center gap-2 mb-2">
        <Avatar class="w-8 h-8 shrink-0">
          <AvatarImage src="https://placehold.co/600x400" />
        </Avatar>
        <span class="text-sm font-semibold">{{ userName }}</span>
      </div>
      <div
        class="text-sm leading-relaxed whitespace-pre-wrap message-content"
        v-html="formattedText"
      ></div>
    </div>
    <!-- User Message -->
    <div
      v-else
      class="px-4 py-2 rounded-full max-w-[80%]"
      :style="{ backgroundColor: userBg, color: userTextColor }"
    >
      <span class="text-sm break-words whitespace-pre-wrap">{{ formattedText }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useMarkdownRenderer } from '../../../composables/use-markdown-renderer'

interface Props {
  text: string
  isAssistant: boolean
  assistantBg?: string
  userBg?: string
  userName?: string
}

const props = withDefaults(defineProps<Props>(), {
  text: '',
  isAssistant: false,
  assistantBg: '#F1F3F5', // default light gray
  userBg: '#222222', // default dark
})

// Contrast function to pick black or white based on hex background
function getContrastColor(hex: string): string {
  // Remove leading hash if present
  const h = hex.replace('#', '')
  // Parse r,g,b
  let r = 0,
    g = 0,
    b = 0
  if (h.length === 3) {
    r = parseInt(h[0] + h[0], 16)
    g = parseInt(h[1] + h[1], 16)
    b = parseInt(h[2] + h[2], 16)
  } else if (h.length === 6) {
    r = parseInt(h.substring(0, 2), 16)
    g = parseInt(h.substring(2, 4), 16)
    b = parseInt(h.substring(4, 6), 16)
  }
  // Calculate luminance
  const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255
  return luminance > 0.5 ? '#000000' : '#FFFFFF'
}

// Computed text colors
const assistantTextColor = computed(() => getContrastColor(props.assistantBg!))
const userTextColor = computed(() => getContrastColor(props.userBg!))

// Use the composable
const { isReady, renderMarkdown } = useMarkdownRenderer()

const formattedTextValue = ref('')

// Function to update the formatted text using the composable
const updateFormattedText = async () => {
  try {
    // Call the render function from the composable
    const html = await renderMarkdown(props.text)
    formattedTextValue.value = html
  } catch (error) {
    console.error('[ChatMessage] Error updating formatted text:', error)
    formattedTextValue.value = props.text || '' // Fallback to plain text
  }
}

// Watch for changes in the input text or the renderer readiness
watch(
  [() => props.text, isReady], // Watch both text prop and isReady state from composable
  async ([newText, readyState], [oldText, oldReadyState]) => {
    // Re-render if the text changes OR if the renderer just became ready
    if (newText !== oldText || (readyState && !oldReadyState)) {
      await updateFormattedText()
    }
  },
  { immediate: true } // Run immediately to render initial text
)

// Computed property for the template
const formattedText = computed(() => formattedTextValue.value)

// Optional: Add a simple onMounted log for component instance tracking
onMounted(() => {})
</script>

<style lang="scss" scoped>
.message-content {
  white-space: pre-wrap;
  word-break: break-word;
  line-height: 1.5;

  :deep(p:not(:last-child)) {
    margin-bottom: 0.5em;
  }

  :deep(pre) {
    margin: 0.8em 0;
    padding: 0.6em 0.8em;
    border-radius: 0.4em;
    background-color: hsla(var(--secondary), 0.8);
    color: hsl(var(--secondary-foreground));
    overflow-x: auto;
    font-family: 'Fira Code', 'JetBrains Mono', monospace;
    font-size: 0.85em;
  }

  :deep(pre code) {
    font-family: inherit;
    padding: 0;
    background-color: transparent;
    color: inherit;
  }

  :deep(code:not(pre code)) {
    font-family: 'Fira Code', 'JetBrains Mono', monospace;
    padding: 0.15em 0.3em;
    margin: 0 0.1em;
    border-radius: 0.25em;
    background-color: hsl(var(--muted));
    color: hsl(var(--muted-foreground));
    font-size: 0.8em;
    word-break: break-all;
  }

  :deep(ul),
  :deep(ol) {
    margin: 0.6em 0;
    padding-left: 1.2em;
  }

  :deep(li) {
    margin: 0.2em 0;
  }

  :deep(li::marker) {
    color: inherit;
    font-size: 0.9em;
  }

  :deep(h1),
  :deep(h2),
  :deep(h3),
  :deep(h4),
  :deep(h5),
  :deep(h6) {
    font-size: 1em;
    font-weight: 600;
    margin-top: 0.8em;
    margin-bottom: 0.4em;
  }

  :deep(blockquote) {
    margin: 0.8em 0;
    padding: 0.4em 0.8em;
    border-left: 2px solid hsl(var(--border));
    color: inherit;
    opacity: 0.9;
    background-color: transparent;
    font-style: normal;
  }

  :deep(table) {
    margin: 0.8em 0;
    border-collapse: collapse;
    width: 100%;
    font-size: 0.9em;
  }

  :deep(th),
  :deep(td) {
    padding: 0.4em 0.6em;
    border: 1px solid hsla(var(--border), 0.5);
    text-align: left;
  }

  :deep(th) {
    background-color: hsla(var(--muted), 0.5);
    font-weight: 600;
  }

  :deep(a) {
    color: inherit;
    text-decoration: underline;
    font-weight: 500;
    text-decoration-thickness: 1px;
    text-underline-offset: 2px;
    &:hover {
      text-decoration: none;
    }
  }

  :deep(> *:last-child) {
    margin-bottom: 0;
  }
}
</style>
