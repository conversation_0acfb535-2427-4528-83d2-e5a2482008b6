<template>
  <BaseTable
    :isLoading="isLoading || isFetching"
    :heads="heads"
    :rows="rows"
    empty-message="No copyright entries found, add a new copyright to get started."
  >
    <template #actions>
      <Button @click="props.onEdit(undefined)"> <BaseIcon name="Plus" /> Add Copyright </Button>
    </template>
    <template #row="{ row }">
      <TableCell class="max-w-[300px] truncate">{{ row.text }}</TableCell>
      <TableCell>
        <a :href="row.url" target="_blank" class="text-blue-600 hover:underline">
          {{ row.url }}
        </a>
      </TableCell>
      <TableCell>{{ getAgentName(row.agent_uid) }}</TableCell>
      <TableCell class="text-right">
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="icon" @click.stop>
              <BaseIcon name="MoreVertical" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem @click="props.onEdit(row.uid)">
              <BaseIcon name="Pencil" class="w-4 h-4 mr-2" />
              Edit
            </DropdownMenuItem>
            <DropdownMenuItem @click="props.onDelete(row.uid)">
              <BaseIcon name="Trash2" class="w-4 h-4 mr-2" />
              Delete
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </TableCell>
    </template>
  </BaseTable>
</template>

<script setup lang="ts">
import BaseTable from '@/components/base/table/BaseTable.vue'
import type { Copyright } from '@/types'
import { queryCopyrights } from '@/services/whitelabel_service'
import { queryAgents } from '@/services/agent_service'

const props = defineProps<{
  onEdit: (uid?: string) => void
  onDelete: (uid: string) => void
}>()

const { data: copyrights, isLoading, isFetching } = queryCopyrights()
const { data: agents } = queryAgents()
const rows = ref<Copyright[]>([])

watchEffect(() => {
  rows.value = copyrights.value?.data ?? []
})

const getAgentName = (uid: string | undefined): string => {
  if (!uid) return 'N/A'

  const agent = agents.value?.data?.find((agent) => agent.uid === uid)

  return agent?.label || uid
}

const heads = [
  { label: 'Text', key: 'text' },
  { label: 'URL', key: 'url' },
  { label: 'Agent', key: 'agent_uid' },
  { label: 'Actions', key: 'actions', align: 'right' },
]
</script>
