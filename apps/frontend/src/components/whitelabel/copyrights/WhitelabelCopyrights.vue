<template>
  <WhitelabelCopyrightsTable :onEdit="handleEdit" :onDelete="handleDelete" />

  <WhitelabelCopyrightsForm ref="formRef" />
</template>

<script setup lang="ts">
import { ref } from 'vue'
import WhitelabelCopyrightsForm from './WhitelabelCopyrightsForm.vue'

import { mutateCopyrightDestroy } from '@/services/whitelabel_service'

// Composables
const { toast } = useToast()

// Refs
const formRef = ref<typeof WhitelabelCopyrightsForm>()

// Functions
const handleEdit = (uid?: string) => {
  formRef.value?.openDialog(uid)
}

const handleDelete = async (uid: string) => {
  const { mutateAsync } = mutateCopyrightDestroy(ref(uid))

  try {
    await mutateAsync(undefined)

    toast({
      title: 'Copyright Deleted',
      description: 'The copyright entry has been successfully deleted.',
    })
  } catch (error: any) {
    toast({
      title: 'Error Deleting Copyright',
      description: error?.message || 'An unexpected error occurred.',
      variant: 'destructive',
    })
  }
}
</script>
