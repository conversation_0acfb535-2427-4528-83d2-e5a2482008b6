<template>
  <BaseTable
    :isLoading="isLoading || isFetching"
    :heads="heads"
    :rows="rows"
    empty-message="No key sets found, add a new key set to get started."
  >
    <template #actions>
      <Button @click="props.onEdit(undefined)"> <BaseIcon name="Plus" /> Add Key </Button>
    </template>
    <template #row="{ row }">
      <TableCell>{{ row.provider }}</TableCell>
      <TableCell>{{ row.secret }}</TableCell>
      <TableCell>{{ getAgentName(row.agent_uid) }}</TableCell>
      <TableCell>{{ row.max_messages_per_month ?? 'Unlimited' }}</TableCell>
      <TableCell class="text-right">
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="icon" @click.stop>
              <BaseIcon name="MoreVertical" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem @click="props.onEdit(row.uid)">
              <BaseIcon name="Pencil" class="w-4 h-4 mr-2" />
              Edit
            </DropdownMenuItem>
            <DropdownMenuItem @click="props.onDelete(row.uid)">
              <BaseIcon name="Trash2" class="w-4 h-4 mr-2" />
              Delete
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </TableCell>
    </template>
  </BaseTable>
</template>

<script setup lang="ts">
import BaseTable from '@/components/base/table/BaseTable.vue'
import type { Key } from '@/types'
import { queryKeys } from '@/services/whitelabel_service'
import { queryAgents } from '@/services/agent_service'

const props = defineProps<{
  onEdit: (uid?: string) => void
  onDelete: (uid: string) => void
}>()

const { data: keys, isLoading, isFetching } = queryKeys()
const { data: agents } = queryAgents()
const rows = ref<Key[]>([])

watchEffect(() => {
  rows.value = keys.value?.data ?? []
})

const getAgentName = (uid: string | undefined): string => {
  if (!uid) return 'N/A'

  const agent = agents.value?.data?.find((agent) => agent.uid === uid)

  return agent?.label || uid
}

const heads = [
  { label: 'Provider', key: 'provider' },
  { label: 'Secret', key: 'secret' },
  { label: 'Agent', key: 'agent_uid' },
  { label: 'Max Messages/Month', key: 'max_messages_per_month' },
  { label: 'Actions', key: 'actions', align: 'right' },
]
</script>
