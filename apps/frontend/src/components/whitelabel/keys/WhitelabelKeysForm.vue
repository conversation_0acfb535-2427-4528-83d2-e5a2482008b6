<template>
  <BaseDialog :dialogId="dialogId" :icon="ICONS.SETTINGS" :title="title" :description="description">
    <BaseForm :isLoading="isLoading" :isSubmitting="isSubmitting" :inputs="8" @submit="onSubmit">
      <FormField v-slot="{ componentField }" name="agent_uid">
        <FormItem>
          <FormLabel>Agent</FormLabel>
          <FormControl>
            <Select v-bind="componentField">
              <SelectTrigger>
                <SelectValue placeholder="Select an agent" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem v-for="agent in agents" :key="agent.uid" :value="agent.uid">
                  {{ agent.label }}
                </SelectItem>
              </SelectContent>
            </Select>
          </FormControl>
          <FormMessage />
        </FormItem>
      </FormField>

      <FormField v-slot="{ componentField }" name="provider">
        <FormItem>
          <FormLabel>Provider</FormLabel>
          <FormControl>
            <Select v-bind="componentField">
              <SelectTrigger>
                <SelectValue placeholder="Select a provider" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem
                  v-for="provider in PROVIDERS"
                  :key="provider.value"
                  :value="provider.value"
                >
                  {{ provider.label }}
                </SelectItem>
              </SelectContent>
            </Select>
          </FormControl>
          <FormMessage />
        </FormItem>
      </FormField>

      <FormField v-slot="{ componentField }" name="secret">
        <FormItem>
          <FormLabel>Secret</FormLabel>
          <FormControl>
            <Input
              v-bind="componentField"
              placeholder="sk-..."
              type="password"
              autocomplete="new-password"
            />
          </FormControl>
          <FormMessage />
        </FormItem>
      </FormField>

      <FormField v-slot="{ componentField }" name="max_messages_per_month">
        <FormItem>
          <FormLabel>Max Messages per Month</FormLabel>
          <FormControl>
            <Input
              v-bind="componentField"
              placeholder="1000 (optional, empty for unlimited)"
              type="number"
              min="0"
            />
          </FormControl>
          <FormMessage />
        </FormItem>
      </FormField>

      <DialogFooter>
        <Button type="button" variant="outline" @click="closeDialog"> Cancel </Button>
        <Button type="submit" :disabled="isSubmitting">
          {{ editingUid ? 'Update' : 'Add' }}
        </Button>
      </DialogFooter>
    </BaseForm>
  </BaseDialog>
</template>

<script setup lang="ts">
import { ref, watchEffect, computed } from 'vue'
import { useForm } from 'vee-validate'
import { toTypedSchema } from '@vee-validate/zod'
import * as z from 'zod'
import { queryKeyById, mutateKey } from '@/services/whitelabel_service'
import { queryAgents } from '@/services/agent_service'
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/shadcn/ui/form'
import { ICONS, PROVIDERS } from '@/constants'

defineProps<{
  dialogId: string
}>()

// Composables
const { toast } = useToast()
const { handleSubmit, isSubmitting, setValues } = useForm({
  initialValues: {
    uid: undefined as string | undefined,
    agent_uid: '',
    provider: '',
    secret: '',
    max_messages_per_month: '',
  },
  validationSchema: toTypedSchema(
    z.object({
      uid: z.string().optional(),
      agent_uid: z.string().min(1, { message: 'Agent is required' }),
      provider: z.string().min(1, { message: 'Provider is required' }),
      secret: z.string().min(1, { message: 'Secret is required' }),
      max_messages_per_month: z.string().optional(),
    })
  ),
})

// Refs
const isDialogOpen = ref(false)
const editingUid = ref<string | undefined>()

// Queries
const mutation = mutateKey(editingUid)
const { isLoading, isError, data } = queryKeyById(editingUid)
const { data: agentsData } = queryAgents()

// Computed
const title = computed(() => `${editingUid.value ? 'Edit' : 'Add'} Key Set`)
const description = computed(
  () => `${editingUid.value ? 'Edit API keys for this set' : 'Add a new set of API keys'}`
)
const agents = computed(() => agentsData.value?.data ?? [])

// Functions
const openDialog = (uid?: string) => {
  editingUid.value = uid
  isDialogOpen.value = true
}

const closeDialog = () => {
  isDialogOpen.value = false
}

const onSubmit = handleSubmit(async (values) => {
  try {
    const payload = {
      agent_uid: values.agent_uid,
      provider: values.provider,
      secret: values.secret,
      max_messages_per_month: values.max_messages_per_month
        ? Number(values.max_messages_per_month)
        : undefined,
    }

    if (editingUid.value) {
      await mutation.mutateAsync({ ...payload, uid: editingUid.value })
    } else {
      await mutation.mutateAsync(payload)
    }

    toast({ title: `${editingUid.value ? 'Updated' : 'Added'} successfully` })

    closeDialog()
  } catch (err: any) {
    toast({
      title: `Failed to ${editingUid.value ? 'update' : 'add'}`,
      description: err.message || 'Operation failed',
      variant: 'destructive',
    })
  }
})

// Watchers
watchEffect(() => {
  if (!editingUid.value) return

  if (isLoading.value) return

  if (isError.value) {
    toast({
      title: 'Error Loading Key Set',
      description: `Key set with ID ${editingUid.value} not found.`,
      variant: 'destructive',
    })
    closeDialog()
    return
  }

  if (data.value) {
    setValues({
      uid: editingUid.value,
      agent_uid: data.value.agent_uid ?? '',
      provider: data.value.provider ?? '',
      secret: data.value.secret ?? '',
      max_messages_per_month: data.value.max_messages_per_month?.toString() ?? '',
    })
  }
})

defineExpose<FormComponent>({
  openDialog,
  closeDialog,
})
</script>
