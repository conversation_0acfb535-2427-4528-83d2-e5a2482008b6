<template>
  <WhitelabelKeysTable :onEdit="handleEdit" :onDelete="handleDelete" />

  <WhitelabelKeysForm ref="formRef" />
</template>

<script setup lang="ts">
import { ref } from 'vue'
import WhitelabelKeysForm from './WhitelabelKeysForm.vue'
import { mutateKeyDestroy } from '@/services/whitelabel_service'

// Composables
const { toast } = useToast()

// Refs
const formRef = ref<typeof WhitelabelKeysForm>()

// Functions
const handleEdit = (uid?: string) => {
  formRef.value?.openDialog(uid)
}

const handleDelete = async (uid: string) => {
  const { mutateAsync } = mutateKeyDestroy(ref(uid))

  try {
    await mutateAsync(undefined)

    toast({
      title: 'Key Set Deleted',
      description: 'The API key set has been successfully deleted.',
    })
  } catch (error: any) {
    toast({
      title: 'Error Deleting Key Set',
      description: error?.message || 'An unexpected error occurred.',
      variant: 'destructive',
    })
  }
}
</script>
