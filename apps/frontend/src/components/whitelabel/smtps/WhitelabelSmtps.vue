<template>
  <WhitelabelSmtpsTable :onEdit="handleEdit" :onDelete="handleDelete" />

  <WhitelabelSmtpsForm ref="formRef" />
</template>

<script setup lang="ts">
import { ref } from 'vue'
import WhitelabelSmtpsForm from './WhitelabelSmtpsForm.vue'
import { mutateSmtpDestroy } from '@/services/whitelabel_service'

// Composables
const { toast } = useToast()

// Refs
const formRef = ref<InstanceType<typeof WhitelabelSmtpsForm> | null>(null)

// Functions
const handleEdit = (uid?: string) => {
  formRef.value?.openDialog(uid)
}

const handleDelete = async (uid: string) => {
  const { mutateAsync } = mutateSmtpDestroy(ref(uid))

  try {
    await mutateAsync(undefined)

    toast({
      title: 'SMTP Config Deleted',
      description: 'The SMTP configuration has been successfully deleted.',
    })
  } catch (error: any) {
    toast({
      title: 'Error Deleting SMTP Config',
      description: error?.message || 'An unexpected error occurred.',
      variant: 'destructive',
    })
  }
}
</script>
