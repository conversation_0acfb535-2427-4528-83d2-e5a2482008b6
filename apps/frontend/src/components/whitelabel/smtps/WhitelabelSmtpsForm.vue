<template>
  <BaseDialog :dialogId="dialogId" :icon="ICONS.SETTINGS" :title="title" :description="description">
    <BaseForm :isLoading="isLoading" :isSubmitting="isSubmitting" :inputs="8" @submit="onSubmit">
      <FormField v-slot="{ componentField }" name="agent_uid">
        <FormItem>
          <FormLabel>Agent</FormLabel>
          <FormControl>
            <Select v-bind="componentField">
              <SelectTrigger>
                <SelectValue placeholder="Select an agent" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem v-for="agent in agents" :key="agent.uid" :value="agent.uid">
                  {{ agent.label }}
                </SelectItem>
              </SelectContent>
            </Select>
          </FormControl>
          <FormMessage />
        </FormItem>
      </FormField>

      <FormField v-slot="{ componentField }" name="host">
        <FormItem>
          <FormLabel>Host</FormLabel>
          <FormControl>
            <Input v-bind="componentField" placeholder="smtp.example.com" />
          </FormControl>
          <FormMessage />
        </FormItem>
      </FormField>

      <FormField v-slot="{ componentField }" name="port">
        <FormItem>
          <FormLabel>Port</FormLabel>
          <FormControl>
            <Input v-bind="componentField" placeholder="587" type="number" />
          </FormControl>
          <FormMessage />
        </FormItem>
      </FormField>

      <FormField v-slot="{ componentField }" name="username">
        <FormItem>
          <FormLabel>Username</FormLabel>
          <FormControl>
            <Input
              v-bind="componentField"
              placeholder="<EMAIL> (optional)"
              autocomplete="username"
            />
          </FormControl>
          <FormMessage />
        </FormItem>
      </FormField>

      <FormField v-slot="{ componentField }" name="password">
        <FormItem>
          <FormLabel>Password</FormLabel>
          <FormControl>
            <Input
              v-bind="componentField"
              placeholder="Enter password"
              type="password"
              autocomplete="new-password"
            />
          </FormControl>
          <FormMessage />
        </FormItem>
      </FormField>

      <FormField v-slot="{ componentField }" name="secure">
        <FormItem>
          <FormLabel>Use TLS/SSL</FormLabel>
          <FormControl>
            <Checkbox v-bind="componentField" />
          </FormControl>
          <FormMessage />
        </FormItem>
      </FormField>

      <FormField v-slot="{ componentField }" name="from_email">
        <FormItem>
          <FormLabel>From Email</FormLabel>
          <FormControl>
            <Input v-bind="componentField" placeholder="<EMAIL>" type="email" />
          </FormControl>
          <FormMessage />
        </FormItem>
      </FormField>

      <FormField v-slot="{ componentField }" name="from_name">
        <FormItem>
          <FormLabel>From Name</FormLabel>
          <FormControl>
            <Input v-bind="componentField" placeholder="Your App Name" />
          </FormControl>
          <FormMessage />
        </FormItem>
      </FormField>

      <DialogFooter>
        <Button type="button" variant="outline" @click="closeDialog"> Cancel </Button>
        <Button type="submit" :disabled="isSubmitting">
          {{ editingUid ? 'Update' : 'Add' }}
        </Button>
      </DialogFooter>
    </BaseForm>
  </BaseDialog>
</template>

<script setup lang="ts">
import { ref, watchEffect, computed } from 'vue'
import { useForm } from 'vee-validate'
import { toTypedSchema } from '@vee-validate/zod'
import * as z from 'zod'
import { querySmtpById, mutateSmtp } from '@/services/whitelabel_service'
import { queryAgents } from '@/services/agent_service'
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/shadcn/ui/form'
import { ICONS } from '@/constants'

defineProps<{
  dialogId: string
}>()

// Composables
const { toast } = useToast()
const { handleSubmit, isSubmitting, setValues } = useForm({
  initialValues: {
    uid: undefined as string | undefined,
    agent_uid: '',
    host: '',
    port: '587',
    username: '',
    password: '',
    secure: true,
    from_email: '',
    from_name: '',
  },
  validationSchema: toTypedSchema(
    z.object({
      uid: z.string().optional(),
      agent_uid: z.string().min(1, { message: 'Agent is required' }),
      host: z.string().min(1, { message: 'Host is required' }),
      port: z.string().min(1, { message: 'Port is required' }),
      username: z.string().optional(),
      password: z.string().optional(),
      secure: z.boolean(),
      from_email: z.string().email({ message: 'Valid email is required' }),
      from_name: z.string().min(1, { message: 'From name is required' }),
    })
  ),
})

// Refs
const isDialogOpen = ref(false)
const editingUid = ref<string | undefined>()

// Queries
const mutation = mutateSmtp(editingUid)
const { isLoading, isError, data } = querySmtpById(editingUid)
const { data: agentsData } = queryAgents()

// Computed
const title = computed(() => `${editingUid.value ? 'Edit' : 'Add'} SMTP Config`)
const description = computed(
  () =>
    `${editingUid.value ? 'Edit SMTP connection details' : 'Add a new SMTP configuration for sending emails'}`
)
const agents = computed(() => agentsData.value?.data ?? [])

// Functions
const openDialog = (uid?: string) => {
  editingUid.value = uid
  isDialogOpen.value = true
}

const closeDialog = () => {
  isDialogOpen.value = false
}

const onSubmit = handleSubmit(async (values) => {
  try {
    const payload = {
      agent_uid: values.agent_uid,
      host: values.host,
      port: Number(values.port),
      username: values.username || undefined,
      password: values.password || undefined,
      secure: values.secure,
      from_email: values.from_email,
      from_name: values.from_name,
    }

    if (editingUid.value) {
      await mutation.mutateAsync({ ...payload, uid: editingUid.value })
    } else {
      await mutation.mutateAsync(payload)
    }

    toast({ title: `${editingUid.value ? 'Updated' : 'Added'} successfully` })

    closeDialog()
  } catch (err: any) {
    toast({
      title: `Failed to ${editingUid.value ? 'update' : 'add'}`,
      description: err.message || 'Operation failed',
      variant: 'destructive',
    })
  }
})

// Watchers
watchEffect(() => {
  if (!editingUid.value) return

  if (isLoading.value) return

  if (isError.value) {
    toast({
      title: 'Error Loading SMTP Config',
      description: `SMTP configuration with ID ${editingUid.value} not found.`,
      variant: 'destructive',
    })
    closeDialog()
    return
  }

  if (data.value) {
    setValues({
      uid: editingUid.value,
      agent_uid: data.value.agent_uid ?? '',
      host: data.value.host ?? '',
      port: data.value.port?.toString() ?? '587',
      username: data.value.username ?? '',
      password: '',
      secure: data.value.secure ?? true,
      from_email: data.value.from_email ?? '',
      from_name: data.value.from_name ?? '',
    })
  }
})

defineExpose<FormComponent>({
  openDialog,
  closeDialog,
})
</script>
