<template>
  <BaseTable
    :isLoading="isLoading || isFetching"
    :heads="heads"
    :rows="rows"
    empty-message="No SMTP configurations found, add a new SMTP configuration to get started."
  >
    <template #actions>
      <Button @click="props.onEdit(undefined)"> <BaseIcon name="Plus" /> Add SMTP </Button>
    </template>
    <template #row="{ row }">
      <TableCell>{{ row.host }}</TableCell>
      <TableCell>{{ row.port }}</TableCell>
      <TableCell>{{ row.username || '-' }}</TableCell>
      <TableCell>{{ row.from_email }}</TableCell>
      <TableCell>{{ getAgentName(row.agent_uid) }}</TableCell>
      <TableCell class="text-right">
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="icon" @click.stop>
              <BaseIcon name="MoreVertical" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem @click="props.onEdit(row.uid)">
              <BaseIcon name="Pencil" class="w-4 h-4 mr-2" />
              Edit
            </DropdownMenuItem>
            <DropdownMenuItem @click="props.onDelete(row.uid)">
              <BaseIcon name="Trash2" class="w-4 h-4 mr-2" />
              Delete
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </TableCell>
    </template>
  </BaseTable>
</template>

<script setup lang="ts">
import BaseTable from '@/components/base/table/BaseTable.vue'
import type { Smtp } from '@/types'
import { querySmtps } from '@/services/whitelabel_service'
import { queryAgents } from '@/services/agent_service'

const props = defineProps<{
  onEdit: (uid?: string) => void
  onDelete: (uid: string) => void
}>()

const { data: smtps, isLoading, isFetching } = querySmtps()
const { data: agents } = queryAgents()
const rows = ref<Smtp[]>([])

watchEffect(() => {
  rows.value = smtps.value?.data ?? []
})

const getAgentName = (uid: string | undefined): string => {
  if (!uid) return 'N/A'

  const agent = agents.value?.data?.find((agent) => agent.uid === uid)

  return agent?.label || uid
}

const heads = [
  { label: 'Host', key: 'host' },
  { label: 'Port', key: 'port' },
  { label: 'Username', key: 'username' },
  { label: 'From Email', key: 'from_email' },
  { label: 'Agent', key: 'agent_uid' },
  { label: 'Actions', key: 'actions', align: 'right' },
]
</script>
