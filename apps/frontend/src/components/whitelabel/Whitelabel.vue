<template>
  <BaseDialog
    :dialogId="dialogId"
    :icon="ICONS.SETTINGS"
    title="Whitelabel"
    description="Manage your whitelabel settings."
  >
    <BaseTabs
      default-value="domains"
      :tabs="[
        { value: 'domains', label: 'Domains', icon: 'Globe' },
        { value: 'copyrights', label: 'Copyrights', icon: 'Copyright' },
        { value: 'keys', label: 'Keys', icon: 'KeyRound' },
        { value: 'smtps', label: 'SMTPs', icon: 'Mail' },
      ]"
    >
      <TabsContent value="domains">
        <WhitelabelDomains />
      </TabsContent>

      <TabsContent value="copyrights">
        <WhitelabelCopyrights />
      </TabsContent>

      <TabsContent value="keys">
        <WhitelabelKeys />
      </TabsContent>

      <TabsContent value="smtps">
        <WhitelabelSmtps />
      </TabsContent>
    </BaseTabs>
  </BaseDialog>
</template>

<script setup lang="ts">
import { ICONS } from '@/constants'

defineProps<{
  dialogId: string
}>()
</script>
