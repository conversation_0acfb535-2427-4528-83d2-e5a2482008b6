<template>
  <BaseDialog :dialogId="dialogId" :icon="ICONS.SETTINGS" :title="title" :description="description">
    <BaseForm :isLoading="isLoading" :isSubmitting="isSubmitting" :inputs="5" @submit="onSubmit">
      <FormField v-slot="{ componentField }" name="agent_uid">
        <FormItem>
          <FormLabel>Agent</FormLabel>
          <FormControl>
            <Select v-bind="componentField">
              <SelectTrigger>
                <SelectValue placeholder="Select an agent" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem v-for="agent in agents" :key="agent.uid" :value="agent.uid">
                  {{ agent.label }}
                </SelectItem>
              </SelectContent>
            </Select>
          </FormControl>
          <FormMessage />
        </FormItem>
      </FormField>

      <FormField v-slot="{ componentField }" name="domain_name">
        <FormItem>
          <FormLabel>Domain Name</FormLabel>
          <FormControl>
            <Input v-bind="componentField" placeholder="example.com" />
          </FormControl>
          <FormMessage />
        </FormItem>
      </FormField>

      <FormField v-slot="{ componentField }" name="company_name">
        <FormItem>
          <FormLabel>Company Name</FormLabel>
          <FormControl>
            <Input v-bind="componentField" placeholder="Your Company Inc." />
          </FormControl>
          <FormMessage />
        </FormItem>
      </FormField>

      <FormField v-slot="{ componentField }" name="primary_color">
        <FormItem>
          <FormLabel>Primary Color</FormLabel>
          <FormControl>
            <BaseColorPicker v-bind="componentField" />
          </FormControl>
          <FormMessage />
        </FormItem>
      </FormField>

      <FormField v-slot="{ componentField }" name="logo_url">
        <FormItem>
          <FormLabel>Logo URL</FormLabel>
          <FormControl>
            <Input v-bind="componentField" placeholder="https://example.com/logo.png" type="url" />
          </FormControl>
          <FormMessage />
        </FormItem>
      </FormField>

      <DialogFooter>
        <Button type="button" variant="outline" @click="closeDialog"> Cancel </Button>
        <Button type="submit" :disabled="isSubmitting">
          {{ editingUid ? 'Update' : 'Add' }}
        </Button>
      </DialogFooter>
    </BaseForm>
  </BaseDialog>
</template>

<script setup lang="ts">
import { ref, watchEffect, computed } from 'vue'
import { useForm } from 'vee-validate'
import { toTypedSchema } from '@vee-validate/zod'
import * as z from 'zod'
import { queryDomainById, mutateDomain } from '@/services/whitelabel_service'
import { queryAgents } from '@/services/agent_service'
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/shadcn/ui/form'
import { ICONS } from '@/constants'

defineProps<{
  dialogId: string
}>()

// Composables
const { toast } = useToast()
const { handleSubmit, isSubmitting, setValues } = useForm({
  initialValues: {
    uid: undefined as string | undefined,
    agent_uid: '',
    domain_name: '',
    company_name: '',
    primary_color: '#000000',
    logo_url: '',
  },
  validationSchema: toTypedSchema(
    z.object({
      uid: z.string().optional(),
      agent_uid: z.string().min(1, { message: 'Agent is required' }),
      domain_name: z.string().min(1, { message: 'Domain name is required' }),
      company_name: z.string().min(1, { message: 'Company name is required' }),
      primary_color: z.string().optional(),
      logo_url: z
        .string()
        .url({ message: 'Valid logo URL is required' })
        .optional()
        .or(z.literal('')),
    })
  ),
})

// Refs
const isDialogOpen = ref(false)
const editingUid = ref<string | undefined>()

// Queries
const mutation = mutateDomain(editingUid)
const { isLoading, isError, data } = queryDomainById(editingUid)
const { data: agentsData } = queryAgents()

// Computed
const title = computed(() => `${editingUid.value ? 'Edit' : 'Add'} Domain`)
const description = computed(
  () => `${editingUid.value ? 'Edit a domain configuration' : 'Add a new domain configuration'}`
)
const agents = computed(() => agentsData.value?.data ?? [])

// Functions
const openDialog = (uid?: string) => {
  editingUid.value = uid
  isDialogOpen.value = true
}

const closeDialog = () => {
  isDialogOpen.value = false
}

const onSubmit = handleSubmit(async (values) => {
  try {
    const payload = {
      agent_uid: values.agent_uid,
      domain_name: values.domain_name,
      company_name: values.company_name,
      primary_color: values.primary_color,
      logo_url: values.logo_url,
    }

    if (editingUid.value) {
      await mutation.mutateAsync({ ...payload, uid: editingUid.value })
    } else {
      await mutation.mutateAsync(payload)
    }

    toast({ title: `${editingUid.value ? 'Updated' : 'Added'} successfully` })

    closeDialog()
  } catch (err: any) {
    toast({
      title: `Failed to ${editingUid.value ? 'update' : 'add'}`,
      description: err.message || 'Operation failed',
      variant: 'destructive',
    })
  }
})

// Watchers
watchEffect(() => {
  if (!editingUid.value) return

  if (isLoading.value) return

  if (isError.value) {
    toast({
      title: 'Error Loading Domain',
      description: `Domain with ID ${editingUid.value} not found.`,
      variant: 'destructive',
    })
    closeDialog()
    return
  }

  if (data.value) {
    setValues({
      uid: editingUid.value,
      agent_uid: data.value.agent_uid ?? '',
      domain_name: data.value.domain_name ?? '',
      company_name: data.value.company_name ?? '',
      primary_color: data.value.primary_color ?? '#000000',
      logo_url: data.value.logo_url ?? '',
    })
  }
})

defineExpose<FormComponent>({
  openDialog,
  closeDialog,
})
</script>
