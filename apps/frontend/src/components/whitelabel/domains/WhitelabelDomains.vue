<template>
  <WhitelabelDomainsTable :onEdit="handleEdit" :onDelete="handleDelete" />

  <WhitelabelDomainsForm ref="formRef" />
</template>

<script setup lang="ts">
import { ref } from 'vue'
import WhitelabelDomainsForm from './WhitelabelDomainsForm.vue'
import { mutateDomainDestroy } from '@/services/whitelabel_service'

// Composables
const { toast } = useToast()

// Refs
const formRef = ref<InstanceType<typeof WhitelabelDomainsForm> | null>(null)

// Functions
const handleEdit = (uid?: string) => {
  formRef.value?.openDialog(uid)
}

const handleDelete = async (uid: string) => {
  const { mutateAsync } = mutateDomainDestroy(ref(uid))

  try {
    await mutateAsync(undefined)

    toast({
      title: 'Domain Deleted',
      description: 'The domain configuration has been successfully deleted.',
    })
  } catch (error: any) {
    toast({
      title: 'Error Deleting Domain',
      description: error?.message || 'An unexpected error occurred.',
      variant: 'destructive',
    })
  }
}
</script>
