<script setup lang="ts">
import { queryPricingTable } from '@/services/subscription_service'
import { ensureAuthMe } from '@/services/auth_service'
import { ICONS } from '@/constants'

defineProps<{
  dialogId: string
}>()

// Composables
const {
  data: products,
  isLoading: isPricingTableLoading,
  error: isPricingTableError,
} = queryPricingTable()
const me = await ensureAuthMe()

const currentSubscriptionInterval = me?.subscriptions?.plan?.subscription?.stripe_interval

let defaultBillingCycle: 'monthly' | 'yearly'

if (currentSubscriptionInterval === 'month') {
  defaultBillingCycle = 'yearly'
} else if (currentSubscriptionInterval === 'year') {
  defaultBillingCycle = 'monthly'
} else {
  defaultBillingCycle = 'monthly'
}

// Refs
const billingCycleToggle = ref<'monthly' | 'yearly'>(defaultBillingCycle)

// Computed
const plans = computed(() => (products.value ?? []).filter((product) => product.type === 'plan'))
const addons = computed(() => (products.value ?? []).filter((product) => product.type === 'addon'))
</script>

<template>
  <BaseDialog
    :dialogId="dialogId"
    title="Upgrade your plan"
    description="Choose the plan that's right for you and save big with yearly billing!"
    :icon="ICONS.SUBSCRIPTIONS"
  >
    <div class="mt-6 mb-8 text-center">
      <div class="inline-flex p-1 border rounded-lg bg-muted">
        <Button
          :variant="billingCycleToggle === 'monthly' ? 'default' : 'ghost'"
          @click="billingCycleToggle = 'monthly'"
          class="w-full px-6 rounded-md"
        >
          Monthly
        </Button>
        <Button
          :variant="billingCycleToggle === 'yearly' ? 'default' : 'ghost'"
          @click="billingCycleToggle = 'yearly'"
          class="w-full px-6 ml-1 rounded-md"
        >
          Yearly
          <span class="ml-2 px-2 py-0.5 text-xs font-semibold text-white bg-green-500 rounded-full"
            >SAVE 20%</span
          >
        </Button>
      </div>
    </div>

    <div v-if="isPricingTableError" class="mt-4 text-center text-red-500">
      {{ isPricingTableError.message }}
    </div>

    <template v-else>
      <h2 class="mt-12 mb-12 text-2xl font-semibold text-center text-gray-800">
        Subscription Plans
      </h2>

      <div
        v-if="isPricingTableLoading"
        class="grid items-start grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-4"
      >
        <div
          v-for="n in 4"
          :key="`plan-skeleton-${n}`"
          class="p-6 border rounded-lg shadow-sm bg-card text-card-foreground"
        >
          <Skeleton class="w-1/3 h-6 mb-2" />
          <div class="flex items-baseline mb-4">
            <Skeleton class="w-1/2 h-10 mr-1" />
            <Skeleton class="w-1/4 h-4" />
          </div>
          <Skeleton class="w-full h-10 mb-6 rounded-md" />
          <div class="space-y-4">
            <div v-for="i in 3" :key="`feature-plan-${n}-${i}`" class="pt-4 border-t">
              <div class="flex items-center justify-between">
                <Skeleton class="w-2/5 h-4" />
                <Skeleton class="w-1/5 h-4" />
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        v-else-if="plans.length > 0"
        class="grid items-start grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-4"
      >
        <ProductCard
          v-for="item in plans"
          :key="item.id"
          :product="item"
          :billing-cycle="billingCycleToggle"
        />
      </div>

      <h2 class="mt-12 mb-12 text-2xl font-semibold text-center text-gray-800">
        Usage-Based Add-ons
      </h2>

      <div
        v-if="isPricingTableLoading"
        class="grid items-start grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3"
      >
        <div
          v-for="n in 3"
          :key="`addon-skeleton-${n}`"
          class="p-6 border rounded-lg shadow-sm bg-card text-card-foreground"
        >
          <Skeleton class="w-1/2 h-6 mb-2" />
          <div class="flex items-baseline mb-4">
            <Skeleton class="w-1/3 h-10 mr-1" />
            <Skeleton class="w-1/4 h-4" />
          </div>
          <Skeleton class="w-full h-10 mb-6 rounded-md" />
          <div class="space-y-2">
            <div class="pt-4 border-t">
              <div class="flex items-center justify-between">
                <Skeleton class="w-1/3 h-4" />
                <Skeleton class="w-1/4 h-4" />
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        v-else-if="addons.length > 0"
        class="grid items-start grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3"
      >
        <ProductCard
          v-for="item in addons"
          :key="item.id"
          :product="item"
          :billing-cycle="billingCycleToggle"
        />
      </div>
    </template>
  </BaseDialog>
</template>
