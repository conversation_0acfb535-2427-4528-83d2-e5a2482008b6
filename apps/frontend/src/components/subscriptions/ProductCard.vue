<script setup lang="ts">
import {
  queryPortalSessionUrl,
  queryEmbeddedCheckoutSession,
  queryPricingTable,
} from '@/services/subscription_service'
import { CheckCircle, ChevronDown, ChevronUp } from 'lucide-vue-next'
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/shadcn/ui/collapsible'
import type { Product } from '@/types'

const props = defineProps({
  product: {
    type: Object as () => Product,
    required: true,
  },
  billingCycle: {
    type: String as () => 'monthly' | 'yearly',
    required: true,
  },
})

// Refs
const expandedSections = ref<Record<string, boolean>>({})
const isCheckoutLoading = ref(false)

// Computed
const currentPriceInfo = computed(() => {
  return props.billingCycle === 'yearly' && props.product.yearly
    ? props.product.yearly
    : props.product.monthly
})

const monthlyPriceForYearlyComparison = computed(() => {
  if (props.billingCycle === 'yearly' && props.product.monthly && props.product.yearly) {
    // This is a simplified calculation. For true monthly equivalent of a yearly price,
    // you might divide props.product.yearly.price / 12.
    // However, often SaaS shows the original monthly price for comparison.
    return props.product.monthly.price
  }
  return null
})

const priceUnitText = computed(() => {
  if (props.product.billing_type === 'fixed') {
    return props.billingCycle === 'yearly' ? 'year' : 'month'
  }
  return 'unit'
})

const actionButtonText = computed(() =>
  props.product.type === 'plan' ? 'Choose Plan' : 'Purchase Add-on'
)

const actionButtonVariant = computed(() => {
  if (props.product.is_user_subscribed) {
    return 'secondary'
  }
  // Emphasize button more if yearly is selected for a plan
  if (props.billingCycle === 'yearly' && props.product.type === 'plan') {
    return 'default' // Or another variant that stands out, e.g., props.product.is_popular ? 'default' : 'primary'
  }
  if (props.product.is_popular) {
    return 'default'
  }
  return 'outline'
})

const showPopularBadge = computed(
  () => props.product.is_popular && !props.product.is_user_subscribed
)

// Functions
const toggleSection = (sectionTitle: string) => {
  expandedSections.value[sectionTitle] = !expandedSections.value[sectionTitle]
}

const isSectionExpanded = (sectionTitle: string) => {
  return expandedSections.value[sectionTitle] ?? false
}

const openCheckout = async () => {
  isCheckoutLoading.value = true

  try {
    const priceIdToUse = currentPriceInfo.value.default_stripe_price
    const embeddedCheckoutSession = await queryEmbeddedCheckoutSession(ref(priceIdToUse), ref(1))

    toast({
      title: 'Checkout',
      description: `Preparing checkout for ${props.product.title}...`,
    })

    if (embeddedCheckoutSession?.url) {
      window.location.href = embeddedCheckoutSession.url

      return
    } else {
      toast({
        title: 'Error',
        description: 'Could not retrieve Stripe checkout session.',
        variant: 'destructive',
      })
    }
  } catch (error) {
    console.error('Checkout process failed:', error)
  } finally {
    isCheckoutLoading.value = false
  }
}
</script>

<template>
  <BaseCard
    class="relative flex flex-col border rounded-lg shadow-sm"
    :class="{
      'border-primary ring-2 ring-primary':
        product.is_popular || (billingCycle === 'yearly' && product.type === 'plan'),
      'opacity-75': product.is_user_subscribed,
    }"
  >
    <div class="h-[26px]">
      <div v-if="showPopularBadge" class="absolute top-0 right-0 -mt-3 -mr-3">
        <span
          class="inline-flex items-center px-3 py-0.5 rounded-full text-xs font-medium bg-green-500 text-primary-foreground"
        >
          Most Popular
        </span>
      </div>
      <div
        v-if="product.type === 'plan' && product.is_user_subscribed"
        class="absolute top-0 left-0 -mt-3 -ml-3"
      >
        <span
          class="inline-flex items-center px-3 py-0.5 rounded-full text-xs font-medium bg-red-500 text-primary-foreground"
        >
          Current Plan
        </span>
      </div>
    </div>

    <div class="flex flex-col h-full p-2 pt-0">
      <div class="mb-4 text-left">
        <h3 class="text-2xl font-semibold text-gray-900">{{ product.title }}</h3>

        <div class="flex items-baseline mt-2">
          <span class="text-5xl font-extrabold tracking-tight text-gray-900">
            ${{ currentPriceInfo.price }}
          </span>
          <span class="ml-1 text-base font-medium text-gray-500">/{{ priceUnitText }}</span>
          <span
            v-if="monthlyPriceForYearlyComparison && product.billing_type === 'fixed'"
            class="ml-2 text-sm text-gray-400 line-through"
          >
            ${{ monthlyPriceForYearlyComparison }}/month
          </span>
        </div>
        <p
          v-if="
            billingCycle === 'yearly' &&
            product.billing_type === 'fixed' &&
            product.monthly &&
            product.yearly &&
            product.monthly.price > product.yearly.price / 12
          "
          class="mt-1 text-xs font-semibold text-green-600"
        >
          Save ${{ (product.monthly.price * 12 - product.yearly.price).toFixed(2) }} per year!
        </p>

        <p class="mt-2 text-sm text-gray-600 min-h-[3rem]">{{ product.description }}</p>
      </div>

      <div class="min-h-[60px] mb-4">
        <BaseButton
          :variant="actionButtonVariant"
          size="lg"
          class="w-full py-3 text-base font-medium"
          @click="openCheckout"
          :isDisabled="props.product.is_user_subscribed"
          :isSubmitting="isCheckoutLoading"
        >
          {{ actionButtonText }}
        </BaseButton>
      </div>

      <div
        class="flex-grow space-y-4"
        v-if="product.pricing_table && product.pricing_table.length > 0"
      >
        <div v-for="section in product.pricing_table" :key="section.title">
          <Collapsible
            :open="isSectionExpanded(section.title)"
            @update:open="() => toggleSection(section.title)"
          >
            <div class="flex items-center justify-between py-2">
              <h4 class="text-xs font-semibold tracking-wider text-gray-700 uppercase">
                {{ section.title }}
              </h4>

              <CollapsibleTrigger asChild>
                <Button variant="ghost" size="sm" class="p-1 text-gray-500 hover:text-gray-700">
                  <span class="text-xs">
                    {{
                      isSectionExpanded(section.title)
                        ? 'Show less'
                        : `Show all (${section.features.length})`
                    }}
                  </span>
                  <component
                    :is="isSectionExpanded(section.title) ? ChevronUp : ChevronDown"
                    class="w-3 h-3 ml-1"
                  />
                </Button>
              </CollapsibleTrigger>
            </div>

            <CollapsibleContent>
              <ul class="pb-2 space-y-2">
                <li
                  v-for="(feature, index) in section.features"
                  :key="feature.label"
                  class="flex items-start text-sm text-gray-600"
                  v-show="isSectionExpanded(section.title)"
                >
                  <CheckCircle class="w-4 h-4 mr-2 text-green-500 shrink-0 mt-0.5" />
                  <span>{{ feature.label }}</span>
                </li>
              </ul>
            </CollapsibleContent>
          </Collapsible>

          <hr class="my-2" />
        </div>
      </div>

      <div class="h-[24px]">
        <ul class="mt-4 space-y-2 text-sm" v-if="product.is_user_subscribed">
          <li>
            <router-link to="/subscriptions/portal" class="text-primary hover:underline">
              Manage my subscription
            </router-link>
          </li>
        </ul>
      </div>
    </div>
  </BaseCard>
</template>
