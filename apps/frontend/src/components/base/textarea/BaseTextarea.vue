<!-- Base Textarea with auto-grow functionality -->
<template>
  <div class="relative">
    <Textarea
      ref="textareaComponentRef"
      v-model="modelValue"
      :placeholder="placeholder"
      :rows="rows"
      :maxlength="maxlength"
      :required="required"
      :class="props.class"
      @input="autoGrow"
    />
    <div v-if="maxlength" class="absolute text-xs bottom-2 right-2 text-muted-foreground">
      {{ modelValue.length }}/{{ maxlength }}
    </div>
  </div>
</template>

<script setup lang="ts">
import type { HTMLAttributes } from 'vue'

const props = withDefaults(
  defineProps<{
    modelValue: string
    placeholder?: string
    rows?: number
    maxlength?: number
    required?: boolean
    class?: HTMLAttributes['class']
  }>(),
  {
    placeholder: '',
    rows: 1,
    maxlength: undefined,
    required: false,
    class: '',
  }
)

const emit = defineEmits(['update:modelValue'])

// Ref to the Textarea *component instance*
const textareaComponentRef = ref<any | null>(null) // Use any for now, ideally get Textarea type

const modelValue = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
})

const getElement = (): HTMLTextAreaElement | null => {
  // Helper to get the underlying textarea element
  return textareaComponentRef.value?.$el as HTMLTextAreaElement | null
}

const autoGrow = () => {
  nextTick(() => {
    const textarea = getElement()
    if (!textarea) {
      console.warn('BaseTextarea: Could not find element for autoGrow in nextTick.')
      return
    }
    textarea.style.height = 'auto'
    textarea.style.height = `${Math.min(textarea.scrollHeight, 300)}px`
  })
}

const resetHeight = () => {
  nextTick(() => {
    const textarea = getElement()
    if (!textarea) {
      console.warn('BaseTextarea: Could not find element for resetHeight in nextTick.')
      return
    }
    textarea.style.height = 'auto'
  })
}

watch(modelValue, () => {
  autoGrow()
})

onMounted(() => {
  // Wait for component to mount and $el to be available
  nextTick(() => {
    autoGrow() // Call autoGrow after mount ensures element should exist
  })
})

defineExpose({
  resetHeight,
})
</script>
