<template>
  <div class="h-full overflow-hidden">
    <Tabs :default-value="defaultValue" class="flex flex-col h-full md:flex-row">
      <div class="w-full md:w-48 md:mr-6">
        <TabsList
          class="flex flex-col w-full space-y-2 md:space-x-0 md:space-y-2 md:overflow-x-visible md:fixed md:w-48"
        >
          <TabsTrigger
            v-for="tab in tabs"
            :key="tab.value"
            :value="tab.value"
            class="justify-start flex-shrink-0 w-full"
          >
            <div class="flex items-center">
              <BaseIcon :name="tab.icon" v-if="tab.icon" class="mr-2" />
              <span>{{ tab.label }}</span>
            </div>
          </TabsTrigger>
        </TabsList>
      </div>

      <div class="flex-1 mt-4 overflow-y-auto md:mt-0 md:h-full">
        <slot />
      </div>
    </Tabs>
  </div>
</template>

<script setup lang="ts">
import type { Component } from 'vue'

interface Tab {
  value: string
  label: string
  icon: string
}

defineProps<{
  defaultValue: string
  tabs: Tab[]
}>()
</script>

<style scoped>
/* Hide scrollbar for Chrome, Safari and Opera */
.overflow-x-auto::-webkit-scrollbar {
  display: none;
}

/* Hide scrollbar for IE, Edge and Firefox */
.overflow-x-auto {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}
</style>
