<template>
  <div class="space-y-4">
    <!-- Label -->
    <div class="space-y-1">
      <Label v-if="label">{{ label }}</Label>
      <p v-if="description" class="text-sm text-muted-foreground">
        {{ description }}
      </p>
    </div>

    <!-- Input slot -->
    <div :class="[error && 'border-destructive']">
      <div class="space-y-4">
        <slot />
      </div>
    </div>

    <!-- Error message -->
    <p v-if="error" class="text-sm text-destructive">
      {{ error }}
    </p>

    <!-- Helper text -->
    <p v-if="helperText" class="text-sm text-muted-foreground">
      {{ helperText }}
    </p>
  </div>
</template>

<script setup lang="ts">
defineProps<{
  label?: string
  description?: string
  error?: string
  helperText?: string
}>()
</script>
