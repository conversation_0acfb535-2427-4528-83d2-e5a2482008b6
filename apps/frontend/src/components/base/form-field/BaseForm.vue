<script setup lang="ts">
const props = defineProps({
  isLoading: { type: Boolean, required: true },
  isSubmitting: { type: Boolean, required: true },
  inputs: { type: Number, required: true },
})

const emit = defineEmits(['submit'])

const isDisabled = computed(() => props.isLoading || props.isSubmitting)

function onSubmit(event: Event) {
  if (!props.isLoading && !props.isSubmitting) {
    emit('submit', event)
  }
}
</script>

<template>
  <form class="grid gap-4" @submit.prevent="onSubmit" v-bind="$attrs" :aria-disabled="isDisabled">
    <BaseSkeleton
      v-if="props.isLoading"
      type="form"
      :rows="props.inputs"
      :headers="0"
      :cols="0"
      class="w-full"
    />
    <template v-else :class="isDisabled ? 'pointer-events-none opacity-60' : ''">
      <slot />
    </template>
  </form>
</template>
