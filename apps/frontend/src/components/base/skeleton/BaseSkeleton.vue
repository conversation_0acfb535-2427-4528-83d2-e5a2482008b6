<template>
  <template v-if="type === 'card'">
    <Skeleton class="w-3/4 h-6" />
    <Skeleton class="w-1/2 h-4" />
    <Skeleton class="w-full h-20" />
  </template>

  <template v-else-if="type === 'table'">
    <div class="flex items-center justify-between mb-4 space-x-2">
      <Skeleton class="flex-1 h-10" />
      <Skeleton class="h-10 w-[150px]" />
    </div>
    <div class="border rounded-md">
      <div class="flex items-center p-4 border-b bg-muted/50">
        <Skeleton v-for="i in headers" :key="`header-${i}`" class="flex-1 h-5 mr-4 last:mr-0" />
      </div>
      <div
        v-for="rowIndex in rows"
        :key="`row-${rowIndex}`"
        class="flex items-center p-3 border-b last:border-b-0"
      >
        <Skeleton
          v-for="colIndex in cols"
          :key="`row-${rowIndex}-col-${colIndex}`"
          class="flex-1 h-5 mr-4 last:mr-0"
        />
        <Skeleton class="w-8 h-8 ml-auto rounded-full" />
      </div>
    </div>
  </template>

  <template v-else-if="type === 'chat'">
    <div class="space-y-4">
      <div class="flex items-start space-x-3">
        <Skeleton class="w-10 h-10 rounded-full" />
        <div class="flex-1 space-y-2">
          <Skeleton class="w-3/4 h-4" />
          <Skeleton class="w-1/2 h-4" />
        </div>
      </div>
      <div class="flex items-start justify-end space-x-3">
        <div class="flex-1 space-y-2 text-right">
          <Skeleton class="w-3/4 h-4 ml-auto" />
          <Skeleton class="w-1/2 h-4 ml-auto" />
        </div>
        <Skeleton class="w-10 h-10 rounded-full" />
      </div>
      <div class="flex items-start space-x-3">
        <Skeleton class="w-10 h-10 rounded-full" />
        <div class="flex-1 space-y-2">
          <Skeleton class="w-1/2 h-4" />
        </div>
      </div>
    </div>
  </template>

  <template v-else-if="type === 'form'">
    <div class="flex flex-col w-full gap-4 mx-auto">
      <div v-for="i in rows" :key="`form-row-${i}`" class="space-y-1">
        <Skeleton :class="`w-32 h-6 rounded-md`" />
        <Skeleton :class="`w-full h-9 rounded-2xl`" />
      </div>
      <Skeleton class="w-full h-9 rounded-2xl" />
    </div>
  </template>

  <template v-else-if="type === 'app'">
    <div class="fixed inset-0 z-50 flex items-center justify-center bg-white/80">
      <div class="flex w-screen h-screen bg-muted">
        <!-- Left Menu (256px) -->
        <div
          class="flex flex-col w-[256px] min-w-[256px] max-w-[256px] h-full p-6 gap-6 bg-white border-r"
        >
          <Skeleton class="w-32 h-8 mb-10" />
          <div class="flex flex-col flex-1 gap-4">
            <Skeleton class="w-full h-8" />
            <Skeleton class="w-full h-8" />
            <Skeleton class="w-full h-8" />
            <Skeleton class="w-full h-8" />
            <Skeleton class="w-full h-8" />
            <Skeleton class="w-full h-8" />
            <Skeleton class="w-full h-8" />
          </div>
          <Skeleton class="w-24 h-8 mt-auto" />
          <Skeleton class="w-24 h-8 mt-auto" />
        </div>
        <!-- Right Content -->
        <div class="flex flex-col flex-1 h-full">
          <div class="flex items-center bg-white border-b px-6 h-[64px] min-h-[64px] max-h-[64px]">
            <Skeleton class="w-1/3 h-10" />
          </div>
          <div class="flex items-center justify-center flex-1 px-6 py-10 bg-muted">
            <div class="flex flex-col w-full max-w-2xl gap-8">
              <div v-for="i in 6" :key="`chat-row-${i}`" class="mb-2">
                <div v-if="i % 2 === 1" class="flex items-start gap-6">
                  <Skeleton class="w-12 h-12 rounded-full" />
                  <div class="flex flex-col flex-1 gap-3">
                    <Skeleton class="w-2/3 h-6" />
                    <Skeleton class="w-1/3 h-6" />
                  </div>
                </div>
                <div v-else class="flex flex-row-reverse items-start justify-end gap-6">
                  <Skeleton class="w-12 h-12 rounded-full" />
                  <div class="flex flex-col items-end flex-1 gap-3">
                    <Skeleton class="w-2/3 h-6" />
                    <Skeleton class="w-1/3 h-6" />
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div
            class="flex items-center justify-center px-10 bg-white border-t h-[158px] min-h-[158px] max-h-[158px]"
          >
            <div class="flex justify-center w-full max-w-xl gap-6 py-2">
              <Skeleton class="w-2/3 h-12" />
              <Skeleton class="w-16 h-12 rounded-full" />
            </div>
          </div>
        </div>
      </div>
    </div>
  </template>

  <template v-else>
    <Skeleton class="w-full h-10" />
  </template>
</template>

<script setup lang="ts">
const props = defineProps({
  type: { type: String, required: true },
  rows: { type: Number, required: true },
  headers: { type: Number, required: true },
  cols: { type: Number, required: true },
})
</script>
