<template>
  <div
    class="relative flex items-center h-10 gap-2 px-3 border rounded-md cursor-pointer hover:bg-muted group"
    :class="{ 'ring-2 ring-primary ring-offset-2': isPickingColor }"
    @click="triggerColorPicker"
  >
    <div class="w-6 h-6 border rounded" :style="{ backgroundColor: modelValue || '#FFFFFF' }" />
    <span class="flex-1 font-mono text-sm truncate">{{
      (modelValue || '#FFFFFF').toUpperCase()
    }}</span>
    <Input
      ref="colorInputRef"
      :model-value="modelValue || '#FFFFFF'"
      @input="updateColor(($event.target as HTMLInputElement).value)"
      type="color"
      class="absolute inset-0 opacity-0 cursor-pointer"
      @focus="isPickingColor = true"
      @blur="isPickingColor = false"
    />
  </div>
</template>

<script setup lang="ts">
defineProps<{
  modelValue?: string | null
}>()

const emit = defineEmits<{ (e: 'update:modelValue', value: string): void }>()

const colorInputRef = ref<HTMLInputElement | null>(null)
const isPickingColor = ref(false)

const triggerColorPicker = () => {
  colorInputRef.value?.click()
}

const updateColor = (value: string) => {
  // Basic validation for hex color format
  if (/^#[0-9A-F]{6}$/i.test(value)) {
    emit('update:modelValue', value)
  }
}
</script>
