<template>
  <div class="flex items-center justify-center w-full">
    <Label
      :for="inputId"
      class="relative flex flex-col items-center justify-center w-full transition-all duration-150 ease-in-out border-2 border-dashed rounded-lg cursor-pointer"
      :class="[
        { 'border-primary': isDragging, 'border-destructive': hasError },
        files.length === 0 ? 'h-[156px] hover:bg-muted/50' : 'h-auto',
      ]"
      @dragenter.prevent="handleDragEnter"
      @dragleave.prevent="handleDragLeave"
      @dragover.prevent="dragOver"
      @drop.prevent="handleDrop"
    >
      <div
        v-if="files.length === 0"
        class="flex flex-col items-center justify-center px-4 py-4 text-center"
      >
        <FileUp class="w-6 h-6 mb-3 text-muted-foreground/80" />
        <p class="mb-1 text-sm text-muted-foreground">
          <span class="font-semibold text-primary">Click to upload</span> or drag and drop
        </p>
        <p class="text-xs text-muted-foreground">{{ acceptText }}</p>
      </div>

      <!-- Files Preview -->
      <div v-else class="w-full">
        <div class="p-2 sm:p-3">
          <ScrollArea :class="previewHeightClass" class="pr-3">
            <div class="space-y-1.5">
              <div
                v-for="(file, index) in files"
                :key="file.id || index"
                class="flex items-center justify-between px-2 py-1.5 transition-colors rounded-lg bg-muted/50 hover:bg-muted/80"
              >
                <div class="flex items-center min-w-0 gap-2">
                  <div
                    class="flex items-center justify-center flex-shrink-0 w-8 h-8 overflow-hidden border rounded bg-background"
                  >
                    <img
                      v-if="file.previewUrl && file.type.startsWith('image/')"
                      :src="file.previewUrl"
                      alt="Preview"
                      class="object-cover w-full h-full"
                    />
                    <BaseIcon v-else :name="file.iconComponent || 'FileText'" class="w-4 h-4 text-muted-foreground" />
                  </div>
                  <div class="flex-1 min-w-0">
                    <div class="flex items-center gap-1.5">
                      <p class="text-xs font-medium truncate" :title="file.name">{{ file.name }}</p>
                      <Badge
                        v-if="getDuplicateCount(file) > 1"
                        variant="secondary"
                        class="ml-1 text-[10px] px-1 py-0 h-[16px] flex-shrink-0"
                      >
                        {{ getDuplicateCount(file) }}x
                      </Badge>
                    </div>
                    <p class="text-[10px] text-muted-foreground">
                      {{ formatFileSize(file.size) }}
                    </p>
                  </div>
                </div>
                <div class="flex items-center flex-shrink-0 gap-1 ml-2">
                  <div v-if="file.progress !== undefined" class="flex items-center gap-1.5">
                    <div class="w-[40px] h-1 rounded-full bg-muted-foreground/20 overflow-hidden">
                      <div
                        class="h-full transition-all duration-300 rounded-full bg-primary"
                        :style="{ width: `${Math.round(file.progress)}%` }"
                      />
                    </div>
                    <span class="text-[10px] text-muted-foreground w-[32px] text-right">
                      {{ Math.round(file.progress) }}%
                    </span>
                  </div>
                  <Button
                    v-if="file.progress === undefined"
                    size="icon"
                    variant="ghost"
                    class="w-6 h-6 rounded-full hover:bg-background/80 text-muted-foreground hover:text-foreground"
                    @click.stop="removeFile(index)"
                  >
                    <X class="w-3.5 h-3.5" />
                  </Button>
                  <Loader2 v-else class="w-3.5 h-3.5 animate-spin text-muted-foreground" />
                </div>
              </div>
            </div>
          </ScrollArea>
        </div>
      </div>

      <Input
        :id="inputId"
        ref="inputRef"
        type="file"
        class="hidden"
        :accept="accept"
        :multiple="multiple"
        @change="handleFileSelect"
      />
    </Label>
  </div>
</template>

<script setup lang="ts">
const props = withDefaults(
  defineProps<{
    accept?: string
    acceptText?: string
    multiple?: boolean
    maxSize?: number
    maxFiles?: number
    initialFiles?: File[]
    previewHeightClass?: string
  }>(),
  {
    accept: undefined,
    acceptText: 'Upload files',
    multiple: true,
    maxSize: 10 * 1024 * 1024, // 10MB
    maxFiles: undefined,
    initialFiles: () => [],
    previewHeightClass: 'h-[160px]', // Default height
  }
)

interface UploadingFile extends File {
  id: string // Unique ID for key binding
  previewUrl?: string // Data URL for image previews
  progress?: number
  iconComponent?: string | null // Store the icon component
}

const emit = defineEmits<{
  (e: 'files-change', files: File[]): void
}>()

const inputId = computed(() => `dropzone-${Math.random().toString(36).substring(7)}`)
const inputRef = ref<HTMLInputElement | null>(null)
const isDragging = ref(false)
const hasError = ref(false)
const files = ref<UploadingFile[]>([])
const { toast } = useToast()

const maxSizeText = computed(() => (props.maxSize ? formatFileSize(props.maxSize) : null))

const handleDragEnter = (e: DragEvent) => {
  e.preventDefault()
  isDragging.value = true
  hasError.value = false
}

const handleDragLeave = (e: DragEvent) => {
  e.preventDefault()
  if (!e.relatedTarget || !(e.currentTarget as Element).contains(e.relatedTarget as Node)) {
    isDragging.value = false
  }
}

const dragOver = (e: DragEvent) => {
  e.preventDefault()
  isDragging.value = true
  hasError.value = false
}

const handleDrop = (e: DragEvent) => {
  e.preventDefault()
  isDragging.value = false
  hasError.value = false
  const droppedFiles = Array.from(e.dataTransfer?.files || [])
  if (droppedFiles.length > 0) {
    handleFiles(droppedFiles)
  }
}

const handleFileSelect = (e: Event) => {
  const input = e.target as HTMLInputElement
  if (input.files) {
    const selectedFiles = Array.from(input.files)
    handleFiles(selectedFiles)
    input.value = ''
  }
}

const getDuplicateCount = (file: UploadingFile) => {
  return files.value.filter((f) => f.name === file.name && f.size === file.size).length
}

const getIconForFileType = (type: string, name: string): string | null => {
  const extension = name.includes('.') ? name.substring(name.lastIndexOf('.')).toLowerCase() : ''

  // Return component constructors directly, removing shallowRef
  if (type.startsWith('image/')) return 'FileImage'
  if (type.startsWith('audio/')) return 'FileAudio'
  if (type.startsWith('video/')) return 'FileVideo'
  if (type === 'application/pdf') return 'FileText'
  if (type === 'application/json' || extension === '.json') return 'FileJson'
  if (type.startsWith('text/csv') || extension === '.csv') return 'FileSpreadsheet'
  if (type.startsWith('text/')) return 'FileText'
  if (
    type.includes('spreadsheet') ||
    type.includes('excel') ||
    extension === '.xlsx' ||
    extension === '.xls'
  )
    return 'FileSpreadsheet'
  if (type.includes('word') || extension === '.docx' || extension === '.doc') return 'FileText'
  if (type.includes('presentation') || extension === '.pptx' || extension === '.ppt')
    return 'FileBadge'
  if (
    type.includes('zip') ||
    type.includes('archive') ||
    extension === '.zip' ||
    extension === '.rar' ||
    extension === '.tar' ||
    extension === '.gz'
  )
    return 'FileArchive'
  if (
    type.includes('font') ||
    extension === '.ttf' ||
    extension === '.otf' ||
    extension === '.woff'
  )
    return 'FileDigit'
  if (
    type.includes('code') ||
    type.includes('script') ||
    extension === '.js' ||
    extension === '.ts' ||
    extension === '.py' ||
    extension === '.java' ||
    extension === '.php' ||
    extension === '.html' ||
    extension === '.css'
  )
    return 'FileCode'
  if (type.includes('terminal') || extension === '.sh' || extension === '.bash')
    return 'FileTerminal'
  if (
    type.includes('key') ||
    type.includes('cert') ||
    extension === '.key' ||
    extension === '.pem' ||
    extension === '.crt'
  )
    return 'FileLock2'

  // Default icon
  return 'FileText'
}

const generatePreview = (file: File, callback: (url: string) => void) => {
  // Only generate preview for images
  if (file.type.startsWith('image/')) {
    const reader = new FileReader()
    reader.onload = (e) => {
      callback(e.target?.result as string)
    }
    reader.onerror = () => {
      console.error('FileReader error for file:', file.name)
      callback('')
    }
    reader.readAsDataURL(file)
  } else {
    callback('') // No preview for non-images
  }
}

const validateFile = (file: File): boolean => {
  // Check file size
  if (file.size > props.maxSize) {
    toast({
      title: 'File too large',
      description: `${file.name} (${formatFileSize(file.size)}) exceeds the ${formatFileSize(props.maxSize)} limit.`,
      variant: 'destructive',
    })
    hasError.value = true // Indicate error
    return false
  }

  // Check file type if accept is specified
  if (props.accept) {
    const fileExtension = file.name.substring(file.name.lastIndexOf('.')).toLowerCase()
    const mimeType = file.type
    const accepted = props.accept.split(',').map((t) => t.trim().toLowerCase())

    const isAccepted = accepted.some((type) => {
      if (type.startsWith('.')) {
        // Check extension
        return fileExtension === type
      } else if (type.endsWith('/*')) {
        // Check MIME type category (e.g., image/*)
        return mimeType.startsWith(type.slice(0, -2))
      } else {
        // Check exact MIME type
        return mimeType === type
      }
    })

    if (!isAccepted) {
      toast({
        title: 'Invalid file type',
        description: `${file.name} (${file.type || 'unknown type'}) is not accepted.`,
        variant: 'destructive',
      })
      hasError.value = true // Indicate error
      return false
    }
  }
  hasError.value = false // Reset error if valid
  return true
}

const handleFiles = (newFiles: File[]) => {
  hasError.value = false // Reset error state

  // --- Replacement Logic for multiple = false ---
  if (!props.multiple && newFiles.length > 0) {
    // If only one file is allowed, clear existing files before processing the new one.
    files.value = []
  }
  // --- End Replacement Logic ---

  const currentFileCount = files.value.length
  // Determine max allowed based on props (Infinity if undefined and multiple)
  const maxAllowed = props.multiple ? (props.maxFiles ?? Infinity) : 1
  const canAddCount = maxAllowed - currentFileCount

  // Check if trying to add files when the limit is already reached (or exceeded)
  if (canAddCount <= 0) {
    // If multiple is true, we show a limit reached error (unless we just cleared for replacement)
    if (props.multiple) {
      toast({
        title: 'Maximum files reached',
        description: `You can only upload up to ${maxAllowed} file(s).`,
        variant: 'destructive',
      })
      hasError.value = true
      return // Stop processing
    }
    // If multiple is false, we already cleared the array, so we can proceed to add the single new file.
  }

  // Determine which files to actually process based on limits and multiple prop
  const filesToAdd = props.multiple ? newFiles : [newFiles[0]] // Take only first if not multiple
  // Ensure we don't process more files than allowed, even if dropped/selected more
  const filesToValidate = filesToAdd.slice(0, Math.max(0, canAddCount))

  // Filter the files based on validation rules
  const validFiles = filesToValidate.filter(validateFile)

  // Handle cases where validation might have failed for all attempted files
  if (
    validFiles.length === 0 &&
    newFiles.length > 0 &&
    !hasError.value // Only show secondary warnings if no primary error (like size/type) occurred
  ) {
    // If no files were valid after filtering, and no specific error was already shown,
    // it implies they were filtered out due to limits or being duplicates of *each other*.
    if (newFiles.length > filesToValidate.length) {
      // This happens if they tried to add more than `canAddCount` files
      toast({
        title: 'File limit exceeded',
        description: `Some files were not added because the limit of ${maxAllowed} was reached.`,
      })
    } else {
      // This case is less likely now with the duplicate check below, but covers edge cases
      toast({
        title: 'No new valid files added',
        description: 'Files may be duplicates or invalid.',
      })
    }
    return // Stop processing
  }

  // Add the valid files
  let duplicatesSkipped = 0
  validFiles.forEach((file) => {
    // Check for logical duplicates (already staged)
    const isStagedDuplicate = files.value.some(
      (f) => f.name === file.name && f.size === file.size && f.lastModified === file.lastModified
    )

    if (!isStagedDuplicate) {
      const iconComponent = getIconForFileType(file.type, file.name) // Get icon

      const uploadingFile: UploadingFile = Object.assign(file, {
        id: `${file.name}-${file.size}-${file.lastModified}-${Math.random()}`,
        previewUrl: undefined,
        progress: undefined,
        iconComponent: iconComponent, // Store icon
      })

      files.value.push(uploadingFile)

      // Generate preview async
      generatePreview(file, (url) => {
        const targetFile = files.value.find((f) => f.id === uploadingFile.id)
        if (targetFile && url) {
          // Check if url is not empty
          targetFile.previewUrl = url
        }
      })
    } else {
      duplicatesSkipped++
    }
  })

  // --- User Feedback Toasts ---

  // If duplicates were skipped during the add process
  if (duplicatesSkipped > 0) {
    toast({
      title: `Duplicate file${duplicatesSkipped > 1 ? 's' : ''} skipped`,
      description: `${duplicatesSkipped} file${duplicatesSkipped > 1 ? 's were' : ' was'} already in the list.`,
    })
  }

  // If only one file is allowed and more were initially provided by the user
  if (!props.multiple && newFiles.length > 1) {
    toast({
      title: 'Multiple files selected',
      description: 'Only the first valid file was added. Multiple files are not allowed.',
    })
  }

  // If some files were invalid (type/size) and filtered out earlier
  const invalidCount = newFiles.length - validFiles.length - duplicatesSkipped
  if (invalidCount > 0 && !hasError.value /* Don't show if a major error like limit occurred */) {
    toast({
      title: `Invalid file${invalidCount > 1 ? 's' : ''} skipped`,
      description: `${invalidCount} file${invalidCount > 1 ? 's were' : ' was'} skipped due to type or size constraints.`,
    })
  }
  // --- End User Feedback Toasts ---

  // Emit the final list of staged files
  emit(
    'files-change',
    files.value.map((f) => f as File)
  )
}

const removeFile = (index: number) => {
  if (index >= 0 && index < files.value.length) {
    files.value.splice(index, 1)
    emit(
      'files-change',
      files.value.map((f) => f as File)
    ) // Emit original File objects
  }
}

const clearFiles = () => {
  files.value = []
  // Reset the actual file input element
  if (inputRef.value) {
    inputRef.value.value = ''
  }
  emit(
    'files-change',
    files.value.map((f) => f as File)
  ) // Emit original File objects
}

// Expose clearFiles method if needed by parent
defineExpose({ clearFiles })

const formatFileSize = (bytes: number): string => {
  if (bytes < 0) return 'Invalid size'
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'] // Added TB
  // Handle potential log(0) or negative values
  if (bytes < 1) return `${bytes} Bytes`
  const i = Math.min(Math.floor(Math.log(bytes) / Math.log(k)), sizes.length - 1)
  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`
}
</script>

<style scoped>
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}
</style>
