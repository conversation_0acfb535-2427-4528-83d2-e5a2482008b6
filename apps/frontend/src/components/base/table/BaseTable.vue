<template>
  <BaseSkeleton
    v-if="isLoading"
    type="table"
    :rows="3"
    :headers="totalColumns"
    :cols="totalColumns"
  />

  <template v-else>
    <div v-if="$slots.actions" class="flex items-center justify-end w-full mb-4">
      <slot name="actions" />
    </div>

    <Card class="w-full pl-2 pr-2 border rounded-md">
      <div class="overflow-x-auto">
        <Table class="w-full table-fixed min-w-[600px]">
          <TableHeader>
            <TableRow>
              <TableHead
                v-for="(head, index) in heads"
                :key="head.key || head.label"
                :style="{ width: totalColumns > 0 ? `${100 / totalColumns}%` : undefined }"
                :class="head.align ? `text-${head.align}` : 'text-left'"
              >
                {{ head.label }}
              </TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            <template v-if="rows.length">
              <TableRow
                v-for="(row, rowIndex) in rows"
                :key="row.id || row.uid"
                class="cursor-pointer hover:bg-muted/50"
              >
                <template v-if="$slots.row">
                  <slot name="row" :row="row" />
                </template>
                <template v-else>
                  <TableCell
                    v-for="(head, colIndex) in heads"
                    :key="head.key || head.label"
                    class="break-words whitespace-normal"
                  >
                    <slot
                      :name="`column-${colIndex}`"
                      :row="row"
                      :value="row[head.key]"
                      :head="head"
                    >
                      {{ row[head.key] }}
                    </slot>
                  </TableCell>
                </template>
              </TableRow>
            </template>
            <TableRow v-else>
              <TableCell :colspan="heads.length" class="h-24 text-center">
                {{ emptyMessage }}
              </TableCell>
            </TableRow>
          </TableBody>
        </Table>
      </div>
    </Card>
  </template>
</template>

<script setup lang="ts">
interface Props {
  title?: string
  description?: string
  heads: {
    label: string
    key: string
    align?: string
  }[]
  rows: any[]
  isLoading: boolean
  emptyMessage: string
}

const props = defineProps<Props>()

const totalColumns = computed(() => {
  return props.heads.length
})
</script>
