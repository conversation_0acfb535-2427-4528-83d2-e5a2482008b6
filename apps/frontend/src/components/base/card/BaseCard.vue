<template>
  <Card class="w-full">
    <CardHeader v-if="title || description" class="px-6 py-4 border-b border-gray-200">
      <CardTitle v-if="title">{{ title }}</CardTitle>
      <CardDescription v-if="description">{{ description }}</CardDescription>
    </CardHeader>
    <CardContent class="p-6">
      <div class="space-y-6">
        <slot />
      </div>
    </CardContent>
    <CardFooter v-if="$slots.footer">
      <slot name="footer" />
    </CardFooter>
  </Card>
</template>

<script setup lang="ts">
defineProps<{
  title?: string
  description?: string
}>()
</script>
