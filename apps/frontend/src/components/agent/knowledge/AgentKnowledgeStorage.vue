<template>
  <BaseCard>
    <form @submit.prevent="handleSubmit" class="space-y-4">
      <!-- Credentials -->
      <div class="grid grid-cols-2 gap-x-2">
        <BaseFormField label="Access Key ID">
          <Input v-model="form.accessKeyId" type="text" placeholder="Enter access key" required />
        </BaseFormField>
        <BaseFormField label="Secret Key">
          <div class="relative">
            <Input
              v-model="form.secretAccessKey"
              :type="showSecret ? 'text' : 'password'"
              placeholder="Enter secret key"
              required
            />
            <Button
              type="button"
              variant="ghost"
              size="icon"
              class="absolute -translate-y-1/2 right-0.5 top-1/2 h-5 w-5"
              @click="showSecret = !showSecret"
            >
              <Eye v-if="!showSecret" class="w-3 h-3" />
              <EyeOff v-else class="w-3 h-3" />
            </Button>
          </div>
        </BaseFormField>
      </div>

      <!-- Bucket Info -->
      <div class="grid grid-cols-2 gap-x-2">
        <BaseFormField label="Bucket Name">
          <Input v-model="form.bucketName" type="text" placeholder="Enter name" required />
        </BaseFormField>
        <BaseFormField label="Region">
          <Input v-model="form.bucketRegion" type="text" placeholder="e.g. us-east-1" required />
        </BaseFormField>
      </div>

      <BaseFormField label="Endpoint">
        <Input v-model="form.bucketEndpoint" type="url" placeholder="Optional" />
      </BaseFormField>

      <div class="grid grid-cols-2 gap-2">
        <Button type="button" variant="outline" :disabled="isTesting" @click="testConnection">
          <Loader2 v-if="isTesting" class="w-3 h-3 mr-1 animate-spin" />
          <CheckCircle2 v-else-if="testStatus === 'success'" class="w-3 h-3 mr-1 text-green-500" />
          <XCircle v-else-if="testStatus === 'error'" class="w-3 h-3 mr-1 text-red-500" />
          <CloudCog v-else class="w-3 h-3 mr-1" />
          Test Connection
        </Button>
        <Button type="submit" :disabled="isTesting || testStatus !== 'success'"> Save </Button>
      </div>
    </form>
  </BaseCard>

  <KnowledgeList
    title="Cloud Storage"
    description="Manage your cloud storage connections."
    :items="storageConfigs"
    titleField="bucketName"
    searchPlaceholder="Search storage..."
    contentEndpoint="/api/knowledge/storage"
    :showRefreshToggle="false"
    @delete="handleDelete"
  >
    <template #item="{ item }">
      <div class="flex items-center gap-3">
        <h3 class="text-sm font-medium truncate">{{ item.bucketName }}</h3>
        <Badge :variant="getStatusVariant(item.status)" class="flex-shrink-0 px-3">
          {{ item.status }}
        </Badge>
      </div>
      <div class="flex items-center gap-2 mt-0.5 text-xs text-muted-foreground">
        <span>{{ formatDate(item.lastUpdated) }}</span>
        <span class="w-1 h-1 rounded-full bg-muted-foreground/50" />
        <span>{{ item.bucketRegion }}</span>
        <span v-if="item.bucketEndpoint" class="w-1 h-1 rounded-full bg-muted-foreground/50" />
        <span v-if="item.bucketEndpoint" class="truncate">{{ item.bucketEndpoint }}</span>
      </div>
    </template>

    <template #actions="{ item }">
      <Button size="icon" variant="ghost" class="w-8 h-8" @click="editConfig(item)">
        <Pencil class="w-4 h-4" />
      </Button>
      <Button size="icon" variant="ghost" class="w-8 h-8" @click="handleDelete(item)">
        <Trash2 class="w-4 h-4" />
      </Button>
    </template>
  </KnowledgeList>
</template>

<script setup lang="ts">
const props = defineProps<{
  agentUid: string
}>()
const emit = defineEmits(['update'])

interface StorageConfig {
  id: string
  bucketName: string
  bucketRegion: string
  bucketEndpoint?: string
  status: 'connected' | 'error'
  lastUpdated: string
}

const form = ref({
  accessKeyId: '',
  secretAccessKey: '',
  bucketName: '',
  bucketRegion: '',
  bucketEndpoint: '',
})

const showSecret = ref(false)
const isTesting = ref(false)
const testStatus = ref<'idle' | 'success' | 'error'>('idle')
const storageConfigs = ref<StorageConfig[]>([])
const { toast } = useToast()

const testConnection = async () => {
  isTesting.value = true
  testStatus.value = 'idle'

  try {
    const response = await fetch('/api/knowledge/storage/test', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        agentUid: props.agentUid,
        ...form.value,
      }),
    })

    if (response.ok) {
      testStatus.value = 'success'
      toast({
        title: 'Connection successful',
        description: 'Successfully connected to the storage bucket',
      })
    } else {
      throw new Error('Connection failed')
    }
  } catch (error) {
    testStatus.value = 'error'
    toast({
      title: 'Connection failed',
      description: 'Failed to connect to the storage bucket. Please check your credentials.',
      variant: 'destructive',
    })
  } finally {
    isTesting.value = false
  }
}

const handleSubmit = async () => {
  if (testStatus.value !== 'success') {
    toast({
      title: 'Test connection required',
      description: 'Please test the connection before saving the configuration.',
      variant: 'destructive',
    })
    return
  }

  const response = await fetch('/api/knowledge/storage', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      agentUid: props.agentUid,
      ...form.value,
    }),
  })

  if (response.ok) {
    form.value = {
      accessKeyId: '',
      secretAccessKey: '',
      bucketName: '',
      bucketRegion: '',
      bucketEndpoint: '',
    }
    testStatus.value = 'idle'
    emit('update', await response.json())
    toast({
      title: 'Configuration saved',
      description: 'The storage configuration has been saved successfully',
    })
  }
}

const editConfig = (config: StorageConfig) => {
  form.value = {
    ...form.value,
    bucketName: config.bucketName,
    bucketRegion: config.bucketRegion,
    bucketEndpoint: config.bucketEndpoint || '',
  }
  testStatus.value = 'idle'
}

const handleDelete = async (config: StorageConfig) => {
  const response = await fetch(`/api/knowledge/storage/${config.id}`, {
    method: 'DELETE',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      agentUid: props.agentUid,
    }),
  })

  if (response.ok) {
    storageConfigs.value = storageConfigs.value.filter((c) => c.id !== config.id)
    toast({
      title: 'Configuration deleted',
      description: 'The storage configuration has been removed',
    })
  }
}

const getStatusVariant = (status: string) => {
  switch (status) {
    case 'connected':
      return 'outline'
    case 'error':
      return 'destructive'
    default:
      return 'secondary'
  }
}

const formatDate = (date: string) => {
  return new Date(date).toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  })
}

onMounted(async () => {
  const response = await fetch(`/api/knowledge/storage?agentUid=${props.agentUid}`)
  if (response.ok) {
    storageConfigs.value = await response.json()
  }
})
</script>
