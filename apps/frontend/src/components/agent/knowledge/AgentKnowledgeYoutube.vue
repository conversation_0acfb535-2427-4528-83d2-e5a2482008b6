<template>
  <div class="space-y-6">
    <!-- Add YouTube Video Form -->
    <BaseCard>
      <form @submit.prevent="handleSubmit" class="space-y-4">
        <div class="space-y-2">
          <Label>YouTube URL</Label>
          <div class="relative">
            <Input
              v-model="form.url"
              placeholder="https://www.youtube.com/watch?v=..."
              type="url"
              required
            />
            <Button
              v-if="form.url"
              type="button"
              variant="ghost"
              size="icon"
              class="absolute -translate-y-1/2 right-2 top-1/2"
              @click="form.url = ''"
            >
              <X class="w-4 h-4" />
            </Button>
          </div>
          <p class="text-xs text-muted-foreground">
            Supports YouTube video URLs, playlist URLs, and channel URLs
          </p>
        </div>

        <Button type="submit" class="w-full" :disabled="!isValidYouTubeUrl">
          Add YouTube Content
        </Button>
      </form>
    </BaseCard>

    <!-- YouTube Content List -->
    <KnowledgeList
      title="YouTube Content"
      description="Manage your YouTube video knowledge base."
      :items="youtubeContent"
      titleField="title"
      searchPlaceholder="Search videos..."
      contentEndpoint="/api/knowledge/youtube"
      @delete="handleDelete"
    >
      <template #item="{ item }">
        <div class="flex items-center gap-3">
          <h3 class="text-sm font-medium truncate">{{ item.title }}</h3>
          <Badge :variant="getStatusVariant(item.status)" class="flex-shrink-0 px-3">
            {{ item.status }}
          </Badge>
        </div>
        <div class="flex items-center gap-2 mt-0.5 text-xs text-muted-foreground">
          <span>{{ formatDate(item.lastUpdated) }}</span>
          <span class="w-1 h-1 rounded-full bg-muted-foreground/50" />
          <span class="truncate">{{ item.url }}</span>
          <span class="w-1 h-1 rounded-full bg-muted-foreground/50" />
          <span>{{ item.duration }}</span>
          <span class="w-1 h-1 rounded-full bg-muted-foreground/50" />
          <span>{{ item.channel }}</span>
        </div>
      </template>

      <template #actions="{ item }">
        <Button size="icon" variant="ghost" class="w-8 h-8" @click="handleDelete(item)">
          <Trash2 class="w-4 h-4" />
        </Button>
      </template>
    </KnowledgeList>
  </div>
</template>

<script setup lang="ts">
const props = defineProps<{
  agentUid: string
}>()
const emit = defineEmits(['update'])

interface YouTubeContent {
  id: string
  title: string
  url: string
  thumbnail: string
  duration: string
  channel: string
  views: number
  publishedAt: string
  status: 'completed' | 'processing' | 'error'
  lastUpdated: string
  refreshSchedule: 'never' | 'weekly' | 'monthly'
}

const form = ref({
  url: '',
})

const { toast } = useToast()

const isValidYouTubeUrl = computed(() => {
  if (!form.value.url) return false
  const url = form.value.url.toLowerCase()
  return (
    url.includes('youtube.com/watch?v=') ||
    url.includes('youtu.be/') ||
    url.includes('youtube.com/playlist?list=') ||
    url.includes('youtube.com/channel/') ||
    url.includes('youtube.com/c/')
  )
})

const youtubeContent = ref<YouTubeContent[]>(
  Array.from({ length: 30 }, (_, i) => {
    const samples = [
      {
        title:
          'Getting Started with Our Platform • youtube.com/watch?v=abc123 • 10:15 • Product Tutorials',
        url: 'https://youtube.com/watch?v=abc123',
        thumbnail: 'https://i.ytimg.com/vi/abc123/maxresdefault.jpg',
        duration: '10:15',
        channel: 'Product Tutorials',
        views: 15000,
        publishedAt: '2023-12-15T10:00:00Z',
      },
      {
        title:
          'Advanced Features Tutorial • youtube.com/watch?v=def456 • 15:30 • Product Tutorials',
        url: 'https://youtube.com/watch?v=def456',
        thumbnail: 'https://i.ytimg.com/vi/def456/maxresdefault.jpg',
        duration: '15:30',
        channel: 'Product Tutorials',
        views: 8500,
        publishedAt: '2023-12-20T14:30:00Z',
      },
      {
        title: 'API Integration Guide • youtube.com/watch?v=ghi789 • 20:45 • Developer Guides',
        url: 'https://youtube.com/watch?v=ghi789',
        thumbnail: 'https://i.ytimg.com/vi/ghi789/maxresdefault.jpg',
        duration: '20:45',
        channel: 'Developer Guides',
        views: 12000,
        publishedAt: '2023-12-25T09:15:00Z',
      },
      {
        title: 'Security Best Practices • youtube.com/watch?v=jkl012 • 25:00 • Security Updates',
        url: 'https://youtube.com/watch?v=jkl012',
        thumbnail: 'https://i.ytimg.com/vi/jkl012/maxresdefault.jpg',
        duration: '25:00',
        channel: 'Security Updates',
        views: 9500,
        publishedAt: '2023-12-30T16:45:00Z',
      },
      {
        title: 'Product Demo 2024 • youtube.com/watch?v=mno345 • 30:20 • Product Updates',
        url: 'https://youtube.com/watch?v=mno345',
        thumbnail: 'https://i.ytimg.com/vi/mno345/maxresdefault.jpg',
        duration: '30:20',
        channel: 'Product Updates',
        views: 25000,
        publishedAt: '2024-01-05T11:30:00Z',
      },
    ]

    const selected = samples[i % samples.length]
    return {
      id: `${i + 1}`,
      title:
        i < samples.length
          ? `${selected.title}`
          : `Sample Video ${i + 1} • youtube.com/watch?v=sample${i} • 10:00 • Sample Channel`,
      url: selected.url,
      thumbnail: selected.thumbnail,
      duration: selected.duration,
      channel: selected.channel,
      views: selected.views,
      publishedAt: selected.publishedAt,
      status: ['completed', 'processing', 'error'][
        Math.floor(Math.random() * 3)
      ] as YouTubeContent['status'],
      lastUpdated: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
      refreshSchedule: ['never', 'weekly', 'monthly'][
        Math.floor(Math.random() * 3)
      ] as YouTubeContent['refreshSchedule'],
    }
  })
)

const handleSubmit = async () => {
  if (!isValidYouTubeUrl.value) return

  const response = await fetch('/api/knowledge/youtube', {
    method: 'POST',
    body: JSON.stringify({
      agentUid: props.agentUid,
      url: form.value.url,
    }),
  })

  if (response.ok) {
    form.value.url = ''
    emit('update', await response.json())
    toast({
      title: 'YouTube content added',
      description: 'The YouTube content has been added to your knowledge base',
    })
  }
}

const handleDelete = async (content: YouTubeContent) => {
  const response = await fetch(`/api/knowledge/youtube/${content.id}`, {
    method: 'DELETE',
    body: JSON.stringify({
      agentUid: props.agentUid,
    }),
  })

  if (response.ok) {
    youtubeContent.value = youtubeContent.value.filter((c) => c.id !== content.id)
    toast({
      title: 'YouTube content deleted',
      description: 'The YouTube content has been removed from your knowledge base',
    })
  }
}

// Load initial YouTube content
onMounted(async () => {
  const response = await fetch(`/api/knowledge/youtube?agentUid=${props.agentUid}`)
  if (response.ok) {
    youtubeContent.value = await response.json()
  }
})

const getStatusVariant = (status: string) => {
  switch (status) {
    case 'completed':
      return 'outline'
    case 'processing':
      return 'secondary'
    case 'error':
      return 'destructive'
    default:
      return 'secondary'
  }
}

const formatDate = (date: string) => {
  return new Date(date).toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  })
}
</script>
