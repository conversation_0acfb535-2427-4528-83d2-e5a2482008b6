<template>
  <BaseCard>
    <form @submit.prevent="handleSubmit" class="space-y-4">
      <div class="space-y-2">
        <Label>Question</Label>
        <Input v-model="form.question" placeholder="What is your return policy?" required />
      </div>
      <div class="space-y-2">
        <Label>Answer</Label>
        <div class="relative">
          <BaseTextarea
            v-model="form.answer"
            placeholder="Our return policy allows returns within 30 days of purchase..."
            :maxlength="10000"
            required
          />
        </div>
      </div>

      <Button type="submit" class="w-full" :disabled="form.answer.length > 10000"> Add Q&A </Button>
    </form>
  </BaseCard>

  <KnowledgeList
    title="Q&A Pairs"
    description="Manage your question and answer pairs."
    :items="qaPairs"
    titleField="question"
    contentEndpoint="/api/knowledge/qa"
    searchPlaceholder="Search Q&A pairs..."
    :showContentButton="true"
    :showEditButton="true"
    :showDeleteButton="true"
    :showDownloadButton="false"
  >
    <template #item="{ item }">
      <div class="flex items-center gap-3">
        <h3 class="text-sm font-medium truncate">{{ item.question }}</h3>
        <Badge :variant="getStatusVariant(item.status)" class="flex-shrink-0 px-3">
          {{ item.status }}
        </Badge>
      </div>
      <div class="flex items-center gap-2 mt-0.5 text-xs text-muted-foreground">
        <span>{{ formatDate(item.lastUpdated) }}</span>
        <span class="w-1 h-1 rounded-full bg-muted-foreground/50" />
        <span class="truncate"
          >{{ item.answer.slice(0, 100) }}{{ item.answer.length > 100 ? '...' : '' }}</span
        >
      </div>
    </template>
  </KnowledgeList>
</template>

<script setup lang="ts">
const props = defineProps<{
  agentUid: string
}>()
const emit = defineEmits(['update'])

interface QAPair {
  id: string
  question: string
  answer: string
  status: 'completed' | 'processing' | 'error'
  lastUpdated: string
}

const form = ref({
  question: '',
  answer: '',
})

const { toast } = useToast()

const qaPairs = ref<QAPair[]>(
  Array.from({ length: 30 }, (_, i) => {
    const faqs = [
      {
        question: 'What are your business hours?',
        answer:
          "Our business hours are Monday to Friday, 9:00 AM to 6:00 PM (EST). We're closed on weekends and major holidays. For urgent matters outside these hours, <NAME_EMAIL>.",
      },
    ]

    const selected = faqs[i % faqs.length]
    return {
      id: `${i + 1}`,
      question: i < faqs.length ? selected.question : `Common Question ${i + 1}?`,
      answer:
        i < faqs.length
          ? selected.answer
          : `Detailed answer to common question ${i + 1}. This includes important information and specific details that users might need to know.`,
      status: ['completed', 'processing', 'error'][
        Math.floor(Math.random() * 3)
      ] as QAPair['status'],
      lastUpdated: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
    }
  })
)

const handleSubmit = async () => {
  if (form.value.answer.length > 10000) return

  const response = await fetch('/api/knowledge/qa', {
    method: 'POST',
    body: JSON.stringify({
      agentUid: props.agentUid,
      ...form.value,
    }),
  })

  if (response.ok) {
    form.value.question = ''
    form.value.answer = ''
    emit('update', await response.json())
    toast({
      title: 'Q&A pair added',
      description: 'The Q&A pair has been added to your knowledge base',
    })
  }
}

const getStatusVariant = (status: string) => {
  switch (status) {
    case 'completed':
      return 'outline'
    case 'processing':
      return 'secondary'
    case 'error':
      return 'destructive'
    default:
      return 'secondary'
  }
}

const formatDate = (date: string) => {
  return new Date(date).toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  })
}

onMounted(async () => {
  const response = await fetch(`/api/knowledge/qa?agentUid=${props.agentUid}`)
  if (response.ok) {
    qaPairs.value = await response.json()
  }
})
</script>
