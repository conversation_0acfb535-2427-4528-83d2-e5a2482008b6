<template>
  <BaseCard>
    <BaseDropzone
      accept=".pdf,.docx,.txt"
      acceptText="PDF, DOCX, TXT (MAX. 10MB)"
      :maxSize="10 * 1024 * 1024"
      @upload="handleUpload"
    >
      <template #file-icon="{ file }">
        <FileText v-if="file.name.endsWith('.txt')" class="w-3.5 h-3.5" />
        <FileType v-else-if="file.name.endsWith('.docx')" class="w-3.5 h-3.5" />
        <FilePdf v-else-if="file.name.endsWith('.pdf')" class="w-3.5 h-3.5" />
        <FileText v-else class="w-3.5 h-3.5" />
      </template>
    </BaseDropzone>
  </BaseCard>

  <KnowledgeList
    title="Documents"
    description="Manage your document knowledge base."
    :items="documents"
    titleField="name"
    contentEndpoint="/api/knowledge/documents"
    searchPlaceholder="Search documents..."
    :showContentButton="true"
    :showRefreshToggle="true"
    :showRefreshButton="true"
    :showDownloadButton="false"
  >
    <template #item="{ item }">
      <div class="flex items-center gap-3">
        <h3 class="text-sm font-medium truncate">{{ item.name }}</h3>
        <Badge :variant="getStatusVariant(item.status)" class="flex-shrink-0 px-3">
          {{ item.status }}
        </Badge>
      </div>
      <div class="flex items-center gap-2 mt-0.5 text-xs text-muted-foreground">
        <span>{{ formatDate(item.createdAt) }}</span>
        <span class="w-1 h-1 rounded-full bg-muted-foreground/50" />
        <span>{{ formatFileSize(item.size) }}</span>
        <span class="w-1 h-1 rounded-full bg-muted-foreground/50" />
        <span>{{ item.type.toUpperCase() }}</span>
      </div>
    </template>
  </KnowledgeList>
</template>

<script setup lang="ts">
const props = defineProps<{
  agentUid: string
}>()
const emit = defineEmits(['update'])

interface Document {
  id: string
  name: string
  size: number
  type: string
  status: 'uploading' | 'processing' | 'processed' | 'error'
  createdAt: string
}

const documents = ref<Document[]>([])
const { toast } = useToast()

const handleUpload = async (files: File[]) => {
  for (const file of files) {
    const formData = new FormData()
    formData.append('agentUid', props.agentUid)
    formData.append('file', file)

    try {
      const response = await fetch('/api/knowledge/documents', {
        method: 'POST',
        body: formData,
      })

      if (response.ok) {
        emit('update', await response.json())
        toast({
          title: 'File uploaded',
          description: `${file.name} has been uploaded successfully`,
        })
      }
    } catch (error) {
      console.error('Error uploading file:', error)
      toast({
        title: 'Upload failed',
        description: `Failed to upload ${file.name}. Please try again.`,
        variant: 'destructive',
      })
    }
  }
}

const getStatusVariant = (status: string) => {
  switch (status) {
    case 'completed':
      return 'outline'
    case 'processing':
      return 'secondary'
    case 'error':
      return 'destructive'
    default:
      return 'secondary'
  }
}

const formatDate = (date: string) => {
  return new Date(date).toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  })
}

const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// Load initial documents
onMounted(async () => {
  const response = await fetch(`/api/knowledge/documents?agentUid=${props.agentUid}`)
  if (response.ok) {
    documents.value = await response.json()
  }
})
</script>
