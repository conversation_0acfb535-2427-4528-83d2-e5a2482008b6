<!-- Content Preview Dialog -->
<template>
  <Dialog :open="isOpen" @update:open="$emit('update:isOpen', false)">
    <DialogContent class="max-w-3xl">
      <DialogHeader>
        <DialogTitle>Content Preview</DialogTitle>
        <DialogDescription>{{ title }}</DialogDescription>
      </DialogHeader>
      <div class="mt-4 border rounded-lg p-4 max-h-[60vh] overflow-auto">
        <div v-if="isLoading" class="flex items-center justify-center py-8">
          <Loader2 class="w-6 h-6 animate-spin" />
        </div>
        <pre v-else class="text-sm whitespace-pre-wrap">{{ content }}</pre>
      </div>
      <DialogFooter>
        <Button variant="outline" @click="$emit('update:isOpen', false)">Close</Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>
</template>

<script setup lang="ts">
const props = defineProps<{
  isOpen: boolean
  title: string
  itemId: string
}>()
const emit = defineEmits(['update:isOpen'])

const { toast } = useToast()
const content = ref<string>('')
const isLoading = ref(false)

watch(
  () => props.isOpen,
  async (newValue) => {
    if (newValue && props.itemId) {
      isLoading.value = true
      try {
        const response = await fetch(`${''}/${props.itemId}/content`)
        if (response.ok) {
          content.value = await response.text()
        } else {
          throw new Error('Failed to load content')
        }
      } catch (error) {
        toast({
          title: 'Error',
          description: 'Failed to load content. Please try again.',
          variant: 'destructive',
        })
        emit('update:isOpen', false)
      } finally {
        isLoading.value = false
      }
    } else {
      content.value = ''
    }
  }
)
</script>
