<template>
  <BaseCard title="Knowledge Base" description="Manage your knowledge base">
    <CardHeader class="pb-2">
      <div class="flex items-center justify-between">
        <div>
          <CardTitle>{{ title }}</CardTitle>
          <CardDescription>{{ description }}</CardDescription>
        </div>
        <div class="flex items-center gap-2">
          <Select v-if="showStatusFilter" v-model="selectedStatus" class="w-[130px]">
            <SelectTrigger>
              <SelectValue :placeholder="statusPlaceholder" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Items</SelectItem>
              <SelectItem value="trained">Trained</SelectItem>
              <SelectItem value="training">Training</SelectItem>
              <SelectItem value="error">Error</SelectItem>
            </SelectContent>
          </Select>
          <Input v-model="searchQuery" :placeholder="searchPlaceholder" class="w-[200px]" />
        </div>
      </div>
    </CardHeader>
    <CardContent class="p-0">
      <RecycleScroller
        class="h-[250px]"
        :items="filteredItems"
        :item-size="50"
        key-field="id"
        v-slot="{ item }"
      >
        <div class="px-4 py-3 border-b last:border-0 hover:bg-muted/50">
          <!-- Item Row -->
          <div class="flex items-center justify-between gap-4">
            <div class="flex-1 min-w-0">
              <slot name="item" :item="item">
                <!-- Default item display -->
                <div class="flex items-center gap-3">
                  <h3 class="text-sm font-medium truncate">
                    {{
                      item[titleField].length > truncateLength
                        ? item[titleField].slice(0, truncateLength) + '...'
                        : item[titleField]
                    }}
                  </h3>
                  <Badge :variant="getStatusVariant(item.status)" class="flex-shrink-0 px-3">
                    {{ item.status }}
                  </Badge>
                </div>
                <div class="flex items-center gap-2 mt-0.5 text-xs text-muted-foreground">
                  <slot name="item-meta" :item="item">
                    <span>{{ formatDate(item.lastUpdated || item.lastCrawled) }}</span>
                    <span
                      v-if="item.refreshSchedule || item.size"
                      class="w-1 h-1 rounded-full bg-muted-foreground/50"
                    />
                    <span v-if="item.refreshSchedule">{{
                      item.refreshSchedule === 'never'
                        ? 'Manual refresh'
                        : `Refreshes ${item.refreshSchedule}`
                    }}</span>
                    <span v-if="item.size">{{ formatFileSize(item.size) }}</span>
                  </slot>
                </div>
              </slot>
            </div>
            <div class="flex items-center gap-2">
              <slot name="actions" :item="item">
                <!-- Default actions -->
                <Button
                  v-if="showContentButton"
                  size="icon"
                  variant="ghost"
                  class="w-8 h-8"
                  @click="handleViewContent(item)"
                  title="View content"
                >
                  <Eye class="w-4 h-4" />
                </Button>
                <Button
                  v-if="showRefreshToggle"
                  size="icon"
                  :variant="item.refreshSchedule !== 'never' ? 'default' : 'ghost'"
                  class="w-8 h-8"
                  @click="handleToggleRefresh(item)"
                  :title="
                    item.refreshSchedule === 'never'
                      ? 'Enable auto-refresh'
                      : 'Disable auto-refresh'
                  "
                >
                  <BaseIcon
                    :name="item.refreshSchedule === 'never' ? 'BellOff' : 'BellRing'"
                  />
                </Button>
                <Button
                  v-if="showRefreshButton"
                  size="icon"
                  variant="ghost"
                  class="w-8 h-8"
                  @click="handleRefresh(item)"
                  :disabled="['crawling', 'processing'].includes(item.status)"
                  :title="
                    ['crawling', 'processing'].includes(item.status)
                      ? 'Training in progress'
                      : 'Retrain'
                  "
                >
                  <RotateCw
                    class="w-4 h-4"
                    :class="{ 'animate-spin': ['crawling', 'processing'].includes(item.status) }"
                  />
                </Button>
                <Button
                  v-if="showEditButton"
                  size="icon"
                  variant="ghost"
                  class="w-8 h-8"
                  @click="handleEdit(item)"
                  title="Edit"
                >
                  <BaseIcon name="Pencil" />
                </Button>
                <Button
                  v-if="showDownloadButton"
                  size="icon"
                  variant="ghost"
                  class="w-8 h-8"
                  @click="handleDownload(item)"
                  title="Download"
                >
                  <BaseIcon name="Download" />
                </Button>
                <Button
                  size="icon"
                  variant="ghost"
                  class="w-8 h-8"
                  @click="confirmDelete(item)"
                  title="Delete"
                >
                  <BaseIcon name="Trash2" />
                </Button>
              </slot>
            </div>
          </div>
        </div>
      </RecycleScroller>
    </CardContent>
  </BaseCard>

  <ContentPreviewDialog
    v-model:isOpen="contentDialogOpen"
    :title="selectedItem?.[titleField]"
    :itemId="selectedItem?.id"
  />

  <!-- Delete Confirmation Dialog -->
  <Dialog :open="!!itemToDelete" @update:open="closeDeleteDialog">
    <DialogContent>
      <DialogHeader>
        <DialogTitle>Delete Item</DialogTitle>
        <DialogDescription>
          Are you sure you want to delete this item? This action cannot be undone.
        </DialogDescription>
      </DialogHeader>
      <div class="p-3 mt-4 text-sm border rounded-lg bg-muted">
        {{ itemToDelete?.[titleField] }}
      </div>
      <DialogFooter class="mt-4">
        <Button variant="outline" @click="closeDeleteDialog">Cancel</Button>
        <Button variant="destructive" @click="handleDelete">Delete</Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>
</template>

<script setup lang="ts">
import { RecycleScroller } from 'vue-virtual-scroller'
import 'vue-virtual-scroller/dist/vue-virtual-scroller.css'

const props = withDefaults(
  defineProps<{
    title: string
    description: string
    items: any[]
    titleField: string
    statusPlaceholder?: string
    searchPlaceholder?: string
    showStatusFilter?: boolean
    showContentButton?: boolean
    showRefreshToggle?: boolean
    showRefreshButton?: boolean
    showEditButton?: boolean
    showDownloadButton?: boolean
    truncateLength?: number
  }>(),
  {
    statusPlaceholder: 'Filter by status',
    searchPlaceholder: 'Search...',
    showStatusFilter: true,
    showContentButton: true,
    showRefreshToggle: false,
    showRefreshButton: false,
    showEditButton: false,
    showDownloadButton: false,
    truncateLength: 120,
  }
)

const emit = defineEmits<{
  (e: 'toggle-refresh', item: any): void
  (e: 'refresh', item: any): void
  (e: 'edit', item: any): void
  (e: 'download', item: any): void
  (e: 'delete', item: any): void
}>()

const searchQuery = ref('')
const selectedStatus = ref('all')
const contentDialogOpen = ref(false)
const selectedItem = ref<any>(null)
const itemToDelete = ref<any>(null)

const handleViewContent = (item: any) => {
  selectedItem.value = item
  contentDialogOpen.value = true
}

const handleToggleRefresh = (item: any) => {
  emit('toggle-refresh', item)
}

const handleRefresh = (item: any) => {
  emit('refresh', item)
}

const handleEdit = (item: any) => {
  emit('edit', item)
}

const handleDownload = (item: any) => {
  emit('download', item)
}

const closeDeleteDialog = () => {
  itemToDelete.value = null
}

const confirmDelete = (item: any) => {
  itemToDelete.value = item
}

const handleDelete = () => {
  emit('delete', itemToDelete.value)
  closeDeleteDialog()
}

const filteredItems = computed(() => {
  let filtered = props.items

  if (selectedStatus.value !== 'all') {
    filtered = filtered.filter((item) => {
      switch (selectedStatus.value) {
        case 'trained':
          return item.status === 'completed'
        case 'training':
          return ['crawling', 'processing'].includes(item.status)
        case 'error':
          return item.status === 'error'
        default:
          return true
      }
    })
  }

  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter((item) => item[props.titleField].toLowerCase().includes(query))
  }

  return filtered
})

const getStatusVariant = (status: string) => {
  switch (status) {
    case 'completed':
      return 'outline'
    case 'crawling':
    case 'processing':
      return 'secondary'
    case 'error':
      return 'destructive'
    default:
      return 'secondary'
  }
}

const formatDate = (date: string) => {
  return new Date(date).toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  })
}

const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}
</script>

<style scoped>
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}
</style>
