<template>
  <div class="space-y-6">
    <!-- Add Text Form -->
    <BaseCard>
      <form @submit.prevent="handleSubmit" class="space-y-4">
        <div class="space-y-2">
          <Label>Title</Label>
          <Input v-model="form.title" placeholder="Company Overview" required />
        </div>
        <div class="space-y-2">
          <Label>Content</Label>
          <BaseTextarea
            v-model="form.content"
            placeholder="Enter your text content here..."
            :maxlength="10000"
            required
          />
        </div>

        <Button type="submit" class="w-full" :disabled="form.content.length > 10000">
          Add Text
        </Button>
      </form>
    </BaseCard>

    <!-- Texts List -->
    <KnowledgeList
      title="Added Texts"
      description="Manage your text knowledge base."
      :items="texts"
      titleField="title"
      contentEndpoint="/api/knowledge/texts"
      searchPlaceholder="Search texts..."
      :showContentButton="true"
      :showEditButton="true"
      @edit="handleEdit"
      @delete="handleDelete"
    >
      <template #item="{ item }">
        <div class="flex items-center gap-3">
          <h3 class="text-sm font-medium truncate">{{ item.title }}</h3>
          <Badge :variant="getStatusVariant(item.status)" class="flex-shrink-0 px-3">
            {{ item.status }}
          </Badge>
        </div>
        <div class="flex items-center gap-2 mt-0.5 text-xs text-muted-foreground">
          <span>{{ formatDate(item.lastUpdated) }}</span>
          <span class="w-1 h-1 rounded-full bg-muted-foreground/50" />
          <span>{{ formatFileSize(item.size) }}</span>
          <span v-if="item.source" class="w-1 h-1 rounded-full bg-muted-foreground/50" />
          <span v-if="item.source" class="truncate">{{ item.source }}</span>
        </div>
      </template>
    </KnowledgeList>
  </div>
</template>

<script setup lang="ts">
const props = defineProps<{
  agentUid: string
}>()
const emit = defineEmits(['update'])

interface Text {
  id: string
  title: string
  content: string
  status: 'completed' | 'processing' | 'error'
  lastUpdated: string
  size: number
  source?: string
}

const form = ref({
  title: '',
  content: '',
})

const { toast } = useToast()

const texts = ref<Text[]>(
  Array.from({ length: 30 }, (_, i) => {
    const samples = [
      {
        title: 'Company Overview',
        content:
          'Founded in 2020, our company is a leading provider of AI-powered solutions. We specialize in developing cutting-edge technology that helps businesses automate their processes and improve customer engagement. With offices in major tech hubs and a team of over 100 experts, we serve clients worldwide.',
      },
      {
        title: 'Privacy Policy',
        content:
          'We take your privacy seriously. All data collected through our platform is encrypted and stored securely. We never share your personal information with third parties without your explicit consent. Our security team regularly audits our systems to ensure the highest level of data protection.',
      },
      {
        title: 'Terms of Service',
        content:
          'By using our services, you agree to these terms. Our platform is provided "as is" without any warranties. We reserve the right to modify or discontinue any feature with reasonable notice. Users must be at least 18 years old and comply with all applicable laws.',
      },
      {
        title: 'Product Features',
        content:
          'Our platform offers real-time analytics, customizable dashboards, and automated reporting. Key features include AI-powered insights, seamless integration with popular tools, and 24/7 monitoring. All plans include unlimited API calls and dedicated support.',
      },
      {
        title: 'Mission Statement',
        content:
          'Our mission is to democratize AI technology and make it accessible to businesses of all sizes. We believe in innovation, transparency, and customer success. Through continuous improvement and feedback, we strive to create solutions that make a real impact.',
      },
    ]

    const selected = samples[i % samples.length]
    return {
      id: `${i + 1}`,
      title: i < samples.length ? selected.title : `Sample Text ${i + 1}`,
      content:
        i < samples.length
          ? selected.content
          : `This is sample text content ${i + 1}. It includes detailed information about various topics and can be used for testing purposes.`,
      status: ['completed', 'processing', 'error'][Math.floor(Math.random() * 3)] as Text['status'],
      lastUpdated: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
      size: Math.floor(Math.random() * 1000000),
      source: i < samples.length ? undefined : `Source ${i + 1}`,
    }
  })
)

const contentDialogOpen = ref(false)
const editDialogOpen = ref(false)
const selectedItem = ref<Text | null>(null)

const handleSubmit = async () => {
  if (form.value.content.length > 10000) return

  const response = await fetch('/api/knowledge/texts', {
    method: 'POST',
    body: JSON.stringify({
      agentUid: props.agentUid,
      ...form.value,
    }),
  })

  if (response.ok) {
    form.value.title = ''
    form.value.content = ''
    emit('update', await response.json())
    toast({
      title: 'Text added',
      description: 'The text has been added to your knowledge base',
    })
  }
}

const handleDelete = async (text: Text) => {
  const response = await fetch(`/api/knowledge/texts/${text.id}`, {
    method: 'DELETE',
    body: JSON.stringify({
      agentUid: props.agentUid,
    }),
  })

  if (response.ok) {
    texts.value = texts.value.filter((t) => t.id !== text.id)
    toast({
      title: 'Text deleted',
      description: 'The text has been removed from your knowledge base',
    })
  }
}

const getStatusVariant = (status: string) => {
  switch (status) {
    case 'completed':
      return 'outline'
    case 'processing':
      return 'secondary'
    case 'error':
      return 'destructive'
    default:
      return 'secondary'
  }
}

const formatDate = (date: string) => {
  return new Date(date).toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  })
}

const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const handleViewContent = (item: Text) => {
  selectedItem.value = item
  contentDialogOpen.value = true
}

const handleEdit = (item: Text) => {
  selectedItem.value = item
  editDialogOpen.value = true
}

onMounted(async () => {
  const response = await fetch(`/api/knowledge/texts?agentUid=${props.agentUid}`)
  if (response.ok) {
    texts.value = await response.json()
  }
})
</script>
