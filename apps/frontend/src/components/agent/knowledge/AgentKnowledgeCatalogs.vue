<template>
  <div class="space-y-6">
    <!-- Add Product Form -->
    <BaseCard>
      <form @submit.prevent="handleSubmit" class="space-y-4">
        <!-- Basic Info -->
        <div class="grid grid-cols-3 gap-3">
          <div class="col-span-2 space-y-1.5">
            <Label>Name</Label>
            <Input v-model="form.name" placeholder="Product name" required />
          </div>
          <div class="space-y-1.5">
            <Label>SKU/ID</Label>
            <Input v-model="form.sku" placeholder="Product SKU" required />
          </div>
        </div>

        <div class="grid grid-cols-3 gap-3">
          <div class="space-y-1.5">
            <Label>Category</Label>
            <Input v-model="form.category" placeholder="Category" required />
          </div>
          <div class="space-y-1.5">
            <Label>Price</Label>
            <Input v-model="form.price" type="number" step="0.01" placeholder="0.00" required />
          </div>
          <div class="space-y-1.5">
            <Label>URL</Label>
            <Input v-model="form.url" type="url" placeholder="Product URL" required />
          </div>
        </div>

        <div class="grid grid-cols-3 gap-3">
          <div class="space-y-1.5">
            <Label>Sizes</Label>
            <Input v-model="form.sizes" placeholder="S, M, L, XL" />
          </div>
          <div class="space-y-1.5">
            <Label>Colors</Label>
            <Input v-model="form.colors" placeholder="Red, Blue, Green" />
          </div>
          <div class="space-y-1.5">
            <Label>Tags</Label>
            <Input v-model="form.tags" placeholder="summer, casual, new" />
          </div>
        </div>

        <div class="space-y-1.5">
          <Label>Description</Label>
          <Textarea
            v-model="form.description"
            placeholder="Product description..."
            :rows="3"
            required
          />
        </div>

        <!-- Images -->
        <div class="space-y-1.5">
          <Label>Images</Label>
          <BaseDropzone
            accept=".jpg,.jpeg,.png,.gif,.webp"
            acceptText="Images (JPG, PNG, GIF, WebP - MAX. 10MB)"
            :maxSize="10 * 1024 * 1024"
            :maxFiles="4"
            @files-change="handleImagesChange"
            @upload="handleImagesUpload"
          >
            <template #file-icon="{ file }">
              <img
                :src="createObjectUrl(file)"
                class="w-3.5 h-3.5 object-cover rounded"
                @load="
                  (e) => {
                    const target = e.target as HTMLImageElement
                    if (target?.src) revokeObjectUrl(target.src)
                  }
                "
              />
            </template>
          </BaseDropzone>
        </div>

        <Button type="submit" class="w-full">Add Product</Button>
      </form>
    </BaseCard>

    <!-- Products List -->
    <KnowledgeList
      title="Products"
      description="Manage your product catalog."
      :items="products"
      titleField="name"
      searchPlaceholder="Search products..."
      contentEndpoint="/api/knowledge/catalogs"
      @delete="handleDelete"
    >
      <template #actions="{ item }">
        <Button size="icon" variant="ghost" class="w-8 h-8" @click="editProduct(item)">
          <Pencil class="w-4 h-4" />
        </Button>
        <Button size="icon" variant="ghost" class="w-8 h-8" @click="handleDelete(item)">
          <Trash2 class="w-4 h-4" />
        </Button>
      </template>
    </KnowledgeList>
  </div>
</template>

<script setup lang="ts">
const props = defineProps<{
  agentUid: string
}>()
const emit = defineEmits(['update'])

interface Product {
  id: string
  name: string
  category: string
  sku: string
  price: number
  url: string
  sizes: string[]
  colors: string[]
  tags: string[]
  description: string
  images: string[]
  status: 'completed' | 'processing' | 'error'
  lastUpdated: string
}

const form = ref({
  name: '',
  category: '',
  sku: '',
  price: 0,
  url: '',
  sizes: '',
  colors: '',
  tags: '',
  description: '',
  images: [] as File[],
})

const products = ref<Product[]>([])

const { toast } = useToast()

const createObjectUrl = (file: File) => URL.createObjectURL(file)
const revokeObjectUrl = (url: string) => URL.revokeObjectURL(url)

const handleImagesChange = (files: File[]) => {
  form.value.images = files
}

const handleImagesUpload = (files: File[]) => {
  form.value.images = files
}

const handleSubmit = async () => {
  const formData = new FormData()
  formData.append('agentUid', props.agentUid)
  formData.append('name', form.value.name)
  formData.append('category', form.value.category)
  formData.append('sku', form.value.sku)
  formData.append('price', form.value.price.toString())
  formData.append('url', form.value.url)
  formData.append(
    'sizes',
    form.value.sizes
      .split(',')
      .map((s) => s.trim())
      .join(',')
  )
  formData.append(
    'colors',
    form.value.colors
      .split(',')
      .map((c) => c.trim())
      .join(',')
  )
  formData.append(
    'tags',
    form.value.tags
      .split(',')
      .map((t) => t.trim())
      .join(',')
  )
  formData.append('description', form.value.description)

  form.value.images.forEach((image, index) => {
    formData.append(`image_${index}`, image)
  })

  const response = await fetch('/api/knowledge/catalogs', {
    method: 'POST',
    body: formData,
  })

  if (response.ok) {
    form.value = {
      name: '',
      category: '',
      sku: '',
      price: 0,
      url: '',
      sizes: '',
      colors: '',
      tags: '',
      description: '',
      images: [],
    }
    emit('update', await response.json())
    toast({
      title: 'Product added',
      description: 'The product has been added to your catalog',
    })
  }
}

const editProduct = async (product: Product) => {
  console.log('Edit product:', product)
}

const handleDelete = async (product: Product) => {
  const response = await fetch(`/api/knowledge/catalogs/${product.id}`, {
    method: 'DELETE',
    body: JSON.stringify({
      agentUid: props.agentUid,
    }),
  })

  if (response.ok) {
    products.value = products.value.filter((p) => p.id !== product.id)
    toast({
      title: 'Product deleted',
      description: 'The product has been removed from your catalog',
    })
  }
}

onMounted(async () => {
  const response = await fetch(`/api/knowledge/catalogs?agentUid=${props.agentUid}`)
  if (response.ok) {
    products.value = await response.json()
  }
})
</script>
