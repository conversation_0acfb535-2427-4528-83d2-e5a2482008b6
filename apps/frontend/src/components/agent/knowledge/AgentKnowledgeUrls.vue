<template>
  <BaseCard>
    <form @submit.prevent="handleSubmit" class="space-y-4">
      <div class="space-y-2">
        <Label>URL</Label>
        <Input v-model="urlInput.url" placeholder="https://example.com/page" type="url" required />
      </div>
      <div class="flex items-center space-x-2">
        <Checkbox id="crawlWebsite" v-model="urlInput.crawlWebsite" :checked="true" />
        <Label for="crawlWebsite" class="text-sm font-normal">
          Crawl entire website (recommended)
        </Label>
      </div>

      <Button type="submit" class="w-full">Add URL</Button>
    </form>
  </BaseCard>

  <KnowledgeList
    title="Added URLs"
    description="Manage your existing URLs."
    :items="urls"
    titleField="url"
    contentEndpoint="/api/knowledge/urls"
    searchPlaceholder="Search URLs..."
    :showContentButton="true"
    :showRefreshToggle="true"
    :showRefreshButton="true"
    :showDownloadButton="false"
  >
    <template #item="{ item }">
      <div class="flex items-center gap-3">
        <h3 class="text-sm font-medium truncate">{{ item.url }}</h3>
        <Badge :variant="getStatusVariant(item.status)" class="flex-shrink-0 px-3">
          {{ item.status }}
        </Badge>
      </div>
      <div class="flex items-center gap-2 mt-0.5 text-xs text-muted-foreground">
        <span>{{ formatDate(item.lastCrawled) }}</span>
        <span class="w-1 h-1 rounded-full bg-muted-foreground/50" />
        <span>{{
          item.refreshSchedule === 'never' ? 'Manual refresh' : `Refreshes ${item.refreshSchedule}`
        }}</span>
      </div>
    </template>
  </KnowledgeList>
</template>

<script setup lang="ts">
const props = defineProps<{
  agentUid: string
}>()
const emit = defineEmits(['update'])

interface Url {
  id: string
  url: string
  status: 'crawling' | 'processing' | 'completed' | 'error'
  lastCrawled: string
  refreshSchedule: 'never' | 'weekly' | 'monthly'
  content?: string
}

const urlInput = ref({
  url: '',
  crawlWebsite: true,
})

const { toast } = useToast()
const urls = ref<Url[]>(
  Array.from({ length: 1000 }, (_, i) => ({
    id: `${i + 1}`,
    url: `https://example${i + 1}.com/page-${i + 1}`,
    status: ['completed', 'crawling', 'error'][Math.floor(Math.random() * 3)] as Url['status'],
    lastCrawled: new Date(Date.now() - Math.random() * 10 * 24 * 60 * 60 * 1000).toISOString(),
    refreshSchedule: ['never', 'weekly', 'monthly'][
      Math.floor(Math.random() * 3)
    ] as Url['refreshSchedule'],
  }))
)

const handleSubmit = async () => {
  const response = await fetch('/api/knowledge/urls', {
    method: 'POST',
    body: JSON.stringify({
      agentUid: props.agentUid,
      url: urlInput.value.url,
      crawlWebsite: urlInput.value.crawlWebsite,
    }),
  })

  if (response.ok) {
    urlInput.value.url = ''
    emit('update', await response.json())
    toast({
      title: 'URL added',
      description: 'The URL has been added to your knowledge base',
    })
  }
}

const getStatusVariant = (status: string) => {
  switch (status) {
    case 'completed':
      return 'outline'
    case 'crawling':
    case 'processing':
      return 'secondary'
    case 'error':
      return 'destructive'
    default:
      return 'secondary'
  }
}

const formatDate = (date: string) => {
  return new Date(date).toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  })
}

// Load initial URLs
onMounted(async () => {
  const response = await fetch(`/api/knowledge/urls?agentUid=${props.agentUid}`)
  if (response.ok) {
    urls.value = await response.json()
  }
})
</script>
