<template>
  <div class="flex flex-col w-full h-full md:flex-row">
    <!-- Left Sidebar -->
    <div
      class="flex flex-col w-full h-full border-r md:w-80 shrink-0"
      :class="{ 'hidden md:flex': selectedChatId && isMobile }"
    >
      <div class="flex-none p-4 border-b">
        <div class="flex items-center gap-2">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" class="justify-between w-24">
                <span>{{ filterType === 'all' ? 'All' : filterType }}</span>
                <ChevronDown class="w-4 h-4 ml-2" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="start" class="w-48">
              <DropdownMenuItem @click="filterType = 'all'">
                <MessageSquare class="w-4 h-4 mr-2" />
                All
              </DropdownMenuItem>
              <DropdownMenuItem @click="filterType = 'unread'">
                <Circle class="w-4 h-4 mr-2" />
                Unread
                <DropdownMenuShortcut>99+</DropdownMenuShortcut>
              </DropdownMenuItem>
              <DropdownMenuItem @click="filterType = 'unresolved'">
                <ArrowRight class="w-4 h-4 mr-2" />
                Unresolved
              </DropdownMenuItem>
              <DropdownMenuItem @click="filterType = 'mentions'">
                <AtSign class="w-4 h-4 mr-2" />
                Mentions
              </DropdownMenuItem>
              <DropdownMenuItem @click="filterType = 'longest-waiting'">
                <Clock class="w-4 h-4 mr-2" />
                Longest Waiting
              </DropdownMenuItem>
              <DropdownMenuItem @click="filterType = 'resolved'">
                <CheckCircle class="w-4 h-4 mr-2" />
                Resolved
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          <div class="relative flex-1">
            <div class="absolute inset-y-0 flex items-center pointer-events-none left-3">
              <Search class="w-4 h-4 text-muted-foreground" />
            </div>
            <Input v-model="searchQuery" placeholder="Search chats..." class="pl-9 h-9" />
          </div>
        </div>
      </div>

      <div class="flex-1 overflow-y-auto max-h-[calc(100vh-200px)]">
        <div class="p-2">
          <div v-if="isLoadingChats" class="space-y-2">
            <ChatItemSkeleton v-for="n in 5" :key="n" />
          </div>

          <div
            v-else-if="filteredAndSortedChats.length === 0"
            class="p-4 text-center text-muted-foreground"
          >
            No chats found
          </div>

          <div v-else class="space-y-1">
            <button
              v-for="chat in filteredAndSortedChats"
              :key="chat.id"
              class="relative w-full p-3 text-left transition-colors border border-transparent rounded-lg hover:bg-accent/50"
              :class="{
                'bg-accent border-border shadow-sm': selectedChatId === chat.id,
                'before:absolute before:left-0 before:top-1/2 before:-translate-y-1/2 before:w-1 before:h-1.5 before:bg-primary before:rounded-full':
                  !chat.read,
              }"
              @click="selectChat(chat.id)"
            >
              <div class="flex items-center justify-between gap-2">
                <span
                  class="flex-1 text-sm font-medium truncate"
                  :class="{ 'text-primary': !chat.read }"
                >
                  {{ truncateText(chat.firstMessage, 80) }}
                </span>
                <div class="flex items-center gap-1.5 shrink-0">
                  <Star v-if="chat.starred" class="w-4 h-4 text-yellow-500 fill-current" />
                  <CheckCircle v-if="chat.status === 'resolved'" class="w-4 h-4 text-green-500" />
                </div>
              </div>
              <div class="flex items-center gap-2 mt-1.5">
                <span class="text-xs text-muted-foreground">
                  {{ formatDate(chat.updatedAt) }}
                </span>
              </div>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Main Content -->
    <div
      class="flex flex-col flex-1 h-full min-w-0"
      :class="{ 'hidden md:flex': !selectedChatId && isMobile }"
    >
      <div v-if="isLoadingChat" class="flex items-center justify-center flex-1">
        <Loader2 class="w-8 h-8 animate-spin" />
      </div>
      <div v-else-if="selectedChat && chatActions" class="flex flex-col h-full">
        <!-- Chat Header -->
        <div class="flex items-center justify-between flex-none p-4 border-b bg-muted/5">
          <div class="flex items-center gap-2">
            <Button variant="ghost" size="icon" class="mr-2 md:hidden" @click="backToList">
              <ArrowLeft class="w-4 h-4" />
            </Button>
            <Button variant="ghost" size="icon" @click="chatActions.toggleStar">
              <Star
                :class="[
                  'h-4 w-4',
                  chatActions.isStarred
                    ? 'fill-yellow-400 text-yellow-400'
                    : 'text-muted-foreground',
                ]"
              />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              :class="{ 'text-green-500': chatActions.isResolved }"
              @click="chatActions.toggleResolved"
            >
              <CheckCircle class="w-4 h-4" />
            </Button>
          </div>
          <div class="flex items-center gap-2">
            <Button variant="ghost" size="icon" @click="downloadChat">
              <Download class="w-4 h-4" />
            </Button>
            <Button variant="ghost" size="icon" class="text-destructive" @click="confirmDelete">
              <Trash class="w-4 h-4" />
            </Button>
            <Button variant="ghost" size="icon" class="md:hidden" @click="toggleDetails">
              <Info class="w-4 h-4" />
            </Button>
          </div>
        </div>

        <!-- Chat Messages -->
        <div class="flex-1 overflow-y-auto max-h-[calc(100vh-200px)]">
          <div class="p-6">
            <DynamicScroller :items="selectedChat.messages" :min-item-size="54" class="scroller">
              <template v-slot="{ item, index, active }">
                <DynamicScrollerItem
                  :item="item"
                  :active="active"
                  :size-dependencies="[item.content]"
                  :data-index="index"
                >
                  <!-- Message content with avatar -->
                  <div
                    class="flex items-start gap-4 py-3"
                    :class="item.sender === 'Support Agent' ? '' : 'justify-end'"
                  >
                    <Avatar v-if="item.sender === 'Support Agent'" class="w-8 h-8 mt-1">
                      <AvatarImage v-if="item.avatar" :src="item.avatar" />
                      <AvatarFallback>S</AvatarFallback>
                    </Avatar>

                    <div class="max-w-[100%]">
                      <div class="flex flex-col gap-1">
                        <div
                          class="inline-block p-3 text-sm leading-relaxed rounded-lg w-fit"
                          :class="
                            item.sender === 'Support Agent'
                              ? 'bg-muted'
                              : 'bg-primary text-primary-foreground ml-auto'
                          "
                        >
                          {{ item.content }}
                        </div>
                      </div>
                      <div
                        v-if="item.sender === 'Support Agent'"
                        class="flex items-center gap-1 mt-1"
                      >
                        <button
                          class="flex items-center gap-1 px-1.5 py-0.5 text-xs transition-colors rounded hover:bg-accent text-muted-foreground hover:text-primary"
                          @click="createQA(item.content)"
                        >
                          <Plus class="w-3 h-3" />
                          <span>Add to Q&A</span>
                        </button>
                      </div>
                    </div>
                  </div>
                </DynamicScrollerItem>
              </template>
            </DynamicScroller>
          </div>
        </div>
      </div>
    </div>

    <!-- Right Sidebar -->
    <div class="flex flex-col h-full border-l w-60 shrink-0">
      <div class="flex items-center justify-between flex-none p-4 border-b">
        <h3 class="text-sm font-medium">Details</h3>
        <Button variant="ghost" size="icon">
          <X class="w-4 h-4" />
        </Button>
      </div>
      <div v-if="isLoadingChat" class="flex items-center justify-center flex-1">
        <Loader2 class="w-8 h-8 animate-spin" />
      </div>
      <div v-else class="flex-1 overflow-y-auto max-h-[calc(100vh-200px)]">
        <div class="p-6 space-y-8">
          <div>
            <div class="space-y-3">
              <div>
                <p class="text-sm text-muted-foreground">Agent</p>
                <p class="text-sm font-medium">AI Agent</p>
              </div>
              <div>
                <p class="text-sm text-muted-foreground">Team</p>
                <p class="text-sm font-medium">Zakaria MEHBI's Workspace</p>
              </div>
            </div>
          </div>

          <div>
            <h4 class="mb-3 text-sm font-medium">Lead Data</h4>
            <div class="space-y-3">
              <div>
                <p class="text-sm text-muted-foreground">Type</p>
                <p class="text-sm font-medium">Lead</p>
              </div>
              <div>
                <p class="text-sm text-muted-foreground">Email</p>
                <p class="text-sm font-medium">-</p>
              </div>
              <div>
                <p class="text-sm text-muted-foreground">Full Name</p>
                <p class="text-sm font-medium">-</p>
              </div>
              <div>
                <p class="text-sm text-muted-foreground">Phone</p>
                <p class="text-sm font-medium">-</p>
              </div>
              <div>
                <p class="text-sm text-muted-foreground">First seen</p>
                <p class="text-sm font-medium">
                  {{ selectedChat ? formatDate(selectedChat.firstSeen) : '-' }}
                </p>
              </div>
              <div>
                <p class="text-sm text-muted-foreground">Last seen</p>
                <p class="text-sm font-medium">
                  {{ selectedChat ? formatDate(selectedChat.lastSeen) : '-' }}
                </p>
              </div>
              <div>
                <p class="text-sm text-muted-foreground">Chat Type</p>
                <p class="text-sm font-medium">Messenger</p>
              </div>
              <div>
                <p class="text-sm text-muted-foreground">Chat Location</p>
                <p class="text-sm font-medium">app.livechatai.com</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { formatDistanceToNow } from 'date-fns'
import { DynamicScroller, DynamicScrollerItem } from 'vue-virtual-scroller'
import 'vue-virtual-scroller/dist/vue-virtual-scroller.css'

interface Message {
  id: string
  sender: string
  content: string
  timestamp: Date
  avatar?: string
}

interface Chat {
  id: string
  firstMessage: string
  status: 'resolved' | 'unresolved'
  updatedAt: Date
  starred?: boolean
  read?: boolean
}

interface ChatSession {
  id: string
  messages: Message[]
  status: 'resolved' | 'unresolved'
  starred?: boolean
  firstSeen: Date
  lastSeen: Date
}

const props = defineProps<{
  agentUid: string
}>()

const searchQuery = ref('')
const chats = ref<Chat[]>([])
const selectedChatId = ref<string | null>(null)
const selectedChat = ref<ChatSession | null>(null)
const isLoadingChats = ref(true)
const isLoadingChat = ref(false)
const filterType = ref('all')

const isMobile = ref(false)
const showDetails = ref(false)

const filteredAndSortedChats = computed(() => {
  let filtered = chats.value
  const query = searchQuery.value.toLowerCase()
  filtered = filtered.filter((chat) => chat.firstMessage.toLowerCase().includes(query))
  switch (filterType.value) {
    case 'unread':
      filtered = filtered.filter((chat) => !chat.read)
      break
    case 'unresolved':
      filtered = filtered.filter((chat) => chat.status === 'unresolved')
      break
    case 'mentions':
      break
    case 'resolved':
      filtered = filtered.filter((chat) => chat.status === 'resolved')
      break
    case 'longest-waiting':
      filtered = [...filtered].sort((a, b) => b.updatedAt.getTime() - a.updatedAt.getTime())
      break
  }
  return filtered
})

const chatActions = computed(() => {
  if (!selectedChat.value) return null
  return {
    isStarred: selectedChat.value.starred,
    isResolved: selectedChat.value.status === 'resolved',
    toggleStar: () => toggleStar(selectedChat.value!),
    toggleResolved: () => toggleResolved(selectedChat.value!),
  }
})

const truncateText = (text: string, maxLength: number) => {
  if (text.length <= maxLength) return text
  return text.substring(0, maxLength) + '...'
}

const formatDate = (date: Date) => {
  return formatDistanceToNow(date, { addSuffix: true })
}

const fetchChats = async (agentUid: string) => {
  try {
    isLoadingChats.value = true
    await new Promise((resolve) => setTimeout(resolve, 1000))
    chats.value = [
      {
        id: '1',
        firstMessage:
          'Hello, I need help with integrating the agent on my website. Can you guide me through the process?',
        status: 'unresolved',
        starred: true,
        updatedAt: new Date(Date.now() - 1000 * 60 * 5),
      },
      {
        id: '2',
        firstMessage:
          "The agent is not responding to my messages. I've tried refreshing but it's still not working.",
        status: 'resolved',
        starred: false,
        updatedAt: new Date(Date.now() - 1000 * 60 * 30),
      },
      {
        id: '3',
        firstMessage:
          'How can I customize the appearance of the agent? I want to match it with my brand colors.',
        status: 'unresolved',
        starred: false,
        updatedAt: new Date(Date.now() - 1000 * 60 * 60 * 2),
      },
      {
        id: '4',
        firstMessage:
          'Is it possible to export my conversation history? I need it for compliance purposes.',
        status: 'resolved',
        starred: true,
        updatedAt: new Date(Date.now() - 1000 * 60 * 60 * 24),
      },
      {
        id: '5',
        firstMessage:
          'The knowledge base integration is not working properly. The agent is not using my uploaded documents.',
        status: 'unresolved',
        starred: false,
        updatedAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 2),
      },
    ]
    if (chats.value.length > 0 && !selectedChatId.value) {
      selectChat(chats.value[0].id)
    }
  } catch (error) {
    console.error('Failed to fetch chats:', error)
  } finally {
    isLoadingChats.value = false
  }
}

const selectChat = async (chatId: string) => {
  try {
    selectedChatId.value = chatId
    isLoadingChat.value = true
    if (isMobile.value) {
      showDetails.value = false
    }
    await new Promise((resolve) => setTimeout(resolve, 800))
    const mockMessages = [
      {
        id: '1',
        sender: 'John Smith',
        content:
          'Hello, I need help with integrating the agent on my website. Can you guide me through the process?',
        timestamp: new Date(Date.now() - 1000 * 60 * 10),
        avatar: undefined,
      },
      {
        id: '2',
        sender: 'Support Agent',
        content:
          "Hi John! I'd be happy to help you with the integration. First, could you tell me which platform you're using for your website?",
        timestamp: new Date(Date.now() - 1000 * 60 * 9),
        avatar: 'https://api.dicebear.com/7.x/avatars/svg?seed=support',
      },
    ]
    const chat = chats.value.find((c) => c.id === chatId)
    selectedChat.value = {
      id: chatId,
      messages: mockMessages,
      status: chat?.status || 'unresolved',
      starred: chat?.starred || false,
      firstSeen: new Date(Date.now() - 1000 * 60 * 60 * 24),
      lastSeen: new Date(Date.now() - 1000 * 60 * 5),
    }
  } catch (error) {
    console.error('Failed to fetch chat:', error)
  } finally {
    isLoadingChat.value = false
  }
}

const downloadChat = () => {
  if (!selectedChat.value) return
  const messages = selectedChat.value.messages.map((m) => ({
    sender: m.sender,
    content: m.content,
    timestamp: m.timestamp,
  }))
  const data = JSON.stringify(messages, null, 2)
  const blob = new Blob([data], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `chat-${selectedChat.value.id}.json`
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)
}

const confirmDelete = () => {
  if (confirm('Are you sure you want to delete this conversation?')) {
    deleteChat()
  }
}

const deleteChat = () => {
  if (!selectedChat.value) return
  const chatId = selectedChat.value.id
  chats.value = chats.value.filter((c) => c.id !== chatId)
  selectedChat.value = null
  selectedChatId.value = null
}

const createQA = (message: Message) => {}

const exportSelected = () => {
  if (!selectedChat.value) return
  downloadChat()
}

const exportAll = () => {}

const toggleStar = (chat: ChatSession) => {
  chat.starred = !chat.starred
  const chatIndex = chats.value.findIndex((c) => c.id === chat.id)
  if (chatIndex !== -1) {
    chats.value[chatIndex].starred = chat.starred
  }
}

const toggleResolved = (chat: ChatSession) => {
  chat.status = chat.status === 'resolved' ? 'unresolved' : 'resolved'
  const chatIndex = chats.value.findIndex((c) => c.id === chat.id)
  if (chatIndex !== -1) {
    chats.value[chatIndex].status = chat.status
  }
}

const checkMobile = () => {
  isMobile.value = window.innerWidth < 768
}

const toggleDetails = () => {
  showDetails.value = !showDetails.value
}

const backToList = () => {
  if (isMobile.value) {
    selectedChatId.value = null
    selectedChat.value = null
  }
}

onBeforeMount(() => {
  checkMobile()
  window.addEventListener('resize', checkMobile)
})

onUnmounted(() => {
  window.removeEventListener('resize', checkMobile)
})

onMounted(() => {
  fetchChats(props.agentUid)
})
</script>

<style scoped>
.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}
</style>
