<template>
  <div class="space-y-4">
    <BaseCard
      title="Webhook Configuration"
      description="Configure a webhook URL to receive events from your agent"
    >
      <form @submit.prevent="saveWebhook" class="space-y-4">
        <div class="space-y-2">
          <Label for="webhook-url">Webhook URL</Label>
          <div class="flex gap-2">
            <Input
              id="webhook-url"
              v-model="webhookUrl"
              placeholder="https://your-server.com/webhook"
              class="flex-1"
            />
            <Button type="submit" :disabled="isSaving">
              {{ webhookUrl ? 'Update' : 'Save' }}
            </Button>
          </div>
        </div>

        <div v-if="webhookUrl" class="pt-2">
          <Alert>
            <AlertCircle class="w-4 h-4" />
            <AlertTitle>Webhook Secret</AlertTitle>
            <AlertDescription class="flex items-center gap-2">
              <span>{{ webhookSecret || 'No secret generated yet' }}</span>
              <Button variant="outline" size="sm" @click="regenerateSecret"> Regenerate </Button>
            </AlertDescription>
          </Alert>
        </div>
      </form>
    </BaseCard>

    <div class="border rounded-md">
      <Table>
        <TableCaption>Events sent to your webhook.</TableCaption>
        <TableHeader>
          <TableRow>
            <TableHead>Event Name</TableHead>
            <TableHead>Description</TableHead>
            <TableHead>Payload</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          <TableRow v-for="event in webhookEvents" :key="event.name">
            <TableCell class="font-medium">{{ event.name }}</TableCell>
            <TableCell>{{ event.description }}</TableCell>
            <TableCell>
              <Button variant="ghost" size="sm" @click="showEventPayload(event)">
                View Payload
              </Button>
            </TableCell>
          </TableRow>
        </TableBody>
      </Table>
    </div>

    <Dialog v-model:open="payloadDialogOpen">
      <DialogContent>
        <DialogHeader>
          <DialogTitle>{{ selectedEvent?.name }} Payload</DialogTitle>
          <DialogDescription> Event payload structure and example </DialogDescription>
        </DialogHeader>
        <div class="p-4 overflow-auto rounded-md bg-muted max-h-96">
          <pre class="text-xs">{{ selectedEvent?.payload }}</pre>
        </div>
      </DialogContent>
    </Dialog>
  </div>
</template>

<script setup lang="ts">
defineProps<{
  agentUid: string
}>()

// Webhooks Tab
const webhookUrl = ref('')
const webhookSecret = ref('')
const isSaving = ref(false)
const payloadDialogOpen = ref(false)
const selectedEvent = ref<Event | null>(null)

interface Event {
  name: string
  description: string
  payload: string
}

const webhookEvents = ref<Event[]>([
  {
    name: 'chat:started',
    description: 'Triggered when a new chat session begins',
    payload: JSON.stringify(
      {
        event: 'chat:started',
        timestamp: new Date().toISOString(),
        sessionId: 'session123456',
        userId: 'user789012',
        metadata: {
          source: 'website',
          page: '/products',
        },
      },
      null,
      2
    ),
  },
  {
    name: 'chat:message',
    description: 'Triggered when a message is sent or received',
    payload: JSON.stringify(
      {
        event: 'chat:message',
        timestamp: new Date().toISOString(),
        sessionId: 'session123456',
        message: {
          id: 'msg345678',
          content: 'Hello, how can I help you today?',
          sender: 'agent',
          timestamp: new Date().toISOString(),
        },
      },
      null,
      2
    ),
  },
  {
    name: 'chat:ended',
    description: 'Triggered when a chat session ends',
    payload: JSON.stringify(
      {
        event: 'chat:ended',
        timestamp: new Date().toISOString(),
        sessionId: 'session123456',
        duration: 125, // seconds
        messageCount: 8,
        resolution: 'completed',
      },
      null,
      2
    ),
  },
  {
    name: 'user:identified',
    description: 'Triggered when a user is identified',
    payload: JSON.stringify(
      {
        event: 'user:identified',
        timestamp: new Date().toISOString(),
        sessionId: 'session123456',
        user: {
          id: 'user789012',
          email: '<EMAIL>',
          name: 'John Doe',
        },
      },
      null,
      2
    ),
  },
  {
    name: 'lead:created',
    description: 'Triggered when a new lead is created',
    payload: JSON.stringify(
      {
        event: 'lead:created',
        timestamp: new Date().toISOString(),
        sessionId: 'session123456',
        lead: {
          id: 'lead901234',
          email: '<EMAIL>',
          name: 'Jane Smith',
          phone: '+1234567890',
          source: 'chat',
          createdAt: new Date().toISOString(),
        },
      },
      null,
      2
    ),
  },
])

const saveWebhook = async () => {
  isSaving.value = true

  try {
    // Simulate API call
    await new Promise((resolve) => setTimeout(resolve, 500))

    if (!webhookSecret.value && webhookUrl.value) {
      webhookSecret.value = generateRandomString(32)
    }
  } catch (error) {
    console.error('Failed to save webhook:', error)
  } finally {
    isSaving.value = false
  }
}

const regenerateSecret = () => {
  webhookSecret.value = generateRandomString(32)
}

const generateRandomString = (length: number) => {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
  let result = ''
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return result
}

const showEventPayload = (event: Event) => {
  selectedEvent.value = event
  payloadDialogOpen.value = true
}
</script>
