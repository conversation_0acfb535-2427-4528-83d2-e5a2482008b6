<template>
  <div class="space-y-4">
    <BaseCard title="WhatsApp Integration" description="Connect your agent to WhatsApp">
      <Alert class="bg-muted text-foreground">
        <div class="space-y-2">
          <p>
            Your agent integrates with WhatsApp and WhatsApp Business, allowing it to respond to
            messages sent to your phone number. You can use an existing number already linked to
            WhatsApp or WhatsApp Business, and you'll still have full control to take over any
            conversation at any time—no need to disconnect!
          </p>
          <p>
            Our integration is not a typical WhatsApp solution. Our custom API connects your agent
            directly to your WhatsApp or WhatsApp Business account, so you don't have to disconnect
            from your account and will work on both individual and group chats.
          </p>
          <p class="font-semibold">Available Commands</p>
          <p>
            Here's a list of pre-defined commands to manage your agent in individual or group chats:
          </p>
          <ul class="pl-5 space-y-1 list-disc">
            <li><span class="font-mono">/help</span>: Lists all available commands.</li>
            <li>
              <span class="font-mono">/reset</span>: Starts a new conversation by resetting the AI.
            </li>
            <li>
              <span class="font-mono">/pause</span>: Pauses the AI, allowing you to take over the
              conversation.
            </li>
            <li>
              <span class="font-mono">/resume</span>: Resumes the AI to continue handling the
              conversation.
            </li>
            <li>
              <span class="font-mono">/ai</span>: Activates the AI in group chats when triggered
              with this command.
            </li>
          </ul>
        </div>
      </Alert>

      <!-- Working Session -->
      <div v-if="sessionStatus === 'WORKING'" class="space-y-4">
        <Alert variant="success">
          <CheckCircle class="w-4 h-4" />
          <AlertTitle>Session Active</AlertTitle>
          <AlertDescription>
            <div class="space-y-2">
              <div class="grid grid-cols-2 gap-2">
                <span class="font-semibold">Status:</span>
                <span>Working</span>
                <span v-if="sessionId" class="font-semibold">Phone:</span>
                <span v-if="sessionId">{{ sessionId }}</span>
                <span v-if="sessionName" class="font-semibold">Name:</span>
                <span v-if="sessionName">{{ sessionName }}</span>
              </div>
            </div>
          </AlertDescription>
        </Alert>
        <Button
          variant="destructive"
          :disabled="isLoading"
          @click="handleDisconnect"
          class="w-full"
        >
          <span v-if="isLoading" class="mr-2"><Loader2 class="w-4 h-4 animate-spin" /></span>
          Disconnect
        </Button>
      </div>

      <!-- Failed or Stopped Session -->
      <div v-else-if="['FAILED', 'STOPPED'].includes(sessionStatus)" class="space-y-4">
        <Alert variant="destructive">
          <AlertCircle class="w-4 h-4" />
          <AlertTitle>Session {{ sessionStatus === 'FAILED' ? 'Failed' : 'Stopped' }}</AlertTitle>
          <AlertDescription>
            <div class="space-y-2">
              <div class="grid grid-cols-2 gap-2">
                <span class="font-semibold">Status:</span>
                <span>{{ sessionStatus === 'FAILED' ? 'Failed' : 'Stopped' }}</span>
                <span v-if="sessionId" class="font-semibold">Phone:</span>
                <span v-if="sessionId">{{ sessionId }}</span>
                <span v-if="sessionName" class="font-semibold">Name:</span>
                <span v-if="sessionName">{{ sessionName }}</span>
              </div>
              <p>
                Your WhatsApp or WhatsApp Business is not connected or has an issue. Please
                disconnect and try again!
              </p>
            </div>
          </AlertDescription>
        </Alert>
        <Button variant="default" :disabled="isLoading" @click="handleDisconnect" class="w-full">
          <span v-if="isLoading" class="mr-2"><Loader2 class="w-4 h-4 animate-spin" /></span>
          Disconnect
        </Button>
      </div>

      <!-- QR Code Scanning Session -->
      <div v-else-if="sessionStatus === 'SCAN_qrCode'" class="space-y-4">
        <Alert variant="warning">
          <ScanLine class="w-4 h-4" />
          <AlertTitle>Scan QR Code</AlertTitle>
          <AlertDescription>
            <div class="grid grid-cols-2 gap-2">
              <span class="font-semibold">Status:</span>
              <span>Waiting for scan</span>
              <span v-if="sessionId" class="font-semibold">Phone:</span>
              <span v-if="sessionId">{{ sessionId }}</span>
              <span v-if="sessionName" class="font-semibold">Name:</span>
              <span v-if="sessionName">{{ sessionName }}</span>
            </div>
          </AlertDescription>
        </Alert>

        <BaseCard v-if="qrCode">
          <div class="flex flex-col items-center space-y-4">
            <img :src="`data:image/png;base64,${qrCode}`" alt="QR Code" class="w-64 h-64" />
            <div class="space-y-2 text-sm">
              <p class="font-semibold">Instructions:</p>
              <ol class="pl-5 space-y-1 list-decimal">
                <li>
                  Open <span class="font-semibold">WhatsApp</span> or
                  <span class="font-semibold">WhatsApp Business</span> on your phone.
                </li>
                <li>
                  Tap <span class="font-semibold">More options</span> or
                  <span class="font-semibold">Settings</span>.
                </li>
                <li>
                  Tap <span class="font-semibold">Linked Devices</span> and
                  <span class="font-semibold">Link a device</span>.
                </li>
                <li>
                  Point your phone to this screen to capture the
                  <span class="font-semibold">QR code</span>.
                </li>
              </ol>
            </div>
          </div>
        </BaseCard>

        <div v-else class="flex justify-center">
          <div class="flex items-center space-x-2">
            <Loader2 class="w-5 h-5 animate-spin" />
            <span>Loading QR code...</span>
          </div>
        </div>
      </div>

      <!-- Starting Session -->
      <div v-else-if="sessionStatus === 'STARTING'" class="space-y-4">
        <Alert>
          <Loader2 class="w-4 h-4 animate-spin" />
          <AlertTitle>Session Starting</AlertTitle>
          <AlertDescription>
            <div class="space-y-2">
              <p>Please wait while we establish the connection to WhatsApp...</p>
            </div>
          </AlertDescription>
        </Alert>
      </div>

      <!-- No Session / Connect Form -->
      <div v-else class="space-y-4">
        <form @submit.prevent="handleGetQrCode" class="space-y-4">
          <BaseFormField
            label="Phone Number"
            description="Enter your phone number with country code to connect WhatsApp or WhatsApp Business."
          >
            <Input
              id="phone"
              v-model="phone"
              required
              placeholder="+****************"
              :disabled="isLoading"
            />
          </BaseFormField>

          <Button type="submit" :disabled="isLoading || !phone" class="w-full">
            <span v-if="isLoading" class="mr-2"><Loader2 class="w-4 h-4 animate-spin" /></span>
            Get QR Code to connect WhatsApp
          </Button>
        </form>
      </div>
    </BaseCard>
  </div>
</template>

<script setup lang="ts">
defineProps<{
  agentUid: string
}>()

// State
const phone = ref('')
const isLoading = ref(false)
const isFirstLoading = ref(true)
const qrCode = ref('')
const sessionStatus = ref('')
const sessionId = ref('')
const sessionName = ref('')
let statusInterval: number | null = null

// Functions
const handleGetQrCode = async () => {
  if (isLoading.value || !phone.value) return

  try {
    isLoading.value = true

    // Simulate API call to get QR code
    await new Promise((resolve) => setTimeout(resolve, 1000))

    // In a real implementation, this would be an API call:
    // const response = await fetch(`/api/whatsapp/get-qr-code`, {
    //   method: 'POST',
    //   headers: { 'Content-Type': 'application/json' },
    //   body: JSON.stringify({ phone: phone.value, agentUid: props.agentUid })
    // })
    // const data = await response.json()
    // if (data.qrCode) {
    //   qrCode.value = data.qrCode
    // }

    // For demo, generate a placeholder QR code
    qrCode.value =
      'iVBORw0KGgoAAAANSUhEUgAAAKQAAACkCAYAAAAZtYVBAAAAAklEQVR4AewaftIAAAU4SURBVO3BQY7kSAwEwfAC//9l3znmqYBCdZIDrbHN7A+s4yjrSMo6krKOpKwjKetIyjqSso6krCMp60jKOpKyjqSsIynrSMo6krKOpKwjKetIyjqSHx6q9JuqTJVuVJkq3ag0VW5UeaLSVOlGpanyRKXfVOWJso6krCMp60h++FiVT6r0RKWbSk9Uuqk0VW4qTZWbSk9U+aTKJ5V1JGUdSVlH8sMvq/SJSlOlqXSj0o1KU+UTKt1UmipNpRuVPlHpN5X1L2UdSVlHUtaR/PASVT6h0m+qNFWaKk2lqdJU+Y3K/7OyjqSsIynrSH74ZZVuVLpRaao0VZoqN5VuKk2Vpkp/UeWm0m8q60jKOpKyjuSHX1bpE5WmSlOlqdJUmSpNlabSTaWp0lRpqtxUmipNlZtKU6Wp8pvKOpKyjqSsI/nhQ5X+JZWmSlPlRqWp0lRpqjRVbip9otK/pKwjKetIyjqSHz5W5X+p0lS5qTRVmipNlabKVGmqTJWmyk2lqXJT6YmyjqSsIynrSH54qNJU+USVpkpTpanSVJkqTZWmyk2lqdJUuak0VZoqU6WpMlWaKk2VmypNlabKVGmqTJWmylRpqkzKOpKyjqSsI/nhQ5WmSlNlqtxUmipNlabKVJkqTZWp0lRpqkyVpspNpanSVLmpNFWaKlPlptJUaapMlabKTaWp8kRZR1LWkZR1JD/8sko3Kt1UaapMlabKVGmqTJWmylRpqkyVpspUaapMlaZKU6WpMlWaKlOlqTJVmipTpanyRKWp0lS5qTRVbipNlanSVGmqTJWmylRpqkyVpso3lXUkZR1JWUfyw4cqTZWmylRpqkyVpspUaapMlabKVGmqTJWmyjdVmipNlabKVGmqTJWmylRpqkyVpspUaapMlaZKU2WqNFWmSlNlqjRVvqmsIynrSMo6Ehs//GNTpakyvak0VaZKU2WqNFWmSlNlqjRVpkpTZao0VaZKU2WqNFWmSlNlqjRVnijrSMo6krKO5IePVZoqN5WmylRpqkyVpspUaapMlaZKU6WpMlWaKlOlqTJVmipTpanyRKWp0lS5qTRVbipNlanSVGmqTJWmylRpqkyVpso3lXUkZR1JWUfyw8eqfFLliUpTpakyvak0VZoqU6WpMlWaKlOlqdJUaapMlaZKU2WqNFWaKk2VqXJTaal0U+k3lXUkZR1JWUfyw8eqfFLliUpTpakyvak0VZoqU6WpMlWaKlOlqdJUaapMlW4qTZWp0lTpptJN5YmyjqSsIynrSH74ZZU+UalRaao0VZoqU6WpMlWaKk2VpkpTpakyn0BpqjRVPlFpqvyXyjqSso6krCP54Usz86bSTaWp0lRpqjRVmipNlabKVGmqvKHSVGmqfEOlqdJU+aSyjqSsIynrSH74ZZW+SaWp0lRpqjRVmipNlabKVGmqNFWmSn9RpakyvznrSMo6krKO5IdKv6nSVGmqTJWmSlOlqdJUaapMlaZKU6Wp0lRpqnRTaapMlabKVGmqNFWmSlNlqjRVmipT5Y2yjqSsIynrSMz+wDqOso6krCMp60jKOpKyjqSsIynrSMo6krKOpKwjKetIyjqSso6krCMp60jKOpKyjqSsIynrSP4H/IRGVOgJX1gAAAAASUVORK5CYII='

    // Once we have a QR code, update status
    sessionStatus.value = 'SCAN_qrCode'
    sessionId.value = phone.value

    // Start polling for status
    startPolling()
  } catch (error) {
    console.error('Failed to fetch QR code:', error)
  } finally {
    isLoading.value = false
  }
}

const handleDisconnect = async () => {
  try {
    isLoading.value = true

    // Simulate API call to disconnect
    await new Promise((resolve) => setTimeout(resolve, 1000))

    // In a real implementation, this would be an API call:
    // await fetch(`/api/whatsapp/disconnect/${props.agentUid}`, {
    //   method: 'DELETE'
    // })

    // Reset state
    sessionStatus.value = ''
    sessionId.value = ''
    sessionName.value = ''
    qrCode.value = ''

    if (statusInterval) {
      clearInterval(statusInterval)
      statusInterval = null
    }
  } catch (error) {
    console.error('Failed to disconnect:', error)
  } finally {
    isLoading.value = false
  }
}

const getSessionStatus = async () => {
  try {
    // In a real implementation, this would be an API call:
    // const response = await fetch(`/api/whatsapp/session/${props.agentUid}`)
    // const data = await response.json()
    // sessionStatus.value = data.status
    // sessionId.value = data.id
    // sessionName.value = data.name

    // For demo, simulate status changes
    if (sessionStatus.value === 'SCAN_qrCode') {
      // Simulate waiting for scan for a few seconds then change to STARTING
      if (Math.random() > 0.7) {
        sessionStatus.value = 'STARTING'
      }
    } else if (sessionStatus.value === 'STARTING') {
      // Simulate starting for a few seconds then change to WORKING
      if (Math.random() > 0.7) {
        sessionStatus.value = 'WORKING'
        sessionName.value = 'John Doe'
      }
    }

    // If we reach a terminal state, stop polling
    if (['WORKING', 'FAILED', 'STOPPED'].includes(sessionStatus.value) && statusInterval) {
      clearInterval(statusInterval)
    }
  } catch (error) {
    console.error('Failed to fetch session status:', error)
    sessionStatus.value = ''
  }
}

const startPolling = () => {
  // Clear any existing interval
  if (statusInterval) {
    clearInterval(statusInterval)
  }

  // Start polling for status updates
  getSessionStatus()
  statusInterval = window.setInterval(getSessionStatus, 3000)
}

// Lifecycle hooks
onMounted(async () => {
  try {
    isFirstLoading.value = true

    // Get initial session status
    await getSessionStatus()
  } catch (error) {
    console.error(error)
  } finally {
    isFirstLoading.value = false
  }
})

onBeforeUnmount(() => {
  // Clean up interval
  if (statusInterval) {
    clearInterval(statusInterval)
  }
})

// Watch for QR code changes
watch(qrCode, (newVal) => {
  if (newVal && !statusInterval) {
    startPolling()
  }
})
</script>
