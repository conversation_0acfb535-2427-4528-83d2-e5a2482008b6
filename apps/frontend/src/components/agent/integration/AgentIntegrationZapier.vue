<template>
  <BaseCard
    title="Zapier Integration"
    description="Connect your agent to thousands of apps with Zapier"
  >
    <div class="flex flex-col gap-4">
      <p>Use Zapier to connect your agent with 3000+ apps without any code.</p>
      <div class="flex gap-2">
        <Button @click="openZapier">
          <Zap class="w-4 h-4 mr-2" />
          Open Zapier App
        </Button>
      </div>
    </div>
  </BaseCard>
</template>

<script setup lang="ts">
defineProps<{
  agentUid: string
}>()

// Zapier Tab
const openZapier = () => {
  window.open('https://zapier.com/apps/your-app', '_blank')
}
</script>
