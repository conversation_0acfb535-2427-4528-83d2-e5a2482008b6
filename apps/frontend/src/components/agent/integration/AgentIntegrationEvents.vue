<template>
  <div class="space-y-4">
    <div class="border rounded-md">
      <Table>
        <TableCaption>JavaScript events emitted by the agent.</TableCaption>
        <TableHeader>
          <TableRow>
            <TableHead>Event Name</TableHead>
            <TableHead>Description</TableHead>
            <TableHead>Payload</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          <TableRow v-for="event in jsEvents" :key="event.name">
            <TableCell class="font-medium">{{ event.name }}</TableCell>
            <TableCell>{{ event.description }}</TableCell>
            <TableCell>
              <Button variant="ghost" size="sm" @click="showEventPayload(event)">
                View Payload
              </Button>
            </TableCell>
          </TableRow>
        </TableBody>
      </Table>
    </div>

    <Dialog v-model:open="payloadDialogOpen">
      <DialogContent>
        <DialogHeader>
          <DialogTitle>{{ selectedEvent?.name }} Payload</DialogTitle>
          <DialogDescription> Event payload structure and example </DialogDescription>
        </DialogHeader>
        <div class="p-4 overflow-auto rounded-md bg-muted max-h-96">
          <pre class="text-xs">{{ selectedEvent?.payload }}</pre>
        </div>
      </DialogContent>
    </Dialog>
  </div>
</template>

<script setup lang="ts">
defineProps<{
  agentUid: string
}>()

// JS Events Tab
const payloadDialogOpen = ref(false)
const selectedEvent = ref<Event | null>(null)

interface Event {
  name: string
  description: string
  payload: string
}

const jsEvents = ref<Event[]>([
  {
    name: 'chat:started',
    description: 'Triggered when a new chat session begins',
    payload: JSON.stringify(
      {
        event: 'chat:started',
        timestamp: new Date().toISOString(),
        sessionId: 'session123456',
        userId: 'user789012',
        metadata: {
          source: 'website',
          page: '/products',
        },
      },
      null,
      2
    ),
  },
  {
    name: 'chat:message',
    description: 'Triggered when a message is sent or received',
    payload: JSON.stringify(
      {
        event: 'chat:message',
        timestamp: new Date().toISOString(),
        sessionId: 'session123456',
        message: {
          id: 'msg345678',
          content: 'Hello, how can I help you today?',
          sender: 'agent',
          timestamp: new Date().toISOString(),
        },
      },
      null,
      2
    ),
  },
  {
    name: 'chat:ended',
    description: 'Triggered when a chat session ends',
    payload: JSON.stringify(
      {
        event: 'chat:ended',
        timestamp: new Date().toISOString(),
        sessionId: 'session123456',
        duration: 125, // seconds
        messageCount: 8,
        resolution: 'completed',
      },
      null,
      2
    ),
  },
  {
    name: 'user:identified',
    description: 'Triggered when a user is identified',
    payload: JSON.stringify(
      {
        event: 'user:identified',
        timestamp: new Date().toISOString(),
        sessionId: 'session123456',
        user: {
          id: 'user789012',
          email: '<EMAIL>',
          name: 'John Doe',
        },
      },
      null,
      2
    ),
  },
  {
    name: 'lead:created',
    description: 'Triggered when a new lead is created',
    payload: JSON.stringify(
      {
        event: 'lead:created',
        timestamp: new Date().toISOString(),
        sessionId: 'session123456',
        lead: {
          id: 'lead901234',
          email: '<EMAIL>',
          name: 'Jane Smith',
          phone: '+1234567890',
          source: 'chat',
          createdAt: new Date().toISOString(),
        },
      },
      null,
      2
    ),
  },
])

const showEventPayload = (event: Event) => {
  selectedEvent.value = event
  payloadDialogOpen.value = true
}
</script>
