<template>
  <BaseCard
    title="API Documentation"
    description="Access our API documentation and Postman collection"
  >
    <div class="flex flex-col gap-2">
      <p>Our API allows you to programmatically interact with your agent.</p>
      <div class="flex gap-2">
        <Button @click="openPostmanCollection">
          <ExternalLink class="w-4 h-4 mr-2" />
          Open Postman Collection
        </Button>
      </div>
    </div>
  </BaseCard>
</template>

<script setup lang="ts">
defineProps<{
  agentUid: string
}>()

// API Tab
const openPostmanCollection = () => {
  window.open('https://www.postman.com/collection/your-collection-id', '_blank')
}
</script>
