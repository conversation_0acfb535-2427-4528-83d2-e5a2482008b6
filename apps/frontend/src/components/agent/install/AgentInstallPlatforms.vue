<template>
  <div class="space-y-4">
    <div class="flex items-center justify-between">
      <div class="flex items-center space-x-2">
        <Puzzle class="w-5 h-5 text-primary" />
        <div>
          <h3 class="text-lg font-medium text-foreground">Platform Guides</h3>
          <p class="text-sm text-muted-foreground">
            Follow our step-by-step guides for popular platforms.
          </p>
        </div>
      </div>
    </div>
    <div class="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
      <Card
        class="transition-colors cursor-pointer hover:border-primary group"
        @click="openTutorial('wordpress')"
      >
        <CardContent class="flex items-center p-4 space-x-3">
          <FileCode class="w-5 h-5 text-foreground" />
          <div class="flex-1">
            <span class="font-medium text-foreground">WordPress</span>
            <p class="text-xs text-muted-foreground">Plugin Installation</p>
          </div>
          <ExternalLink
            class="w-4 h-4 transition-colors text-muted-foreground group-hover:text-primary"
          />
        </CardContent>
      </Card>
      <Card
        class="transition-colors cursor-pointer hover:border-primary group"
        @click="openTutorial('wix')"
      >
        <CardContent class="flex items-center p-4 space-x-3">
          <Globe class="w-5 h-5 text-foreground" />
          <div class="flex-1">
            <span class="font-medium text-foreground">Wix</span>
            <p class="text-xs text-muted-foreground">Custom Element</p>
          </div>
          <ExternalLink
            class="w-4 h-4 transition-colors text-muted-foreground group-hover:text-primary"
          />
        </CardContent>
      </Card>
      <Card
        class="transition-colors cursor-pointer hover:border-primary group"
        @click="openTutorial('gtm')"
      >
        <CardContent class="flex items-center p-4 space-x-3">
          <Tags class="w-5 h-5 text-foreground" />
          <div class="flex-1">
            <span class="font-medium text-foreground">GTM</span>
            <p class="text-xs text-muted-foreground">Tag Manager Install</p>
          </div>
          <ExternalLink
            class="w-4 h-4 transition-colors text-muted-foreground group-hover:text-primary"
          />
        </CardContent>
      </Card>
      <Card
        class="transition-colors cursor-pointer hover:border-primary group"
        @click="openTutorial('sites')"
      >
        <CardContent class="flex items-center p-4 space-x-3">
          <LayoutGrid class="w-5 h-5 text-foreground" />
          <div class="flex-1">
            <span class="font-medium text-foreground">Google Sites</span>
            <p class="text-xs text-muted-foreground">Embed Instructions</p>
          </div>
          <ExternalLink
            class="w-4 h-4 transition-colors text-muted-foreground group-hover:text-primary"
          />
        </CardContent>
      </Card>
    </div>
  </div>
</template>

<script setup lang="ts">
const tutorialUrls = {
  wordpress: 'https://wordpress.org/plugins/insertchat/',
  wix: 'https://insertchat.notion.site/Wix-1de4b38220e1430890c46372b76aaaf1',
  gtm: 'https://insertchat.notion.site/Google-Tag-Manager-6a792d15cf824b49a3574d344b081e02',
  sites: 'https://insertchat.notion.site/Google-Sites-d5fb1eda0e95497c8049850f1543adf4',
}

const openTutorial = (type: keyof typeof tutorialUrls) => {
  window.open(tutorialUrls[type], '_blank', 'noopener,noreferrer')
}
</script>
