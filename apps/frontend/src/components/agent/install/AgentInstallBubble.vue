<template>
  <div class="space-y-6">
    <div class="space-y-6">
      <div class="flex items-center space-x-2">
        <FileText class="w-5 h-5 text-primary" />
        <h3 class="text-lg font-medium text-foreground">Install in 2 Steps</h3>
      </div>
      <div class="space-y-4 ml-7">
        <div class="flex items-start space-x-3">
          <Badge variant="outline" class="px-2 py-1 text-sm"> 1 </Badge>
          <div class="space-y-1">
            <p class="text-sm font-medium text-foreground">
              Add the code below to your website's <code>&lt;head&gt;</code> tag
            </p>
          </div>
        </div>

        <div class="flex items-start space-x-3">
          <Badge variant="outline" class="px-2 py-1 text-sm"> 2 </Badge>
          <div class="space-y-1">
            <p class="text-sm font-medium text-foreground">Refresh your page</p>
            <p class="text-sm text-muted-foreground">
              The chat bubble will appear on your website.
            </p>
          </div>
        </div>
      </div>
    </div>

    <BaseCard title="Installation Code" description="Copy and paste this code to your website">
      <div class="flex justify-end mb-4">
        <Button variant="ghost" size="sm" class="h-8 px-2" @click="copyCode">
          <Copy class="w-4 h-4 mr-2" />
          <span class="text-sm">Copy</span>
        </Button>
      </div>

      <div class="relative p-6 font-mono text-sm rounded-md bg-muted/50 dark:bg-muted/10">
        <div class="max-w-full overflow-x-auto">
          <pre
            class="break-words whitespace-pre-wrap"
          ><code class="text-foreground">{{ scriptTemplate }}</code></pre>
        </div>
      </div>
    </BaseCard>

    <BaseCard
      title="Mobile Optimization"
      description="Add this meta tag to ensure your agent works perfectly on mobile devices"
    >
      <div class="flex justify-end mb-4">
        <Button variant="ghost" size="sm" class="h-8 px-2" @click="copyViewportTag">
          <Copy class="w-4 h-4 mr-2" />
          <span class="text-sm">Copy</span>
        </Button>
      </div>

      <div class="relative p-6 font-mono text-sm rounded-md bg-muted/50 dark:bg-muted/10">
        <div class="max-w-full overflow-x-auto">
          <pre
            class="break-words whitespace-pre-wrap"
          ><code class="text-foreground">&lt;meta name="viewport" content="width=device-width, height=device-height, initial-scale=1.0, maximum-scale=1.0" /&gt;</code></pre>
        </div>
      </div>
    </BaseCard>

    <AgentInstallPlatformGuides />
    <AgentInstallConfigVariables />
  </div>
</template>

<script setup lang="ts">
const props = defineProps<{
  agentUid: string
}>()

const appStore = useAppStore()

const scriptTemplate = computed(() => {
  const domain = appStore.currentDomain

  return `<script type="text/javascript" src="https://${domain}/widgets/chatbot.js" async><\/script>
<script>
ICG_BOT_ID = '${props.agentUid}';
ICG_BOT_TYPE = 'bubble';
ICG_BOT_HEIGHT = 750;
ICG_BOT_BG_COLOR = '#fff';
ICG_BOT_AUTOFOCUS = false;
ICG_BOT_OVERRIDE_OPENER = '';
ICG_USER_ID = '';
ICG_USER_EMAIL = '';
ICG_USER_FIRSTNAME = '';
ICG_USER_LASTNAME = '';
ICG_USER_TAGS = [];
ICG_USER_METADATA = {};
<\/script>`
})

const copyCode = () => {
  navigator.clipboard.writeText(scriptTemplate.value)
}

const copyViewportTag = () => {
  navigator.clipboard.writeText(
    '<meta name="viewport" content="width=device-width, height=device-height, initial-scale=1.0, maximum-scale=1.0" />'
  )
}
</script>
