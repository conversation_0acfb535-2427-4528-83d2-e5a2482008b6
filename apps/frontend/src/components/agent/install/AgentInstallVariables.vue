<template>
  <BaseCard title="Configuration Variables" description="Understanding the installation parameters">
    <div class="space-y-6">
      <div class="grid gap-4">
        <div class="grid grid-cols-[200px,1fr] items-start">
          <div>
            <code class="px-2 py-1 font-mono text-xs rounded-md text-foreground bg-muted"
              >ICG_BOT_ID</code
            >
            <p class="mt-1 text-xs text-muted-foreground">Required</p>
          </div>
          <div>
            <p class="font-medium">Agent Identifier</p>
            <p class="text-sm text-muted-foreground">
              A unique identifier for your chat agent. This ID connects the widget to your specific
              agent configuration.
            </p>
          </div>
        </div>

        <div class="grid grid-cols-[200px,1fr] items-start">
          <div>
            <code class="px-2 py-1 font-mono text-xs rounded-md text-foreground bg-muted"
              >ICG_BOT_TYPE</code
            >
            <p class="mt-1 text-xs text-muted-foreground">Required</p>
          </div>
          <div>
            <p class="font-medium">Agent Display Type</p>
            <p class="text-sm text-muted-foreground">
              Determines how the chat appears on your website:
            </p>
            <div class="flex gap-4 mt-2 text-sm text-muted-foreground">
              <span class="flex items-center gap-1">
                <MessageSquare class="w-4 h-4 text-primary" />
                'bubble
              </span>
              <span class="flex items-center gap-1">
                <LayoutTemplate class="w-4 h-4 text-primary" />
                'window'
              </span>
            </div>
          </div>
        </div>

        <div class="grid grid-cols-[200px,1fr] items-start">
          <div>
            <code class="px-2 py-1 font-mono text-xs rounded-md text-foreground bg-muted"
              >ICG_BOT_HEIGHT</code
            >
            <p class="mt-1 text-xs text-muted-foreground">Optional</p>
          </div>
          <div>
            <p class="font-medium">Chat Window Height</p>
            <p class="text-sm text-muted-foreground">
              Sets the height of the chat window in pixels. Default is 750px.
            </p>
          </div>
        </div>

        <div class="grid grid-cols-[200px,1fr] items-start">
          <div>
            <code class="px-2 py-1 font-mono text-xs rounded-md text-foreground bg-muted"
              >ICG_BOT_BG_COLOR</code
            >
            <p class="mt-1 text-xs text-muted-foreground">Optional</p>
          </div>
          <div>
            <p class="font-medium">Background Color</p>
            <p class="text-sm text-muted-foreground">
              The initial background color shown during chat widget loading.
            </p>
          </div>
        </div>

        <div class="grid grid-cols-[200px,1fr] items-start">
          <div>
            <code class="px-2 py-1 font-mono text-xs rounded-md text-foreground bg-muted"
              >ICG_USER_ID</code
            >
            <p class="mt-1 text-xs text-muted-foreground">Optional</p>
          </div>
          <div>
            <p class="font-medium">User Identifier</p>
            <p class="text-sm text-muted-foreground">
              A unique identifier for the current user. Use this to track conversations.
            </p>
          </div>
        </div>

        <div class="grid grid-cols-[200px,1fr] items-start">
          <div>
            <code class="px-2 py-1 font-mono text-xs rounded-md text-foreground bg-muted"
              >ICG_USER_EMAIL</code
            >
            <p class="mt-1 text-xs text-muted-foreground">Optional</p>
          </div>
          <div>
            <p class="font-medium">User Email</p>
            <p class="text-sm text-muted-foreground">
              The email address of the current user. Useful for follow-ups.
            </p>
          </div>
        </div>

        <div class="grid grid-cols-[200px,1fr] items-start">
          <div>
            <code class="px-2 py-1 font-mono text-xs rounded-md text-foreground bg-muted"
              >ICG_USER_TAGS</code
            >
            <p class="mt-1 text-xs text-muted-foreground">Optional</p>
          </div>
          <div>
            <p class="font-medium">User Tags</p>
            <p class="text-sm text-muted-foreground">
              An array of tags to categorize or segment users.
            </p>
          </div>
        </div>

        <div class="grid grid-cols-[200px,1fr] items-start">
          <div>
            <code class="px-2 py-1 font-mono text-xs rounded-md text-foreground bg-muted"
              >ICG_USER_METADATA</code
            >
            <p class="mt-1 text-xs text-muted-foreground">Optional</p>
          </div>
          <div>
            <p class="font-medium">User Metadata</p>
            <p class="text-sm text-muted-foreground">
              Additional custom data about the user as a JSON object.
            </p>
          </div>
        </div>
      </div>
    </div>
  </BaseCard>
</template>

<script setup lang="ts"></script>
