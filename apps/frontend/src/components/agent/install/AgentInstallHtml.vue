<template>
  <div class="space-y-6">
    <div class="space-y-6">
      <div class="flex items-center space-x-2">
        <BaseIcon name="FileText" />
        <h3 class="text-lg font-medium text-foreground">Install in 2 Steps</h3>
      </div>
      <div class="space-y-4 ml-7">
        <div class="flex items-start space-x-3">
          <Badge variant="outline" class="px-2 py-1 text-sm"> 1 </Badge>
          <div class="space-y-1">
            <p class="text-sm font-medium text-foreground">
              Add this <code>&lt;div&gt;</code> where you want the chat to appear
            </p>
            <div class="relative p-3 font-mono text-sm rounded-md bg-muted/50 dark:bg-muted/10">
              <div class="max-w-full overflow-x-auto">
                <pre
                  class="break-words whitespace-pre-wrap"
                ><code class="text-foreground">&lt;div id="insertchat-container"&gt;&lt;/div&gt;</code></pre>
              </div>
            </div>
          </div>
        </div>

        <div class="flex items-start space-x-3">
          <Badge variant="outline" class="px-2 py-1 text-sm"> 2 </Badge>
          <div class="space-y-1">
            <p class="text-sm font-medium">
              Add the code below to your website's <code>&lt;head&gt;</code> tag
            </p>
          </div>
        </div>
      </div>
    </div>

    <BaseCard
      title="Container Element"
      description="Add this HTML where you want the chat to appear"
    >
      <div class="flex justify-end mb-4">
        <Button variant="ghost" size="sm" class="h-8 px-2" @click="copyContainer">
          <BaseIcon name="Copy" />
          <span class="text-sm">Copy</span>
        </Button>
      </div>

      <div class="relative p-6 font-mono text-sm rounded-md bg-muted/50 dark:bg-muted/10">
        <div class="max-w-full overflow-x-auto">
          <pre
            class="break-words whitespace-pre-wrap"
          ><code class="text-foreground">{{ containerTemplate }}</code></pre>
        </div>
      </div>
    </BaseCard>

    <AgentInstallPlatformGuides />
    <AgentInstallConfigVariables />
  </div>
</template>

<script setup lang="ts">
const props = defineProps<{
  agentUid: string
}>()

const appStore = useAppStore()

const containerTemplate = computed(() => {
  const domain = appStore.currentDomain

  return `<iframe
  allowfullscreen
  title="AI Agent"
  role="dialog"
  id="ICG-iframe"
  allow="accelerometer; autoplay; camera; display-capture; encrypted-media; fullscreen; gamepad; geolocation; gyroscope; hid; identity-credentials-get; idle-detection; local-fonts; magnetometer; microphone; midi; otp-credentials; payment; picture-in-picture; publickey-credentials-get; screen-wake-lock; serial; storage-access; usb; window-management; xr-spatial-tracking"
  sandbox="allow-forms allow-modals allow-orientation-lock allow-pointer-lock allow-popups allow-popups-to-escape-sandbox allow-presentation allow-same-origin allow-scripts allow-top-navigation allow-top-navigation-by-user-activation"
  src="https://${domain}/embed/${props.agentUid}"
  style="display: block; border: 0; overflow: hidden; height: 100%; width: 100%; background-color: #fff;">
</iframe>`
})

const copyContainer = () => {
  navigator.clipboard.writeText(containerTemplate.value)
}
</script>
