<template>
  <div class="space-y-6">
    <div class="space-y-6">
      <div class="flex items-center space-x-2">
        <Link class="w-5 h-5 text-primary" />
        <h3 class="text-lg font-medium text-foreground">Copy the link below</h3>
      </div>
      <p class="text-sm text-foreground ml-7">
        Share it with your users to start chatting instantly.
      </p>
    </div>

    <BaseCard title="Direct URL" description="Share this link with your users">
      <div class="flex justify-end mb-4">
        <Button variant="ghost" size="sm" class="h-8 px-2" @click="copyDirectLink">
          <Copy class="w-4 h-4 mr-2" />
          <span class="text-sm">Copy</span>
        </Button>
      </div>
      <div class="relative p-4 font-mono text-sm rounded-md bg-muted/50 dark:bg-muted/10">
        <div class="max-w-full overflow-x-auto">
          <pre
            class="break-words whitespace-pre-wrap"
          ><code class="text-foreground">{{ directLink }}</code></pre>
        </div>
      </div>
    </BaseCard>

    <BaseCard
      title="HTML Direct Link Example"
      description="Example of using the direct link in HTML"
    >
      <div class="flex justify-end mb-4">
        <Button variant="ghost" size="sm" class="h-8 px-2" @click="copyHtmlExample">
          <Copy class="w-4 h-4 mr-2" />
          <span class="text-sm">Copy</span>
        </Button>
      </div>

      <div class="relative p-6 font-mono text-sm rounded-md bg-muted/50 dark:bg-muted/10">
        <div class="max-w-full overflow-x-auto">
          <pre
            class="break-words whitespace-pre-wrap"
          ><code class="text-foreground">{{ htmlExample }}</code></pre>
        </div>
      </div>
    </BaseCard>
  </div>
</template>

<script setup lang="ts">
const props = defineProps<{
  agentUid: string
}>()

const appStore = useAppStore()

const directLink = computed(() => {
  const domain = appStore.currentDomain

  return `https://${domain}/embed/${props.agentUid}`
})

const htmlExample = computed(() => {
  return `<a href="${directLink.value}" target="_blank" rel="noopener noreferrer">
  Chat with us
</a>

<!-- Or as a button -->
<button onclick="window.open('${directLink.value}', '_blank', 'noopener,noreferrer')">
  Start Chat
</button>`
})

const copyDirectLink = () => {
  navigator.clipboard.writeText(directLink.value)
}

const copyHtmlExample = () => {
  navigator.clipboard.writeText(htmlExample.value)
}
</script>
