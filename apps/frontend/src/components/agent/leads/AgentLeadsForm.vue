<template>
  <BaseDialog
    title="Edit Lead"
    description="Edit the lead details"
    icon="User"
    :dialogId="dialogId"
  >
    <form @submit.prevent="saveLead" class="space-y-4">
      <div class="space-y-2">
        <Label for="name">Name</Label>
        <Input id="name" v-model="localForm.name" required />
      </div>
      <div class="space-y-2">
        <Label for="email">Email</Label>
        <Input id="email" type="email" v-model="localForm.email" required />
      </div>
      <div class="space-y-2">
        <Label for="phone">Phone</Label>
        <Input id="phone" type="tel" v-model="localForm.phone" />
      </div>
      <DialogFooter>
        <Button type="button" variant="outline" @click="emit('update:open', false)">
          Cancel
        </Button>
        <Button type="submit">Save</Button>
      </DialogFooter>
    </form>
  </BaseDialog>
</template>

<script setup lang="ts">
interface LeadForm {
  name: string
  email: string
  phone?: string
}

const props = defineProps<{
  dialogId: string
  initialFormData: LeadForm
}>()
const emit = defineEmits(['update:open', 'save'])

const localForm = ref({ ...props.initialFormData })

watch(
  () => props.initialFormData,
  (newValue) => {
    localForm.value = { ...newValue }
  },
  { deep: true }
)

const saveLead = () => {
  emit('save', { ...localForm.value })
  emit('update:open', false)
}
</script>
