<template>
  <div class="space-y-4">
    <div class="flex items-center space-x-2">
      <div class="flex-1">
        <Input
          :model-value="searchQuery"
          @update:model-value="emit('update:searchQuery', $event)"
          placeholder="Search leads..."
        />
      </div>
    </div>
    <div class="border rounded-md">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Internal UID</TableHead>
            <TableHead>ID</TableHead>
            <TableHead>Name</TableHead>
            <TableHead>Email</TableHead>
            <TableHead>Phone</TableHead>
            <TableHead>Created At</TableHead>
            <TableHead>Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          <TableRow v-for="lead in filteredLeads" :key="lead.id">
            <TableCell class="font-mono">{{ lead.internalUid }}</TableCell>
            <TableCell class="font-mono">{{ lead.id }}</TableCell>
            <TableCell>{{ lead.name }}</TableCell>
            <TableCell>{{ lead.email }}</TableCell>
            <TableCell>{{ lead.phone || '-' }}</TableCell>
            <TableCell>{{ formatDate(lead.createdAt) }}</TableCell>
            <TableCell>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="icon">
                    <MoreVertical class="w-4 h-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem @click="emit('edit-lead', lead)">
                    <Pencil class="w-4 h-4 mr-2" />
                    Edit
                  </DropdownMenuItem>
                  <DropdownMenuItem @click="emit('view-lead-history', lead)">
                    <History class="w-4 h-4 mr-2" />
                    View History
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </TableCell>
          </TableRow>
          <TableRow v-if="filteredLeads.length === 0">
            <TableCell colspan="7" class="h-24 text-center"> No results found. </TableCell>
          </TableRow>
        </TableBody>
      </Table>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Lead {
  id: string
  internalUid: string
  name: string
  email: string
  phone?: string
  createdAt: Date
}

const props = defineProps<{
  leads: Lead[]
  searchQuery: string
}>()
const emit = defineEmits(['update:searchQuery', 'edit-lead', 'view-lead-history'])

const filteredLeads = computed(() => {
  const query = props.searchQuery.toLowerCase()
  if (!query) {
    return props.leads
  }
  return props.leads.filter(
    (lead) =>
      lead.name.toLowerCase().includes(query) ||
      lead.email.toLowerCase().includes(query) ||
      lead.phone?.toLowerCase().includes(query) ||
      lead.id.toLowerCase().includes(query) ||
      lead.internalUid.toLowerCase().includes(query)
  )
})

const formatDate = (date: Date) => {
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  }).format(date)
}
</script>
