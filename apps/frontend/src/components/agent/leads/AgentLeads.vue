<template>
  <BaseCard title="Leads" description="View and manage your collected leads.">
    <AgentLeadsTable
      :leads="leads"
      :search-query="searchQuery"
      @update:search-query="searchQuery = $event"
      @edit-lead="editLead"
      @view-lead-history="viewLeadHistory"
    />
  </BaseCard>

  <AgentLeadsForm v-model:open="isLeadFormOpen" :initialFormData="leadForm" @save="saveLead" />
</template>

<script setup lang="ts">
interface Lead {
  id: string
  internalUid: string
  name: string
  email: string
  phone?: string
  createdAt: Date
}

interface LeadForm {
  name: string
  email: string
  phone?: string
}

const searchQuery = ref('')
const isLeadFormOpen = ref(false)
const editingLead = ref<Lead | null>(null)
const leadForm = ref<LeadForm>({
  name: '',
  email: '',
  phone: '',
})

const leads = ref<Lead[]>([
  {
    id: 'LD001',
    internalUid: 'INT_001',
    name: '<PERSON>',
    email: '<EMAIL>',
    phone: '+1234567890',
    createdAt: new Date(),
  },
  {
    id: 'LD002',
    internalUid: 'INT_002',
    name: '<PERSON>',
    email: '<EMAIL>',
    createdAt: new Date(Date.now() - 86400000),
  },
])

const editLead = (lead: Lead) => {
  editingLead.value = lead
  leadForm.value = {
    name: lead.name,
    email: lead.email,
    phone: lead.phone || '',
  }
  isLeadFormOpen.value = true
}

const saveLead = (updatedFormData: LeadForm) => {
  if (editingLead.value) {
    const index = leads.value.findIndex((l) => l.id === editingLead.value?.id)
    if (index !== -1) {
      leads.value[index] = {
        ...leads.value[index],
        ...updatedFormData,
      }
    }
  }
  editingLead.value = null
}

const viewLeadHistory = (lead: Lead) => {}
</script>
