<template>
  <div class="space-y-4">
    <form @submit.prevent class="space-y-4">
      <BaseFormField label="Status" description="Enable or disable calendar integration">
        <div class="flex items-center gap-2">
          <Switch v-model="enabled" />
          <Badge :variant="enabled ? 'default' : 'secondary'" class="font-medium">
            {{ enabled ? 'Enabled' : 'Disabled' }}
          </Badge>
        </div>
      </BaseFormField>

      <BaseFormField label="Calendar URL" description="The URL for your calendar integration">
        <Input v-model="form.url" type="url" placeholder="Enter your calendar URL" required />
      </BaseFormField>
    </form>
  </div>
</template>

<script setup lang="ts">
defineProps<{
  agentUid: string
}>()

const emit = defineEmits<{
  (e: 'update', data: any): void
}>()

const loading = ref(false)
const enabled = ref(false)

const form = ref({
  url: '',
})

const handleSubmit = async () => {
  try {
    loading.value = true
    emit('update', {
      type: 'calendar',
      config: {
        ...form.value,
        enabled: enabled.value,
      },
    })
  } catch (error) {
    console.error('Failed to save Calendar configuration:', error)
  } finally {
    loading.value = false
  }
}

defineExpose({
  handleSubmit,
})
</script>
