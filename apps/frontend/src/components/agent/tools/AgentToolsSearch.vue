<template>
  <div class="space-y-4">
    <div class="flex items-center justify-between">
      <div>
        <h3 class="text-lg font-medium">Google Search Configuration</h3>
        <p class="text-sm text-muted-foreground">Configure settings for web search capabilities</p>
      </div>
      <div class="flex items-center gap-2">
        <Badge :variant="enabled ? 'default' : 'secondary'" class="font-medium">
          {{ enabled ? 'Enabled' : 'Disabled' }}
        </Badge>
        <Switch v-model="enabled" />
      </div>
    </div>

    <Alert>
      <AlertDescription>
        This tool enables web search capabilities using Google. No additional configuration is
        required.
      </AlertDescription>
    </Alert>

    <div class="flex justify-end space-x-2">
      <Button @click="handleSubmit" :loading="loading">Save</Button>
    </div>
  </div>
</template>

<script setup lang="ts">
defineProps<{
  agentUid: string
}>()

const emit = defineEmits<{
  (e: 'update', data: any): void
}>()

const loading = ref(false)
const enabled = ref(false)

const handleSubmit = async () => {
  try {
    loading.value = true
    emit('update', {
      type: 'webSearch',
      config: {
        enabled: enabled.value,
      },
    })
  } catch (error) {
    console.error('Failed to save Google Search configuration:', error)
  } finally {
    loading.value = false
  }
}
</script>
