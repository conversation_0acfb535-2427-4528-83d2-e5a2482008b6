<template>
  <BaseDialog
    title="Tools"
    description="Manage your agent's tools and integrations."
    icon="Wrench"
    :dialogId="dialogId"
  >
    <div v-if="!selectedTool" class="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3 md:gap-6">
      <Card
        v-for="tool in tools"
        :key="tool.id"
        class="cursor-pointer hover:bg-muted"
        @click="selectTool(tool)"
      >
        <CardHeader>
          <CardTitle class="flex items-center space-x-2">
            <BaseIcon :name="tool.icon" />
            <span>{{ tool.name }}</span>
          </CardTitle>
          <CardDescription>{{ tool.description }}</CardDescription>
        </CardHeader>
        <CardContent>
          <div class="flex items-center justify-between space-x-4">
            <div class="flex items-center space-x-2">
              <div
                class="rounded-full size-2"
                :class="tool.enabled ? 'bg-primary' : 'bg-muted-foreground/25'"
              />
              <Badge :variant="tool.enabled ? 'default' : 'secondary'" class="font-medium">
                {{ tool.enabled ? 'Enabled' : 'Disabled' }}
              </Badge>
            </div>
            <Button variant="outline" size="sm" class="gap-2" @click="selectTool(tool)">
              <BaseIcon name="Settings2" />
              Settings
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>

    <div class="flex flex-col -mx-6 space-y-6" v-else>
      <div class="px-6 pb-4 border-b">
        <div class="flex items-center space-x-2">
          <Button variant="ghost" size="sm" class="gap-2 -ml-2" @click="selectedTool = null">
            <BaseIcon name="ChevronLeft" />
            Back
          </Button>
          <Separator orientation="vertical" class="h-4" />
          <div class="flex items-center space-x-2">
            <BaseIcon :name="selectedTool.icon" class="text-muted-foreground" />
            <h2 class="text-lg font-semibold">{{ selectedTool.name }}</h2>
          </div>
        </div>
        <p class="mt-1 text-sm text-muted-foreground">{{ selectedTool.description }}</p>
      </div>

      <div class="px-6">
        <component
          :is="selectedTool.component"
          :agentUid="agentUid"
          ref="toolComponentRef"
          @update="handleToolUpdate"
        />
      </div>
    </div>

    <template #footer>
      <div class="flex justify-end gap-2">
        <Button variant="outline" @click="handleClose">Cancel</Button>
        <Button v-if="selectedTool" @click="handleSave" :loading="isSaving">Save</Button>
      </div>
    </template>
  </BaseDialog>
</template>

<script setup lang="ts">
const AgentDialogToolAstra = defineAsyncComponent(() => import('./agent-tools-astra.vue'))
const AgentDialogToolCalendar = defineAsyncComponent(() => import('./agent-tools-calendar.vue'))
const AgentDialogToolHubspot = defineAsyncComponent(() => import('./agent-tools-hubspot.vue'))
const AgentDialogToolImage = defineAsyncComponent(() => import('./agent-tools-image.vue'))
const AgentDialogToolPerplexity = defineAsyncComponent(() => import('./agent-tools-perplexity.vue'))
const AgentDialogToolRequestHuman = defineAsyncComponent(
  () => import('./agent-tools-request-human.vue')
)
const AgentDialogToolResolvedChat = defineAsyncComponent(
  () => import('./agent-tools-resolved-chat.vue')
)
const AgentDialogToolGoogleSearch = defineAsyncComponent(() => import('./agent-tools-search.vue'))
const AgentDialogToolShippingTracking = defineAsyncComponent(
  () => import('./agent-tools-shipping-tracking.vue')
)
const AgentDialogToolShopify = defineAsyncComponent(() => import('./agent-tools-shopify.vue'))
const AgentDialogToolWooCommerce = defineAsyncComponent(
  () => import('./agent-tools-woocommerce.vue')
)
const AgentDialogToolZendesk = defineAsyncComponent(() => import('./agent-tools-zendesk.vue'))

const props = defineProps<{
  agentUid: string
  dialogId: string
}>()

const emit = defineEmits<{
  (e: 'close'): void
}>()

interface Tool {
  id: string
  name: string
  description: string
  icon: string
  component: any
  enabled: boolean
}

// Interface for the exposed methods of tool components
interface ToolComponentInstance {
  handleSubmit: () => Promise<void> | void
}

const tools: Tool[] = [
  {
    id: 'astra',
    name: 'Astra',
    description: 'Connect to your Astra database',
    icon: 'Database',
    component: AgentDialogToolAstra,
    enabled: false,
  },
  {
    id: 'calendar',
    name: 'Calendar',
    description: 'Manage calendar events and scheduling',
    icon: 'Calendar',
    component: AgentDialogToolCalendar,
    enabled: false,
  },
  {
    id: 'hubspot',
    name: 'Hubspot',
    description: 'Connect with Hubspot CRM',
    icon: 'Users',
    component: AgentDialogToolHubspot,
    enabled: false,
  },
  {
    id: 'image',
    name: 'Image',
    description: 'Process and analyze images',
    icon: 'ImageIcon',
    component: AgentDialogToolImage,
    enabled: false,
  },
  {
    id: 'perplexity',
    name: 'Perplexity',
    description: 'Advanced AI search and analysis',
    icon: 'Brain',
    component: AgentDialogToolPerplexity,
    enabled: false,
  },
  {
    id: 'request-human',
    name: 'Request Human',
    description: 'Escalate conversations to human agents',
    icon: 'UserCog',
    component: AgentDialogToolRequestHuman,
    enabled: false,
  },
  {
    id: 'resolved-chat',
    name: 'Resolved Chat',
    description: 'Mark conversations as resolved',
    icon: 'MessageSquare',
    component: AgentDialogToolResolvedChat,
    enabled: false,
  },
  {
    id: 'google-search',
    name: 'Google Search',
    description: 'Search the web using Google',
    icon: 'Search',
    component: AgentDialogToolGoogleSearch,
    enabled: false,
  },
  {
    id: 'shipping-tracking',
    name: 'Shipping Tracking',
    description: 'Track shipments and deliveries',
    icon: 'Truck',
    component: AgentDialogToolShippingTracking,
    enabled: false,
  },
  {
    id: 'shopify',
    name: 'Shopify',
    description: 'Integrate with Shopify store',
    icon: 'ShoppingBag',
    component: AgentDialogToolShopify,
    enabled: false,
  },
  {
    id: 'woocommerce',
    name: 'WooCommerce',
    description: 'Connect to WooCommerce store',
    icon: 'ShoppingCart',
    component: AgentDialogToolWooCommerce,
    enabled: false,
  },
  {
    id: 'zendesk',
    name: 'Zendesk',
    description: 'Integrate with Zendesk support',
    icon: 'HeadphonesIcon',
    component: AgentDialogToolZendesk,
    enabled: false,
  },
]

const selectedTool = ref<Tool | null>(null)
const toolComponentRef = ref<ToolComponentInstance | null>(null)
const isSaving = ref(false)

const selectTool = (tool: Tool) => {
  selectedTool.value = tool
}

const handleToolUpdate = async (data: any) => {
  // Handle tool configuration updates
}

const handleClose = () => {
  emit('close')
}

const handleSave = async () => {
  if (toolComponentRef.value?.handleSubmit) {
    isSaving.value = true
    try {
      await toolComponentRef.value.handleSubmit()
      // Optional: Add success handling, e.g., close dialog or show toast
      // handleClose()
    } catch (error) {
      console.error(`Error saving tool ${selectedTool.value?.id}:`, error)
      // Optional: Show error toast to the user
    } finally {
      isSaving.value = false
    }
  } else {
    // Handle case where component might not have handleSubmit or ref is not set
    console.warn('handleSubmit method not found on the tool component instance.')
    // Decide if closing is appropriate here
    // handleClose()
  }
}
</script>
