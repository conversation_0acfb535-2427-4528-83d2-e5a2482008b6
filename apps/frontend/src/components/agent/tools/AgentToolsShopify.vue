<template>
  <div class="space-y-4">
    <form @submit.prevent class="space-y-4">
      <BaseFormField label="Status" description="Enable or disable Shopify integration">
        <div class="flex items-center gap-2">
          <Switch v-model="enabled" />
          <Badge :variant="enabled ? 'default' : 'secondary'" class="font-medium">
            {{ enabled ? 'Enabled' : 'Disabled' }}
          </Badge>
        </div>
      </BaseFormField>

      <BaseFormField label="Store URL" description="Your Shopify store URL">
        <Input
          v-model="form.storeUrl"
          type="url"
          placeholder="https://your-store.myshopify.com"
          required
        />
      </BaseFormField>

      <BaseFormField label="Access Token" description="Your Shopify access token">
        <Input
          v-model="form.accessToken"
          type="password"
          placeholder="Enter access token"
          required
        />
      </BaseFormField>
    </form>
  </div>
</template>

<script setup lang="ts">
defineProps<{
  agentUid: string
}>()

const emit = defineEmits<{
  (e: 'update', data: any): void
}>()

const loading = ref(false)
const enabled = ref(false)

const form = ref({
  storeUrl: '',
  accessToken: '',
})

const handleSubmit = async () => {
  try {
    loading.value = true
    emit('update', {
      type: 'shopify',
      config: {
        ...form.value,
        enabled: enabled.value,
      },
    })
  } catch (error) {
    console.error('Failed to save Shopify configuration:', error)
  } finally {
    loading.value = false
  }
}

defineExpose({
  handleSubmit,
})
</script>
