<template>
  <div class="space-y-4">
    <div class="flex items-center justify-between">
      <div>
        <h3 class="text-lg font-medium">Astra Database Configuration</h3>
        <p class="text-sm text-muted-foreground">
          Configure your Astra database connection settings
        </p>
      </div>
    </div>

    <form @submit.prevent="handleSubmit" class="space-y-4">
      <div class="grid gap-4">
        <div class="grid gap-2">
          <Label for="databaseId">Database ID</Label>
          <Input
            id="databaseId"
            v-model="form.databaseId"
            placeholder="Enter your Astra database ID"
          />
        </div>

        <div class="grid gap-2">
          <Label for="region">Region</Label>
          <Input id="region" v-model="form.region" placeholder="Enter your database region" />
        </div>

        <div class="grid gap-2">
          <Label for="keyspace">Keyspace</Label>
          <Input id="keyspace" v-model="form.keyspace" placeholder="Enter your keyspace name" />
        </div>

        <div class="grid gap-2">
          <Label for="applicationToken">Application Token</Label>
          <Input
            id="applicationToken"
            v-model="form.applicationToken"
            type="password"
            placeholder="Enter your application token"
          />
        </div>
      </div>

      <div class="flex justify-end space-x-2">
        <Button type="submit" :loading="loading">Save</Button>
      </div>
    </form>
  </div>
</template>

<script setup lang="ts">
defineProps<{
  agentUid: string
}>()

const emit = defineEmits<{
  (e: 'update', data: any): void
}>()

const loading = ref(false)

const form = ref({
  databaseId: '',
  region: '',
  keyspace: '',
  applicationToken: '',
})

const handleSubmit = async () => {
  try {
    loading.value = true
    // Here you would typically make an API call to save the configuration
    // For now, we'll just emit the update event with the form data
    emit('update', {
      type: 'astra',
      config: form.value,
    })
  } catch (error) {
    console.error('Failed to save Astra configuration:', error)
  } finally {
    loading.value = false
  }
}
</script>
