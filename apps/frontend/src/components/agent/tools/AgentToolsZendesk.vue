<template>
  <div class="space-y-4">
    <form @submit.prevent class="space-y-4">
      <BaseFormField label="Status" description="Enable or disable Zendesk integration">
        <div class="flex items-center gap-2">
          <Switch v-model="enabled" />
          <Badge :variant="enabled ? 'default' : 'secondary'" class="font-medium">
            {{ enabled ? 'Enabled' : 'Disabled' }}
          </Badge>
        </div>
      </BaseFormField>

      <BaseFormField label="API Key" description="Your Zendesk API key">
        <Input v-model="form.apiKey" type="password" placeholder="Enter API key" required />
      </BaseFormField>

      <BaseFormField label="API Email" description="Your Zendesk API email">
        <Input v-model="form.apiEmail" type="email" placeholder="Enter API email" required />
      </BaseFormField>

      <BaseFormField label="Subdomain" description="Your Zendesk subdomain">
        <Input v-model="form.subdomain" type="text" placeholder="your-subdomain" required />
      </BaseFormField>

      <BaseFormField label="Support Emails" description="Email addresses for support notifications">
        <div class="flex gap-2">
          <Input v-model="newEmail" type="email" placeholder="Add email address" />
          <Button type="button" variant="outline" @click="addEmail">Add</Button>
        </div>
        <div v-if="form.emails.length > 0" class="mt-2">
          <div
            v-for="(email, index) in form.emails"
            :key="index"
            class="flex items-center gap-2 mb-2"
          >
            <span class="text-sm">{{ email }}</span>
            <Button type="button" variant="ghost" size="icon" @click="removeEmail(index)">
              <X class="w-4 h-4" />
            </Button>
          </div>
        </div>
      </BaseFormField>
    </form>
  </div>
</template>

<script setup lang="ts">
defineProps<{
  agentUid: string
}>()

const emit = defineEmits<{
  (e: 'update', data: any): void
}>()

const loading = ref(false)
const enabled = ref(false)
const newEmail = ref('')

const form = ref({
  apiKey: '',
  apiEmail: '',
  subdomain: '',
  emails: [] as string[],
})

const addEmail = () => {
  if (newEmail.value && !form.value.emails.includes(newEmail.value)) {
    form.value.emails.push(newEmail.value)
    newEmail.value = ''
  }
}

const removeEmail = (index: number) => {
  form.value.emails.splice(index, 1)
}

const handleSubmit = async () => {
  try {
    loading.value = true
    emit('update', {
      type: 'zendesk',
      config: {
        ...form.value,
        enabled: enabled.value,
      },
    })
  } catch (error) {
    console.error('Failed to save Zendesk configuration:', error)
  } finally {
    loading.value = false
  }
}

defineExpose({
  handleSubmit,
})
</script>
