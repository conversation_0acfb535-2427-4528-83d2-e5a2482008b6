<template>
  <div class="space-y-4">
    <form @submit.prevent class="space-y-4">
      <BaseFormField label="Status" description="Enable or disable WooCommerce integration">
        <div class="flex items-center gap-2">
          <Switch v-model="enabled" />
          <Badge :variant="enabled ? 'default' : 'secondary'" class="font-medium">
            {{ enabled ? 'Enabled' : 'Disabled' }}
          </Badge>
        </div>
      </BaseFormField>

      <BaseFormField label="Store URL" description="Your WooCommerce store URL">
        <Input v-model="form.storeUrl" type="url" placeholder="https://your-store.com" required />
      </BaseFormField>

      <BaseFormField label="Consumer Key" description="Your WooCommerce consumer key">
        <Input
          v-model="form.consumerKey"
          type="password"
          placeholder="Enter consumer key"
          required
        />
      </BaseFormField>

      <BaseFormField label="Consumer Secret" description="Your WooCommerce consumer secret">
        <Input
          v-model="form.consumerSecret"
          type="password"
          placeholder="Enter consumer secret"
          required
        />
      </BaseFormField>
    </form>
  </div>
</template>

<script setup lang="ts">
defineProps<{
  agentUid: string
}>()

const emit = defineEmits<{
  (e: 'update', data: any): void
}>()

const loading = ref(false)
const enabled = ref(false)

const form = ref({
  storeUrl: '',
  consumerKey: '',
  consumerSecret: '',
})

const handleSubmit = async () => {
  try {
    loading.value = true
    emit('update', {
      type: 'woocommerce',
      config: {
        ...form.value,
        enabled: enabled.value,
      },
    })
  } catch (error) {
    console.error('Failed to save WooCommerce configuration:', error)
  } finally {
    loading.value = false
  }
}

defineExpose({
  handleSubmit,
})
</script>
