<template>
  <div class="space-y-4">
    <div class="flex items-center justify-between">
      <div>
        <h3 class="text-lg font-medium">Request Human Configuration</h3>
        <p class="text-sm text-muted-foreground">Configure settings for human agent escalation</p>
      </div>
      <div class="flex items-center gap-2">
        <Badge :variant="enabled ? 'default' : 'secondary'" class="font-medium">
          {{ enabled ? 'Enabled' : 'Disabled' }}
        </Badge>
        <Switch v-model="enabled" />
      </div>
    </div>

    <Alert>
      <AlertDescription>
        This tool allows the agent to request human intervention when needed. No additional
        configuration is required.
      </AlertDescription>
    </Alert>

    <div class="flex justify-end space-x-2">
      <Button @click="handleSubmit" :loading="loading">Save</Button>
    </div>
  </div>
</template>

<script setup lang="ts">
defineProps<{
  agentUid: string
}>()

const emit = defineEmits<{
  (e: 'update', data: any): void
}>()

const loading = ref(false)
const enabled = ref(false)

const handleSubmit = async () => {
  try {
    loading.value = true
    emit('update', {
      type: 'requestHuman',
      config: {
        enabled: enabled.value,
      },
    })
  } catch (error) {
    console.error('Failed to save Request Human configuration:', error)
  } finally {
    loading.value = false
  }
}
</script>
