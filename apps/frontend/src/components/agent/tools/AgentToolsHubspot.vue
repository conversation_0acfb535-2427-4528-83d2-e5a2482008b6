<template>
  <div class="space-y-4">
    <form @submit.prevent class="space-y-4">
      <BaseFormField label="Status" description="Enable or disable Hubspot integration">
        <div class="flex items-center gap-2">
          <Switch v-model="enabled" />
          <Badge :variant="enabled ? 'default' : 'secondary'" class="font-medium">
            {{ enabled ? 'Enabled' : 'Disabled' }}
          </Badge>
        </div>
      </BaseFormField>

      <BaseFormField label="API Key" description="Your Hubspot API key">
        <Input v-model="form.apiKey" type="password" placeholder="Enter your API key" required />
      </BaseFormField>

      <BaseFormField label="Portal ID" description="Your Hubspot portal ID">
        <Input v-model="form.portalId" type="text" placeholder="Enter your portal ID" required />
      </BaseFormField>
    </form>
  </div>
</template>

<script setup lang="ts">
defineProps<{
  agentUid: string
}>()

const emit = defineEmits<{
  (e: 'update', data: any): void
}>()

const loading = ref(false)
const enabled = ref(false)

const form = ref({
  apiKey: '',
  portalId: '',
})

const handleSubmit = async () => {
  try {
    loading.value = true
    emit('update', {
      type: 'hubspot',
      config: {
        ...form.value,
        enabled: enabled.value,
      },
    })
  } catch (error) {
    console.error('Failed to save Hubspot configuration:', error)
  } finally {
    loading.value = false
  }
}

defineExpose({
  handleSubmit,
})
</script>
