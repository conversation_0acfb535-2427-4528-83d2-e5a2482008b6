<template>
  <div class="space-y-4">
    <form @submit.prevent class="space-y-4">
      <BaseFormField label="Status" description="Enable or disable image processing">
        <div class="flex items-center gap-2">
          <Switch v-model="enabled" />
          <Badge :variant="enabled ? 'default' : 'secondary'" class="font-medium">
            {{ enabled ? 'Enabled' : 'Disabled' }}
          </Badge>
        </div>
      </BaseFormField>
    </form>

    <Alert>
      <AlertDescription>
        This tool enables image processing and analysis capabilities. No additional configuration is
        required.
      </AlertDescription>
    </Alert>
  </div>
</template>

<script setup lang="ts">
defineProps<{
  agentUid: string
}>()

const emit = defineEmits<{
  (e: 'update', data: any): void
}>()

const loading = ref(false)
const enabled = ref(false)

const handleSubmit = async () => {
  try {
    loading.value = true
    emit('update', {
      type: 'image',
      config: {
        enabled: enabled.value,
      },
    })
  } catch (error) {
    console.error('Failed to save Image Processing configuration:', error)
  } finally {
    loading.value = false
  }
}

defineExpose({
  handleSubmit,
})
</script>
