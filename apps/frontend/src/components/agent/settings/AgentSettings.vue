<template>
  <BaseDialog
    title="Settings"
    description="Configure your agent's behavior and capabilities."
    :icon="ICONS.AGENTS"
    :dialogId="dialogId"
  >
    <BaseTabs
      default-value="general"
      :tabs="[
        { value: 'general', label: 'General', icon: 'Setting<PERSON>' },
        { value: 'behavior', label: 'AI Settings', icon: 'Brain' },
        { value: 'advanced', label: 'Advanced', icon: 'Code2' },
      ]"
    >
      <!-- General Settings -->
      <TabsContent value="general" class="w-full mt-0">
        <BaseCard title="Basic Settings" description="Configure your agent's core settings">
          <BaseFormField label="Name" description="Give your agent a unique name">
            <Input v-model="settings.name" placeholder="Agent name" />
          </BaseFormField>

          <BaseFormField label="Model" description="Select the AI model to power your agent">
            <Select v-model="settings.model">
              <SelectTrigger>
                <SelectValue placeholder="Select model" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="gpt-4">GPT-4 (Recommended)</SelectItem>
                <SelectItem value="gpt-3.5">GPT-3.5 Turbo</SelectItem>
                <SelectItem value="claude-3">Claude 3 Opus</SelectItem>
              </SelectContent>
            </Select>
          </BaseFormField>

          <BaseFormField label="Timezone" description="Select the agent's primary timezone">
            <Select v-model="settings.timezone">
              <SelectTrigger>
                <SelectValue placeholder="Select timezone" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem v-for="tz in timezones" :key="tz.value" :value="tz.value">
                  {{ tz.label }}
                </SelectItem>
              </SelectContent>
            </Select>
          </BaseFormField>

          <BaseFormField label="Voice" description="Choose how your agent sounds">
            <div class="flex flex-col items-start gap-2 sm:flex-row sm:items-center">
              <Select v-model="settings.voice" class="flex-1 w-full">
                <SelectTrigger>
                  <SelectValue placeholder="Select voice" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem v-for="voice in voices" :key="voice.id" :value="voice.id">
                    {{ voice.name }} ({{ voice.description }})
                  </SelectItem>
                </SelectContent>
              </Select>
              <Button
                variant="outline"
                size="sm"
                class="w-full mt-1 sm:w-auto sm:mt-0"
                :disabled="!settings.voice || isPlayingVoice"
                @click="playVoiceSample"
              >
                <BaseIcon name="Volume2" v-if="!isPlayingVoice" class="w-4 h-4 mr-2" />
                <BaseIcon name="Loader2" v-else class="w-4 h-4 mr-2 animate-spin" />
                Preview Voice
              </Button>
            </div>
          </BaseFormField>
        </BaseCard>
      </TabsContent>

      <!-- Behavior Settings -->
      <TabsContent value="behavior" class="w-full mt-0">
        <!-- Response Behavior -->
        <BaseCard
          title="Response Behavior"
          description="Configure how your agent responds to questions"
        >
          <!-- Knowledge Boundary -->
          <BaseFormField
            label="Knowledge Boundary"
            description="Restrict responses to only known information"
          >
            <Select v-model="settings.knowledgeBoundary">
              <SelectTrigger>
                <SelectValue placeholder="Select boundary" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="strict">Answer only if known</SelectItem>
                <SelectItem value="flexible">Answer all questions</SelectItem>
              </SelectContent>
            </Select>
          </BaseFormField>

          <!-- Creativity Level -->
          <BaseFormField
            label="Creativity Level"
            description="Adjust how creative the responses should be"
          >
            <div class="space-y-4">
              <div class="flex justify-between">
                <div class="text-sm">{{ settings.creativity[0].toFixed(1) }}</div>
              </div>
              <Slider v-model="settings.creativity" :min="0" :max="2" :step="0.1" class="w-full" />
              <div class="flex justify-between text-sm text-muted-foreground">
                <span>Precise</span>
                <span>Balanced</span>
                <span>Creative</span>
              </div>
            </div>
          </BaseFormField>
        </BaseCard>

        <!-- Personality -->
        <BaseCard title="Personality" description="Define your agent's character and tone">
          <BaseFormField
            label="Personality"
            description="Define how your agent interacts with users"
          >
            <Select v-model="settings.personaTemplate" class="mb-2">
              <SelectTrigger>
                <SelectValue placeholder="Select a pre-defined persona" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="professional">Professional Assistant</SelectItem>
                <SelectItem value="friendly">Friendly Helper</SelectItem>
                <SelectItem value="expert">Domain Expert</SelectItem>
                <SelectItem value="casual">Casual Conversationalist</SelectItem>
                <SelectItem value="custom">Custom Persona</SelectItem>
              </SelectContent>
            </Select>

            <Textarea
              v-model="settings.persona"
              placeholder="Describe your agent's personality and behavior..."
              :rows="4"
            />
          </BaseFormField>
        </BaseCard>

        <!-- Features -->
        <BaseCard title="Features" description="Enable or disable agent capabilities">
          <!-- Smart Suggestions -->
          <BaseFormField
            label="Smart Suggestions"
            description="Enable contextual response suggestions"
          >
            <Switch v-model="settings.smartSuggestions" />
          </BaseFormField>

          <!-- Vision Mode -->
          <BaseFormField label="Vision Mode" description="Enable image analysis capabilities">
            <Switch v-model="settings.visionMode" />
          </BaseFormField>
        </BaseCard>
      </TabsContent>

      <!-- Advanced Settings -->
      <TabsContent value="advanced" class="w-full mt-0">
        <BaseCard title="Security" description="Configure security settings for your agent">
          <BaseFormField
            label="Security PIN"
            description="6-digit PIN to protect sensitive operations"
          >
            <PinInput id="pin-input" v-model="settings.securityPin" placeholder="○">
              <PinInputGroup class="flex-wrap gap-1">
                <template v-for="(id, index) in 6" :key="id">
                  <PinInputInput class="border rounded-md" :index="index" />
                  <template v-if="index !== 5">
                    <PinInputSeparator />
                  </template>
                </template>
              </PinInputGroup>
            </PinInput>
          </BaseFormField>
        </BaseCard>

        <!-- Replace Custom Code section -->
        <BaseCard title="Tracking" description="Configure tracking for your agent">
          <BaseFormField
            label="Google Tag Manager ID"
            description="Enter your GTM ID (e.g., GTM-XXXXXXX)"
          >
            <Input v-model="settings.gtmId" placeholder="GTM-XXXXXXX" class="font-mono text-sm" />
          </BaseFormField>
        </BaseCard>
      </TabsContent>
    </BaseTabs>

    <template #footer>
      <div class="flex items-end justify-end gap-2 mt-3">
        <Button variant="outline" @click="onClose"> Cancel </Button>
        <Button type="submit" @click="onSave"> Save </Button>
      </div>
    </template>
  </BaseDialog>
</template>

<script setup lang="ts">
import { useConfirmDialog } from '@/composables/use-confirm-dialog'
import timezonesData from '@/assets/timezones.json'
import VoicesObject from '@/assets/voices.json'
import { ICONS } from '@/constants'

const props = defineProps<{
  dialogId: string
  agentUid: string
}>()
const { toast } = useToast()
const { openConfirmDialog } = useConfirmDialog()
const timezones = ref(timezonesData)
const voices = ref(VoicesObject)
const isPlayingVoice = ref(false)
const isLoading = ref(false)
const isSaving = ref(false)
const hasUnsavedChanges = ref(false)
const audioPlayer = ref<HTMLAudioElement | null>(null)

const settings = ref({
  name: '',
  model: 'gpt-4',
  timezone: 'America/New_York',
  voice: null,
  knowledgeBoundary: 'flexible',
  creativity: [1.0],
  personaTemplate: 'professional',
  persona: '',
  smartSuggestions: true,
  visionMode: false,
  securityPin: '',
  gtmId: '',
})

const originalSettings = ref({})

onMounted(async () => {
  try {
    isLoading.value = true

    if (!settings.value.timezone) {
      settings.value.timezone = 'America/New_York'
    }
    // Ensure other defaults if needed
    if (!settings.value.creativity) settings.value.creativity = [1.0]
    if (settings.value.securityPin === undefined) settings.value.securityPin = ''
    if (settings.value.gtmId === undefined) settings.value.gtmId = ''

    originalSettings.value = JSON.parse(JSON.stringify(settings.value)) // Track original state
    audioPlayer.value = new Audio()
    audioPlayer.value.addEventListener('ended', () => {
      isPlayingVoice.value = false
    })
  } catch (error) {
    toast({
      title: 'Error loading settings',
      description: 'Failed to load agent settings. Please try again.',
      variant: 'destructive',
    })
  } finally {
    isLoading.value = false
  }
})

// Watch for changes
watch(
  () => settings.value,
  (newVal) => {
    hasUnsavedChanges.value = JSON.stringify(newVal) !== JSON.stringify(originalSettings.value)
  },
  { deep: true }
)

// Reverted onSave function
const onSave = async () => {
  try {
    isSaving.value = true
    // TODO: Implement API call to save settings

    // Example: await saveAgentSettingsAPI(props.agentUid, settings.value);

    // Simulate API delay
    await new Promise((resolve) => setTimeout(resolve, 1000))

    toast({
      title: 'Settings saved (simulated)',
      description: 'Your agent settings have been updated successfully.',
    })
    originalSettings.value = JSON.parse(JSON.stringify(settings.value)) // Update original state
    hasUnsavedChanges.value = false // Reset unsaved changes flag
  } catch (error) {
    console.error('Failed to save agent settings:', error)
    toast({
      title: 'Error saving settings',
      description: 'Failed to save agent settings. Please try again.',
      variant: 'destructive',
    })
  } finally {
    isSaving.value = false
  }
}

const playVoiceSample = async () => {
  if (!settings.value.voice || !audioPlayer.value) {
    return
  }

  const selectedVoice = voices.value.find((v) => v.id === settings.value.voice)
  if (!selectedVoice || !selectedVoice.sampleUrl) {
    toast({ title: 'No sample available', variant: 'destructive' })
    return
  }

  try {
    isPlayingVoice.value = true
    audioPlayer.value.src = selectedVoice.sampleUrl
    await audioPlayer.value.play()
  } catch (error) {
    console.error('Error playing voice sample:', error)
    toast({ title: 'Error playing sample', variant: 'destructive' })
    isPlayingVoice.value = false
  }
}

watch(
  () => settings.value.personaTemplate,
  (newTemplate) => {
    if (newTemplate && newTemplate !== 'custom') {
      settings.value.persona = getPersonaTemplate(newTemplate)
    }
  }
)

const getPersonaTemplate = (template: string): string => {
  switch (template) {
    case 'professional':
      return 'Act as a professional assistant. Be polite, clear, and concise. Avoid slang and overly casual language.'
    case 'friendly':
      return 'Be a friendly and helpful assistant. Use a warm and approachable tone. Feel free to use emojis occasionally.'
    case 'expert':
      return 'Respond as a domain expert. Provide detailed and accurate information. Maintain a formal and knowledgeable tone.'
    case 'casual':
      return 'Engage in casual conversation. Be relaxed and informal. Use everyday language.'
    default:
      return ''
  }
}
</script>

<style scoped>
@media (max-width: 640px) {
  :deep(.form-field) {
    margin-bottom: 1.5rem;
  }

  :deep(.card) {
    padding: 1rem;
    margin-bottom: 1rem;
  }
}
</style>
