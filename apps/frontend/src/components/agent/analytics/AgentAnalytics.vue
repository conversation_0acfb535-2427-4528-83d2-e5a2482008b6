<script setup lang="ts">
const emit = defineEmits<{
  close: []
}>()

// Your component logic here
</script>

<template>
  <div class="w-full">
    <DialogHeader>
      <h2 class="text-lg font-medium">Analytics</h2>
      <p class="text-sm text-muted-foreground">View your chat analytics and insights</p>
    </DialogHeader>

    <!-- Your component content here -->

    <DialogFooter>
      <button @click="emit('close')">Close</button>
    </DialogFooter>
  </div>
</template>
