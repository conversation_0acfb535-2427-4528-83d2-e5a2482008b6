<template>
  <BaseTable
    :heads="tableHeads"
    :rows="props.feedbacks"
    :actions="['edit', 'delete']"
    empty-message="No results found."
    @edit="(row) => emit('view-details', row)"
    @delete="(row) => emit('delete-feedback', row)"
  >
    <template #cell-date="{ value }">
      {{ formatDate(value) }}
    </template>
    <template #cell-status="{ value }">
      <Badge :variant="value === 'positive' ? 'outline' : 'destructive'">
        {{ value }}
      </Badge>
    </template>
    <template #cell-message="{ value }">
      <span class="max-w-[300px] truncate">{{ truncateText(value, 150) }}</span>
    </template>
    <template #cell-qaStatus="{ value }">
      <Badge :variant="value === 'created' ? 'outline' : 'secondary'">
        {{ value === 'created' ? 'Q&A Created' : 'Pending' }}
      </Badge>
    </template>
    <template #actions="{ row }">
      <Button variant="ghost" size="icon" @click.stop="emit('view-details', row)">
        <Eye class="w-4 h-4 mr-2" />
      </Button>
      <Button
        v-if="row.qaStatus !== 'created'"
        variant="ghost"
        size="icon"
        @click.stop="emit('create-qa', row)"
      >
        <Plus class="w-4 h-4 mr-2" />
      </Button>
      <Button variant="ghost" size="icon" @click.stop="emit('delete-feedback', row)">
        <Trash class="w-4 h-4 mr-2" />
      </Button>
    </template>
  </BaseTable>
</template>

<script setup lang="ts">
import BaseTable from '@/components/base/table/BaseTable.vue'
import { type Feedback } from './types'

const props = defineProps<{
  feedbacks: Feedback[]
}>()
const emit = defineEmits(['view-details', 'create-qa', 'delete-feedback'])

const tableHeads = [
  { label: 'Date', key: 'createdAt', slot: true },
  { label: 'Status', key: 'status', slot: true },
  { label: 'Message', key: 'message', slot: true },
  { label: 'Q&A Status', key: 'qaStatus', slot: true },
]

const formatDate = (date: Date | string): string => {
  const dateObj = typeof date === 'string' ? new Date(date) : date
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  }).format(dateObj)
}

const truncateText = (text: string, length: number): string => {
  if (!text) return ''

  return text.length > length ? text.substring(0, length) + '...' : text
}
</script>
