<template>
  <TabsContent value="messages" class="w-full mt-0 overflow-y-auto">
    <BaseCard title="Chat Input" description="Customize the message input field">
      <BaseFormField
        label="Input Placeholder"
        description="Text shown in the message input before typing"
      >
        <Input v-model="localPlaceholder" type="text" placeholder="Ask a question..." />
      </BaseFormField>
    </BaseCard>

    <!-- Openers -->
    <BaseCard
      title="Welcome Messages"
      description="Set up automatic messages to start conversations"
    >
      <div v-for="(opener, index) in localOpeners" :key="index">
        <BaseFormField
          :label="`Opener ${index + 1}`"
          description="Add a welcome message to start conversations"
          class="mb-4"
        >
          <div class="flex items-center gap-2">
            <Textarea
              v-model="localOpeners[index]"
              :placeholder="`Enter opener message ${index + 1}`"
            />
            <div class="flex flex-col items-center gap-1">
              <Button
                variant="ghost"
                size="icon"
                class="w-7 h-7"
                :disabled="index === 0"
                @click="moveOpener(index, 'up')"
              >
                <ChevronUpIcon class="w-4 h-4" />
              </Button>
              <Button
                variant="ghost"
                size="icon"
                class="w-7 h-7"
                :disabled="index === localOpeners.length - 1"
                @click="moveOpener(index, 'down')"
              >
                <ChevronDownIcon class="w-4 h-4" />
              </Button>
              <Button variant="ghost" size="icon" class="w-7 h-7" @click="removeOpener(index)">
                <TrashIcon class="w-4 h-4" />
              </Button>
            </div>
          </div>
        </BaseFormField>
      </div>

      <Button
        variant="outline"
        class="w-full mt-2"
        :disabled="localOpeners.length >= 5"
        @click="addOpener"
      >
        Add Opener
      </Button>
    </BaseCard>

    <!-- Suggestions -->
    <BaseCard title="Quick Replies" description="Configure suggested responses for visitors">
      <div v-for="(suggestion, index) in localSuggestions" :key="index">
        <BaseFormField
          :label="`Suggestion ${index + 1}`"
          description="Quick response options for visitors to choose from"
          class="mb-4"
        >
          <div class="flex items-center gap-2">
            <Input
              v-model="localSuggestions[index]"
              :placeholder="`Enter suggestion ${index + 1}`"
            />
            <div class="flex flex-col items-center gap-1">
              <Button
                variant="ghost"
                size="icon"
                class="w-7 h-7"
                :disabled="index === 0"
                @click="moveSuggestion(index, 'up')"
              >
                <ChevronUpIcon class="w-4 h-4" />
              </Button>
              <Button
                variant="ghost"
                size="icon"
                class="w-7 h-7"
                :disabled="index === localSuggestions.length - 1"
                @click="moveSuggestion(index, 'down')"
              >
                <ChevronDownIcon class="w-4 h-4" />
              </Button>
              <Button variant="ghost" size="icon" class="w-7 h-7" @click="removeSuggestion(index)">
                <TrashIcon class="w-4 h-4" />
              </Button>
            </div>
          </div>
        </BaseFormField>
      </div>

      <Button
        variant="outline"
        class="w-full mt-2"
        :disabled="localSuggestions.length >= 5"
        @click="addSuggestion"
      >
        Add Suggestion
      </Button>
    </BaseCard>

    <!-- Bubble Notifications -->
    <BaseCard
      title="Attention Messages"
      description="Configure messages to attract visitor attention"
    >
      <div v-for="(notification, index) in localNotifications" :key="index">
        <BaseFormField
          :label="`Notification ${index + 1}`"
          description="Messages that appear in the floating button to attract attention"
          class="mb-4"
        >
          <div class="flex items-center gap-2">
            <Input
              v-model="localNotifications[index]"
              :placeholder="`Enter notification ${index + 1}`"
            />
            <div class="flex flex-col items-center gap-1">
              <Button
                variant="ghost"
                size="icon"
                class="w-7 h-7"
                :disabled="index === 0"
                @click="moveNotification(index, 'up')"
              >
                <ChevronUpIcon class="w-4 h-4" />
              </Button>
              <Button
                variant="ghost"
                size="icon"
                class="w-7 h-7"
                :disabled="index === localNotifications.length - 1"
                @click="moveNotification(index, 'down')"
              >
                <ChevronDownIcon class="w-4 h-4" />
              </Button>
              <Button
                variant="ghost"
                size="icon"
                class="w-7 h-7"
                @click="removeNotification(index)"
              >
                <TrashIcon class="w-4 h-4" />
              </Button>
            </div>
          </div>
        </BaseFormField>
      </div>

      <Button
        variant="outline"
        class="w-full mt-2"
        :disabled="localNotifications.length >= 5"
        @click="addNotification"
      >
        Add Notification
      </Button>
    </BaseCard>
  </TabsContent>
</template>

<script setup lang="ts">
import { ChevronUpIcon, ChevronDownIcon, TrashIcon } from '@radix-icons/vue'

const props = defineProps<{
  placeholder: string
  openers: string[]
  suggestions: string[]
  notifications: string[]
}>()
const emit = defineEmits([
  'update:placeholder',
  'update:openers',
  'update:suggestions',
  'update:notifications',
])

const { placeholder, openers, suggestions, notifications } = toRefs(props)

const localPlaceholder = ref(placeholder.value)
const localOpeners = reactive([...openers.value])
const localSuggestions = reactive([...suggestions.value])
const localNotifications = reactive([...notifications.value])

watch(localPlaceholder, (newValue) => {
  emit('update:placeholder', newValue)
})

watch(localOpeners, (newValue) => {
  emit('update:openers', newValue)
})

watch(localSuggestions, (newValue) => {
  emit('update:suggestions', newValue)
})

watch(localNotifications, (newValue) => {
  emit('update:notifications', newValue)
})

watch(placeholder, (newValue) => {
  localPlaceholder.value = newValue
})

watch(openers, (newValue) => {
  localOpeners.splice(0, localOpeners.length, ...newValue)
})

watch(suggestions, (newValue) => {
  localSuggestions.splice(0, localSuggestions.length, ...newValue)
})

watch(notifications, (newValue) => {
  localNotifications.splice(0, localNotifications.length, ...newValue)
})

// Opener Methods
const addOpener = () => {
  if (localOpeners.length < 5) {
    localOpeners.push('')
  }
}

const removeOpener = (index: number) => {
  localOpeners.splice(index, 1)
}

const moveOpener = (index: number, direction: 'up' | 'down') => {
  const newIndex = direction === 'up' ? index - 1 : index + 1

  if (newIndex >= 0 && newIndex < localOpeners.length) {
    const item = localOpeners.splice(index, 1)[0]
    localOpeners.splice(newIndex, 0, item)
  }
}

// Suggestion Methods
const addSuggestion = () => {
  if (localSuggestions.length < 5) {
    localSuggestions.push('')
  }
}

const removeSuggestion = (index: number) => {
  localSuggestions.splice(index, 1)
}

const moveSuggestion = (index: number, direction: 'up' | 'down') => {
  const newIndex = direction === 'up' ? index - 1 : index + 1

  if (newIndex >= 0 && newIndex < localSuggestions.length) {
    const item = localSuggestions.splice(index, 1)[0]
    localSuggestions.splice(newIndex, 0, item)
  }
}

// Notification Methods
const addNotification = () => {
  if (localNotifications.length < 5) {
    localNotifications.push('')
  }
}

const removeNotification = (index: number) => {
  localNotifications.splice(index, 1)
}

const moveNotification = (index: number, direction: 'up' | 'down') => {
  const newIndex = direction === 'up' ? index - 1 : index + 1

  if (newIndex >= 0 && newIndex < localNotifications.length) {
    const item = localNotifications.splice(index, 1)[0]
    localNotifications.splice(newIndex, 0, item)
  }
}
</script>
