<template>
  <TabsContent value="branding" class="w-full mt-0 overflow-y-auto">
    <BaseCard title="Visual Identity" description="Customize your agent's logo and avatar">
      <div class="grid gap-6 sm:grid-cols-2">
        <!-- Logo Upload -->
        <BaseFormField
          label="Company Logo"
          description="Your logo will appear in the agent's header"
        >
          <div
            class="relative flex flex-col items-center justify-center h-32 gap-2 p-2 border-2 border-dashed rounded-lg cursor-pointer group border-muted-foreground/25 hover:border-muted-foreground/50"
            @click="triggerLogoUpload"
          >
            <input
              ref="logoInputRef"
              type="file"
              accept="image/*"
              class="hidden"
              @change="handleLogoUpload"
            />
            <img
              v-if="localLogo"
              :src="localLogo"
              alt="Logo"
              class="object-contain w-full h-full"
            />
            <template v-else>
              <UploadIcon
                class="w-8 h-8 text-muted-foreground/50 group-hover:text-muted-foreground/80"
              />
              <p class="text-sm text-muted-foreground">Click to upload</p>
              <p class="text-xs text-muted-foreground/75">PNG, JPG or SVG (100x100px)</p>
            </template>
          </div>
        </BaseFormField>

        <!-- Agent Avatar -->
        <BaseFormField label="Agent Avatar" description="Choose how your agent appears to visitors">
          <div
            :class="[
              'relative flex flex-col items-center justify-center h-32 gap-2 p-2 border-2 border-dashed rounded-lg group border-muted-foreground/25',
              localSelectedAvatar === null
                ? 'cursor-pointer hover:border-muted-foreground/50'
                : 'opacity-50 cursor-not-allowed',
            ]"
            @click="localSelectedAvatar === null && triggerAvatarUpload()"
          >
            <input
              ref="avatarInputRef"
              type="file"
              accept="image/*"
              class="hidden"
              :disabled="localSelectedAvatar !== null"
              @change="handleAvatarUpload"
            />
            <img
              v-if="localAvatar"
              :src="localAvatar"
              alt="Agent Avatar"
              class="object-contain w-full h-full"
            />
            <template v-else>
              <UploadIcon
                :class="[
                  'w-8 h-8 text-muted-foreground/50',
                  localSelectedAvatar === null && 'group-hover:text-muted-foreground/80',
                ]"
              />
              <p class="text-sm text-muted-foreground">
                {{
                  localSelectedAvatar === null
                    ? 'Click to upload custom'
                    : 'Using pre-designed avatar'
                }}
              </p>
              <p v-if="localSelectedAvatar === null" class="text-xs text-muted-foreground/75">
                PNG, JPG or SVG (100x100px)
              </p>
            </template>
          </div>
        </BaseFormField>
      </div>

      <!-- Suggested AI Avatars -->
      <BaseFormField
        label="Pre-designed Avatars"
        description="Select from our collection of professional avatars"
        class="mt-6"
      >
        <div class="grid grid-cols-4 gap-2 mt-3 sm:grid-cols-6 md:grid-cols-8 sm:gap-3">
          <Button
            v-for="i in 27"
            :key="i"
            variant="outline"
            class="relative w-12 h-12 p-0 rounded-full group hover:border-primary"
            :class="{ 'border-2 border-primary': localSelectedAvatar === i }"
            @click="selectAvatar(i)"
          >
            <img
              :src="`https://cdn.insertchat.com/public/avatars/assistant-${i}.svg`"
              alt="Agent Avatar"
              class="object-cover w-full h-full rounded-full"
            />
            <div
              v-if="localSelectedAvatar === i"
              class="absolute inset-0 flex items-center justify-center rounded-full bg-primary/20"
            >
              <CheckIcon class="w-4 h-4 text-primary" />
            </div>
          </Button>
        </div>
      </BaseFormField>
    </BaseCard>
  </TabsContent>
</template>

<script setup lang="ts">
import { CheckIcon, UploadIcon } from '@radix-icons/vue'

const props = defineProps<{
  logo: string | null
  avatar: string | null
  selectedAvatar: number | null
}>()
const emit = defineEmits(['update:logo', 'update:avatar', 'update:selectedAvatar'])

const { logo, avatar, selectedAvatar } = toRefs(props)

const localLogo = ref(logo.value)
const localAvatar = ref(avatar.value)
const localSelectedAvatar = ref(selectedAvatar.value)
const logoInputRef = ref<HTMLInputElement | null>(null)
const avatarInputRef = ref<HTMLInputElement | null>(null)

watch(localLogo, (newValue) => {
  emit('update:logo', newValue)
})

watch(localAvatar, (newValue) => {
  emit('update:avatar', newValue)
})

watch(localSelectedAvatar, (newValue) => {
  emit('update:selectedAvatar', newValue)
})

watch(logo, (newValue) => {
  localLogo.value = newValue
})

watch(avatar, (newValue) => {
  localAvatar.value = newValue
})

watch(selectedAvatar, (newValue) => {
  localSelectedAvatar.value = newValue
})

const triggerLogoUpload = () => {
  logoInputRef.value?.click()
}

const handleLogoUpload = (event: Event) => {
  const file = (event.target as HTMLInputElement).files?.[0]

  if (file) {
    const reader = new FileReader()

    reader.onload = (e) => {
      localLogo.value = e.target?.result as string
    }

    reader.readAsDataURL(file)
  }
}

const triggerAvatarUpload = () => {
  avatarInputRef.value?.click()
}

const handleAvatarUpload = (event: Event) => {
  const file = (event.target as HTMLInputElement).files?.[0]

  if (file) {
    const reader = new FileReader()

    reader.onload = (e) => {
      localAvatar.value = e.target?.result as string
      localSelectedAvatar.value = null
    }

    reader.readAsDataURL(file)
  }
}

const selectAvatar = (index: number) => {
  if (localSelectedAvatar.value === index) {
    localSelectedAvatar.value = null
  } else {
    localSelectedAvatar.value = index
    localAvatar.value = null
  }
}
</script>
