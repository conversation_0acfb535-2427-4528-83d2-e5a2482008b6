<template>
  <TabsContent value="display" class="w-full mt-0 overflow-y-auto">
    <BaseCard
      title="Launcher Settings"
      description="Configure how the chat launcher appears on your website"
    >
      <div class="grid gap-6 sm:grid-cols-2">
        <!-- Position -->
        <BaseFormField label="Position" description="Choose where your agent appears on the page">
          <Select v-model="localPosition">
            <SelectTrigger>
              <SelectValue placeholder="Select position" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="bottom-right">Bottom Right (Recommended)</SelectItem>
              <SelectItem value="bottom-left">Bottom Left</SelectItem>
            </SelectContent>
          </Select>
        </BaseFormField>

        <!-- Button Color -->
        <BaseFormField
          label="Button Color"
          description="Pick a color that matches your website's design"
        >
          <BaseColorPicker v-model="localBubbleColor" />
        </BaseFormField>
      </div>

      <!-- Auto Open -->
      <BaseFormField
        label="Auto Open"
        description="Automatically open the chat window when the page loads"
        class="mt-6"
      >
        <Switch v-model="localAutoOpen" />
      </BaseFormField>
    </BaseCard>

    <!-- Message Colors -->
    <BaseCard title="Chat Appearance" description="Customize the look of your chat messages">
      <div class="grid gap-6 sm:grid-cols-2">
        <!-- User Message Color -->
        <BaseFormField label="Visitor Messages" description="Background color for visitor messages">
          <BaseColorPicker v-model="localUserMsgColor" />
        </BaseFormField>

        <!-- Agent Message Color -->
        <BaseFormField label="Agent Messages" description="Background color for agent responses">
          <BaseColorPicker v-model="localAiMsgColor" />
        </BaseFormField>
      </div>
    </BaseCard>

    <!-- Buttons -->
    <BaseCard title="Chat Features" description="Choose which features to show in your chat window">
      <div class="grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3">
        <BaseFormField label="History" description="Allow users to view chat history">
          <Switch v-model="localButtons.history" />
        </BaseFormField>
        <BaseFormField label="Share" description="Enable conversation sharing">
          <Switch v-model="localButtons.share" />
        </BaseFormField>
        <BaseFormField label="Feedback" description="Allow users to rate responses">
          <Switch v-model="localButtons.feedback" />
        </BaseFormField>
        <BaseFormField label="Copy" description="Enable copying messages">
          <Switch v-model="localButtons.copy" />
        </BaseFormField>
      </div>
    </BaseCard>
  </TabsContent>
</template>

<script setup lang="ts">
interface Buttons {
  history: boolean
  share: boolean
  feedback: boolean
  copy: boolean
}

const props = defineProps<{
  autoOpen: boolean
  position: string
  bubbleColor: string
  userMsgColor: string
  aiMsgColor: string
  buttons: Buttons
}>()
const emit = defineEmits([
  'update:autoOpen',
  'update:position',
  'update:bubbleColor',
  'update:userMsgColor',
  'update:aiMsgColor',
  'update:buttons',
])

const { autoOpen, position, bubbleColor, userMsgColor, aiMsgColor, buttons } = toRefs(props)

const localAutoOpen = ref(autoOpen.value)
const localPosition = ref(position.value)
const localBubbleColor = ref(bubbleColor.value)
const localUserMsgColor = ref(userMsgColor.value)
const localAiMsgColor = ref(aiMsgColor.value)
const localButtons = reactive({ ...buttons.value })

watch(localAutoOpen, (newValue) => {
  emit('update:autoOpen', newValue)
})

watch(localPosition, (newValue) => {
  emit('update:position', newValue)
})

watch(localBubbleColor, (newValue) => {
  emit('update:bubbleColor', newValue)
})

watch(localUserMsgColor, (newValue) => {
  emit('update:userMsgColor', newValue)
})

watch(localAiMsgColor, (newValue) => {
  emit('update:aiMsgColor', newValue)
})

watch(localButtons, (newValue) => {
  emit('update:buttons', newValue)
})

watch(autoOpen, (newValue) => {
  localAutoOpen.value = newValue
})

watch(position, (newValue) => {
  localPosition.value = newValue
})

watch(bubbleColor, (newValue) => {
  localBubbleColor.value = newValue
})

watch(userMsgColor, (newValue) => {
  localUserMsgColor.value = newValue
})

watch(aiMsgColor, (newValue) => {
  localAiMsgColor.value = newValue
})

watch(
  buttons,
  (newValue) => {
    Object.assign(localButtons, newValue)
  },
  { deep: true }
)
</script>
