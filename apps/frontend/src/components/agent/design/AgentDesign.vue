<template>
  <BaseDialog
    title="Design Your Agent"
    description="Customize your agent's appearance to match your brand"
    icon="Palette"
    :dialogId="dialogId"
  >
    <BaseTabs
      default-value="branding"
      :tabs="[
        { value: 'branding', label: 'Branding', icon: 'Palette' },
        { value: 'display', label: 'Display', icon: 'Layout' },
        { value: 'messages', label: 'Messages', icon: 'MessageSquare' },
      ]"
    >
      <AgentDesignBranding
        v-model:logo="logo"
        v-model:avatar="avatar"
        v-model:selectedAvatar="selectedAvatar"
      />

      <AgentDesignDisplay
        v-model:autoOpen="autoOpen"
        v-model:position="position"
        v-model:bubbleColor="bubbleColor"
        v-model:userMsgColor="userMsgColor"
        v-model:aiMsgColor="aiMsgColor"
        v-model:buttons="buttons"
      />

      <AgentDesignMessages
        v-model:placeholder="placeholder"
        v-model:openers="openers"
        v-model:suggestions="suggestions"
        v-model:notifications="notifications"
      />
    </BaseTabs>

    <template #footer>
      <div class="flex justify-end gap-2">
        <Button variant="outline" @click="handleClose">Cancel</Button>
        <Button @click="handleSave" :loading="isSaving">Save</Button>
      </div>
    </template>
  </BaseDialog>
</template>

<script setup lang="ts">
const props = defineProps<{
  agentUid: number
  dialogId: string
}>()

// Brand Identity State
const logo = ref<string | null>(null)
const avatar = ref<string | null>(null)
const selectedAvatar = ref<number | null>(null)

// Display Settings State
const autoOpen = ref(false)
const position = ref('bottom-right')
const bubbleColor = ref('#000000')
const userMsgColor = ref('#E1F8FF')
const aiMsgColor = ref('#F6F6F6')

// Button Settings
const buttons = reactive({
  history: true,
  share: true,
  feedback: true,
  copy: true,
})

// Message Settings
const placeholder = ref('Ask a question...')
const openers = ref<string[]>(["Hello, I'm your AI agent!", 'Hello 2'])
const suggestions = ref<string[]>(['Hi Ava!', 'Sug 2'])
const notifications = ref<string[]>(['Notification'])

// Dialog State
const isSaving = ref(false)

// TODO: Fetch initial settings based on agentUid

// Dialog Methods
const handleClose = () => {
  props.onClose()
}

const handleSave = async () => {
  isSaving.value = true
  // Simulate API call
  await new Promise((resolve) => setTimeout(resolve, 1000))

  const settingsToSave = {
    logo: logo.value,
    avatar: avatar.value,
    selectedAvatar: selectedAvatar.value,
    position: position.value,
    bubbleColor: bubbleColor.value,
    autoOpen: autoOpen.value,
    userMsgColor: userMsgColor.value,
    aiMsgColor: aiMsgColor.value,
    buttons: { ...buttons },
    placeholder: placeholder.value,
    openers: [...openers.value],
    suggestions: [...suggestions.value],
    notifications: [...notifications.value],
  }

  // TODO: Implement actual save logic here with settingsToSave
  // Make API call to update agent settings

  isSaving.value = false
  props.onClose()
}
</script>
