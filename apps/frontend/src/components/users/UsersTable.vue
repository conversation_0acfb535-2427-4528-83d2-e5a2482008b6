<template>
  <BaseTable
    :isLoading="isLoading || isFetching"
    :heads="heads"
    :rows="users"
    empty-message="No users found, add a new user to get started."
  >
    <template #actions>
      <Button @click="props.onEdit(undefined)"> <BaseIcon name="Plus" /> Add User </Button>
    </template>
    <template #row="{ row }">
      <TableCell>{{ row.first_name }}</TableCell>
      <TableCell>{{ row.last_name }}</TableCell>
      <TableCell>
        <a :href="`mailto:${row.email}`" class="text-blue-600 hover:underline">
          {{ row.email }}
        </a>
      </TableCell>
      <TableCell>
        <Badge :variant="row.role === 'admin' ? 'destructive' : 'secondary'">
          {{ row.role }}
        </Badge>
      </TableCell>
      <TableCell>{{ row.created_at }}</TableCell>
      <TableCell class="text-right">
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="icon" @click.stop>
              <BaseIcon name="MoreVertical" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem @click="props.onEdit(row.uid)">
              <BaseIcon name="Pencil" class="w-4 h-4 mr-2" />
              Edit
            </DropdownMenuItem>
            <DropdownMenuItem @click="props.onDelete(row.uid)">
              <BaseIcon name="Trash2" class="w-4 h-4 mr-2" />
              Delete
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </TableCell>
    </template>
  </BaseTable>
</template>

<script setup lang="ts">
import BaseTable from '@/components/base/table/BaseTable.vue'
import type { User } from '@/types'
import { queryUsers } from '@/services/user_service'

const props = defineProps<{
  onEdit: (uid?: string) => void
  onDelete: (uid: string) => void
}>()

const { data, isLoading, isFetching } = queryUsers()
const users = ref<User[]>([])

watchEffect(() => {
  users.value = data.value?.data ?? []
})

const heads = [
  { label: 'First Name', key: 'first_name' },
  { label: 'Last Name', key: 'last_name' },
  { label: 'Email', key: 'email' },
  { label: 'Role', key: 'role' },
  { label: 'Created At', key: 'created_at' },
  { label: 'Actions', key: 'actions', align: 'right' },
]
</script>
