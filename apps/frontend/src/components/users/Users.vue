<template>
  <BaseDialog
    :icon="ICONS.USERS"
    title="Users"
    description="Manage your users."
    :dialogId="dialogId"
  >
    <UsersTable :onEdit="handleEdit" :onDelete="handleDelete" />
  </BaseDialog>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ICONS } from '@/constants'
import { mutateUserDestroy } from '@/services/user_service'
import { useDialogStore, DIALOG_KEYS } from '@/stores/dialog'

defineProps<{
  dialogId: string
}>()

// Composables
const { toast } = useToast()
const dialogStore = useDialogStore()

// Functions
const handleEdit = (uid?: string) => {
  dialogStore.openDialog(DIALOG_KEYS.USERS_FORM, {
    uid,
  })
}

const handleDelete = async (uid: string) => {
  const { mutateAsync } = mutateUserDestroy(ref(uid))

  try {
    await mutateAsync(undefined)

    toast({
      title: 'User Deleted',
      description: 'The user has been successfully deleted.',
    })
  } catch (error: any) {
    toast({
      title: 'Error Deleting User',
      description: error?.message || 'An unexpected error occurred.',
      variant: 'destructive',
    })
  }
}
</script>
