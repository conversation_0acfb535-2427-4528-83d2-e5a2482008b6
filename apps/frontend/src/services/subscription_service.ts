import { useQuery } from '@tanstack/vue-query'
import { useApi, useApiAnon } from '@/plugins/api'
import { type Ref } from 'vue'
import { queryClient } from '@/plugins/tanstack-query'

export const queryPricingTable = () => {
  const apiAnon = useApiAnon()

  return useQuery({
    queryKey: ['subscriptions.embedded-checkout-session'],
    queryFn: () => apiAnon.open['pricing-table'].$get().unwrap(),
  })
}

export const queryEmbeddedCheckoutSession = (stripePriceId: Ref<string>, quantity: Ref<number>) => {
  const api = useApi()

  return queryClient.fetchQuery({
    staleTime: 0,
    gcTime: 0,
    queryKey: ['subscriptions.embedded-checkout-session', stripePriceId.value, quantity.value],
    queryFn: () =>
      api.subscriptions['get-embedded-checkout-session']({
        stripe_price_id: stripePriceId.value,
      })({
        quantity: quantity.value,
      })
        .$get({
          query: {
            stripe_price_id: stripePriceId.value,
            quantity: quantity.value,
          },
        })
        .unwrap(),
  })
}

export const queryPortalSessionUrl = (stripeCustomerId: Ref<string>) => {
  const api = useApi()

  return useQuery({
    staleTime: 0,
    gcTime: 0,
    queryKey: ['subscriptions.portal-session-url'],
    queryFn: () =>
      api.subscriptions['get-portal-session-url']({ stripe_customer_id: stripeCustomerId.value })
        .$get()
        .unwrap(),
  })
}
