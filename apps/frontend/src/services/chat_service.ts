import { queryClient } from '@/plugins/tanstack-query'
import { useMutation, useQuery } from '@tanstack/vue-query'
import { useApi } from '@/plugins/api'
import { type Ref, computed } from 'vue'
import type { RequestFilters } from '@/types/filters'

/**
 * Query to get chat history for a specific agent
 */
export const queryChatHistory = (agentUid: Ref<string | undefined>, filters?: Ref<RequestFilters>) => {
  const api = useApi()

  return useQuery({
    queryKey: ['chats', 'history', agentUid.value, filters?.value],
    queryFn: () => 
      api.chats.history({ agent_uid: agentUid.value! })
        .$get(filters?.value ? { query: filters.value } : {})
        .unwrap(),
    enabled: computed(() => !!agentUid.value),
  })
}

/**
 * Query to get all chats (general list)
 */
export const queryChats = (filters?: Ref<RequestFilters>) => {
  const api = useApi()

  return useQuery({
    queryKey: ['chats', filters?.value],
    queryFn: () => api.chats.$get(filters?.value ? { query: filters.value } : {}).unwrap(),
  })
}

/**
 * Query to get a specific chat by UID
 */
export const queryChatById = (uid: Ref<string | undefined>) => {
  const api = useApi()

  return useQuery({
    queryKey: ['chats', uid.value],
    queryFn: () => api.chats({ uid: uid.value! }).$get().unwrap(),
    enabled: computed(() => !!uid.value),
  })
}

/**
 * Query to search chats for a specific agent
 */
export const queryChatSearch = (
  agentUid: Ref<string | undefined>, 
  searchQuery: Ref<string | undefined>,
  filters?: Ref<RequestFilters>
) => {
  const api = useApi()

  return useQuery({
    queryKey: ['chats', 'search', agentUid.value, searchQuery.value, filters?.value],
    queryFn: () => 
      api.chats.search({ agent_uid: agentUid.value! })
        .$get({ 
          query: { 
            query: searchQuery.value!,
            ...(filters?.value || {})
          } 
        })
        .unwrap(),
    enabled: computed(() => !!agentUid.value && !!searchQuery.value),
  })
}

// --- Mutations ---

/**
 * Mutation to create a new chat
 */
export const mutateChatCreate = () => {
  const api = useApi()
  return useMutation({
    mutationFn: (payload: any) => api.chats.$post({ body: payload } as any).unwrap(),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['chats'] })
    },
  })
}

/**
 * Mutation to update a chat
 */
export const mutateChatUpdate = (uid: Ref<string | undefined>) => {
  const api = useApi()
  return useMutation({
    mutationFn: (payload: any) =>
      api.chats({ uid: uid.value! })
        .$put({ body: payload } as any)
        .unwrap(),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['chats', uid.value] })
      queryClient.invalidateQueries({ queryKey: ['chats'] })
    },
  })
}

/**
 * Mutation to delete a chat
 */
export const mutateChatDelete = (uid: Ref<string | undefined>) => {
  const api = useApi()
  return useMutation({
    mutationFn: () => api.chats({ uid: uid.value! }).$delete().unwrap(),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['chats', uid.value] })
      queryClient.invalidateQueries({ queryKey: ['chats'] })
    },
  })
}

/**
 * Mutation to archive a chat
 */
export const mutateChatArchive = (uid: Ref<string | undefined>) => {
  const api = useApi()
  return useMutation({
    mutationFn: () => api.chats.archive({ uid: uid.value! }).$get().unwrap(),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['chats', uid.value] })
      queryClient.invalidateQueries({ queryKey: ['chats'] })
    },
  })
}

/**
 * Mutation to append metadata to a chat
 */
export const mutateChatAppendMetadata = (uid: Ref<string | undefined>) => {
  const api = useApi()
  return useMutation({
    mutationFn: (metadata: object) =>
      api.chats.metadata({ uid: uid.value! })
        .$put({ body: metadata } as any)
        .unwrap(),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['chats', uid.value] })
    },
  })
}
