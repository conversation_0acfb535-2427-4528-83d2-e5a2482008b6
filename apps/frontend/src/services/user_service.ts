import { queryClient } from '@/plugins/tanstack-query'
import { useMutation, useQuery } from '@tanstack/vue-query'
import { useApi } from '@/plugins/api'
import { type Ref } from 'vue'
import type { UserUpdatePayload, UserCreatePayload } from '@/types'
import type { RequestFilters } from '@/types/filters'

// List users
export const queryUsers = (filters?: Ref<RequestFilters>) => {
  const api = useApi()

  return useQuery({
    queryKey: ['users', filters?.value],
    queryFn: () => api.users.$get(filters?.value ? { query: filters.value } : {}).unwrap(),
  })
}

// Single user
export const queryUserById = (uid: Ref<string | undefined>) => {
  const api = useApi()

  return useQuery({
    queryKey: ['users', uid],
    queryFn: () => api.users({ uid: uid.value! }).$get().unwrap(),
  })
}

// Mutations
export const mutateUser = (uid: Ref<string | undefined>) => {
  const api = useApi()

  return useMutation({
    mutationFn: (payload: UserCreatePayload | UserUpdatePayload) => {
      if (uid.value) {
        return api.users({ uid: uid.value! }).$put(payload).unwrap()
      }

      return api.users.$post(payload).unwrap()
    },
    onSuccess: () => {
      if (uid.value) {
        queryClient.invalidateQueries({ queryKey: ['users', uid.value] })
      }

      queryClient.invalidateQueries({ queryKey: ['users'] })
    },
  })
}

export const mutateUserDestroy = (uid: Ref<string | undefined>) => {
  const api = useApi()

  return useMutation({
    mutationFn: () => api.users({ uid: uid.value! }).$delete().unwrap(),
    onSuccess() {
      queryClient.invalidateQueries({ queryKey: ['users'] })
    },
  })
}
