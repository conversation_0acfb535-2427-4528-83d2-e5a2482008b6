import { queryClient } from '@/plugins/tanstack-query'
import { useMutation, useQuery } from '@tanstack/vue-query'
import { useApi } from '@/plugins/api'
import { type Ref } from 'vue'
import type {
  CopyrightCreatePayload,
  CopyrightUpdatePayload,
  DomainCreatePayload,
  DomainUpdatePayload,
  KeyCreatePayload,
  KeyUpdatePayload,
  SmtpCreatePayload,
  SmtpUpdatePayload,
} from '@/types'

// ========= COPYRIGHTS =========

// List copyrights
export const queryCopyrights = () => {
  const api = useApi()

  return useQuery({
    queryKey: ['whitelabel', 'copyrights'],
    queryFn: () => api.copyrights.$get().unwrap(),
  })
}

// Single copyright
export const queryCopyrightById = (uid: Ref<string | undefined>) => {
  const api = useApi()

  return useQuery({
    queryKey: ['whitelabel', 'copyrights', uid],
    queryFn: () => api.copyrights({ uid: uid.value! }).$get().unwrap(),
    enabled: computed(() => !!uid.value),
  })
}

// Copyright mutations
export const mutateCopyright = (uid: Ref<string | undefined>) => {
  const api = useApi()

  return useMutation({
    mutationFn: (payload: CopyrightCreatePayload | CopyrightUpdatePayload) => {
      if (uid.value) {
        return api.copyrights({ uid: uid.value! }).$put(payload).unwrap()
      }

      return api.copyrights.$post(payload).unwrap()
    },
    onSuccess: () => {
      if (uid.value) {
        queryClient.invalidateQueries({ queryKey: ['whitelabel', 'copyrights', uid.value] })
      }

      queryClient.invalidateQueries({ queryKey: ['whitelabel', 'copyrights'] })
    },
  })
}

export const mutateCopyrightDestroy = (uid: Ref<string | undefined>) => {
  const api = useApi()

  return useMutation({
    mutationFn: () => api.copyrights({ uid: uid.value! }).$delete().unwrap(),
    onSuccess() {
      queryClient.invalidateQueries({ queryKey: ['whitelabel', 'copyrights'] })
    },
  })
}

// ========= DOMAINS =========

// List domains
export const queryDomains = () => {
  const api = useApi()

  return useQuery({
    queryKey: ['whitelabel', 'domains'],
    queryFn: () => api.domains.$get().unwrap(),
  })
}

// Single domain
export const queryDomainById = (uid: Ref<string | undefined>) => {
  const api = useApi()

  return useQuery({
    queryKey: ['whitelabel', 'domains', uid],
    queryFn: () => api.domains({ uid: uid.value! }).$get().unwrap(),
    enabled: computed(() => !!uid.value),
  })
}

// Domain mutations
export const mutateDomain = (uid: Ref<string | undefined>) => {
  const api = useApi()

  return useMutation({
    mutationFn: (payload: DomainCreatePayload | DomainUpdatePayload) => {
      if (uid.value) {
        return api.domains({ uid: uid.value! }).$put(payload).unwrap()
      }

      return api.domains.$post(payload).unwrap()
    },
    onSuccess: () => {
      if (uid.value) {
        queryClient.invalidateQueries({ queryKey: ['whitelabel', 'domains', uid.value] })
      }

      queryClient.invalidateQueries({ queryKey: ['whitelabel', 'domains'] })
    },
  })
}

export const mutateDomainDestroy = (uid: Ref<string | undefined>) => {
  const api = useApi()

  return useMutation({
    mutationFn: () => api.domains({ uid: uid.value! }).$delete().unwrap(),
    onSuccess() {
      queryClient.invalidateQueries({ queryKey: ['whitelabel', 'domains'] })
    },
  })
}

// ========= KEYS =========

// List keys
export const queryKeys = () => {
  const api = useApi()

  return useQuery({
    queryKey: ['whitelabel', 'keys'],
    queryFn: () => api.keys.$get().unwrap(),
  })
}

// Single key
export const queryKeyById = (uid: Ref<string | undefined>) => {
  const api = useApi()

  return useQuery({
    queryKey: ['whitelabel', 'keys', uid],
    queryFn: () => api.keys({ uid: uid.value! }).$get().unwrap(),
    enabled: computed(() => !!uid.value),
  })
}

// Key mutations
export const mutateKey = (uid: Ref<string | undefined>) => {
  const api = useApi()

  return useMutation({
    mutationFn: (payload: KeyCreatePayload | KeyUpdatePayload) => {
      if (uid.value) {
        return api.keys({ uid: uid.value! }).$put(payload).unwrap()
      }

      return api.keys.$post(payload).unwrap()
    },
    onSuccess: () => {
      if (uid.value) {
        queryClient.invalidateQueries({ queryKey: ['whitelabel', 'keys', uid.value] })
      }

      queryClient.invalidateQueries({ queryKey: ['whitelabel', 'keys'] })
    },
  })
}

export const mutateKeyDestroy = (uid: Ref<string | undefined>) => {
  const api = useApi()

  return useMutation({
    mutationFn: () => api.keys({ uid: uid.value! }).$delete().unwrap(),
    onSuccess() {
      queryClient.invalidateQueries({ queryKey: ['whitelabel', 'keys'] })
    },
  })
}

// ========= SMTPS =========

// List smtps
export const querySmtps = () => {
  const api = useApi()

  return useQuery({
    queryKey: ['whitelabel', 'smtps'],
    queryFn: () => api.smtps.$get().unwrap(),
  })
}

// Single smtp
export const querySmtpById = (uid: Ref<string | undefined>) => {
  const api = useApi()

  return useQuery({
    queryKey: ['whitelabel', 'smtps', uid],
    queryFn: () => api.smtps({ uid: uid.value! }).$get().unwrap(),
    enabled: computed(() => !!uid.value),
  })
}

// Smtp mutations
export const mutateSmtp = (uid: Ref<string | undefined>) => {
  const api = useApi()

  return useMutation({
    mutationFn: (payload: SmtpCreatePayload | SmtpUpdatePayload) => {
      if (uid.value) {
        return api.smtps({ uid: uid.value! }).$put(payload).unwrap()
      }

      return api.smtps.$post(payload).unwrap()
    },
    onSuccess: () => {
      if (uid.value) {
        queryClient.invalidateQueries({ queryKey: ['whitelabel', 'smtps', uid.value] })
      }

      queryClient.invalidateQueries({ queryKey: ['whitelabel', 'smtps'] })
    },
  })
}

export const mutateSmtpDestroy = (uid: Ref<string | undefined>) => {
  const api = useApi()

  return useMutation({
    mutationFn: () => api.smtps({ uid: uid.value! }).$delete().unwrap(),
    onSuccess() {
      queryClient.invalidateQueries({ queryKey: ['whitelabel', 'smtps'] })
    },
  })
}
