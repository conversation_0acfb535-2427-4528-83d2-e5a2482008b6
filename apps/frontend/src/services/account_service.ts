import { useMutation } from '@tanstack/vue-query'
import type { InferRequestType, InferResponseType } from '@tuyau/client'
import { useApi, useApiAnon } from '@/plugins/api'
import { queryClient } from '@/plugins/tanstack-query'

export const mutateAccountEmail = () => {
  const api = useApi()

  return useMutation({
    mutationFn: (
      payload: InferRequestType<ReturnType<typeof useApi>['users']['change-email']['$post']>
    ) => api.users['change-email'].$post(payload).unwrap(),
    onSuccess(data) {
      queryClient.setQueryData(
        ['auth.me'],
        (old: InferResponseType<ReturnType<typeof useApiAnon>['auth']['me']['$get']>) => ({
          ...old,
          user: {
            ...old.user,
            email: data.email,
          },
        })
      )
    },
  })
}

export const mutateAccountDetails = () => {
  const api = useApi()

  return useMutation({
    mutationFn: (
      payload: InferRequestType<ReturnType<typeof useApi>['users']['change-infos']['$post']>
    ) => api.users['change-infos'].$post(payload).unwrap(),
    onSuccess(data) {
      queryClient.setQueryData(
        ['auth.me'],
        (old: InferResponseType<ReturnType<typeof useApiAnon>['auth']['me']['$get']>) => ({
          ...old,
          user: {
            ...old.user,
            first_name: data.first_name,
            last_name: data.last_name,
          },
        })
      )
    },
  })
}

export const mutateAccountPassword = () => {
  const api = useApi()

  return useMutation({
    mutationFn: (
      payload: InferRequestType<ReturnType<typeof useApi>['users']['change-password']['$post']>
    ) => api.users['change-password'].$post(payload).unwrap(),
    onSuccess(data) {},
  })
}
