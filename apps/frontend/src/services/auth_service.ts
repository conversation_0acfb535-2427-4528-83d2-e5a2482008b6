import { useQuery, useMutation } from '@tanstack/vue-query'
import { useApiAnon } from '@/plugins/api'
import type { InferRequestType } from '@tuyau/client'
import { queryClient } from '@/plugins/tanstack-query'

// Me
export const prefetchAuthMe = () => {
  const apiAnon = useApiAnon()

  return queryClient.prefetchQuery({
    queryKey: ['auth.me'],
    queryFn: () => apiAnon.auth.me.$get().unwrap(),
  })
}

export const logout = () => {
  const apiAnon = useApiAnon()
  const appStore = useAppStore()

  return useMutation({
    mutationFn: () => apiAnon.auth.logout.$get().unwrap(),
    onSuccess: () => {
      appStore.reset()
      queryClient.clear()
    },
  })
}

export const ensureAuthMe = () => {
  const apiAnon = useApiAnon()

  return queryClient.ensureQueryData({
    queryKey: ['auth.me'],
    queryFn: () => apiAnon.auth.me.$get().unwrap(),
  })
}

// Domain
export const prefetchAuthDomain = (dn: string) => {
  const apiAnon = useApiAnon()

  return queryClient.prefetchQuery({
    queryKey: ['auth.domain', dn],
    queryFn: () => apiAnon.auth.domain.$get({ query: { dn } }).unwrap(),
  })
}

export const ensureAuthDomain = (dn: string) => {
  const apiAnon = useApiAnon()

  return queryClient.ensureQueryData({
    queryKey: ['auth.domain', dn],
    queryFn: () => apiAnon.auth.domain.$get({ query: { dn } }).unwrap(),
  })
}

export const queryAuthDomain = (dn: string) => {
  const apiAnon = useApiAnon()

  return useQuery({
    queryKey: ['auth.domain', dn],
    queryFn: () => apiAnon.auth.domain.$get({ query: { dn } }).unwrap(),
  })
}

// Login
export const mutateAuthLogin = () => {
  const apiAnon = useApiAnon()

  return useMutation({
    mutationFn: async (
      payload: InferRequestType<ReturnType<typeof useApiAnon>['auth']['login']['$post']>
    ) => apiAnon.auth.login.$post(payload).unwrap(),
    onSuccess(data) {
      queryClient.invalidateQueries({ queryKey: ['auth.me'] })
    },
  })
}

export const mutateAuthRegister = () => {
  const apiAnon = useApiAnon()

  return useMutation({
    mutationFn: (
      payload: InferRequestType<ReturnType<typeof useApiAnon>['auth']['register']['$post']>
    ) => apiAnon.auth.register.$post(payload).unwrap(),
  })
}

export const mutateAuthResetPassword = () => {
  const apiAnon = useApiAnon()

  return useMutation({
    mutationFn: (
      payload: InferRequestType<ReturnType<typeof useApiAnon>['auth']['reset-password']['$post']>
    ) => apiAnon.auth['reset-password'].$post(payload).unwrap(),
  })
}

export const mutateAuthAccessGoogle = () => {
  const apiAnon = useApiAnon()

  return useMutation({
    mutationFn: (
      payload: InferRequestType<ReturnType<typeof useApiAnon>['auth']['access-google']['$post']>
    ) => apiAnon.auth['access-google'].$post(payload).unwrap(),
  })
}
