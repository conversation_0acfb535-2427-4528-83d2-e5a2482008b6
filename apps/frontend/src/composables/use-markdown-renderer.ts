import { marked } from 'marked'
import markedBidi from 'marked-bidi'
import extendedTables from 'marked-extended-tables'
import { markedSmartypants } from 'marked-smartypants'
import markedLinkifyIt from 'marked-linkify-it'
import markedMoreLists from 'marked-more-lists'
import { markedXhtml } from 'marked-xhtml'
import { createHighlighter } from 'shiki'
import type { Highlighter } from 'shiki'
import markedShiki from 'marked-shiki'
import {
  transformerNotationDiff,
  transformerNotationHighlight,
  transformerNotationWordHighlight,
  transformerNotationFocus,
  transformerNotationErrorLevel,
  transformerMetaHighlight,
  transformerMetaWordHighlight,
} from '@shikijs/transformers'

const isReady = ref(false)
const highlighterInstance = shallowRef<Highlighter | null>(null)
let initializePromise: Promise<void> | null = null

async function initializeRenderer() {
  if (isReady.value) {
    return
  }
  if (initializePromise) {
    return initializePromise
  }

  initializePromise = (async () => {
    try {
      const highlighter = await createHighlighter({
        themes: ['github-light', 'github-dark'],
        langs: [
          'javascript',
          'typescript',
          'vue',
          'css',
          'html',
          'json',
          'bash',
          'markdown',
          'yaml',
          'scss',
          'sql',
          'python',
          'php',
          'java',
          'c',
          'cpp',
          'csharp',
          'go',
          'rust',
          'ruby',
          'swift',
        ],
      })
      highlighterInstance.value = highlighter

      marked.setOptions({
        breaks: true,
        gfm: true,
        async: true, // Important: marked.parse needs to be async
      })

      marked.use(markedSmartypants())
      marked.use(markedBidi())
      marked.use(extendedTables())
      marked.use(markedLinkifyIt())
      marked.use(markedMoreLists())
      marked.use(markedXhtml())
      marked.use(
        markedShiki({
          highlight(code: string, lang: string, props: string[]) {
            if (!highlighterInstance.value) {
              return `<pre><code>${code}</code></pre>`
            }

            try {
              return highlighterInstance.value.codeToHtml(code, {
                lang: lang || 'text',
                themes: {
                  light: 'github-light',
                  dark: 'github-dark',
                },
                meta: { __raw: props?.join(' ') || '' },
                transformers: [
                  transformerNotationDiff({ matchAlgorithm: 'v3' }),
                  transformerNotationHighlight({ matchAlgorithm: 'v3' }),
                  transformerNotationWordHighlight({ matchAlgorithm: 'v3' }),
                  transformerNotationFocus({ matchAlgorithm: 'v3' }),
                  transformerNotationErrorLevel({ matchAlgorithm: 'v3' }),
                  transformerMetaHighlight(),
                  transformerMetaWordHighlight(),
                ],
              })
            } catch (error) {
              console.error('[useMarkdownRenderer] Error in syntax highlighting:', error)

              return `<pre><code>${code}</code></pre>` // Fallback
            }
          },
        })
      )

      isReady.value = true
    } catch (error) {
      console.error(
        '[useMarkdownRenderer] Failed to initialize Shiki highlighter or marked:',
        error
      )
      // Keep isReady false
    } finally {
      initializePromise = null // Reset promise after completion/failure
    }
  })()

  return initializePromise
}

// Initialize on load (or first use)
initializeRenderer()

export const useMarkdownRenderer = () => {
  const renderMarkdown = async (text: string): Promise<string> => {
    await initializeRenderer() // Ensure initialization is complete

    if (!isReady.value) {
      return text // Return plain text if initialization failed
    }

    try {
      // Use marked.parse for async rendering
      const html = await marked.parse(text || '')
      // Note: DOMPurify step is removed as it was commented out in the original component
      // If needed, add it here or consider if sanitization is required globally
      return html
    } catch (error) {
      console.error('[useMarkdownRenderer] Error rendering markdown:', error)
      return text // Fallback to plain text on error
    }
  }

  return {
    isReady,
    renderMarkdown,
  }
}
