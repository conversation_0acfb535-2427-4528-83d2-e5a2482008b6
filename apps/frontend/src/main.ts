import { createApp } from 'vue'
import App from './App.vue'
import './assets/index.css'
import { installPinia } from '@/plugins/pinia'
import { installRouter } from '@/plugins/router'
import { installUnhead } from '@/plugins/unhead'
import { setupNavigationGuards } from '@/plugins/navigation-guards'
import { installTanstackQuery } from '@/plugins/tanstack-query'
import { installApi } from '@/plugins/api'

const app = createApp(App)

installPinia(app)
installUnhead(app)
const router = installRouter(app)
installApi(app)
installTanstackQuery(app)
setupNavigationGuards(router)

app.mount('#app')
