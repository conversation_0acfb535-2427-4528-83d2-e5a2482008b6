<template>
  <BaseSkeleton v-if="appStore.isNavigating" type="app" :rows="0" :headers="0" :cols="0" />
  <Suspense v-else>
    <template #default>
      <router-view />
    </template>
    <template #fallback>
      <BaseSkeleton type="app" :rows="0" :headers="0" :cols="0" />
    </template>
  </Suspense>
  <Toaster />
  <DialogProvider />
  <StagewiseToolbar v-if="isDevMode" :config="stagewiseConfig" />
</template>

<script setup lang="ts">
import { StagewiseToolbar } from '@stagewise/toolbar-vue'

const themeStore = useThemeStore()
const appStore = useAppStore()

themeStore.setColorMode('light')

const isDevMode = import.meta.env.DEV

const stagewiseConfig = {
  plugins: [],
}
</script>
