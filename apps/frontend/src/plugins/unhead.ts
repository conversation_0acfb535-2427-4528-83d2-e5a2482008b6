import { createHead, type VueHeadClient } from '@unhead/vue/client'
import type { App } from 'vue'

let instance: ReturnType<typeof createHead> | null = null

export const installUnhead = (app: App) => {
  const head = createHead()

  instance = head

  app.use(head)
}

export const getHead = (): VueHeadClient => {
  if (!instance) {
    throw new Error('Head has not been initialized')
  }

  return instance
}
