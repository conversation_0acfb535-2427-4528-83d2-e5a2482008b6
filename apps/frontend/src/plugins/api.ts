import { createTuyau } from '@tuyau/client'
import { api } from '@insertchat/backend/api'
import { useAppStore } from '@/stores/app'
// import { superjson } from '@tuyau/superjson/plugin'
import type { App, getCurrentInstance } from 'vue'
import type { Router } from 'vue-router'

let internalRouterInstance: Router | null = null

const instance = createTuyau({
  api,
  baseUrl: import.meta.env.VITE_API_URL || '',
  // plugins: [superjson()],
  headers: {
    'Content-Type': 'application/json',
  },
  hooks: {
    beforeRequest: [
      async (request: Request) => {
        const appStore = useAppStore()
        const token = appStore.authToken

        if (token) {
          request.headers.set('Authorization', `Bearer ${token}`)
        }
      },
    ],
    beforeRetry: [
      async ({ request, options, error, retryCount }) => {
        console.warn(
          `[Tuyau] beforeRetry: Attempting retry ${retryCount + 1} for request to ${request.url}. Error:`,
          error?.message,
          'Request:',
          request,
          'Options:',
          options
        )
      },
    ],
    afterResponse: [
      async (request, options, response) => {
        const responseClone = await response.clone()
        let serverResponse = (await responseClone.json().catch(() => ({}))) || {}

        if (typeof serverResponse.json === 'object' && serverResponse.json !== null) {
          serverResponse = serverResponse.json
        }

        const responseText = await response
          .clone()
          .text()
          .catch(() => '')
        const status = serverResponse?.status || response.status
        const code = serverResponse?.code ? serverResponse.code : ''
        const message = serverResponse?.message || response.statusText
        const extra = !serverResponse?.message && responseText ? `: ${responseText}` : ''
        const description = `${message}\n${extra}`.trim() || 'An unexpected error occurred'

        if (code === 'SUBSCRIPTION_NOT_FOUND') {
          useDialogStore().close()

          if (internalRouterInstance) {
            internalRouterInstance.push('/subscriptions')
          }
        }

        if (code === 'E_UNAUTHORIZED_ACCESS') {
          useAppStore().reset()
          useDialogStore().close()

          toast({
            title: 'Session expired',
            description: 'Please log in again.',
            variant: 'destructive',
          })

          if (internalRouterInstance) {
            internalRouterInstance.push('/auth/login')
          }
        }

        if (status >= 400) {
          toast({
            title: 'Error',
            description,
            variant: 'destructive',
          })
        }

        return response
      },
    ],
    beforeError: [
      async (error) => {
        console.error(
          '[Tuyau] beforeError: Handling API Error. Message:',
          (error as Error).message,
          'Full Error:',
          error
        )

        toast({
          title: 'API Error',
          description: (error as Error).message || 'An unexpected error occurred',
          variant: 'destructive',
        })

        return error
      },
    ],
  },
})

export const useApiAnon = () => {
  return instance.v2
}

export const useApi = () => {
  const appStore = useAppStore()
  const appUid = appStore.currentAppUid

  if (!appUid) {
    throw new Error('No app uid found')
  }

  return instance.v2({ app_uid: appUid })
}

export const installApi = (app: App, router: Router) => {
  internalRouterInstance = router
  app.provide('api', instance)
}
