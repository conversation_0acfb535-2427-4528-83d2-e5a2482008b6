import type { Router } from 'vue-router'
import { useHead } from '@unhead/vue'
import { ensureAuthMe, ensureAuthDomain } from '@/services/auth_service'
import { extractDomain } from '@/utils/url'

export const setupNavigationGuards = (router: Router) => {
  router.beforeEach(async (to, from, next) => {
    const appStore = useAppStore()
    // const head = getHead()

    appStore.setNavigating(true)

    // Extract and store current domain
    const currentDomain = extractDomain(window.location.href)
    if (currentDomain && currentDomain !== appStore.currentDomain) {
      appStore.setCurrentDomain(currentDomain)
    }

    try {
      const requiresAuth = to.meta.requiresAuth

      // Domain
      try {
        const brand = await ensureAuthDomain(currentDomain)

        if (!brand) {
          throw new Error('Domain fetch failed')
        }

        const { logo_url, favicon_url, company_name, primary_color } = brand

        document.documentElement.style.setProperty('--primary', primary_color)
        document.documentElement.style.setProperty('--tw-ring-color', primary_color)
        document.documentElement.style.setProperty('--ring', primary_color)
        document.documentElement.style.setProperty('--input', '0, 100%, 50%')
        document.documentElement.style.setProperty('--border', '0, 100%, 50%')

        /*
        head.push({
          title: `Dashboard | ${company_name}`,
          link: [
            {
              rel: 'icon',
              href: favicon_url,
            },
          ],
          meta: [
            {
              name: 'theme-color',
              content: primary_color || '#ffffff',
            },
          ],
        })
        */
      } catch (error) {
        console.error(error)
      }

      // Auth
      try {
        const me = await ensureAuthMe()

        const isLogged = me?.user?.uid !== null

        // Set current app uid
        if (me?.app?.uid && me.app.uid !== appStore.currentAppUid) {
          appStore.setCurrentAppUid(me.app.uid)
        }

        if (requiresAuth === false) {
          if (isLogged === true) {
            appStore.setNavigating(false)

            return next()
          }

          if (isLogged === false) {
            appStore.setNavigating(false)

            return next()
          }
        }

        if (requiresAuth === true) {
          if (!me.subscriptions?.plan && !to.path.startsWith('/subscriptions')) {
            return next({ path: '/subscriptions' })
          }

          if (isLogged === true) {
            appStore.setNavigating(false)

            return next()
          }

          if (isLogged === false) {
            toast({
              title: 'Authentication required',
              description: 'Please log in to continue.',
            })

            appStore.setNavigating(false)

            return next({ path: '/auth/login', query: { redirect: to.fullPath } })
          }
        }
      } catch (error: any) {
        console.error(error)

        if (requiresAuth || error?.response?.code === 'E_UNAUTHORIZED_ACCESS') {
          toast({ title: 'Session expired', description: 'Please log in again.' })

          appStore.setNavigating(false)

          if (to.path === '/auth/login') {
            return next()
          }

          return next({ path: '/auth/login' })
        }

        appStore.setNavigating(false)

        throw error
      }
    } catch (error) {
      toast({
        title: 'Navigation error',
        description: 'Server unreachable. Please try again later.',
      })

      appStore.setNavigating(false)

      if (to.path === '/auth/login') {
        return next()
      }

      return next({ path: '/auth/login' })
    }
  })
}
