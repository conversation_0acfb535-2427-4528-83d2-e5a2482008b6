import type { App } from 'vue'
import { VueQueryPlugin, QueryClient, VUE_QUERY_CLIENT } from '@tanstack/vue-query'

export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 1000 * 60 * 5,
      refetchOnWindowFocus: false,
      refetchOnReconnect: false,
      retryOnMount: false,
      retry: false,
    },
  },
})

export const installTanstackQuery = (app: App) => {
  console.log('installTanstackQuery')

  app.use(VueQueryPlugin, {
    queryClient,
    enableDevtools: import.meta.env.DEV,
  })
}
