import { createRouter, createWebHistory, type Router } from 'vue-router'
// @ts-ignore
import { routes } from 'vue-router/auto-routes'
import type { App } from 'vue'
// getCurrentInstance should not be used for router initialization logic in getRouter
// import { getCurrentInstance } from 'vue'

// Commenting out getCurrentApp as its use in getRouter for lazy initialization is problematic.
// If needed elsewhere, ensure it's called in a valid Vue component context.
/*
export const getCurrentApp = () => {
  const instance = getCurrentInstance()

  if (!instance) {
    throw new Error('getCurrentInstance() returned null. Ensure this is called within component setup or lifecycle hook.')
  }

  return instance.appContext.app
}
*/

let routerInstance: Router // Renamed for clarity

export const installRouter = (app: App): Router => {
  // Optional: Could add a check or warning if routerInstance already exists,
  // but simple reassignment is also fine if app re-initialization is a scenario.
  routerInstance = createRouter({
    history: createWebHistory(),
    routes,
  })

  app.use(routerInstance)
  return routerInstance
}

export const getRouter = (): Router => {
  if (!routerInstance) {
    // This error indicates a problem with the application's setup order.
    // The router must be installed via installRouter(app) before getRouter() is called.
    console.error(
      "[Router] getRouter() called before router was installed. Ensure installRouter(app) is called in your application's entry point."
    )
    throw new Error('Router not initialized. Call installRouter(app) first.')
  }
  return routerInstance
}
