import { InferRequestType, InferResponseType } from '@tuyau/client'
import { useApi } from '@/plugins/api'

const api = useApi()

export type Agents = InferResponseType<typeof api.agents.$get>
export type Agent = Agents['data'][number]
export type AgentCreatePayload = InferRequestType<typeof api.agents.$post>
export type AgentUpdatePayload = InferRequestType<
  ReturnType<ReturnType<typeof useApi>['agents']>['$put']
>
