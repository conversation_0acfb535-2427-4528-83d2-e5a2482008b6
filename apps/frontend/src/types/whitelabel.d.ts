import { InferRequestType, InferResponseType } from '@tuyau/client'
import { useApi } from '@/plugins/api'

const api = useApi()

// Copyrights
export type Copyrights = InferResponseType<typeof api.whitelabel.copyrights.$get>
export type Copyright = Copyrights['data'][number]
export type CopyrightCreatePayload = InferRequestType<typeof api.whitelabel.copyrights.$post>
export type CopyrightUpdatePayload = InferRequestType<
  ReturnType<ReturnType<typeof useApi>['whitelabel']['copyrights']>['$put']
>

// Domains
export type Domains = InferResponseType<typeof api.whitelabel.domains.$get>
export type Domain = Domains['data'][number]
export type DomainCreatePayload = InferRequestType<typeof api.whitelabel.domains.$post>
export type DomainUpdatePayload = InferRequestType<
  ReturnType<ReturnType<typeof useApi>['whitelabel']['domains']>['$put']
>

// Keys
export type Keys = InferResponseType<typeof api.whitelabel.keys.$get>
export type Key = Keys['data'][number]
export type KeyCreatePayload = InferRequestType<typeof api.whitelabel.keys.$post>
export type KeyUpdatePayload = InferRequestType<
  ReturnType<ReturnType<typeof useApi>['whitelabel']['keys']>['$put']
>

// SMTPs
export type Smtps = InferResponseType<typeof api.whitelabel.smtps.$get>
export type Smtp = Smtps['data'][number]
export type SmtpCreatePayload = InferRequestType<typeof api.whitelabel.smtps.$post>
export type SmtpUpdatePayload = InferRequestType<
  ReturnType<ReturnType<typeof useApi>['whitelabel']['smtps']>['$put']
>
