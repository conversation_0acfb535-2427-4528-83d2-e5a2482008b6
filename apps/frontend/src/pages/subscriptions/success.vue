<route lang="yaml">
meta:
  requiresAuth: true
</route>

<script setup lang="ts">
import { useRouter } from 'vue-router'

const router = useRouter()

const goToHome = () => {
  router.push('/')
}

const goToPortal = () => {
  router.push('/subscriptions/portal')
}
</script>

<template>
  <div class="flex items-center justify-center min-h-screen p-6 bg-muted/30">
    <div class="w-full max-w-md">
      <BaseCard
        title="Subscription Activated!"
        description="Your purchase has been successfully processed"
      >
        <div class="flex flex-col items-center space-y-6">
          <div class="p-4 bg-green-100 rounded-full">
            <BaseIcon name="CheckCircle" :size="32" class="text-green-600" />
          </div>

          <div class="space-y-3 text-center">
            <h3 class="text-lg font-semibold text-gray-900">🎉 Success!</h3>
            <p class="text-sm leading-relaxed text-muted-foreground">
              Thank you for your purchase! Your new features are now active and ready to use.
            </p>
          </div>

          <div class="w-full space-y-3">
            <BaseButton
              :is-submitting="false"
              :is-disabled="false"
              class="w-full"
              @click="goToHome"
            >
              <BaseIcon name="Bot" :size="16" class="mr-2" />
              Start Building Agents
            </BaseButton>
          </div>

          <div class="w-full pt-4 border-t">
            <div class="space-y-2 text-center">
              <p class="text-xs text-muted-foreground">Need to manage your subscription?</p>
              <BaseButton
                :is-submitting="false"
                :is-disabled="false"
                variant="ghost"
                size="sm"
                @click="goToPortal"
              >
                <BaseIcon name="Settings" :size="14" class="mr-1" />
                Billing Portal
              </BaseButton>
            </div>
          </div>
        </div>
      </BaseCard>
    </div>
  </div>
</template>
