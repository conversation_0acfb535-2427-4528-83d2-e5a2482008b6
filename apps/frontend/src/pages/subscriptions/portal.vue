<route lang="yaml">
meta:
  requiresAuth: true
</route>

<script setup lang="ts">
import { queryPortalSessionUrl } from '@/services/subscription_service'
import { ensureAuthMe } from '@/services/auth_service'

const me = await ensureAuthMe()
const { data, isLoading, error } = queryPortalSessionUrl(ref(me.app.stripe_customer_id))

const redirectToPortal = (url: string) => {
  if (typeof window !== 'undefined') {
    //  window.location.href = url
  }
}

const reloadPage = () => {
  if (typeof window !== 'undefined') {
    window.location.reload()
  }
}

watch(data, () => {
  if (data.value?.url) {
    toast({
      title: 'Customer Portal',
      description: 'Redirecting to your customer portal...',
    })

    try {
      redirectToPortal(data.value.url)
    } catch (error: any) {
      toast({
        title: 'Error',
        description: error.message || 'Failed to redirect to customer portal.',
        variant: 'destructive',
      })
    }
  }
})

watch(error, () => {
  if (error.value) {
    toast({
      title: 'Error',
      description: 'Could not retrieve customer portal session link.',
      variant: 'destructive',
    })
  }
})
</script>

<template>
  <div class="flex items-center justify-center min-h-screen p-6 bg-muted/30">
    <div class="w-full max-w-md">
      <BaseCard
        title="Customer Portal"
        description="Access your subscription and billing information"
      >
        <div class="flex flex-col items-center space-y-6">
          <template v-if="isLoading">
            <BaseSkeleton type="form" :rows="1" :headers="0" :cols="0" />

            <div class="flex items-center space-x-3">
              <BaseIcon name="Loader2" :size="20" class="animate-spin text-primary" />
              <p class="text-sm text-muted-foreground">Preparing your customer portal...</p>
            </div>
          </template>

          <template v-else-if="error">
            <div class="flex flex-col items-center space-y-4">
              <div class="p-3 rounded-full bg-destructive/10">
                <BaseIcon name="AlertCircle" :size="24" class="text-destructive" />
              </div>

              <div class="space-y-2 text-center">
                <h3 class="text-sm font-semibold">Unable to Load Portal</h3>
                <p class="text-sm text-muted-foreground">
                  We couldn't retrieve your customer portal session. Please try again or contact
                  support.
                </p>
              </div>

              <BaseButton
                :is-submitting="false"
                :is-disabled="false"
                variant="outline"
                class="w-full"
                @click="() => typeof window !== 'undefined' && window.location.reload()"
              >
                <BaseIcon name="RefreshCw" :size="16" class="mr-2" />
                Try Again
              </BaseButton>
            </div>
          </template>

          <template v-else-if="data?.url">
            <div class="flex flex-col items-center space-y-4">
              <div class="p-3 rounded-full bg-primary/10">
                <BaseIcon name="ExternalLink" :size="24" class="text-primary" />
              </div>

              <div class="space-y-2 text-center">
                <h3 class="text-sm font-semibold">Redirecting...</h3>
                <p class="text-sm text-muted-foreground">
                  You'll be redirected to your customer portal in a moment.
                </p>
              </div>

              <BaseButton
                :is-submitting="false"
                :is-disabled="false"
                class="w-full"
                @click="() => (window.location.href = data.url)"
              >
                <BaseIcon name="CreditCard" :size="16" class="mr-2" />
                Open Customer Portal
              </BaseButton>
            </div>
          </template>

          <template v-else>
            <div class="flex flex-col items-center space-y-4">
              <div class="p-3 rounded-full bg-muted">
                <BaseIcon name="HelpCircle" :size="24" class="text-muted-foreground" />
              </div>

              <div class="space-y-2 text-center">
                <h3 class="text-sm font-semibold">No Portal Session</h3>
                <p class="text-sm text-muted-foreground">
                  Unable to create a customer portal session.
                </p>
              </div>

              <BaseButton
                :is-submitting="false"
                :is-disabled="false"
                variant="outline"
                class="w-full"
                @click="() => window.location.reload()"
              >
                <BaseIcon name="RefreshCw" :size="16" class="mr-2" />
                Retry
              </BaseButton>
            </div>
          </template>
        </div>
      </BaseCard>
    </div>
  </div>
</template>
