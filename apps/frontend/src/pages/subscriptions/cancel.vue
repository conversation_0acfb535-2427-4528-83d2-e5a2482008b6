<route lang="yaml">
meta:
  requiresAuth: true
</route>

<script setup lang="ts">
import { useRouter } from 'vue-router'

const router = useRouter()

const goToSubscriptions = () => {
  router.push('/subscriptions')
}

const goToApp = () => {
  router.push('/')
}
</script>

<template>
  <div class="flex items-center justify-center min-h-screen p-6 bg-muted/30">
    <div class="w-full max-w-md">
      <BaseCard title="Checkout Cancelled" description="Your subscription checkout was cancelled">
        <div class="flex flex-col items-center space-y-6">
          <div class="p-4 bg-yellow-100 rounded-full">
            <BaseIcon name="XCircle" :size="32" class="text-yellow-600" />
          </div>

          <div class="space-y-3 text-center">
            <h3 class="text-lg font-semibold text-gray-900">No Worries!</h3>
            <p class="text-sm leading-relaxed text-muted-foreground">
              Your checkout was cancelled and no payment was processed. You can try again anytime or
              explore our features with your current plan.
            </p>
          </div>

          <div class="w-full space-y-3">
            <BaseButton
              :is-submitting="false"
              :is-disabled="false"
              class="w-full"
              @click="goToSubscriptions"
            >
              <BaseIcon name="CreditCard" :size="16" class="mr-2" />
              View Subscription Plans
            </BaseButton>

            <BaseButton
              :is-submitting="false"
              :is-disabled="false"
              variant="outline"
              class="w-full"
              @click="goToApp"
            >
              <BaseIcon name="ArrowLeft" :size="16" class="mr-2" />
              Continue with Current Plan
            </BaseButton>
          </div>
        </div>
      </BaseCard>
    </div>
  </div>
</template>
