<route lang="yaml">
meta:
  requiresAuth: true
</route>

<template>
  <SidebarProvider :defaultOpen="sidebarState">
    <!-- Sidebar -->
    <ChatSidebar />

    <!-- Chat -->
    <SidebarInset class="flex flex-col flex-1 h-screen overflow-hidden">
      <ChatHeader />

      <!-- Scrollable Message Area -->
      <ScrollArea class="flex-1 w-full">
        <!-- Adjusted width classes -->
        <div class="w-full px-4 h-full mx-auto md:w-[70%] lg:w-[60%] xl:w-[55%]">
          <!-- Welcome Message -->
          <ChatWelcomeMessage
            v-if="messages.length === 0"
            class="flex items-center justify-center h-full"
          />

          <!-- Messages -->
          <div v-else class="pt-4 pb-4">
            <!-- Added padding for messages -->
            <ChatMessage
              v-for="(message, index) in messages"
              :key="index"
              :text="message.text"
              :is-assistant="!message.isUser"
              :user-name="message.isUser ? 'You' : 'Agent'"
            />
          </div>
        </div>
      </ScrollArea>

      <!-- Fixed Textarea and Footer Container -->
      <div class="w-full md:w-[55%] mx-auto px-4 flex-shrink-0">
        <!-- Textarea -->
        <ChatTextarea @send="handleSendMessage" class="mt-4" />

        <!-- Footer -->
        <ChatFooter class="mt-2" />
      </div>
    </SidebarInset>
  </SidebarProvider>
</template>

<script setup lang="ts">
import { useCookies } from '@vueuse/integrations/useCookies'
import { loadGtmScript } from '@/utils/gtm'

const cookies = useCookies(['sidebar:state'])
const cookieName = 'sidebar:state'
const sidebarState = ref<boolean>(cookies.get(cookieName))

// Dummy messages data
const messages = ref([
  {
    text: 'Hello! How can I help you today?',
    isUser: false,
  },
  {
    text: 'I need help with setting up a new project.',
    isUser: true,
  },
  {
    text: "Sure, I'd be happy to help you set up a new project. What kind of project are you looking to create? Is it a web application, mobile app, or something else?",
    isUser: false,
  },
  {
    text: 'I want to create a Vue.js web application.',
    isUser: true,
  },
  {
    text: 'Great choice! Vue.js is an excellent framework for building modern web applications. Here are the steps to set up a new Vue.js project:\n\n1. Make sure you have Node.js installed\n2. Install Vue CLI: `npm install -g @vue/cli`\n3. Create a new project: `vue create my-project`\n4. Choose the features you need (Babel, Router, Vuex, etc.)\n5. Navigate to your project: `cd my-project`\n6. Start the development server: `npm run serve`\n\nWould you like me to explain any of these steps in more detail?',
    isUser: false,
  },
  {
    text: "# Vue.js Project Structure\n\nHere's an overview of a typical Vue.js project structure:",
    isUser: false,
  },
  {
    text: '<pre>\n├── public/\n│   ├── index.html\n│   └── favicon.ico\n├── src/\n│   ├── assets/\n│   ├── components/\n│   ├── views/\n│   ├── App.vue\n│   └── main.js\n├── package.json\n└── README.md</pre>',
    isUser: false,
  },
  {
    text: 'Can you explain what each folder is for?',
    isUser: true,
  },
  {
    text: "## Project Folders Explained\n\n- **public/**: Static assets that won't be processed by webpack\n- **src/**: Source code of your application\n- **assets/**: Images, fonts, and other resources\n- **components/**: Reusable Vue components\n- **views/**: Page components used by the router",
    isUser: false,
  },
  {
    text: 'How do I create a new component?',
    isUser: true,
  },
  {
    text: 'What about styling components?',
    isUser: true,
  },
  {
    text: 'Vue components can be styled in multiple ways:\n\n1. **Scoped CSS**:\n```css\n<style scoped>\n.hello {\n  color: #2c3e50;\n  margin-top: 60px;\n}\n</style>\n```\n\n2. **CSS Modules**:\n```css\n<style module>\n.title {\n  font-size: 2em;\n  text-align: center;\n}\n</style>\n```',
    isUser: false,
  },
  {
    text: 'Can you show me how to handle user input?',
    isUser: true,
  },
  {
    text: 'Here\'s an example form component:\n\n```vue\n<template>\n  <form @submit.prevent="handleSubmit">\n    <input v-model="username" placeholder="Username">\n    <input v-model="password" type="password">\n    <button type="submit">Login</button>\n  </form>\n</template>\n```\n\nThe `@submit.prevent` prevents the default form submission.',
    isUser: false,
  },
  {
    text: 'What about making API calls?',
    isUser: true,
  },
  {
    text: "Here's how to make API calls using the Fetch API:\n\n```javascript\nasync function fetchData() {\n  try {\n    const response = await fetch('https://api.example.com/data')\n    const data = await response.json()\n    console.log(data)\n  } catch (error) {\n    console.error('Error:', error)\n  }\n}\n```",
    isUser: false,
  },
  {
    text: "Can you explain Vue's reactivity system?",
    isUser: true,
  },
  {
    text: "## Vue 3 Reactivity\n\nVue 3 uses a Proxy-based reactivity system. Here's a simple example:\n\n```javascript\nconst count = ref(0)\n\n// The template will automatically update when count changes\ncount.value++\n```\n\n### Reactive Objects\n\n```javascript\nconst state = reactive({\n  count: 0,\n  message: 'Hello'\n})\n```",
    isUser: false,
  },
  {
    text: 'How do I handle routing?',
    isUser: true,
  },
  {
    text: "First, install Vue Router:\n\n<pre><code>npm install vue-router@4</code></pre>\n\nThen set up your routes:\n\n```javascript\nconst routes = [\n  { path: '/', component: Home },\n  { path: '/about', component: About },\n  { path: '/users/:id', component: UserProfile }\n]\n```",
    isUser: false,
  },
  {
    text: 'What about state management?',
    isUser: true,
  },
  {
    text: "# State Management with Pinia\n\nPinia is the recommended state management solution for Vue 3. Here's an example store:\n\n```javascript\nexport const useStore = defineStore('main', {\n  state: () => ({\n    count: 0,\n    name: 'John'\n  }),\n  actions: {\n    increment() {\n      this.count++\n    }\n  }\n})\n```",
    isUser: false,
  },
  {
    text: 'How do I deploy my Vue app?',
    isUser: true,
  },
  {
    text: '## Deployment Steps\n\n1. Build your application:\n<pre><code>npm run build</code></pre>\n\n2. The built files will be in the `dist` directory\n\n3. Deploy to your hosting service:\n\n```bash\n# Example for Netlify\ngit init\ngit add dist\ngit commit -m "Deploy"\ngit push\n```',
    isUser: false,
  },
  {
    text: 'What about testing?',
    isUser: true,
  },
  {
    text: "Here's an example test using Vitest:\n\n```javascript\nimport { mount } from '@vue/test-utils'\nimport { describe, it, expect } from 'vitest'\nimport Counter from './Counter.vue'\n\ndescribe('Counter.vue', () => {\n  it('increments count when button is clicked', async () => {\n    const wrapper = mount(Counter)\n    await wrapper.find('button').trigger('click')\n    expect(wrapper.text()).toContain('Count: 1')\n  })\n})\n```",
    isUser: false,
  },
  {
    text: 'Can you show me some advanced component patterns?',
    isUser: true,
  },
  {
    text: '## Advanced Component Patterns\n\n### 1. Composables\n```javascript\nfunction useCounter() {\n  const count = ref(0)\n  const increment = () => count.value++\n  return { count, increment }\n}\n```\n\n### 2. Slots\n```vue\n<template>\n  <div class="card">\n    <header>\n      <slot name="header">Default header</slot>\n    </header>\n    <slot>Default content</slot>\n  </div>\n</template>\n```',
    isUser: false,
  },
  {
    text: 'What about animations?',
    isUser: true,
  },
  {
    text: 'Vue provides transition components for animations:\n\n```vue\n<template>\n  <Transition name="fade">\n    <p v-if="show">Hello Vue!</p>\n  </Transition>\n</template>\n\n<style>\n.fade-enter-active,\n.fade-leave-active {\n  transition: opacity 0.5s ease;\n}\n\n.fade-enter-from,\n.fade-leave-to {\n  opacity: 0;\n}\n</style>\n```',
    isUser: false,
  },
  {
    text: 'How do I handle errors?',
    isUser: true,
  },
  {
    text: '## Error Handling in Vue\n\n1. **Component Error Boundaries**:\n```vue\n<template>\n  <ErrorBoundary>\n    <SomeComponent />\n    <template #fallback="{ error }">\n      <div class="error">{{ error }}</div>\n    </template>\n  </ErrorBoundary>\n</template>\n```\n\n2. **Global Error Handler**:\n```javascript\napp.config.errorHandler = (err) => {\n  console.error(err)\n  // Send to error tracking service\n}\n```',
    isUser: false,
  },
  {
    text: 'This is really helpful! Can you summarize the key concepts?',
    isUser: true,
  },
  {
    text: '# Vue.js Key Concepts\n\n1. **Components**\n   - Building blocks of Vue apps\n   - Reusable and composable\n\n2. **Reactivity**\n   - Automatic UI updates\n   - `ref()` and `reactive()`\n\n3. **Template Syntax**\n   - `v-bind` and `v-on`\n   - Directives and expressions\n\n4. **Composition API**\n   - More flexible code organization\n   - Better TypeScript support\n\n> Remember: Vue is progressive - you can adopt features as needed!',
    isUser: false,
  },
])

const handleSendMessage = (message: string) => {
  // Add user message
  messages.value.push({
    text: message,
    isUser: true,
  })

  // Simulate agent response (in a real app, this would be an API call)
  setTimeout(() => {
    messages.value.push({
      text: `I received your message: "${message}". This is a simulated response.`,
      isUser: false,
    })
  }, 1000)
}

// Removed the loadGtmScript function definition from here

onMounted(() => {
  // TODO: Fetch agent settings here, including the gtmId
  const agentGtmId = 'GTM-XXXXXXX' // Replace with actual fetched GTM ID

  if (agentGtmId) {
    loadGtmScript(agentGtmId) // Use the imported function
  } else {
    console.log('No GTM ID found for this agent.')
  }
})
</script>
