<script setup lang="ts">
import type { HTMLAttributes } from 'vue'
import { Button } from 'src/shadcn/ui/button'
import { cn } from 'src/shadcn/utils'
import { useSidebar } from './utils'

const props = defineProps<{
  class?: HTMLAttributes['class']
}>()

const { toggleSidebar } = useSidebar()
</script>

<template>
  <Button
    data-sidebar="trigger"
    variant="ghost"
    size="icon"
    :class="cn('h-7 w-7', props.class)"
    @click="toggleSidebar"
  >
    <BaseIcon name="PanelLeft" />
    <span class="sr-only">Toggle Sidebar</span>
  </Button>
</template>
