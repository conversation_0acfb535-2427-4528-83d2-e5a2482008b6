{"$schema": "https://shadcn-vue.com/schema.json", "style": "new-york", "typescript": true, "tailwind": {"config": "tailwind.config.js", "css": "src/assets/index.css", "baseColor": "zinc", "cssVariables": true, "prefix": ""}, "iconLibrary": "lucide", "aliases": {"utils": "src/shadcn/utils", "components": "src/shadcn/components", "ui": "src/shadcn/ui", "lib": "src/shadcn/lib", "composables": "src/shadcn/composables"}}