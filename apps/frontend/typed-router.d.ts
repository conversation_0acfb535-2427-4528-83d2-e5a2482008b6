/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-router. ‼️ DO NOT MODIFY THIS FILE ‼️
// It's recommended to commit this file.
// Make sure to add this file to your tsconfig.json file as an "includes" or "files" entry.

declare module 'vue-router/auto-routes' {
  import type {
    RouteRecordInfo,
    ParamValue,
    ParamValueOneOrMore,
    ParamValueZeroOrMore,
    ParamValueZeroOrOne,
  } from 'vue-router'

  /**
   * Route name map generated by unplugin-vue-router
   */
  export interface RouteNamedMap {
    '/': RouteRecordInfo<'/', '/', Record<never, never>, Record<never, never>>,
    '/auth/login': RouteRecordInfo<'/auth/login', '/auth/login', Record<never, never>, Record<never, never>>,
    '/auth/register': RouteRecordInfo<'/auth/register', '/auth/register', Record<never, never>, Record<never, never>>,
    '/auth/reset': RouteRecordInfo<'/auth/reset', '/auth/reset', Record<never, never>, Record<never, never>>,
    '/subscriptions/cancel': RouteRecordInfo<'/subscriptions/cancel', '/subscriptions/cancel', Record<never, never>, Record<never, never>>,
    '/subscriptions/portal': RouteRecordInfo<'/subscriptions/portal', '/subscriptions/portal', Record<never, never>, Record<never, never>>,
    '/subscriptions/success': RouteRecordInfo<'/subscriptions/success', '/subscriptions/success', Record<never, never>, Record<never, never>>,
  }
}
