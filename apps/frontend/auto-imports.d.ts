/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// noinspection JSUnusedGlobalSymbols
// Generated by unplugin-auto-import
// biome-ignore lint: disable
export {}
declare global {
  const AlertDialog: typeof import('./src/shadcn/ui/alert-dialog/index')['AlertDialog']
  const AlertDialogAction: typeof import('./src/shadcn/ui/alert-dialog/index')['AlertDialogAction']
  const AlertDialogCancel: typeof import('./src/shadcn/ui/alert-dialog/index')['AlertDialogCancel']
  const AlertDialogContent: typeof import('./src/shadcn/ui/alert-dialog/index')['AlertDialogContent']
  const AlertDialogDescription: typeof import('./src/shadcn/ui/alert-dialog/index')['AlertDialogDescription']
  const AlertDialogFooter: typeof import('./src/shadcn/ui/alert-dialog/index')['AlertDialogFooter']
  const AlertDialogHeader: typeof import('./src/shadcn/ui/alert-dialog/index')['AlertDialogHeader']
  const AlertDialogTitle: typeof import('./src/shadcn/ui/alert-dialog/index')['AlertDialogTitle']
  const AlertDialogTrigger: typeof import('./src/shadcn/ui/alert-dialog/index')['AlertDialogTrigger']
  const Avatar: typeof import('./src/shadcn/ui/avatar/index')['Avatar']
  const AvatarFallback: typeof import('./src/shadcn/ui/avatar/index')['AvatarFallback']
  const AvatarImage: typeof import('./src/shadcn/ui/avatar/index')['AvatarImage']
  const Badge: typeof import('./src/shadcn/ui/badge/index')['Badge']
  const Breadcrumb: typeof import('./src/shadcn/ui/breadcrumb/index')['Breadcrumb']
  const BreadcrumbEllipsis: typeof import('./src/shadcn/ui/breadcrumb/index')['BreadcrumbEllipsis']
  const BreadcrumbItem: typeof import('./src/shadcn/ui/breadcrumb/index')['BreadcrumbItem']
  const BreadcrumbLink: typeof import('./src/shadcn/ui/breadcrumb/index')['BreadcrumbLink']
  const BreadcrumbList: typeof import('./src/shadcn/ui/breadcrumb/index')['BreadcrumbList']
  const BreadcrumbPage: typeof import('./src/shadcn/ui/breadcrumb/index')['BreadcrumbPage']
  const BreadcrumbSeparator: typeof import('./src/shadcn/ui/breadcrumb/index')['BreadcrumbSeparator']
  const Button: typeof import('./src/shadcn/ui/button/index')['Button']
  const Card: typeof import('./src/shadcn/ui/card/index')['Card']
  const CardContent: typeof import('./src/shadcn/ui/card/index')['CardContent']
  const CardDescription: typeof import('./src/shadcn/ui/card/index')['CardDescription']
  const CardFooter: typeof import('./src/shadcn/ui/card/index')['CardFooter']
  const CardHeader: typeof import('./src/shadcn/ui/card/index')['CardHeader']
  const CardTitle: typeof import('./src/shadcn/ui/card/index')['CardTitle']
  const Checkbox: typeof import('./src/shadcn/ui/checkbox/index')['Checkbox']
  const Collapsible: typeof import('./src/shadcn/ui/collapsible/index')['Collapsible']
  const CollapsibleContent: typeof import('./src/shadcn/ui/collapsible/index')['CollapsibleContent']
  const CollapsibleTrigger: typeof import('./src/shadcn/ui/collapsible/index')['CollapsibleTrigger']
  const DIALOG_KEYS: typeof import('./src/stores/dialog')['DIALOG_KEYS']
  const Dialog: typeof import('./src/shadcn/ui/dialog/index')['Dialog']
  const DialogClose: typeof import('./src/shadcn/ui/dialog/index')['DialogClose']
  const DialogContent: typeof import('./src/shadcn/ui/dialog/index')['DialogContent']
  const DialogDescription: typeof import('./src/shadcn/ui/dialog/index')['DialogDescription']
  const DialogFooter: typeof import('./src/shadcn/ui/dialog/index')['DialogFooter']
  const DialogHeader: typeof import('./src/shadcn/ui/dialog/index')['DialogHeader']
  const DialogScrollContent: typeof import('./src/shadcn/ui/dialog/index')['DialogScrollContent']
  const DialogTitle: typeof import('./src/shadcn/ui/dialog/index')['DialogTitle']
  const DialogTrigger: typeof import('./src/shadcn/ui/dialog/index')['DialogTrigger']
  const DropdownMenu: typeof import('./src/shadcn/ui/dropdown-menu/index')['DropdownMenu']
  const DropdownMenuCheckboxItem: typeof import('./src/shadcn/ui/dropdown-menu/index')['DropdownMenuCheckboxItem']
  const DropdownMenuContent: typeof import('./src/shadcn/ui/dropdown-menu/index')['DropdownMenuContent']
  const DropdownMenuGroup: typeof import('./src/shadcn/ui/dropdown-menu/index')['DropdownMenuGroup']
  const DropdownMenuItem: typeof import('./src/shadcn/ui/dropdown-menu/index')['DropdownMenuItem']
  const DropdownMenuLabel: typeof import('./src/shadcn/ui/dropdown-menu/index')['DropdownMenuLabel']
  const DropdownMenuPortal: typeof import('./src/shadcn/ui/dropdown-menu/index')['DropdownMenuPortal']
  const DropdownMenuRadioGroup: typeof import('./src/shadcn/ui/dropdown-menu/index')['DropdownMenuRadioGroup']
  const DropdownMenuRadioItem: typeof import('./src/shadcn/ui/dropdown-menu/index')['DropdownMenuRadioItem']
  const DropdownMenuSeparator: typeof import('./src/shadcn/ui/dropdown-menu/index')['DropdownMenuSeparator']
  const DropdownMenuShortcut: typeof import('./src/shadcn/ui/dropdown-menu/index')['DropdownMenuShortcut']
  const DropdownMenuSub: typeof import('./src/shadcn/ui/dropdown-menu/index')['DropdownMenuSub']
  const DropdownMenuSubContent: typeof import('./src/shadcn/ui/dropdown-menu/index')['DropdownMenuSubContent']
  const DropdownMenuSubTrigger: typeof import('./src/shadcn/ui/dropdown-menu/index')['DropdownMenuSubTrigger']
  const DropdownMenuTrigger: typeof import('./src/shadcn/ui/dropdown-menu/index')['DropdownMenuTrigger']
  const EffectScope: typeof import('vue')['EffectScope']
  const FORM_ITEM_INJECTION_KEY: typeof import('./src/shadcn/ui/form/index')['FORM_ITEM_INJECTION_KEY']
  const Form: typeof import('./src/shadcn/ui/form/index')['Form']
  const FormControl: typeof import('./src/shadcn/ui/form/index')['FormControl']
  const FormDescription: typeof import('./src/shadcn/ui/form/index')['FormDescription']
  const FormField: typeof import('./src/shadcn/ui/form/index')['FormField']
  const FormFieldArray: typeof import('./src/shadcn/ui/form/index')['FormFieldArray']
  const FormItem: typeof import('./src/shadcn/ui/form/index')['FormItem']
  const FormLabel: typeof import('./src/shadcn/ui/form/index')['FormLabel']
  const FormMessage: typeof import('./src/shadcn/ui/form/index')['FormMessage']
  const ICONS: typeof import('./src/constants')['ICONS']
  const Input: typeof import('./src/shadcn/ui/input/index')['Input']
  const Label: typeof import('./src/shadcn/ui/label/index')['Label']
  const NavigationMenu: typeof import('./src/shadcn/ui/navigation-menu/index')['NavigationMenu']
  const NavigationMenuContent: typeof import('./src/shadcn/ui/navigation-menu/index')['NavigationMenuContent']
  const NavigationMenuIndicator: typeof import('./src/shadcn/ui/navigation-menu/index')['NavigationMenuIndicator']
  const NavigationMenuItem: typeof import('./src/shadcn/ui/navigation-menu/index')['NavigationMenuItem']
  const NavigationMenuLink: typeof import('./src/shadcn/ui/navigation-menu/index')['NavigationMenuLink']
  const NavigationMenuList: typeof import('./src/shadcn/ui/navigation-menu/index')['NavigationMenuList']
  const NavigationMenuTrigger: typeof import('./src/shadcn/ui/navigation-menu/index')['NavigationMenuTrigger']
  const NavigationMenuViewport: typeof import('./src/shadcn/ui/navigation-menu/index')['NavigationMenuViewport']
  const PROVIDERS: typeof import('./src/constants')['PROVIDERS']
  const PinInput: typeof import('./src/shadcn/ui/pin-input/index')['PinInput']
  const PinInputGroup: typeof import('./src/shadcn/ui/pin-input/index')['PinInputGroup']
  const PinInputInput: typeof import('./src/shadcn/ui/pin-input/index')['PinInputInput']
  const PinInputSeparator: typeof import('./src/shadcn/ui/pin-input/index')['PinInputSeparator']
  const ScrollArea: typeof import('./src/shadcn/ui/scroll-area/index')['ScrollArea']
  const ScrollBar: typeof import('./src/shadcn/ui/scroll-area/index')['ScrollBar']
  const Select: typeof import('./src/shadcn/ui/select/index')['Select']
  const SelectContent: typeof import('./src/shadcn/ui/select/index')['SelectContent']
  const SelectGroup: typeof import('./src/shadcn/ui/select/index')['SelectGroup']
  const SelectItem: typeof import('./src/shadcn/ui/select/index')['SelectItem']
  const SelectItemText: typeof import('./src/shadcn/ui/select/index')['SelectItemText']
  const SelectLabel: typeof import('./src/shadcn/ui/select/index')['SelectLabel']
  const SelectScrollDownButton: typeof import('./src/shadcn/ui/select/index')['SelectScrollDownButton']
  const SelectScrollUpButton: typeof import('./src/shadcn/ui/select/index')['SelectScrollUpButton']
  const SelectSeparator: typeof import('./src/shadcn/ui/select/index')['SelectSeparator']
  const SelectTrigger: typeof import('./src/shadcn/ui/select/index')['SelectTrigger']
  const SelectValue: typeof import('./src/shadcn/ui/select/index')['SelectValue']
  const Separator: typeof import('./src/shadcn/ui/separator/index')['Separator']
  const Sheet: typeof import('./src/shadcn/ui/sheet/index')['Sheet']
  const SheetClose: typeof import('./src/shadcn/ui/sheet/index')['SheetClose']
  const SheetContent: typeof import('./src/shadcn/ui/sheet/index')['SheetContent']
  const SheetDescription: typeof import('./src/shadcn/ui/sheet/index')['SheetDescription']
  const SheetFooter: typeof import('./src/shadcn/ui/sheet/index')['SheetFooter']
  const SheetHeader: typeof import('./src/shadcn/ui/sheet/index')['SheetHeader']
  const SheetTitle: typeof import('./src/shadcn/ui/sheet/index')['SheetTitle']
  const SheetTrigger: typeof import('./src/shadcn/ui/sheet/index')['SheetTrigger']
  const Sidebar: typeof import('./src/shadcn/ui/sidebar/index')['Sidebar']
  const SidebarContent: typeof import('./src/shadcn/ui/sidebar/index')['SidebarContent']
  const SidebarFooter: typeof import('./src/shadcn/ui/sidebar/index')['SidebarFooter']
  const SidebarGroup: typeof import('./src/shadcn/ui/sidebar/index')['SidebarGroup']
  const SidebarGroupAction: typeof import('./src/shadcn/ui/sidebar/index')['SidebarGroupAction']
  const SidebarGroupContent: typeof import('./src/shadcn/ui/sidebar/index')['SidebarGroupContent']
  const SidebarGroupLabel: typeof import('./src/shadcn/ui/sidebar/index')['SidebarGroupLabel']
  const SidebarHeader: typeof import('./src/shadcn/ui/sidebar/index')['SidebarHeader']
  const SidebarInput: typeof import('./src/shadcn/ui/sidebar/index')['SidebarInput']
  const SidebarInset: typeof import('./src/shadcn/ui/sidebar/index')['SidebarInset']
  const SidebarMenu: typeof import('./src/shadcn/ui/sidebar/index')['SidebarMenu']
  const SidebarMenuAction: typeof import('./src/shadcn/ui/sidebar/index')['SidebarMenuAction']
  const SidebarMenuBadge: typeof import('./src/shadcn/ui/sidebar/index')['SidebarMenuBadge']
  const SidebarMenuButton: typeof import('./src/shadcn/ui/sidebar/index')['SidebarMenuButton']
  const SidebarMenuItem: typeof import('./src/shadcn/ui/sidebar/index')['SidebarMenuItem']
  const SidebarMenuSkeleton: typeof import('./src/shadcn/ui/sidebar/index')['SidebarMenuSkeleton']
  const SidebarMenuSub: typeof import('./src/shadcn/ui/sidebar/index')['SidebarMenuSub']
  const SidebarMenuSubButton: typeof import('./src/shadcn/ui/sidebar/index')['SidebarMenuSubButton']
  const SidebarMenuSubItem: typeof import('./src/shadcn/ui/sidebar/index')['SidebarMenuSubItem']
  const SidebarProvider: typeof import('./src/shadcn/ui/sidebar/index')['SidebarProvider']
  const SidebarRail: typeof import('./src/shadcn/ui/sidebar/index')['SidebarRail']
  const SidebarSeparator: typeof import('./src/shadcn/ui/sidebar/index')['SidebarSeparator']
  const SidebarTrigger: typeof import('./src/shadcn/ui/sidebar/index')['SidebarTrigger']
  const Skeleton: typeof import('./src/shadcn/ui/skeleton/index')['Skeleton']
  const Slider: typeof import('./src/shadcn/ui/slider/index')['Slider']
  const Switch: typeof import('./src/shadcn/ui/switch/index')['Switch']
  const Table: typeof import('./src/shadcn/ui/table/index')['Table']
  const TableBody: typeof import('./src/shadcn/ui/table/index')['TableBody']
  const TableCaption: typeof import('./src/shadcn/ui/table/index')['TableCaption']
  const TableCell: typeof import('./src/shadcn/ui/table/index')['TableCell']
  const TableEmpty: typeof import('./src/shadcn/ui/table/index')['TableEmpty']
  const TableFooter: typeof import('./src/shadcn/ui/table/index')['TableFooter']
  const TableHead: typeof import('./src/shadcn/ui/table/index')['TableHead']
  const TableHeader: typeof import('./src/shadcn/ui/table/index')['TableHeader']
  const TableRow: typeof import('./src/shadcn/ui/table/index')['TableRow']
  const Tabs: typeof import('./src/shadcn/ui/tabs/index')['Tabs']
  const TabsContent: typeof import('./src/shadcn/ui/tabs/index')['TabsContent']
  const TabsList: typeof import('./src/shadcn/ui/tabs/index')['TabsList']
  const TabsTrigger: typeof import('./src/shadcn/ui/tabs/index')['TabsTrigger']
  const Textarea: typeof import('./src/shadcn/ui/textarea/index')['Textarea']
  const Toast: typeof import('./src/shadcn/ui/toast/index')['Toast']
  const ToastAction: typeof import('./src/shadcn/ui/toast/index')['ToastAction']
  const ToastClose: typeof import('./src/shadcn/ui/toast/index')['ToastClose']
  const ToastDescription: typeof import('./src/shadcn/ui/toast/index')['ToastDescription']
  const ToastProvider: typeof import('./src/shadcn/ui/toast/index')['ToastProvider']
  const ToastTitle: typeof import('./src/shadcn/ui/toast/index')['ToastTitle']
  const ToastViewport: typeof import('./src/shadcn/ui/toast/index')['ToastViewport']
  const Toaster: typeof import('./src/shadcn/ui/toast/index')['Toaster']
  const Tooltip: typeof import('./src/shadcn/ui/tooltip/index')['Tooltip']
  const TooltipContent: typeof import('./src/shadcn/ui/tooltip/index')['TooltipContent']
  const TooltipProvider: typeof import('./src/shadcn/ui/tooltip/index')['TooltipProvider']
  const TooltipTrigger: typeof import('./src/shadcn/ui/tooltip/index')['TooltipTrigger']
  const acceptHMRUpdate: typeof import('pinia')['acceptHMRUpdate']
  const avatarVariant: typeof import('./src/shadcn/ui/avatar/index')['avatarVariant']
  const badgeVariants: typeof import('./src/shadcn/ui/badge/index')['badgeVariants']
  const buttonVariants: typeof import('./src/shadcn/ui/button/index')['buttonVariants']
  const computed: typeof import('vue')['computed']
  const createApp: typeof import('vue')['createApp']
  const createPinia: typeof import('pinia')['createPinia']
  const customRef: typeof import('vue')['customRef']
  const defineAsyncComponent: typeof import('vue')['defineAsyncComponent']
  const defineComponent: typeof import('vue')['defineComponent']
  const defineStore: typeof import('pinia')['defineStore']
  const dialogConfigs: typeof import('./src/stores/dialog')['dialogConfigs']
  const dialogs: typeof import('./src/stores/dialog')['dialogs']
  const effectScope: typeof import('vue')['effectScope']
  const getActivePinia: typeof import('pinia')['getActivePinia']
  const getCurrentApp: typeof import('./src/plugins/router')['getCurrentApp']
  const getCurrentInstance: typeof import('vue')['getCurrentInstance']
  const getCurrentScope: typeof import('vue')['getCurrentScope']
  const getHead: typeof import('./src/plugins/unhead')['getHead']
  const getRouter: typeof import('./src/plugins/router')['getRouter']
  const h: typeof import('vue')['h']
  const inject: typeof import('vue')['inject']
  const installApi: typeof import('./src/plugins/api')['installApi']
  const installPinia: typeof import('./src/plugins/pinia')['installPinia']
  const installRouter: typeof import('./src/plugins/router')['installRouter']
  const installTanstackQuery: typeof import('./src/plugins/tanstack-query')['installTanstackQuery']
  const installUnhead: typeof import('./src/plugins/unhead')['installUnhead']
  const isProxy: typeof import('vue')['isProxy']
  const isReactive: typeof import('vue')['isReactive']
  const isReadonly: typeof import('vue')['isReadonly']
  const isRef: typeof import('vue')['isRef']
  const mapActions: typeof import('pinia')['mapActions']
  const mapGetters: typeof import('pinia')['mapGetters']
  const mapState: typeof import('pinia')['mapState']
  const mapStores: typeof import('pinia')['mapStores']
  const mapWritableState: typeof import('pinia')['mapWritableState']
  const markRaw: typeof import('vue')['markRaw']
  const navigationMenuTriggerStyle: typeof import('./src/shadcn/ui/navigation-menu/index')['navigationMenuTriggerStyle']
  const nextTick: typeof import('vue')['nextTick']
  const onActivated: typeof import('vue')['onActivated']
  const onBeforeMount: typeof import('vue')['onBeforeMount']
  const onBeforeRouteLeave: typeof import('vue-router')['onBeforeRouteLeave']
  const onBeforeRouteUpdate: typeof import('vue-router')['onBeforeRouteUpdate']
  const onBeforeUnmount: typeof import('vue')['onBeforeUnmount']
  const onBeforeUpdate: typeof import('vue')['onBeforeUpdate']
  const onDeactivated: typeof import('vue')['onDeactivated']
  const onErrorCaptured: typeof import('vue')['onErrorCaptured']
  const onMounted: typeof import('vue')['onMounted']
  const onRenderTracked: typeof import('vue')['onRenderTracked']
  const onRenderTriggered: typeof import('vue')['onRenderTriggered']
  const onScopeDispose: typeof import('vue')['onScopeDispose']
  const onServerPrefetch: typeof import('vue')['onServerPrefetch']
  const onUnmounted: typeof import('vue')['onUnmounted']
  const onUpdated: typeof import('vue')['onUpdated']
  const onWatcherCleanup: typeof import('vue')['onWatcherCleanup']
  const provide: typeof import('vue')['provide']
  const queryClient: typeof import('./src/plugins/tanstack-query')['queryClient']
  const reactive: typeof import('vue')['reactive']
  const readonly: typeof import('vue')['readonly']
  const ref: typeof import('vue')['ref']
  const resolveComponent: typeof import('vue')['resolveComponent']
  const setActivePinia: typeof import('pinia')['setActivePinia']
  const setMapStoreSuffix: typeof import('pinia')['setMapStoreSuffix']
  const setupNavigationGuards: typeof import('./src/plugins/navigation-guards')['setupNavigationGuards']
  const shallowReactive: typeof import('vue')['shallowReactive']
  const shallowReadonly: typeof import('vue')['shallowReadonly']
  const shallowRef: typeof import('vue')['shallowRef']
  const sheetVariants: typeof import('./src/shadcn/ui/sheet/index')['sheetVariants']
  const sidebarMenuButtonVariants: typeof import('./src/shadcn/ui/sidebar/index')['sidebarMenuButtonVariants']
  const storeToRefs: typeof import('pinia')['storeToRefs']
  const toRaw: typeof import('vue')['toRaw']
  const toRef: typeof import('vue')['toRef']
  const toRefs: typeof import('vue')['toRefs']
  const toValue: typeof import('vue')['toValue']
  const toast: typeof import('./src/shadcn/ui/toast/index')['toast']
  const toastVariants: typeof import('./src/shadcn/ui/toast/index')['toastVariants']
  const triggerRef: typeof import('vue')['triggerRef']
  const unref: typeof import('vue')['unref']
  const useApi: typeof import('./src/plugins/api')['useApi']
  const useApiAnon: typeof import('./src/plugins/api')['useApiAnon']
  const useAppStore: typeof import('./src/stores/app')['useAppStore']
  const useAttrs: typeof import('vue')['useAttrs']
  const useConfirmDialog: typeof import('./src/composables/use-confirm-dialog')['useConfirmDialog']
  const useConstants: typeof import('@/common/constants')['useConstants']
  const useCookies: typeof import('@vueuse/integrations/useCookies')['useCookies']
  const useCssModule: typeof import('vue')['useCssModule']
  const useCssVars: typeof import('vue')['useCssVars']
  const useCurrentApp: typeof import('./src/plugins/router')['useCurrentApp']
  const useDialog: typeof import('./src/composables/use-dialog')['useDialog']
  const useDialogStore: typeof import('./src/stores/dialog')['useDialogStore']
  const useDomainStore: typeof import('@/stores')['useDomainStore']
  const useHead: typeof import('./src/plugins/unhead')['useHead']
  const useId: typeof import('vue')['useId']
  const useLink: typeof import('vue-router')['useLink']
  const useMarkdownRenderer: typeof import('./src/composables/use-markdown-renderer')['useMarkdownRenderer']
  const useModel: typeof import('vue')['useModel']
  const useRoute: typeof import('vue-router')['useRoute']
  const useRouter: typeof import('vue-router')['useRouter']
  const useSidebar: typeof import('./src/shadcn/ui/sidebar/index')['useSidebar']
  const useSlots: typeof import('vue')['useSlots']
  const useSubStore: typeof import('@/stores')['useSubStore']
  const useTemplateRef: typeof import('vue')['useTemplateRef']
  const useThemeStore: typeof import('./src/stores/theme')['useThemeStore']
  const useToast: typeof import('./src/shadcn/ui/toast/index')['useToast']
  const useTypedDialog: typeof import('./src/composables/use-dialog')['useTypedDialog']
  const useUserStore: typeof import('@/stores')['useUserStore']
  const watch: typeof import('vue')['watch']
  const watchEffect: typeof import('vue')['watchEffect']
  const watchPostEffect: typeof import('vue')['watchPostEffect']
  const watchSyncEffect: typeof import('vue')['watchSyncEffect']
}
// for type re-export
declare global {
  // @ts-ignore
  export type { Component, Slot, Slots, ComponentPublicInstance, ComputedRef, DirectiveBinding, ExtractDefaultPropTypes, ExtractPropTypes, ExtractPublicPropTypes, InjectionKey, PropType, Ref, MaybeRef, MaybeRefOrGetter, VNode, WritableComputedRef } from 'vue'
  import('vue')
  // @ts-ignore
  export type { AvatarVariants } from './src/shadcn/ui/avatar/index'
  import('./src/shadcn/ui/avatar/index')
  // @ts-ignore
  export type { BadgeVariants } from './src/shadcn/ui/badge/index'
  import('./src/shadcn/ui/badge/index')
  // @ts-ignore
  export type { ButtonVariants } from './src/shadcn/ui/button/index'
  import('./src/shadcn/ui/button/index')
  // @ts-ignore
  export type { SheetVariants } from './src/shadcn/ui/sheet/index'
  import('./src/shadcn/ui/sheet/index')
  // @ts-ignore
  export type { SidebarProps, SidebarMenuButtonVariants } from './src/shadcn/ui/sidebar/index'
  import('./src/shadcn/ui/sidebar/index')
  // @ts-ignore
  export type { ToastProps } from './src/shadcn/ui/toast/index'
  import('./src/shadcn/ui/toast/index')
  // @ts-ignore
  export type { SidebarMenuButtonProps } from './src/shadcn/ui/sidebar/SidebarMenuButtonChild.vue'
  import('./src/shadcn/ui/sidebar/SidebarMenuButtonChild.vue')
  // @ts-ignore
  export type { UseDialogOptions } from './src/composables/use-dialog'
  import('./src/composables/use-dialog')
  // @ts-ignore
  export type { DialogInstance, DialogConfig, BaseDialogProps, AgentInstallProps, AgentDesignProps, AgentSettingsProps, AgentKnowledgeProps, AgentToolsProps, AgentInboxProps, AgentIntegrationProps, AgentFeedbacksProps, AgentFeedbacksFormProps, AgentLeadsFormProps, AccountProps, UsersProps, UsersFormProps, SubscriptionsProps, SupportProps, WhitelabelProps, WhitelabelCopyrightsFormProps, WhitelabelDomainsFormProps, WhitelabelKeysFormProps, WhitelabelSmtpsFormProps, DialogPropsMap, DialogKey } from './src/stores/dialog'
  import('./src/stores/dialog')
}
