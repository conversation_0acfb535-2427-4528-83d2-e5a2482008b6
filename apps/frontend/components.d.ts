/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    Account: typeof import('./src/components/account/Account.vue')['default']
    AccountDetails: typeof import('./src/components/account/AccountProfile.vue')['default']
    AccountEmail: typeof import('./src/components/account/AccountEmail.vue')['default']
    AccountPassword: typeof import('./src/components/account/AccountPassword.vue')['default']
    AccountProfile: typeof import('./src/components/account/AccountProfile.vue')['default']
    AddonCard: typeof import('./src/components/billing/AddonCard.vue')['default']
    AgentAnalytics: typeof import('./src/components/agent/analytics/AgentAnalytics.vue')['default']
    AgentChats: typeof import('./src/components/agent/chats/AgentChats.vue')['default']
    AgentChatsForm: typeof import('./src/components/agent/chats/AgentChatsForm.vue')['default']
    AgentChatsTable: typeof import('./src/components/agent/chats/AgentChatsTable.vue')['default']
    AgentDesign: typeof import('./src/components/agent/design/AgentDesign.vue')['default']
    AgentDesignBranding: typeof import('./src/components/agent/design/AgentDesignBranding.vue')['default']
    AgentDesignDisplay: typeof import('./src/components/agent/design/AgentDesignDisplay.vue')['default']
    AgentDesignMessages: typeof import('./src/components/agent/design/AgentDesignMessages.vue')['default']
    AgentFeedbacks: typeof import('./src/components/agent/feedbacks/AgentFeedbacks.vue')['default']
    AgentFeedbacksForm: typeof import('./src/components/agent/feedbacks/AgentFeedbacksForm.vue')['default']
    AgentFeedbacksTable: typeof import('./src/components/agent/feedbacks/AgentFeedbacksTable.vue')['default']
    AgentInbox: typeof import('./src/components/agent/inbox/AgentInbox.vue')['default']
    AgentInstall: typeof import('./src/components/agent/install/AgentInstall.vue')['default']
    AgentInstallBubble: typeof import('./src/components/agent/install/AgentInstallBubble.vue')['default']
    AgentInstallHtml: typeof import('./src/components/agent/install/AgentInstallHtml.vue')['default']
    AgentInstallPlatforms: typeof import('./src/components/agent/install/AgentInstallPlatforms.vue')['default']
    AgentInstallQrCode: typeof import('./src/components/agent/install/AgentInstallQrCode.vue')['default']
    AgentInstallUrl: typeof import('./src/components/agent/install/AgentInstallUrl.vue')['default']
    AgentInstallVariables: typeof import('./src/components/agent/install/AgentInstallVariables.vue')['default']
    AgentInstallWindow: typeof import('./src/components/agent/install/AgentInstallWindow.vue')['default']
    AgentIntegration: typeof import('./src/components/agent/integration/AgentIntegration.vue')['default']
    AgentIntegrationApi: typeof import('./src/components/agent/integration/AgentIntegrationApi.vue')['default']
    AgentIntegrationEvents: typeof import('./src/components/agent/integration/AgentIntegrationEvents.vue')['default']
    AgentIntegrationWebhooks: typeof import('./src/components/agent/integration/AgentIntegrationWebhooks.vue')['default']
    AgentIntegrationWhatsapp: typeof import('./src/components/agent/integration/AgentIntegrationWhatsapp.vue')['default']
    AgentIntegrationZapier: typeof import('./src/components/agent/integration/AgentIntegrationZapier.vue')['default']
    AgentKnowledge: typeof import('./src/components/agent/knowledge/AgentKnowledge.vue')['default']
    AgentKnowledgeCatalogs: typeof import('./src/components/agent/knowledge/AgentKnowledgeCatalogs.vue')['default']
    AgentKnowledgeDocuments: typeof import('./src/components/agent/knowledge/AgentKnowledgeDocuments.vue')['default']
    AgentKnowledgeList: typeof import('./src/components/agent/knowledge/AgentKnowledgeList.vue')['default']
    AgentKnowledgeMedias: typeof import('./src/components/agent/knowledge/AgentKnowledgeMedias.vue')['default']
    AgentKnowledgePreview: typeof import('./src/components/agent/knowledge/AgentKnowledgePreview.vue')['default']
    AgentKnowledgeQa: typeof import('./src/components/agent/knowledge/AgentKnowledgeQa.vue')['default']
    AgentKnowledgeStorage: typeof import('./src/components/agent/knowledge/AgentKnowledgeStorage.vue')['default']
    AgentKnowledgeTexts: typeof import('./src/components/agent/knowledge/AgentKnowledgeTexts.vue')['default']
    AgentKnowledgeUrls: typeof import('./src/components/agent/knowledge/AgentKnowledgeUrls.vue')['default']
    AgentKnowledgeYoutube: typeof import('./src/components/agent/knowledge/AgentKnowledgeYoutube.vue')['default']
    AgentLeads: typeof import('./src/components/agent/leads/AgentLeads.vue')['default']
    AgentLeadsForm: typeof import('./src/components/agent/leads/AgentLeadsForm.vue')['default']
    AgentLeadsTable: typeof import('./src/components/agent/leads/AgentLeadsTable.vue')['default']
    AgentSettings: typeof import('./src/components/agent/settings/AgentSettings.vue')['default']
    AgentTools: typeof import('./src/components/agent/tools/AgentTools.vue')['default']
    AgentToolsAstra: typeof import('./src/components/agent/tools/AgentToolsAstra.vue')['default']
    AgentToolsCalendar: typeof import('./src/components/agent/tools/AgentToolsCalendar.vue')['default']
    AgentToolsHubspot: typeof import('./src/components/agent/tools/AgentToolsHubspot.vue')['default']
    AgentToolsImage: typeof import('./src/components/agent/tools/AgentToolsImage.vue')['default']
    AgentToolsPerplexity: typeof import('./src/components/agent/tools/AgentToolsPerplexity.vue')['default']
    AgentToolsRequestHuman: typeof import('./src/components/agent/tools/AgentToolsRequestHuman.vue')['default']
    AgentToolsResolvedChat: typeof import('./src/components/agent/tools/AgentToolsResolvedChat.vue')['default']
    AgentToolsSearch: typeof import('./src/components/agent/tools/AgentToolsSearch.vue')['default']
    AgentToolsShippingTracking: typeof import('./src/components/agent/tools/AgentToolsShippingTracking.vue')['default']
    AgentToolsShopify: typeof import('./src/components/agent/tools/AgentToolsShopify.vue')['default']
    AgentToolsWoocommerce: typeof import('./src/components/agent/tools/AgentToolsWoocommerce.vue')['default']
    AgentToolsZendesk: typeof import('./src/components/agent/tools/AgentToolsZendesk.vue')['default']
    AlertDialog: typeof import('./src/shadcn/ui/alert-dialog/AlertDialog.vue')['default']
    AlertDialogAction: typeof import('./src/shadcn/ui/alert-dialog/AlertDialogAction.vue')['default']
    AlertDialogCancel: typeof import('./src/shadcn/ui/alert-dialog/AlertDialogCancel.vue')['default']
    AlertDialogContent: typeof import('./src/shadcn/ui/alert-dialog/AlertDialogContent.vue')['default']
    AlertDialogDescription: typeof import('./src/shadcn/ui/alert-dialog/AlertDialogDescription.vue')['default']
    AlertDialogFooter: typeof import('./src/shadcn/ui/alert-dialog/AlertDialogFooter.vue')['default']
    AlertDialogHeader: typeof import('./src/shadcn/ui/alert-dialog/AlertDialogHeader.vue')['default']
    AlertDialogTitle: typeof import('./src/shadcn/ui/alert-dialog/AlertDialogTitle.vue')['default']
    AlertDialogTrigger: typeof import('./src/shadcn/ui/alert-dialog/AlertDialogTrigger.vue')['default']
    AuthLogin: typeof import('./src/components/auth/AuthLogin.vue')['default']
    AuthRegister: typeof import('./src/components/auth/AuthRegister.vue')['default']
    AuthReset: typeof import('./src/components/auth/AuthReset.vue')['default']
    Avatar: typeof import('./src/shadcn/ui/avatar/Avatar.vue')['default']
    AvatarFallback: typeof import('./src/shadcn/ui/avatar/AvatarFallback.vue')['default']
    AvatarImage: typeof import('./src/shadcn/ui/avatar/AvatarImage.vue')['default']
    Badge: typeof import('./src/shadcn/ui/badge/Badge.vue')['default']
    BaseButton: typeof import('./src/components/base/button/BaseButton.vue')['default']
    BaseCard: typeof import('./src/components/base/card/BaseCard.vue')['default']
    BaseColorPicker: typeof import('./src/components/base/color-picker/BaseColorPicker.vue')['default']
    BaseDialog: typeof import('./src/components/base/dialog/BaseDialog.vue')['default']
    BaseDropzone: typeof import('./src/components/base/dropzone/BaseDropzone.vue')['default']
    BaseForm: typeof import('./src/components/base/form-field/BaseForm.vue')['default']
    BaseFormField: typeof import('./src/components/base/form-field/BaseFormField.vue')['default']
    BaseIcon: typeof import('./src/components/base/icon/BaseIcon.vue')['default']
    BaseSkeleton: typeof import('./src/components/base/skeleton/BaseSkeleton.vue')['default']
    BaseTable: typeof import('./src/components/base/table/BaseTable.vue')['default']
    BaseTabs: typeof import('./src/components/base/tabs/BaseTabs.vue')['default']
    BaseTextarea: typeof import('./src/components/base/textarea/BaseTextarea.vue')['default']
    Billing: typeof import('./src/components/billing/Subscriptions.vue')['default']
    Breadcrumb: typeof import('./src/shadcn/ui/breadcrumb/Breadcrumb.vue')['default']
    BreadcrumbEllipsis: typeof import('./src/shadcn/ui/breadcrumb/BreadcrumbEllipsis.vue')['default']
    BreadcrumbItem: typeof import('./src/shadcn/ui/breadcrumb/BreadcrumbItem.vue')['default']
    BreadcrumbLink: typeof import('./src/shadcn/ui/breadcrumb/BreadcrumbLink.vue')['default']
    BreadcrumbList: typeof import('./src/shadcn/ui/breadcrumb/BreadcrumbList.vue')['default']
    BreadcrumbPage: typeof import('./src/shadcn/ui/breadcrumb/BreadcrumbPage.vue')['default']
    BreadcrumbSeparator: typeof import('./src/shadcn/ui/breadcrumb/BreadcrumbSeparator.vue')['default']
    Button: typeof import('./src/shadcn/ui/button/Button.vue')['default']
    Card: typeof import('./src/shadcn/ui/card/Card.vue')['default']
    CardContent: typeof import('./src/shadcn/ui/card/CardContent.vue')['default']
    CardDescription: typeof import('./src/shadcn/ui/card/CardDescription.vue')['default']
    CardFooter: typeof import('./src/shadcn/ui/card/CardFooter.vue')['default']
    CardHeader: typeof import('./src/shadcn/ui/card/CardHeader.vue')['default']
    CardTitle: typeof import('./src/shadcn/ui/card/CardTitle.vue')['default']
    ChatButtonModels: typeof import('./src/components/chat/button/ChatButtonModels.vue')['default']
    ChatButtonShare: typeof import('./src/components/chat/button/ChatButtonShare.vue')['default']
    ChatFooter: typeof import('./src/components/chat/footer/ChatFooter.vue')['default']
    ChatHeader: typeof import('./src/components/chat/header/ChatHeader.vue')['default']
    ChatMessage: typeof import('./src/components/chat/message/ChatMessage.vue')['default']
    ChatMessageActions: typeof import('./src/components/chat/message/ChatMessageActions.vue')['default']
    ChatSidebar: typeof import('./src/components/chat/sidebar/ChatSidebar.vue')['default']
    ChatSidebarAgents: typeof import('./src/components/chat/sidebar/ChatSidebarAgents.vue')['default']
    ChatSidebarHistory: typeof import('./src/components/chat/sidebar/ChatSidebarHistory.vue')['default']
    ChatSidebarLogo: typeof import('./src/components/chat/sidebar/ChatSidebarLogo.vue')['default']
    ChatSidebarMenu: typeof import('./src/components/chat/sidebar/ChatSidebarMenu.vue')['default']
    ChatSidebarMenuAgent: typeof import('./src/components/chat/sidebar/ChatSidebarMenuAgent.vue')['default']
    ChatSidebarMenuUser: typeof import('./src/components/chat/sidebar/ChatSidebarMenuUser.vue')['default']
    ChatSidebarMenuWhitelabel: typeof import('./src/components/chat/sidebar/ChatSidebarMenuWhitelabel.vue')['default']
    ChatSidebarSearch: typeof import('./src/components/chat/sidebar/ChatSidebarSearch.vue')['default']
    ChatSpeedDial: typeof import('./src/components/chat/speed-dial/ChatSpeedDial.vue')['default']
    ChatTextarea: typeof import('./src/components/chat/textarea/ChatTextarea.vue')['default']
    ChatTextareaFiles: typeof import('./src/components/chat/textarea/ChatTextareaFiles.vue')['default']
    ChatTextareaStt: typeof import('./src/components/chat/textarea/ChatTextareaStt.vue')['default']
    ChatTextareaSubmit: typeof import('./src/components/chat/textarea/ChatTextareaSubmit.vue')['default']
    ChatWelcomeMessage: typeof import('./src/components/chat/WelcomeMessage/ChatWelcomeMessage.vue')['default']
    Checkbox: typeof import('./src/shadcn/ui/checkbox/Checkbox.vue')['default']
    Collapsible: typeof import('./src/shadcn/ui/collapsible/Collapsible.vue')['default']
    CollapsibleContent: typeof import('./src/shadcn/ui/collapsible/CollapsibleContent.vue')['default']
    CollapsibleTrigger: typeof import('./src/shadcn/ui/collapsible/CollapsibleTrigger.vue')['default']
    Dialog: typeof import('./src/shadcn/ui/dialog/Dialog.vue')['default']
    DialogClose: typeof import('./src/shadcn/ui/dialog/DialogClose.vue')['default']
    DialogContent: typeof import('./src/shadcn/ui/dialog/DialogContent.vue')['default']
    DialogDescription: typeof import('./src/shadcn/ui/dialog/DialogDescription.vue')['default']
    DialogFooter: typeof import('./src/shadcn/ui/dialog/DialogFooter.vue')['default']
    DialogHeader: typeof import('./src/shadcn/ui/dialog/DialogHeader.vue')['default']
    DialogProvider: typeof import('./src/components/dialog/DialogProvider.vue')['default']
    DialogScrollContent: typeof import('./src/shadcn/ui/dialog/DialogScrollContent.vue')['default']
    DialogTest: typeof import('./src/components/test/DialogTest.vue')['default']
    DialogTitle: typeof import('./src/shadcn/ui/dialog/DialogTitle.vue')['default']
    DialogTrigger: typeof import('./src/shadcn/ui/dialog/DialogTrigger.vue')['default']
    DropdownMenu: typeof import('./src/shadcn/ui/dropdown-menu/DropdownMenu.vue')['default']
    DropdownMenuCheckboxItem: typeof import('./src/shadcn/ui/dropdown-menu/DropdownMenuCheckboxItem.vue')['default']
    DropdownMenuContent: typeof import('./src/shadcn/ui/dropdown-menu/DropdownMenuContent.vue')['default']
    DropdownMenuGroup: typeof import('./src/shadcn/ui/dropdown-menu/DropdownMenuGroup.vue')['default']
    DropdownMenuItem: typeof import('./src/shadcn/ui/dropdown-menu/DropdownMenuItem.vue')['default']
    DropdownMenuLabel: typeof import('./src/shadcn/ui/dropdown-menu/DropdownMenuLabel.vue')['default']
    DropdownMenuRadioGroup: typeof import('./src/shadcn/ui/dropdown-menu/DropdownMenuRadioGroup.vue')['default']
    DropdownMenuRadioItem: typeof import('./src/shadcn/ui/dropdown-menu/DropdownMenuRadioItem.vue')['default']
    DropdownMenuSeparator: typeof import('./src/shadcn/ui/dropdown-menu/DropdownMenuSeparator.vue')['default']
    DropdownMenuShortcut: typeof import('./src/shadcn/ui/dropdown-menu/DropdownMenuShortcut.vue')['default']
    DropdownMenuSub: typeof import('./src/shadcn/ui/dropdown-menu/DropdownMenuSub.vue')['default']
    DropdownMenuSubContent: typeof import('./src/shadcn/ui/dropdown-menu/DropdownMenuSubContent.vue')['default']
    DropdownMenuSubTrigger: typeof import('./src/shadcn/ui/dropdown-menu/DropdownMenuSubTrigger.vue')['default']
    DropdownMenuTrigger: typeof import('./src/shadcn/ui/dropdown-menu/DropdownMenuTrigger.vue')['default']
    FormControl: typeof import('./src/shadcn/ui/form/FormControl.vue')['default']
    FormDescription: typeof import('./src/shadcn/ui/form/FormDescription.vue')['default']
    FormItem: typeof import('./src/shadcn/ui/form/FormItem.vue')['default']
    FormLabel: typeof import('./src/shadcn/ui/form/FormLabel.vue')['default']
    FormMessage: typeof import('./src/shadcn/ui/form/FormMessage.vue')['default']
    Input: typeof import('./src/shadcn/ui/input/Input.vue')['default']
    Label: typeof import('./src/shadcn/ui/label/Label.vue')['default']
    NavigationMenu: typeof import('./src/shadcn/ui/navigation-menu/NavigationMenu.vue')['default']
    NavigationMenuContent: typeof import('./src/shadcn/ui/navigation-menu/NavigationMenuContent.vue')['default']
    NavigationMenuIndicator: typeof import('./src/shadcn/ui/navigation-menu/NavigationMenuIndicator.vue')['default']
    NavigationMenuItem: typeof import('./src/shadcn/ui/navigation-menu/NavigationMenuItem.vue')['default']
    NavigationMenuLink: typeof import('./src/shadcn/ui/navigation-menu/NavigationMenuLink.vue')['default']
    NavigationMenuList: typeof import('./src/shadcn/ui/navigation-menu/NavigationMenuList.vue')['default']
    NavigationMenuTrigger: typeof import('./src/shadcn/ui/navigation-menu/NavigationMenuTrigger.vue')['default']
    NavigationMenuViewport: typeof import('./src/shadcn/ui/navigation-menu/NavigationMenuViewport.vue')['default']
    PinInput: typeof import('./src/shadcn/ui/pin-input/PinInput.vue')['default']
    PinInputGroup: typeof import('./src/shadcn/ui/pin-input/PinInputGroup.vue')['default']
    PinInputInput: typeof import('./src/shadcn/ui/pin-input/PinInputInput.vue')['default']
    PinInputSeparator: typeof import('./src/shadcn/ui/pin-input/PinInputSeparator.vue')['default']
    PlanCard: typeof import('./src/components/billing/PlanCard.vue')['default']
    ProductCard: typeof import('./src/components/subscriptions/ProductCard.vue')['default']
    QuantityInput: typeof import('./src/components/billing/QuantityInput.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    ScrollArea: typeof import('./src/shadcn/ui/scroll-area/ScrollArea.vue')['default']
    ScrollBar: typeof import('./src/shadcn/ui/scroll-area/ScrollBar.vue')['default']
    Select: typeof import('./src/shadcn/ui/select/Select.vue')['default']
    SelectContent: typeof import('./src/shadcn/ui/select/SelectContent.vue')['default']
    SelectGroup: typeof import('./src/shadcn/ui/select/SelectGroup.vue')['default']
    SelectItem: typeof import('./src/shadcn/ui/select/SelectItem.vue')['default']
    SelectItemText: typeof import('./src/shadcn/ui/select/SelectItemText.vue')['default']
    SelectLabel: typeof import('./src/shadcn/ui/select/SelectLabel.vue')['default']
    SelectScrollDownButton: typeof import('./src/shadcn/ui/select/SelectScrollDownButton.vue')['default']
    SelectScrollUpButton: typeof import('./src/shadcn/ui/select/SelectScrollUpButton.vue')['default']
    SelectSeparator: typeof import('./src/shadcn/ui/select/SelectSeparator.vue')['default']
    SelectTrigger: typeof import('./src/shadcn/ui/select/SelectTrigger.vue')['default']
    SelectValue: typeof import('./src/shadcn/ui/select/SelectValue.vue')['default']
    Separator: typeof import('./src/shadcn/ui/separator/Separator.vue')['default']
    Sheet: typeof import('./src/shadcn/ui/sheet/Sheet.vue')['default']
    SheetClose: typeof import('./src/shadcn/ui/sheet/SheetClose.vue')['default']
    SheetContent: typeof import('./src/shadcn/ui/sheet/SheetContent.vue')['default']
    SheetDescription: typeof import('./src/shadcn/ui/sheet/SheetDescription.vue')['default']
    SheetFooter: typeof import('./src/shadcn/ui/sheet/SheetFooter.vue')['default']
    SheetHeader: typeof import('./src/shadcn/ui/sheet/SheetHeader.vue')['default']
    SheetTitle: typeof import('./src/shadcn/ui/sheet/SheetTitle.vue')['default']
    SheetTrigger: typeof import('./src/shadcn/ui/sheet/SheetTrigger.vue')['default']
    Sidebar: typeof import('./src/shadcn/ui/sidebar/Sidebar.vue')['default']
    SidebarContent: typeof import('./src/shadcn/ui/sidebar/SidebarContent.vue')['default']
    SidebarFooter: typeof import('./src/shadcn/ui/sidebar/SidebarFooter.vue')['default']
    SidebarGroup: typeof import('./src/shadcn/ui/sidebar/SidebarGroup.vue')['default']
    SidebarGroupAction: typeof import('./src/shadcn/ui/sidebar/SidebarGroupAction.vue')['default']
    SidebarGroupContent: typeof import('./src/shadcn/ui/sidebar/SidebarGroupContent.vue')['default']
    SidebarGroupLabel: typeof import('./src/shadcn/ui/sidebar/SidebarGroupLabel.vue')['default']
    SidebarHeader: typeof import('./src/shadcn/ui/sidebar/SidebarHeader.vue')['default']
    SidebarInput: typeof import('./src/shadcn/ui/sidebar/SidebarInput.vue')['default']
    SidebarInset: typeof import('./src/shadcn/ui/sidebar/SidebarInset.vue')['default']
    SidebarMenu: typeof import('./src/shadcn/ui/sidebar/SidebarMenu.vue')['default']
    SidebarMenuAction: typeof import('./src/shadcn/ui/sidebar/SidebarMenuAction.vue')['default']
    SidebarMenuBadge: typeof import('./src/shadcn/ui/sidebar/SidebarMenuBadge.vue')['default']
    SidebarMenuButton: typeof import('./src/shadcn/ui/sidebar/SidebarMenuButton.vue')['default']
    SidebarMenuButtonChild: typeof import('./src/shadcn/ui/sidebar/SidebarMenuButtonChild.vue')['default']
    SidebarMenuItem: typeof import('./src/shadcn/ui/sidebar/SidebarMenuItem.vue')['default']
    SidebarMenuSkeleton: typeof import('./src/shadcn/ui/sidebar/SidebarMenuSkeleton.vue')['default']
    SidebarMenuSub: typeof import('./src/shadcn/ui/sidebar/SidebarMenuSub.vue')['default']
    SidebarMenuSubButton: typeof import('./src/shadcn/ui/sidebar/SidebarMenuSubButton.vue')['default']
    SidebarMenuSubItem: typeof import('./src/shadcn/ui/sidebar/SidebarMenuSubItem.vue')['default']
    SidebarProvider: typeof import('./src/shadcn/ui/sidebar/SidebarProvider.vue')['default']
    SidebarRail: typeof import('./src/shadcn/ui/sidebar/SidebarRail.vue')['default']
    SidebarSeparator: typeof import('./src/shadcn/ui/sidebar/SidebarSeparator.vue')['default']
    SidebarTrigger: typeof import('./src/shadcn/ui/sidebar/SidebarTrigger.vue')['default']
    Skeleton: typeof import('./src/shadcn/ui/skeleton/Skeleton.vue')['default']
    Slider: typeof import('./src/shadcn/ui/slider/Slider.vue')['default']
    Subscriptions: typeof import('./src/components/subscriptions/Subscriptions.vue')['default']
    SubscriptionsItem: typeof import('./src/components/subscriptions/SubscriptionsItem.vue')['default']
    Support: typeof import('./src/components/support/Support.vue')['default']
    Switch: typeof import('./src/shadcn/ui/switch/Switch.vue')['default']
    Table: typeof import('./src/shadcn/ui/table/Table.vue')['default']
    TableBody: typeof import('./src/shadcn/ui/table/TableBody.vue')['default']
    TableCaption: typeof import('./src/shadcn/ui/table/TableCaption.vue')['default']
    TableCell: typeof import('./src/shadcn/ui/table/TableCell.vue')['default']
    TableEmpty: typeof import('./src/shadcn/ui/table/TableEmpty.vue')['default']
    TableFooter: typeof import('./src/shadcn/ui/table/TableFooter.vue')['default']
    TableHead: typeof import('./src/shadcn/ui/table/TableHead.vue')['default']
    TableHeader: typeof import('./src/shadcn/ui/table/TableHeader.vue')['default']
    TableRow: typeof import('./src/shadcn/ui/table/TableRow.vue')['default']
    Tabs: typeof import('./src/shadcn/ui/tabs/Tabs.vue')['default']
    TabsContent: typeof import('./src/shadcn/ui/tabs/TabsContent.vue')['default']
    TabsList: typeof import('./src/shadcn/ui/tabs/TabsList.vue')['default']
    TabsTrigger: typeof import('./src/shadcn/ui/tabs/TabsTrigger.vue')['default']
    Team: typeof import('./src/components/team/Users.vue')['default']
    TeamForm: typeof import('./src/components/team/UsersForm.vue')['default']
    Textarea: typeof import('./src/shadcn/ui/textarea/Textarea.vue')['default']
    ThemeToggle: typeof import('./src/components/theme/ThemeToggle.vue')['default']
    Toast: typeof import('./src/shadcn/ui/toast/Toast.vue')['default']
    ToastAction: typeof import('./src/shadcn/ui/toast/ToastAction.vue')['default']
    ToastClose: typeof import('./src/shadcn/ui/toast/ToastClose.vue')['default']
    ToastDescription: typeof import('./src/shadcn/ui/toast/ToastDescription.vue')['default']
    Toaster: typeof import('./src/shadcn/ui/toast/Toaster.vue')['default']
    ToastProvider: typeof import('./src/shadcn/ui/toast/ToastProvider.vue')['default']
    ToastTitle: typeof import('./src/shadcn/ui/toast/ToastTitle.vue')['default']
    ToastViewport: typeof import('./src/shadcn/ui/toast/ToastViewport.vue')['default']
    Tooltip: typeof import('./src/shadcn/ui/tooltip/Tooltip.vue')['default']
    TooltipContent: typeof import('./src/shadcn/ui/tooltip/TooltipContent.vue')['default']
    TooltipProvider: typeof import('./src/shadcn/ui/tooltip/TooltipProvider.vue')['default']
    TooltipTrigger: typeof import('./src/shadcn/ui/tooltip/TooltipTrigger.vue')['default']
    UserForm: typeof import('./src/components/users/UsersForm.vue')['default']
    Users: typeof import('./src/components/users/Users.vue')['default']
    UsersForm: typeof import('./src/components/users/UsersForm.vue')['default']
    UsersTable: typeof import('./src/components/users/UsersTable.vue')['default']
    Whitelabel: typeof import('./src/components/whitelabel/Whitelabel.vue')['default']
    WhitelabelCopyrights: typeof import('./src/components/whitelabel/copyrights/WhitelabelCopyrights.vue')['default']
    WhitelabelCopyrightsForm: typeof import('./src/components/whitelabel/copyrights/WhitelabelCopyrightsForm.vue')['default']
    WhitelabelCopyrightsTable: typeof import('./src/components/whitelabel/copyrights/WhitelabelCopyrightsTable.vue')['default']
    WhitelabelDomains: typeof import('./src/components/whitelabel/domains/WhitelabelDomains.vue')['default']
    WhitelabelDomainsForm: typeof import('./src/components/whitelabel/domains/WhitelabelDomainsForm.vue')['default']
    WhitelabelDomainsTable: typeof import('./src/components/whitelabel/domains/WhitelabelDomainsTable.vue')['default']
    WhitelabelKeys: typeof import('./src/components/whitelabel/keys/WhitelabelKeys.vue')['default']
    WhitelabelKeysForm: typeof import('./src/components/whitelabel/keys/WhitelabelKeysForm.vue')['default']
    WhitelabelKeysTable: typeof import('./src/components/whitelabel/keys/WhitelabelKeysTable.vue')['default']
    WhitelabelSmtps: typeof import('./src/components/whitelabel/smtps/WhitelabelSmtps.vue')['default']
    WhitelabelSmtpsForm: typeof import('./src/components/whitelabel/smtps/WhitelabelSmtpsForm.vue')['default']
    WhitelabelSmtpsTable: typeof import('./src/components/whitelabel/smtps/WhitelabelSmtpsTable.vue')['default']
  }
}
