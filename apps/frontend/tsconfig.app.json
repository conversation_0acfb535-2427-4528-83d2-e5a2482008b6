{
  "extends": "@vue/tsconfig/tsconfig.dom.json",
  "compilerOptions": {
    "composite": false,
    "baseUrl": ".",
    "rootDir": ".",
    "paths": {
      "@/*": ["./src/*"],
      "src/*": ["./src/*"],
      "@src/*": ["./src/*"]
    },

    "tsBuildInfoFile": "./node_modules/.tmp/tsconfig.app.tsbuildinfo",

    /* Linting */
    "strict": true,
    "noUnusedLocals": false,
    "noUnusedParameters": false,
    "noFallthroughCasesInSwitch": true,
    "noUncheckedSideEffectImports": true,

    /* Skip Library Check */
    "skipLibCheck": true
  },
  "references": [],
  "types": ["vite/client", "unplugin-vue-router/client"],
  "include": [
    "src/**/*.ts",
    "src/**/*.tsx",
    "src/**/*.vue",
    "src/vite-env.d.ts",
    "./auto-imports.d.ts",
    "./components.d.ts",
    "./typed-router.d.ts"
  ],
  "exclude": ["src/**/__tests__/*", "../../server", "node_modules"]
}
