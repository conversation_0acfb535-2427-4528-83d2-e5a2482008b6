{"compilerOptions": {"baseUrl": ".", "paths": {"@/*": ["./src/*"], "src/*": ["./src/*"], "@src/*": ["./src/*"]}, "strict": true, "skipLibCheck": true, "noUnusedLocals": false, "noUnusedParameters": false, "noFallthroughCasesInSwitch": true, "noUncheckedSideEffectImports": true, "noEmit": true, "isolatedModules": true, "esModuleInterop": true, "target": "ESNext", "module": "ESNext", "moduleResolution": "<PERSON><PERSON><PERSON>", "jsx": "preserve", "lib": ["ESNext", "DOM", "DOM.Iterable"]}, "include": ["src/**/*.ts", "src/**/*.tsx", "src/**/*.vue", "./auto-imports.d.ts", "./components.d.ts", "./typed-router.d.ts", "./vite-env.d.ts"], "exclude": ["node_modules", "dist", "../server", "../../server"]}