{"name": "client_v2_vue", "private": true, "version": "2.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "add-component": "pnpm dlx shadcn-vue@latest add"}, "dependencies": {"@insertchat/backend": "workspace:*", "@radix-icons/vue": "^1.0.0", "@shikijs/transformers": "^3.4.1", "@tanstack/vue-query": "^5.76.0", "@tanstack/vue-table": "^8.21.3", "@tuyau/client": "^0.2.7", "@tuyau/superjson": "^0.1.0", "@types/dompurify": "^3.2.0", "@unhead/vue": "^2.0.8", "@vee-validate/zod": "^4.15.0", "@vinejs/vine": "^3.0.1", "@vueuse/core": "^13.2.0", "@vueuse/integrations": "^13.2.0", "@wdns/vue-code-block": "^2.3.5", "autoprefixer": "^10.4.21", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dompurify": "^3.2.5", "lucide-vue-next": "^0.511.0", "marked": "^15.0.12", "marked-bidi": "^1.0.12", "marked-extended-tables": "^2.0.1", "marked-footnote": "^1.2.4", "marked-linkify-it": "^3.1.12", "marked-more-lists": "^1.0.0", "marked-shiki": "^1.2.0", "marked-smartypants": "^1.1.9", "marked-xhtml": "^1.0.12", "pinia": "^3.0.2", "postcss": "^8.5.3", "qrcode-vue3": "^1.7.1", "radix-vue": "^1.9.17", "reka-ui": "^2.2.1", "sass-embedded": "^1.88.0", "shiki": "^3.4.1", "tailwind-merge": "^3.3.0", "tailwindcss": "3.4.17", "tailwindcss-animate": "^1.0.7", "unhead": "^2.0.8", "universal-cookie": "^8.0.1", "vee-validate": "^4.15.0", "vue": "^3.5.13", "vue-router": "^4.5.1", "vue-virtual-scroller": "^2.0.0-beta.8", "zod": "^3.24.4"}, "devDependencies": {"@stagewise/toolbar-vue": "^0.1.2", "@tanstack/eslint-plugin-query": "^5.74.7", "@types/marked": "^6.0.0", "@types/node": "^22.15.18", "@types/prismjs": "^1.26.5", "@vitejs/plugin-vue": "^5.2.4", "@vue/tsconfig": "^0.7.0", "eslint": "^9.26.0", "eslint-plugin-vue": "^10.1.0", "prettier": "^3.5.3", "typescript": "~5.8.3", "unplugin-auto-import": "^19.2.0", "unplugin-unused": "^0.5.0", "unplugin-vue-components": "^28.5.0", "unplugin-vue-router": "^0.12.0", "vite": "^6.3.5", "vue-tsc": "^2.2.10"}, "imports": {"@insertchat/backend/validators": "@insertchat/backend/app/validators.ts", "@insertchat/backend/api": "@insertchat/backend/.adonisjs/api.ts"}}