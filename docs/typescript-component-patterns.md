# TypeScript Component Patterns

## Architecture Overview

The codebase uses **function props** throughout the component hierarchy for consistent type safety:

```
Main Component (Users.vue)
    ↓ passes handlers as props
Table Component (UsersTable.vue)
    ↓ passes handlers as props  
Base Component (BaseTable.vue)
    ↓ calls props.onEdit/onDelete
```

## Form Component Pattern

### Interface Definition
```typescript
// types/form.ts
export interface FormComponent {
  openDialog: (uid?: string) => void
  closeDialog: () => void
}
```

### Form Component Implementation
```vue
<script setup lang="ts">
import type { FormComponent } from '@/types/form'

// Functions must match interface signatures
const openDialog: FormComponent['openDialog'] = (uid?: string) => {
  editingUid.value = uid
  isDialogOpen.value = true
}

const closeDialog: FormComponent['closeDialog'] = () => {
  isDialogOpen.value = false
}

// Required: Must expose the interface
defineExpose<FormComponent>({ 
  openDialog,
  closeDialog
})
</script>
```

### Main Component Usage
```vue
<script setup lang="ts">
import type { FormComponent } from '@/types/form'

// TypeScript enforces the interface exists
const formRef = ref<FormComponent | null>(null)

const handleEdit = (uid?: string) => {
  // TypeScript ensures openDialog exists with correct signature
  formRef.value?.openDialog(uid)
}
</script>
```

## Table Component Pattern

### Interface Definition
```typescript
// types/form.ts
export interface TableProps {
  onEdit: (uid?: string) => void
  onDelete: (uid: string) => void
}

export interface TableHandlers {
  handleEdit: (uid?: string) => void
  handleDelete: (uid: string) => void
}
```

### Table Component Implementation
```vue
<template>
  <BaseTable
    :heads="heads"
    :rows="entities"
    :actions="['edit', 'delete']"
    :isLoading="isLoading"
    empty-message="No entities found"
    :onEdit="(row: Entity) => props.onEdit(row.uid)"
    :onDelete="(row: Entity) => props.onDelete(row.uid)"
  >
    <template #actions>
      <Button @click="props.onEdit(undefined)">Add Entity</Button>
    </template>
  </BaseTable>
</template>

<script setup lang="ts">
import type { TableProps } from '@/types/form'

// TypeScript enforces correct prop function signatures
interface Props extends TableProps {}
const props = defineProps<Props>()

// BaseTable handles all row actions automatically
// Just pass the handlers as props to BaseTable
</script>
```

### Main Component Usage
```vue
<template>
  <EntityTable :onEdit="handleEdit" :onDelete="handleDelete" />
</template>

<script setup lang="ts">
import type { TableHandlers } from '@/types/form'

// TypeScript enforces correct function signatures
const handleEdit: TableHandlers['handleEdit'] = (uid?: string) => {
  formRef.value?.openDialog(uid)
}

const handleDelete: TableHandlers['handleDelete'] = async (uid: string) => {
  // uid is guaranteed to be string, not undefined
  const { mutateAsync } = mutateEntityDestroy(ref(uid))
  await mutateAsync(undefined)
}
</script>
```

## Benefits

1. **Type Safety**: TypeScript catches missing methods or wrong signatures
2. **IntelliSense**: Better autocomplete and error detection
3. **Consistency**: Enforces standard patterns across all components
4. **Refactoring**: Safer when changing interface signatures
5. **Documentation**: Interfaces serve as living documentation

## BaseTable Integration

BaseTable now accepts optional function props for consistent handling:

```typescript
// BaseTable props interface
interface Props {
  // ... other props
  onEdit?: (row: any) => void
  onDelete?: (row: any) => void
}
```

### Benefits of Updated BaseTable:
- 🔄 **Consistent Pattern**: Same props approach throughout hierarchy
- 🎯 **Automatic Actions**: No need for custom dropdown menus
- 🔧 **Flexible**: Can pass different handlers per usage
- 🐛 **No Emit Bugs**: Eliminates missing `defineEmits` issues

## Required Patterns

- ✅ All form components must implement `FormComponent` interface
- ✅ All table components must extend `TableProps` interface for props
- ✅ All main components must use `TableHandlers` for handler functions
- ✅ Always use `defineExpose<FormComponent>()` in forms
- ✅ Always type function signatures explicitly: `const func: Interface['method']`
- ✅ Tables receive handlers as props (`:onEdit`, `:onDelete`), not events
- ✅ Pass table handlers directly to `BaseTable` as props
- ✅ Let `BaseTable` handle action buttons automatically 