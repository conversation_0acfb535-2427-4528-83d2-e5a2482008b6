---
description: 
globs: apps/backend/**
alwaysApply: false
---
# Rule: Backend Naming Conventions

**Rule Name**: `backend_naming`

## 📁 File Naming Patterns (Required)

All backend files use `snake_case` exactly as established:

```
{entity}_service.ts       # Service classes
{entities}_controller.ts  # Controllers (plural)  
{entity}_validator.ts     # Validators
{entity}_presenter.ts     # Presenters
{entity}_model.ts         # Models
{entities}_routes.ts      # Routes (plural)
```

**Examples**:
- `product_service.ts`, `user_service.ts`, `order_service.ts`
- `products_controller.ts`, `users_controller.ts`, `orders_controller.ts`
- `product_validator.ts`, `user_validator.ts`, `order_validator.ts`

## 🏷️ Class Naming Patterns (Required)

All classes use `PascalCase` with descriptive suffixes:

```typescript
// Services
export default class ProductService { }

// Controllers  
export default class ProductsController { }

// Models
export default class Product extends BaseModel { }

// Presenters
export default class ProductPresenter { }

// Exceptions
export default class RestrictedException extends Exception { }
```

## 🎯 Method Naming Standards (Required)

### Controller Methods (HTTP Verbs)

```typescript
export default class ProductsController {
  async index({ request, response }: HttpContext) { }     // GET /products
  async show({ request, response }: HttpContext) { }      // GET /products/:uid
  async store({ request, response }: HttpContext) { }     // POST /products
  async update({ request, response }: HttpContext) { }    // PUT /products/:uid
  async destroy({ request, response }: HttpContext) { }   // DELETE /products/:uid
  
  // Custom actions (Examples)
  async duplicate({ request, response }: HttpContext) { }
  async updateImage({ request, response }: HttpContext) { }
  async updateStatus({ request, response }: HttpContext) { }
  async getFactorySettings({ response }: HttpContext) { }
  async bulkImport(ctx: HttpContext) { }
}
```

### Service Methods (Business Logic)

```typescript
export default class ProductService {
  // Standard CRUD
  async index(filter: FilterType) { }
  async show(uid: string) { }
  async store(payload: CreatePayload) { }
  async update(uid: string, payload: UpdatePayload) { }
  async destroy(uid: string) { }
  
  // Custom business operations
  async duplicate(uid: string) { }
  async updateImage(params: ImageParams) { }
  async updateStatus(params: StatusParams) { }
}
```

## 🗂️ Variable & Property Naming (Required)

### Database Columns (snake_case)

```typescript
// Model properties match database columns exactly
@column({ meta: { type: 'string' }})
declare app_uid: string

@column({ meta: { type: 'object' }})
declare metadata: Infer<typeof entityMetadataValidator>
```

### Method Parameters & Variables (camelCase)

```typescript
// Service method parameters
async store(payload: CreatePayload) {
  const entityUid = payload.entityUid
}

async updateImage(params: {
  uid: string
  key: ImageKeyType
}) {
  const { uid, key } = params
  // Implementation...
}

// Local variables
async index(filter: Infer<typeof requestFilterValidator>) {
  const page = filter.page || 1
  // Implementation...
}
```

## ✅ Validator Naming Patterns (Required)

```typescript
// Schema definitions (camelCase)
const productSchema = vine.object({
  name: vine.string().trim(),
  status: vine.enum(['draft', 'published', 'archived'])
})

// Compiled validators (suffix with 'Validator')
export const productStoreValidator = vine.compile(productSchema)
```

## 🔧 Constant Naming Standards (Required)

All constants use `UPPER_SNAKE_CASE`:

```typescript
// Global constants
export const DEFAULT_ENTITY_SETTINGS = {
  settings: {
    priority: 5,
    notifications_enabled: true
  }
} as const
```

## 🛣️ Route Parameter Naming (Required)

Use descriptive parameter names in routes:

```typescript
// Route definitions
router.get('/:uid', [controller, 'show'])
router.put('/update-image/:uid', [controller, 'updateImage'])
router.put('/update-status/:entity_uid', [controller, 'updateStatus'])

// In controllers, access via request.param()
const uid = request.param('uid')
const entityUid = request.param(':entity_uid')
```

## 📊 JSON Column Property Naming (Required)

For JSONB columns, maintain consistent naming between database and TypeScript:

```typescript
// Database column (snake_case)
@column({ meta: { type: 'object' }})
declare metadata: Infer<typeof entityMetadataValidator> | null

// Computed property for safe access
@computed()
get computed_metadata(): Infer<typeof entityMetadataValidator> {
  return { ...DEFAULT_ENTITY_SETTINGS.metadata, ...(this.metadata || {}) }
}

// TypeScript interface properties (camelCase)
interface MetadataConfig {
  isPublic: boolean
  displayName: string
  shortDescription: string
  iconUrl: string
}
```

## ⚠️ Error Handling Naming (Required)

```typescript
// Exception class naming
export default class RestrictedException extends Exception { }

// Event naming (domain:action pattern)
emitter.emit('analytics:track', payload)
emitter.emit('entity:duplicated', { uid, newUid })
```

## 🎯 Critical Naming Rules

### ✅ ALWAYS Use
1. **File names**: `snake_case` (`product_service.ts`)
2. **Class names**: `PascalCase` with suffixes (`ProductService`, `ProductsController`)
3. **Methods**: REST verbs for controllers (`index`, `show`, `store`, `update`, `destroy`)
4. **Database columns**: `snake_case` (`app_uid`, `category_name`)
5. **Constants**: `UPPER_SNAKE_CASE` (`DEFAULT_ENTITY_SETTINGS`)
6. **Variables**: `camelCase` (`entityUid`, `categoryName`)

### ❌ NEVER Use
1. **Inconsistent casing** between similar entities
2. **Generic names** without suffixes (`Product` instead of `ProductService`)
3. **Mixed naming strategies** within the same context
4. **Shortened or abbreviated names** unless universally understood