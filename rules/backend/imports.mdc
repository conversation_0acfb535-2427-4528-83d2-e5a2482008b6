---
description: 
globs: apps/backend/**
alwaysApply: false
---
# Rule: Backend Architecture & Structure

**Rule Name**: `backend_imports`

## 🔗 Import Path Standards (Mandatory)

Always use absolute imports with `#app/` alias:

```typescript
// ✅ REQUIRED - Absolute imports
import AgentService from '#app/modules/agents/agent_service'
import { DEFAULT_AGENT_SETTINGS } from '#app/constants'
import RestrictedException from '#app/exceptions/restricted_exception'
import { requestFilterValidator } from '#app/validators'
import StorageHelper from '#app/helpers/storage_helper'

// ❌ FORBIDDEN - Relative imports
import AgentService from '../../modules/agents/agent_service'
import { DEFAULT_AGENT_SETTINGS } from '../constants'
```

