---
description: 
globs: apps/backend/**
alwaysApply: false
---
# Rule: Backend Service Layer Patterns

**Rule Name**: `backend_services`

## Service Purpose

* A **Service** encapsulates all business logic for a domain.
* It must be injected and used only from **controllers** or other services.
* Services must never:
  * Perform validation
  * Serialize data
  * Directly handle HTTP responses

## Method Responsibilities

* Business logic only — no input validation (except for internal logic, see "Validation Handling") or output formatting.
* Handle DB queries through `this.ctx.app.related('entityName').query()`.
* Fetch user info via `this.ctx.user` and application/environment/config via `this.ctx.app`.
* Apply data scoping based on user roles or properties (e.g., filtering results in an `index` method based on `this.ctx.user.role` or `this.ctx.user.assigned_entities`).
* Emit events using `emitter` for decoupled side effects (see `event_emitter_convention`).
* Set default values for new entities if these defaults are context-specific to the service's creation logic or derived from user/app context. Universal defaults are better in models.

## Method Types

| Use Case     | Signature                                                                |
| ------------ | ------------------------------------------------------------------------ |
| Fetch all    | `async index(filter: SchemaType)`                                        |
| Fetch one    | `async show(uid: string)`                                                |
| Create       | `async store(payload: SchemaType)`                                       |
| Update       | `async update(uid: string, payload: SchemaType)`                         |
| Destroy      | `async destroy(uid: string)`                                             |
| Custom logic | `async duplicate(uid: string)`, `async updateImage(params: {...})`, etc. |

## Context-Aware Access

* Always use:

  * `this.ctx.user`, `this.ctx.app`, `this.ctx.request`
* Never rely on global state or static models

---

## Validation Handling

* Validation must already be completed in the controller
* Exception: methods calling a **Vine validator explicitly** for internal logic (e.g. onboarding update) are allowed

```ts
await agentUpdateOnboardingValidator.validate({ key, status })
```

## Example

```typescript
@inject()
export default class EntityService {
  private ctx: HttpContext

  constructor(ctx: HttpContext) {
    this.ctx = ctx
  }

  async index(filter: Infer<typeof requestFilterValidator>) {
    const page = filter.page || 1

    return await this.ctx.app
      .related('entities')
      .query()
      .filter(filter)
      .paginate(page, env.get('APP_PAGINATION_LIMIT'))
  }

  async show(uid: string) {
    return await this.ctx.app.related('entities').query().where('uid', uid).firstOrFail()
  }

  async store(payload: Infer<typeof entityStoreValidator>) {
    const entity = await this.ctx.app.related('entities').create(payload)
    
    emitter.emit('analytics:track', {
      appUid: entity?.app_uid,
      eventName: 'entity_created',
    })
    
    return entity
  }

  async update(uid: string, payload: Infer<typeof entityUpdateValidator>) {
    const entity = await this.ctx.app.related('entities').query().where('uid', uid).firstOrFail()
    return entity.merge(payload).save()
  }

  async destroy(uid: string) {
    const entity = await this.ctx.app.related('entities').query().where('uid', uid).firstOrFail()
    return await entity.delete()
  }
}
```


## Required Patterns

1. **Always use** `@inject()` decorator
2. **Always use** `private ctx: HttpContext` constructor pattern
3. **Always use** `this.ctx.app.related()` for database queries
4. **Always use** `env.get('APP_PAGINATION_LIMIT')` for pagination
5. **Always use** `filter(filter)` for automatic filtering with BaseFilter
6. **Always emit** events with `emitter.emit()` for side effects
7. **Always use** `model.merge(payload).save()` for updates
8. **Always use** spread syntax `{...model.computed_property}` for JSONB updates
9. **Never use** direct model queries (use `this.ctx.app.related()`)
10. **Always validate** business rules in services before database operations

