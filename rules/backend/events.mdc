---
description: 
globs: apps/backend/**
alwaysApply: false
---
# Rule: Backend Event Patterns

**Rule Name**: `backend_events`

## Event Emission Convention (Required)

All side effects must be handled through event emission using the domain:action pattern:

```typescript
import emitter from '@adonisjs/core/services/emitter'

// Event naming: {domain}:{action}
emitter.emit('google_ads:create', payload)
emitter.emit('user:upgraded', { userUid, plan })
```

## Real Event Examples from Codebase

### Google Ads Conversion Tracking (from AgentService)

```typescript
// Emit conversion events for business tracking
emitter.emit('google_ads:create', {
  appUid: agent?.app_uid,
  conversionName: 'created_chatbot',
  conversionValue: 2,
  conversionCurrency: 'USD',
})
```

## Event Placement in Services (Required Pattern)

Events should be emitted **after successful database operations**:

```typescript
async store(payload: Infer<typeof agentStoreValidator>) {
  // 1. Perform database operation first
  const agent = await this.ctx.app.related('agents').create({
    label: payload.label,
    purpose: payload.purpose,
    // ... other fields
  })

  // 2. Emit events for side effects after success
  emitter.emit('google_ads:create', {
    appUid: agent?.app_uid,
    conversionName: 'created_chatbot',
    conversionValue: 2,
    conversionCurrency: 'USD',
  })

  // 3. Return the result
  return agent
}
```

## Event Naming Conventions (Required)

### Domain:Action Pattern

- **google_ads:create** - Google Ads conversion tracking
- **email:send** - Email notifications
- **agent:duplicated** - Agent duplication events
- **user:upgraded** - User plan upgrades
- **training:completed** - Training completion events

### Event Payload Structure

Events should include relevant context:

```typescript
// Always include entity UIDs for tracking
emitter.emit('agent:duplicated', {
  originalUid: originalAgent.uid,
  duplicatedUid: newAgent.uid,
  appUid: this.ctx.app.uid,
  userUid: this.ctx.user?.uid,
})
```

## When to Use Events vs Direct Calls

### Use Events For:
- **Side effects** that shouldn't block the main operation
- **Analytics tracking** (Google Ads, usage metrics)
- **Email notifications** 
- **Third-party integrations**
- **Background processing triggers**

### Use Direct Calls For:
- **Core business logic** required for the operation
- **Database operations** within the same transaction
- **Validation** that must complete before proceeding
- **Immediate responses** needed by the client

## Event Handling Best Practices

### 1. Fire and Forget Pattern

```typescript
// Events should not affect the main operation outcome
async store(payload: CreatePayload) {
  try {
    const agent = await this.ctx.app.related('agents').create(payload)
    
    // This should not fail the main operation
    emitter.emit('google_ads:create', {
      appUid: agent.app_uid,
      conversionName: 'created_chatbot',
    })
    
    return agent
  } catch (error) {
    // Don't emit events if main operation fails
    throw error
  }
}
```

### 2. Event Payload Validation

```typescript
// Include only necessary data in event payloads
emitter.emit('user:created', {
  uid: user.uid,
  email: user.email,
  role: user.role,
  // Don't include sensitive data like passwords
})
```

### 3. Context Preservation

```typescript
// Include context that event handlers might need
emitter.emit('email:send', {
  templateName: 'welcome',
  to: [{ email: user.email, name: user.first_name }],
  appUid: this.ctx.app.uid,        // App context
  userUid: this.ctx.user?.uid,     // User context
  variables: {
    firstName: user.first_name,
    appName: this.ctx.app.name,
  },
})
```

## Required Patterns Summary

1. **Always use** domain:action naming pattern
2. **Always emit** events after successful database operations
3. **Always include** relevant UIDs in event payloads
4. **Always preserve** context (appUid, userUid) in events
5. **Never block** main operations with event failures
6. **Never include** sensitive data in event payloads
7. **Always use** events for side effects and analytics
8. **Never use** events for core business logic
9. **Always emit** events in services, not controllers
10. **Always structure** payloads consistently within event types

