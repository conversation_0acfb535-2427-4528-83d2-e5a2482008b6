---
description: 
globs: apps/backend/**
alwaysApply: false
---
# Rule: Backend Model Layer Patterns

**Rule Name**: `backend_models`

## Model Class Structure (Required)

All models MUST follow this pattern:

```typescript
import { Filterable } from 'adonis-lucid-filter'
import { compose } from '@adonisjs/core/helpers'
import { BaseModel, SnakeCaseNamingStrategy } from '@adonisjs/lucid/orm'
import { beforeCreate, column, computed } from '@adonisjs/lucid/orm'
import { v4 as uuid } from 'uuid'
import BaseFilter from '#app/modules/base/base_filter'

export default class Entity extends compose(BaseModel, Filterable) {
  serializeExtras = false
  static selfAssignPrimaryKey = true
  static namingStrategy = new SnakeCaseNamingStrategy()
  static $filter = () => BaseFilter

  @beforeCreate()
  static createUUID(model: Entity) {
    model.uid = uuid()
  }
}
```

## Column Definitions

All columns require comprehensive meta information:

```typescript
// Primary key
@column({
  isPrimary: true,
  meta: { searchable: true, type: 'string', validations: [{ name: 'uuid' }] },
})
declare uid: string

// String field
@column({ meta: { searchable: true, type: 'string' } })
declare name: string

// JSONB column with type safety
@column({ meta: { searchable: true, type: 'object' } })
declare settings: Infer<typeof entitySettingsValidator>

// Timestamps
@column.dateTime({ autoCreate: true, meta: { searchable: true, type: 'date' } })
declare created_at: DateTime

// Timestamps
@column.dateTime({ autoCreate: true, meta: { searchable: true, type: 'date' } })
declare updated_at: DateTime
```

## Computed Properties

```typescript
// Safe JSONB access with defaults
@computed()
get computed_settings(): Infer<typeof entitySettingsValidator> {
  return { ...DEFAULT_ENTITY_SETTINGS.settings, ...(this.settings || {}) }
}

// Business logic computed properties
@computed()
get is_published(): boolean {
  return this.status === 'published' && this.published_at !== null
}
```

## Model Relations

```typescript
// Belongs to
@belongsTo(() => App, {
  localKey: 'uid',
  foreignKey: 'app_uid',
})
declare app: BelongsTo<typeof App>

// Has many
@hasMany(() => Item, {
  localKey: 'uid',
  foreignKey: 'entity_uid',
})
declare items: HasMany<typeof Item>
```

## Required Patterns

1. **Always extend** `compose(BaseModel, Filterable)`
2. **Always use** `SnakeCaseNamingStrategy`
3. **Always set** `serializeExtras = false`
4. **Always set** `static $filter = () => BaseFilter`
5. **Always use** `@beforeCreate()` with `uuid()` for primary keys
6. **Always provide** comprehensive `meta` with `searchable`, `type`, `validations`
7. **Always create** computed properties for JSONB defaults using spread syntax
8. **Never use** direct model queries outside services

