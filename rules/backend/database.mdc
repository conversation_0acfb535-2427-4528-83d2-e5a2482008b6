---
description: 
globs: apps/backend/**
alwaysApply: false
---
# Rule: Backend Database Patterns

**Rule Name**: `backend_database`

## Database Query Patterns (Mandatory)

All database queries in services MUST use the `this.ctx.app.related()` pattern:

```typescript
// ✅ Required Pattern - Always use app.related()
await this.ctx.app.related('agents').query().where('uid', uid).firstOrFail()
await this.ctx.app.related('agents').create(payload)
await this.ctx.app.related('agents').query().filter(filter).paginate(page, limit)

// ✅ Required Pattern - With preloading
const provider = await this.ctx.app.related('providers')
  .query()
  .preload('agent')
  .where('uid', uid)
  .firstOrFail()

// ✅ Required Pattern - Multiple preloads
await oldAgent.preload('providers', (query) => query.preload('sources'))
```

## Forbidden Query Patterns

```typescript
// ❌ NEVER use direct model queries in services
Agent.find(uid)
Agent.all()
Agent.create(payload)
User.findBy('email', email)  // Exception: validation checks only

// ❌ NEVER use raw database queries for CRUD
await db.from('agents').where('uid', uid)
await db.rawQuery('SELECT * FROM agents WHERE uid = ?', [uid])
```

## Pagination Pattern (Required)

Always use environment-based pagination limits:

```typescript
import env from '#start/env'

// Required pagination pattern
async index(filter: Infer<typeof requestFilterValidator>) {
  const page = filter.page || 1
  return await this.ctx.app
    .related('agents')
    .query()
    .filter(filter)
    .paginate(page, env.get('APP_PAGINATION_LIMIT'))
}
```

## Filtering with BaseFilter (Universal Pattern)

All models use BaseFilter for consistent query capabilities:

```typescript
// Automatic filtering based on model meta.searchable columns
const entities = await this.ctx.app
  .related('agents')
  .query()
  .filter({
    search: 'customer support',     // Searches all meta.searchable columns
    filters: { purpose: 'support' }, // Exact match filters
    orderBy: 'created_at',          // Sorting
    order: 'desc'                   // Sort direction
  })
  .paginate(page, limit)
```

## Transaction Patterns (Complex Operations)

For operations requiring transactions:

```typescript
import db from '@adonisjs/lucid/services/db'

async destroy(uid: string) {
  const [provider, sources] = await Promise.all([
    this.ctx.app.related('providers')
      .query()
      .select('uid', 'agent_uid')
      .preload('agent')
      .where('uid', uid)
      .firstOrFail(),
    this.ctx.app.related('sources').query().select('uid').where('provider_uid', uid),
  ])

  const embeddingTable = provider!.agent.getEmbeddingTableName()
  const sourcesUids = sources.map((source) => source.uid)

  await db.transaction(async (trx) => {
    await Promise.all([
      trx.rawQuery(
        `DELETE FROM embeddings.${embeddingTable} WHERE source_uid = ANY(:sources_uids)`,
        { sources_uids: sourcesUids }
      ),
      trx.from('sources').whereIn('uid', sourcesUids).delete(),
      trx.from('providers').where('uid', uid).delete(),
    ])
  })

  return true
}
```

## Update Patterns (JSONB Columns)

Use spread syntax for safe JSONB updates:

```typescript
// Update JSONB columns safely with computed properties
async updateOnboarding(params: { uid: string; key: string; status: boolean }) {
  const { uid, key, status } = params
  const agent = await this.ctx.app.related('agents').query().where('uid', uid).firstOrFail()

  return await agent
    .merge({
      onboarding: {
        ...agent.computed_onboarding, // Use computed property for defaults
        [key]: status,
      },
    })
    .save()
}

// Update nested JSONB structures
async updateImage(params: { uid: string; key: string; url: string }) {
  const { uid, key, url } = params
  const agent = await this.ctx.app.related('agents').query().where('uid', uid).firstOrFail()

  if (key === 'mobile_app_icon_512_512') {
    const mobileApp = { ...agent.computed_mobile_app }
    
    if (mobileApp && mobileApp.icon_512_512) {
      mobileApp.icon_512_512.url = url
      await agent.merge({ mobile_app: mobileApp }).save()
    }
  }
}
```

## Relation Loading Patterns

### Eager Loading (Preload)

```typescript
// Single relation
const agent = await this.ctx.app.related('agents')
  .query()
  .preload('app')
  .where('uid', uid)
  .firstOrFail()

// Multiple relations
const agent = await this.ctx.app.related('agents')
  .query()
  .preload('app')
  .preload('providers')
  .where('uid', uid)
  .firstOrFail()

// Nested relations
const agent = await this.ctx.app.related('agents')
  .query()
  .preload('providers', (query) => query.preload('sources'))
  .where('uid', uid)
  .firstOrFail()
```

### Lazy Loading

```typescript
// Load relations on existing models
await agent.load('providers')
await agent.load('providers', (query) => query.preload('sources'))
```

## Dynamic Table Operations (Advanced)

For dynamic table creation and management:

```typescript
// Create embedding tables dynamically (from EmbeddingService)
async createTable(tableName: string) {
  const schema = 'embeddings'
  const indexName = `ix_${tableName}_embedding`

  await db.rawQuery(`
    CREATE TABLE IF NOT EXISTS ${schema}.${tableName} (
      uid UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
      source_uid UUID NOT NULL,
      embedding vector(1536) NOT NULL,
      content TEXT NOT NULL,
      metadata JSONB,
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    )
  `)

  await db.rawQuery(`
    CREATE INDEX IF NOT EXISTS ${indexName} 
    ON ${schema}.${tableName} 
    USING ivfflat (embedding vector_cosine_ops) 
    WITH (lists = 100)
  `)
}
```

## Query Optimization Patterns

### Select Specific Columns

```typescript
// Only select needed columns for performance
const providers = await this.ctx.app.related('providers')
  .query()
  .select('uid', 'agent_uid', 'type', 'status')
  .where('agent_uid', agentUid)
```

### Conditional Queries

```typescript
// Role-based filtering
async index(filter: Infer<typeof requestFilterValidator>) {
  const page = filter.page || 1
  
  let query = this.ctx.app.related('agents').query().filter(filter)
  
  if (this.ctx.user?.role === 'client') {
    const assignedAgents = this.ctx.user?.assigned_agents ?? []
    query = query.whereIn('uid', assignedAgents)
  }
  
  return query.paginate(page, env.get('APP_PAGINATION_LIMIT'))
}
```

## Error Handling Patterns

### Business Logic Validation

```typescript
// Validate before database operations
async store(payload: Infer<typeof userStoreValidator>) {
  const { email } = payload

  const existingUser = await User.findBy('email', email)

  if (existingUser) {
    throw {
      message: 'Email already used',
      status: 400,
      code: 'auth/user-already-exists',
    }
  }

  return await this.ctx.app.related('users').create(payload)
}
```

### Let Lucid Exceptions Bubble Up

```typescript
// Don't catch and re-throw Lucid exceptions
async show(uid: string) {
  // Let firstOrFail() throw ModelNotFoundException naturally
  return await this.ctx.app.related('agents').query().where('uid', uid).firstOrFail()
}
```

## Required Patterns Summary

1. **Always use** `this.ctx.app.related()` for all CRUD operations in services
2. **Always use** `env.get('APP_PAGINATION_LIMIT')` for pagination
3. **Always use** `.filter(filter)` for automatic BaseFilter integration
4. **Always use** spread syntax for JSONB updates: `{...model.computed_property, ...changes}`
5. **Always use** computed properties for safe JSONB access
6. **Always preload** relations when you know you'll need them
7. **Always select** specific columns for performance when possible
8. **Never use** direct model queries in services (except for validation)
9. **Never catch** Lucid exceptions unless handling them specifically
10. **Always use** transactions for multi-table operations

