---
description: 
globs: apps/backend/**
alwaysApply: false
---
# Rule: Backend Controller Layer Patterns

**Rule Name**: `backend_controllers`

## Controller Class Structure (Real Pattern)

All controllers MUST follow this exact pattern from the codebase:

## Method Naming & Return Structure

| Purpose          | Method Name  | Return Format                            |
| ---------------- | ------------ | ---------------------------------------- |
| List items       | `index()`    | `response.status(200).json(...)`         |
| Show single item | `show()`     | `response.status(200).json(...)`         |
| Create item      | `store()`    | `response.status(201).json(...)`         |
| Update item      | `update()`   | `response.status(201).json(...)`         |
| Delete item      | `destroy()`  | `response.status(202).json({ success })` |
| Other action     | `verbNoun()` | Match appropriate status & structure     |

## Example

```typescript
import { inject } from '@adonisjs/core'
import { HttpContext } from '@adonisjs/core/http'
import EntityService from '#app/modules/entities/entity_service'
import { requestFilterValidator, entityStoreValidator } from '#app/validators'
import EntityPresenter from '#app/modules/entities/entity_presenter'

@inject()
export default class EntitiesController {
  constructor(private entityService: EntityService) {}

  async index({ request, response }: HttpContext) {
    const payload = await request.validateUsing(requestFilterValidator)
    const entities = await this.entityService.index(payload)
    return response.status(200).json(EntityPresenter.serializePaginated(entities))
  }

  async show({ request, response }: HttpContext) {
    const uid = request.param('uid')
    const entity = await this.entityService.show(uid)
    return response.status(200).json(EntityPresenter.serialize(entity))
  }

  async store({ request, response }: HttpContext) {
    const payload = await request.validateUsing(entityStoreValidator)
    const entity = await this.entityService.store(payload)
    return response.status(201).json(EntityPresenter.serialize(entity))
  }

  async update({ request, response }: HttpContext) {
    const uid = request.param('uid')
    const payload = await request.validateUsing(entityUpdateValidator)
    const entity = await this.entityService.update(uid, payload)
    return response.status(201).json(EntityPresenter.serialize(entity))
  }

  async destroy({ request, response }: HttpContext) {
    const uid = request.param('uid')
    await this.entityService.destroy(uid)
    return response.status(204)
  }
}
```

## Authorization Pattern (When Required)

```typescript
async store({ app, user, request, response }: HttpContext) {
  const payload = await request.validateUsing(entityStoreValidator)

  if (!user?.role || !['admin', 'owner', 'manager'].includes(user?.role)) {
    throw new RestrictedException()
  }

  await app.canConsumeEntities({ throwError: true })

  const entity = await this.entityService.store(payload)
  return response.status(201).json(EntityPresenter.serialize(entity))
}
```

## File Upload Pattern (Example)

```typescript
async updateImage({ app, request, response }: HttpContext) {
  const uid = request.param('uid')
  let key = 'image'

  request.multipart.onFile('*', {
    size: '10mb',
    extnames: ALLOWED_IMAGE_EXTENSIONS,
  }, async (multipartStream, reporter) => {
    key = multipartStream.name
    const storageClient = new StorageHelper('entities')
    const { directUrl } = await storageClient.saveStream({
      multipartStream,
      basePath: `${app.uid}/${uid}`,
    })
    return { directUrl }
  })

  await request.multipart.process()
  
  const imageFile = request.file(key)
  const directUrl = imageFile.meta.directUrl

  if (directUrl) {
    const entity = await this.entityService.updateImage({ uid, key, url: directUrl })
    return response.status(201).json(EntityPresenter.serialize(entity))
  }
}
```

## Context Destructuring Patterns

```typescript
// Standard CRUD methods
async index({ request, response }: HttpContext) {
  // Extract request and response only
}

// Complex operations
async complexOperation(ctx: HttpContext) {
  const { app, user, request, response } = ctx
  // Use full context for complex operations
}
```

## Response Patterns

```typescript
// List resources (200 with pagination)
return response.status(200).json(Presenter.serializePaginated(entities))

// Show single resource (200)
return response.status(200).json(Presenter.serialize(entity))

// Create resource (201)
return response.status(201).json(Presenter.serialize(entity))

// Update resource (201)
return response.status(201).json(Presenter.serialize(entity))

// Delete resource (204)
return response.status(204)
```

## Required Patterns Summary

1. **Always use** `@inject()` decorator
2. **Always inject** services in constructor
3. **Always use** `request.validateUsing()` for input validation
4. **Always use** `request.param()` for route parameters
5. **Always use** presenters for response serialization
6. **Always return** appropriate HTTP status codes (200, 201, 204)
7. **Always check** authorization at controller level when needed
8. **Never catch** errors unless handling them specifically
9. **Always destructure** HttpContext appropriately for the method
10. **Always use** consistent naming: `{Entities}Controller` with plural names

