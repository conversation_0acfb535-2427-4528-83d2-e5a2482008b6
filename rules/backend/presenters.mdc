---
description: 
globs: apps/backend/**
alwaysApply: false
---
# Rule: Backend Presenter Patterns

**Rule Name**: `backend_presenters`

## Purpose & Responsibilities

* Presenters **must only format data for output**
* Used by controllers to **decouple model structure from API responses**
* Must never include any business logic or transformation beyond flattening/preparing fields
* Provide consistent API response structure across all endpoints

## Required Class Structure

Each presenter must be a `default class {Entity}Presenter` with these static methods:

* `serialize(item: Model)` – for single items
* `serializeMany(items: Model[])` – for arrays
* `serializePaginated(items: ModelPaginatorContract<Model>)` – for paginated lists

## Standard Implementation Pattern

```typescript
import type { ModelPaginatorContract } from '@adonisjs/lucid/types/model'
import Entity from '#app/modules/entities/entity_model'

export default class EntityPresenter {
  static serialize(entity: Entity) {
    return {
      // Primary identifiers
      uid: entity.uid,
      app_uid: entity.app_uid,
      
      // Basic properties
      name: entity.name,
      status: entity.status,
      
      // Safe JSONB access via computed properties
      settings: entity.computed_settings,
      
      // Timestamps
      created_at: entity.created_at,
      updated_at: entity.updated_at,
    }
  }

  static serializeMany(entities: Entity[]) {
    return entities.map((entity) => this.serialize(entity))
  }

  static serializePaginated(entities: ModelPaginatorContract<Entity>) {
    return {
      page: entities.currentPage,
      per_page: entities.perPage,
      total: entities.total,
      last_page: entities.lastPage,
      data: this.serializeMany(entities.all()),
    }
  }
}
```

## Pagination Structure (Required Format)

The `serializePaginated` method must always return this exact structure:

```typescript
static serializePaginated(items: ModelPaginatorContract<Model>) {
  return {
    page: items.currentPage,
    per_page: items.perPage,
    total: items.total,
    last_page: items.lastPage,
    data: this.serializeMany(items.all()),
  }
}
```

## Data Inclusion Patterns

### 1. Direct Model Properties

```typescript
static serialize(entity: Entity) {
  return {
    // Always include primary key
    uid: entity.uid,
    
    // Include foreign keys when relevant
    app_uid: entity.app_uid,
    
    // Include basic model columns
    name: entity.name,
    status: entity.status,
  }
}
```

### 2. Computed Properties (Preferred)

```typescript
static serialize(entity: Entity) {
  return {
    // Use computed properties for JSONB columns (safe access with defaults)
    settings: entity.computed_settings,
    
    // Use computed properties for derived values
    is_published: entity.is_published,
  }
}
```

### 3. Loaded Relations (When Preloaded)

```typescript
static serialize(entity: Entity) {
  return {
    uid: entity.uid,
    name: entity.name,
    
    // Include relations only if they're loaded
    app: entity.app ? AppPresenter.serialize(entity.app) : null,
    items: entity.items ? this.serializeItems(entity.items) : undefined,
  }
}

private static serializeItems(items: Item[]) {
  return items.map(item => ({
    uid: item.uid,
    name: item.name,
    status: item.status,
  }))
}
```

## Response Naming Conventions

- Use `snake_case` for API response keys (matches database conventions)
- Keep consistent field names between related presenters
- Use descriptive property names that match the model's purpose

```typescript
// ✅ Correct - snake_case response keys
static serialize(entity: Entity) {
  return {
    uid: entity.uid,
    app_uid: entity.app_uid,
    is_published: entity.is_published,
    created_at: entity.created_at,
  }
}

// ❌ Wrong - mixed or camelCase keys
static serialize(entity: Entity) {
  return {
    uid: entity.uid,
    appUid: entity.app_uid,         // Wrong casing
    isPublished: entity.is_published, // Wrong casing
  }
}
```

## Scoped Presenters (Optional)

For different views of the same entity, create additional methods:

```typescript
export default class EntityPresenter {
  // Default full representation
  static serialize(entity: Entity) {
    return {
      uid: entity.uid,
      name: entity.name,
      status: entity.status,
      // ... all fields
    }
  }

  // Summary view for listings
  static serializeSummary(entity: Entity) {
    return {
      uid: entity.uid,
      name: entity.name,
      status: entity.status,
      created_at: entity.created_at,
    }
  }

  // Public view for embed contexts
  static serializePublic(entity: Entity) {
    return {
      uid: entity.uid,
      name: entity.name,
      settings: entity.computed_settings,
    }
  }
}
```

## Controller Integration (Required Usage)

```typescript
// Single item response
async show({ request, response }: HttpContext) {
  const uid = request.param('uid')
  const entity = await this.entityService.show(uid)
  return response.status(200).json(EntityPresenter.serialize(entity))
}

// Array response
async index({ request, response }: HttpContext) {
  const payload = await request.validateUsing(requestFilterValidator)
  const entities = await this.entityService.index(payload)
  return response.status(200).json(EntityPresenter.serializePaginated(entities))
}
```

## Required Patterns Summary

1. **Always use** static methods only
2. **Always implement** `serialize`, `serializeMany`, `serializePaginated`
3. **Always use** `snake_case` for response keys
4. **Always use** computed properties for JSONB access
5. **Always use** consistent pagination structure
6. **Always leverage** model computed properties over raw data
7. **Never include** business logic in presenters
8. **Never transform** data beyond formatting
9. **Always handle** nullable relations gracefully
10. **Always use** appropriate method names for different views (`serializeSummary`, `serializePublic`)

