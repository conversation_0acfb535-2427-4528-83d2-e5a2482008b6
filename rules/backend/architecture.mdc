---
description: 
globs: apps/backend/**
alwaysApply: false
---
# Rule: Backend Architecture & Structure

**Rule Name**: `backend_architecture`

## 🏗️ Module Architecture

Every module MUST follow this exact 6-file structure found in the codebase:

```
app/modules/{entity}/
├── {entity}_model.ts        # Lucid model with BaseFilter integration
├── {entity}_service.ts      # Business logic with @inject() pattern  
├── {entities}_controller.ts # HTTP handlers with service injection
├── {entity}_validator.ts    # VineJS schemas with vine.compile()
├── {entity}_presenter.ts    # Response serialization
└── {entities}_routes.ts     # Route definitions with lazy imports
```

**Real Examples in Codebase**:
- `app/modules/agents/` - Complete agent management
- `app/modules/users/` - User management with roles
- `app/modules/rag/providers/` - RAG provider management

## 📁 Directory Responsibilities

```
apps/server/
├── app/
│   ├── modules/{entity}/           # Feature modules (CRUD + business logic)
│   ├── ai/                         # AI-specific tools and completions
│   │   ├── completions/            # OpenAI completion handlers
│   │   ├── helpers/                # AI utility functions
│   │   └── tools/                  # AI tool implementations
│   ├── automations/                # Background jobs and schedulers
│   │   ├── jobs/                   # Queue job handlers
│   │   └── schedulers/             # Cron-like scheduled tasks
│   ├── exceptions/                 # Custom exception classes
│   ├── helpers/                    # Utility classes and functions
│   ├── middleware/                 # Request processing middleware
│   ├── resources/                  # Static files and templates
│   │   ├── emails/                 # Email templates
│   │   └── files/                  # JSON configs and static data
│   ├── services/                   # Cross-cutting concerns
│   ├── constants.ts                # Global constants and configurations
│   ├── validators.ts               # Shared validation schemas
│   └── helpers.ts                  # Helper class exports
├── config/                         # Application configuration
├── database/migrations/            # Database schema changes
├── providers/                      # Service providers
├── start/routes/                   # Route registration
├── tasks/                          # CLI commands
├── tests/                          # Test suites
└── types/                          # TypeScript type definitions
```

## 🎯 Pattern Extraction Process (Critical)

Before generating any new component, you MUST:

1. **Identify Component Type**: Determine what type of component is needed
2. **Locate Similar Components**: Find existing examples of the same type
3. **Analyze Patterns**: Extract structure, imports, naming, and logic patterns
4. **Generate Consistent Code**: Match existing patterns exactly

### Analysis Examples:

```typescript
// If creating a new service, analyze existing services:
// - app/modules/agents/agent_service.ts
// - app/modules/users/user_service.ts  
// - app/modules/rag/providers/provider_service.ts

// Extract patterns like:
// - @inject() decorator usage
// - HttpContext constructor injection
// - this.ctx.app.related() database queries
// - Event emission with emitter.emit()
// - Error handling strategies
```

## 🧱 Dependency Injection Pattern (Universal)

All services and controllers use AdonisJS injection:

```typescript
// Service Pattern (Real Example)
@inject()
export default class AgentService {}
```

## 📋 Component Responsibilities (Enforced)

### Models
- Data structure and relationships
- Column definitions with comprehensive meta
- Computed properties for safe JSONB access
- Business logic instance methods
- Database table representation

### Services
- All business logic and data manipulation
- Database queries via `this.ctx.app.related()`
- Event emission for side effects
- User context and permission handling
- Complex operations and workflows

### Controllers
- HTTP request/response handling
- Input validation via `request.validateUsing()`
- Authorization checks
- Service method coordination
- Response serialization via presenters

### Validators
- Input validation schemas with VineJS
- Immediate compilation with `vine.compile()`
- Type-safe validation rules
- Reusable schema components

### Presenters
- Response data serialization
- Output formatting and structure
- API response standardization
- Pagination handling

### Routes
- HTTP route definitions
- Lazy controller imports
- Middleware application
- Resource grouping and prefixing

## 🚨 Architecture Constraints

### Required Patterns
1. **Always use** the 6-file module structure
2. **Always use** `#app/` import aliases
3. **Always analyze** existing components before creating new ones
4. **Always follow** dependency injection patterns
5. **Always separate** concerns correctly between layers

### Forbidden Patterns
1. **Never use** relative imports (`../`, `../../`)
2. **Never mix** responsibilities between layers
3. **Never create** files outside the established structure
4. **Never ignore** existing patterns in favor of generic knowledge
5. **Never skip** the pattern extraction process

## 🎯 Integration Requirements

New components must:
1. **Match existing style** exactly
2. **Use established patterns** from similar components  
3. **Follow naming conventions** consistently
4. **Integrate seamlessly** with existing modules
5. **Work immediately** without modifications