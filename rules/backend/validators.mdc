---
description: 
globs: apps/backend/**
alwaysApply: false
---
# Rule: Backend Validator Patterns

**Rule Name**: `backend_validators`

## General Rules (Mandatory)

* All validation must use **VineJS**.
* All schemas must be **compiled immediately** using `vine.compile(...)`.
* Use **nested schemas** for structured or repeatable objects.
* **Never use TypeScript interfaces** for validation purposes.
* **Always export** each compiled validator.

## Naming & Structure

* Schema variables: use `camelCase`
  → `entitySchema`, `settingsSchema`, `storeSchema`
* Compiled validators: suffix with `Validator`
  → `entityUpdateValidator`, `entitySettingsValidator`
* Export all validators individually.

## Schema Definitions (Real Examples)

Use reusable schemas for nested blocks and comprehensive validation:

```typescript
import vine from '@vinejs/vine'

// Base schema for shared structure
const entityBaseSchema = vine.object({
  name: vine.string().trim().minLength(1),
  status: vine.enum(['active', 'inactive', 'pending']),
  temperature: vine.number().min(0).max(1),
})
```

## Validator Compilation (Required Pattern)

```typescript
// Standard store validator
export const entityStoreValidator = vine.compile(entityBaseSchema)

// Update validator with optional fields
const entityUpdateProperties = Object.fromEntries(
  Object.entries(entityBaseSchema.getProperties()).map(([key, schema]) => [
    key,
    schema.optional() // Makes each field from base schema optional
  ])
)

export const entityUpdateValidator = vine.compile(vine.object(entityUpdateProperties))

// Specialized validators
export const entitySettingsValidator = vine.compile(entitySettingsSchema)

// Single-field validators for specific operations
export const entityStatusValidator = vine.compile(
  vine.enum(['active', 'inactive', 'pending'])
)
```

## Export Pattern (Required)

```typescript
// Always export all validators in a single block
export {
  entityStoreValidator,
  entityUpdateValidator,
  entitySettingsValidator,
  entityTagsValidator,
  entityStatusValidator,
}
```

## Usage in Controllers (Integration Pattern)

```typescript
// Standard usage with type inference
async store({ request, response }: HttpContext) {
  const payload = await request.validateUsing(entityStoreValidator)
  // payload is typed as Infer<typeof entityStoreValidator>
  
  const entity = await this.entityService.store(payload)
  return response.status(201).json(EntityPresenter.serialize(entity))
}
```

## Type Integration with Models (Real Pattern)

```typescript
// Use validators for model column typing
@column({ meta: { searchable: true, type: 'object' } })
declare settings: Infer<typeof entitySettingsValidator>

@column({ meta: { searchable: true, type: 'array' } })
declare tags: Infer<typeof entityTagsValidator>
```

## Required Patterns Summary

1. **Always use** `vine.compile()` immediately after schema definition
2. **Always use** `camelCase` for schema variables
3. **Always use** `Validator` suffix for compiled validators
4. **Always use** `.trim()` on string inputs unless whitespace is significant
5. **Always use** `vine.enum()` for controlled value sets
6. **Always use** minimum/maximum constraints for numbers and arrays
7. **Always export** validators individually for clear imports
8. **Always integrate** with TypeScript using `Infer<typeof validator>`
9. **Never use** inline validation in controllers or services
10. **Always reuse** base schemas for store/update patterns with `.optional()` modifications

