---
description: 
globs: apps/backend/**
alwaysApply: false
---
# Rule: Backend Route Patterns

**Rule Name**: `backend_routes`

## General Structure (Required)

All route files must:

* Use `router` from `@adonisjs/core/services/router`
* Use `middleware` from `#start/kernel` if needed
* Export a default arrow function (`export default () => {}`)
* Load controllers lazily via `() => import(...)`
  → **Do not use static imports**

```typescript
import router from '@adonisjs/core/services/router'
import { middleware } from '#start/kernel'

const controller = () => import('#app/modules/agents/agents_controller')

export default () => {
  router
    .group(() => {
      // Route definitions
    })
    .prefix('/agents')
    .use(middleware.auth())
}
```

## Route Declaration (Required Pattern)

* All routes must be declared inside a `router.group(...)`
* Groups must define a `.prefix(...)` matching the resource (e.g. `/agents`)
* Use consistent HTTP methods: `GET`, `POST`, `PUT`, `DELETE`
* Always include route parameters with `:` prefix (e.g. `:uid`)

## Standard CRUD Route Patterns

```typescript
import router from '@adonisjs/core/services/router'
import { middleware } from '#start/kernel'

const controller = () => import('#app/modules/agents/agents_controller')

export default () => {
  router
    .group(() => {
      // Standard CRUD routes
      router.get('/', [controller, 'index'])                    // GET /agents
      router.get('/:uid', [controller, 'show'])                 // GET /agents/:uid
      router.post('/', [controller, 'store'])                   // POST /agents
      router.put('/:uid', [controller, 'update'])               // PUT /agents/:uid
      router.delete('/:uid', [controller, 'destroy'])           // DELETE /agents/:uid
      
      // Custom actions
      router.post('/duplicate/:uid', [controller, 'duplicate']) // POST /agents/duplicate/:uid
      router.put('/update-image/:uid', [controller, 'updateImage'])
      router.put('/update-onboarding/:uid', [controller, 'updateOnboarding'])
      
      // Factory/configuration endpoints
      router.get('/factory/settings', [controller, 'getFactorySettings'])
      router.get('/factory/tools', [controller, 'getFactoryTools'])
    })
    .prefix('/agents')
    .use(middleware.auth())
}
```

## Route → Controller Mapping

| HTTP Method | Route Path                     | Controller Method        | Purpose                    |
|-------------|--------------------------------|--------------------------|----------------------------|
| GET         | `/`                           | `index()`                | List all items             |
| GET         | `/:uid`                       | `show(uid)`              | Get single item            |
| POST        | `/`                           | `store(payload)`         | Create new item            |
| PUT         | `/:uid`                       | `update(uid, payload)`   | Update existing item       |
| DELETE      | `/:uid`                       | `destroy(uid)`           | Delete item                |
| POST        | `/duplicate/:uid`             | `duplicate(uid)`         | Duplicate item             |
| PUT         | `/update-image/:uid`          | `updateImage()`          | Update specific property   |
| GET         | `/factory/settings`           | `getFactorySettings()`   | Get default configuration  |

## Middleware Application (Specific Rules)

Apply middleware at the most granular level:

```typescript
export default () => {
  router
    .group(() => {
      // Public routes (no middleware)
      router.get('/factory/settings', [controller, 'getFactorySettings'])
      
      // Protected routes with authentication
      router.get('/', [controller, 'index']).use(middleware.auth())
      router.post('/', [controller, 'store']).use([middleware.auth(), middleware.acl('create_agents')])
      
      // Long-running operations
      router.post('/duplicate/:uid', [controller, 'duplicateWithTraining'])
        .use([middleware.auth(), middleware.no_timeout()])
    })
    .prefix('/agents')
}
```

**Middleware Rules**:
- Apply to specific routes using `.use()` method
- Pass multiple middlewares as an array `[middleware.auth(), middleware.acl()]`
- Avoid applying middleware to entire groups unless it applies to every route
- Use descriptive middleware names (`auth`, `acl`, `no_timeout`)

## Public vs Protected Route Groups

```typescript
export default () => {
  // Public routes
  router
    .group(() => {
      router.get('/factory/settings', [controller, 'getFactorySettings'])
      router.get('/public/:uid', [controller, 'getPublicData'])
    })
    .prefix('/agents')

  // Protected routes  
  router
    .group(() => {
      router.get('/', [controller, 'index'])
      router.post('/', [controller, 'store'])
      router.put('/:uid', [controller, 'update'])
      router.delete('/:uid', [controller, 'destroy'])
    })
    .prefix('/agents')
    .use(middleware.auth())
}
```

## Route Parameter Patterns

Use descriptive parameter names:

```typescript
export default () => {
  router
    .group(() => {
      // Standard UID parameter
      router.get('/:uid', [controller, 'show'])
      router.put('/:uid', [controller, 'update'])
      
      // Multiple parameters
      router.get('/:agent_uid/providers/:provider_uid', [controller, 'showProvider'])
      
      // Optional parameters (use query strings instead)
      router.get('/', [controller, 'index']) // ?page=1&per_page=10&search=term
    })
    .prefix('/agents')
}
```

## Required Patterns Summary

1. **Always use** lazy controller imports with `() => import()`
2. **Always group** routes with `router.group()`
3. **Always set** `.prefix()` to match resource name
4. **Always use** descriptive parameter names (`:uid`, `:agent_uid`)
5. **Always order** routes logically (factory → custom → CRUD)
6. **Always apply** middleware at route level, not group level (unless universal)
7. **Always use** appropriate HTTP methods (GET, POST, PUT, DELETE)
8. **Never use** static controller imports
9. **Never mix** public and protected routes in the same group
10. **Always export** default arrow function

