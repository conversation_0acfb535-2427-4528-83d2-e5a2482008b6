---
description: Component Patterns with Real Implementation Examples
globs: apps/frontend/**
alwaysApply: false
---
# Rule: Frontend Component Patterns

**Rule Name**: `frontend_components`

## Component Architecture Blueprint

Every Vue component MUST follow this composition API pattern:

```vue
<script setup lang="ts">
// Imports (external libraries, components, types)
// ...

const emit = defineEmits<{ edit: [uid: string]; delete: [uid: string] }>()
const props = defineProps<{
  isLoading?: boolean
  data?: EntityType[]
}>()

// Composables and external state
const { toast } = useToast()
const { data, isLoading } = useSomeQuery()

// Local reactive state
const localState = ref('')
const computedValue = computed(() => localState.value.toUpperCase())

// Methods/functions
function handleAction(uid: string) {
  emit('edit', uid)
}

// Watchers (if needed)
watchEffect(() => {
  if (data.value) {
    // React to data changes
  }
})

// Expose public API (if needed)
defineExpose({ handleAction })
</script>

<template>
  <!-- Template implementation -->
</template>
```

## Feature Module Structure

Every feature follows the exact 3-component pattern:

```
components/{domain}/{feature}/
├── {Domain}{Feature}.vue           # Main orchestrator
├── {Domain}{Feature}Table.vue      # Data display  
└── {Domain}{Feature}Form.vue       # Data input
```

### Table Component Pattern

```vue
<template>
  <BaseTable
    :isLoading="isLoading || isFetching"
    :heads="heads"
    :rows="entities"
    :actions="['edit']"
    empty-message="No entities found, add a new entity to get started."
    @edit="(row: Entity) => emit('edit', row.uid)"
  >
    <template #actions>
      <Button @click="emit('edit', undefined)"> 
        <BaseIcon name="Plus" /> Add Entity 
      </Button>
    </template>
    
    <template #row="{ row }">
      <TableCell class="max-w-[300px] truncate">{{ row.name }}</TableCell>
      <TableCell>{{ row.status }}</TableCell>
      <TableCell class="text-right">
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="icon" @click.stop>
              <BaseIcon name="MoreVertical" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem @click="emit('edit', row.uid)">
              <BaseIcon name="Pencil" class="w-4 h-4 mr-2" />
              Edit
            </DropdownMenuItem>
            <DropdownMenuItem @click="emit('delete', row.uid)">
              <BaseIcon name="Trash2" class="w-4 h-4 mr-2" />
              Delete
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </TableCell>
    </template>
  </BaseTable>
</template>

<script setup lang="ts">
const emit = defineEmits(['edit', 'delete'])

const { data, isLoading, isFetching } = queryEntities()
const { data: relatedData } = queryRelatedData()
const entities = ref<Entity[]>([])

watchEffect(() => {
  entities.value = data.value?.data ?? []
})

const getRelatedName = (relatedUid: string | undefined): string => {
  if (!relatedUid) return 'N/A'
  const related = relatedData.value?.data?.find((item) => item.uid === relatedUid)
  return related?.name || relatedUid
}

const heads = [
  { label: 'Name', key: 'name' },
  { label: 'Status', key: 'status' },
  { label: 'Related', key: 'relatedUid' },
]
</script>
```

### Form Component Pattern

```vue
<template>
  <BaseDialog :open="true" :icon="ICONS.FEATURE_ICON" :title="title" :description="description">
    <BaseForm :isLoading="isLoading" :isSubmitting="isSubmitting" :inputs="3" @submit="onSubmit">
      <FormField v-slot="{ componentField }" name="name">
        <FormItem>
          <FormLabel>Name</FormLabel>
          <FormControl>
            <Input v-bind="componentField" placeholder="Enter name" />
          </FormControl>
          <FormMessage />
        </FormItem>
      </FormField>

      <DialogFooter>
        <Button type="button" variant="outline" @click="closeDialog">Cancel</Button>
        <Button type="submit" :disabled="isSubmitting">
          {{ editingUid ? 'Update' : 'Add' }}
        </Button>
      </DialogFooter>
    </BaseForm>
  </BaseDialog>
</template>

<script setup lang="ts">
const { toast } = useToast()
const { handleSubmit, isSubmitting, setValues } = useForm({
  initialValues: {
    relatedUid: '',
    name: '',
    description: '',
  },
  validationSchema: toTypedSchema(
    z.object({
      relatedUid: z.string().min(1, { message: 'Related item is required' }),
      name: z.string().min(1, { message: 'Name is required' }),
      description: z.string().optional(),
    })
  ),
})

const editingUid = ref<string | undefined>()
const mutation = mutateEntity(editingUid)
const { isLoading, isError, data } = queryEntityById(editingUid)
const { data: relatedData } = queryRelatedData()

const title = computed(() => `${editingUid.value ? 'Edit' : 'Add'} Entity`)
const relatedItems = computed(() => relatedData.value?.data ?? [])

function openDialog(uid?: string) {
  editingUid.value = uid
}

const onSubmit = handleSubmit(async (values) => {
  try {
    await mutation.mutateAsync(values)
    toast({ title: `${editingUid.value ? 'Updated' : 'Added'} successfully` })
  } catch (err: any) {
    toast({
      title: `Failed to ${editingUid.value ? 'update' : 'add'}`,
      description: err.message || 'Operation failed',
      variant: 'destructive',
    })
  }
})

watchEffect(() => {
  if (data.value && editingUid.value) {
    setValues({
      relatedUid: data.value.relatedUid ?? '',
      name: data.value.name ?? '',
      description: data.value.description ?? '',
    })
  }
})

defineExpose({ openDialog })
</script>
```

### Main Component Pattern

```vue
<template>
  <div class="space-y-6">
    <EntityManagementTable 
      @edit="formRef?.openDialog($event)" 
      @delete="handleDelete" 
    />
    
    <EntityManagementForm 
      ref="formRef" 
    />
  </div>
</template>

<script setup lang="ts">
const formRef = ref<InstanceType<typeof EntityManagementForm>>()

function handleDelete(uid: string) {
  // Delete logic using mutateEntityDestroy
}
</script>
```

## Required shadcn/ui Import Pattern

```ts
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/shadcn/ui/form'
```

## Component Communication Pattern

- **Tables emit**: `edit` (with row data), `delete` (with row uid)
- **Forms expose**: `openDialog(uid?: string)` method via `defineExpose`
- **Main components**: Coordinate between table and form using refs

## Required Form Validation Pattern

```ts
import { useForm } from 'vee-validate'
import { toTypedSchema } from '@vee-validate/zod'
import * as z from 'zod'

const { handleSubmit, isSubmitting, setValues } = useForm({
  initialValues: { /* ... */ },
  validationSchema: toTypedSchema(
    z.object({
      field: z.string().min(1, { message: 'Field is required' }),
      email: z.string().email({ message: 'Valid email is required' }),
      url: z.string().url({ message: 'Valid URL is required' }),
    })
  ),
})
```

## Error Handling Pattern

```ts
const { toast } = useToast()

try {
  await mutation.mutateAsync(payload)
  toast({ title: 'Success message' })
} catch (err: any) {
  toast({
    title: 'Error title',
    description: err.message || 'Operation failed',
    variant: 'destructive',
  })
}
```