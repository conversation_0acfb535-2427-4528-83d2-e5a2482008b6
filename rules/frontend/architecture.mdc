---
description: 
globs: apps/frontend/**
alwaysApply: false
---
# Rule: Module Pattern for Table/Form Components

**Rule Name**: `frontend_architecture`

This rule standardizes the creation of modules containing a table, form, and main component with consistent patterns, architecture, and naming conventions.

## Module Structure Requirements

Each module MUST follow this exact structure:
```
ModuleName/
├── ModuleNameTable.vue     # Data table component
├── ModuleNameForm.vue      # Add/Edit form component  
├── ModuleName.vue          # Main coordination component (optional)
└── index.ts               # Export barrel (optional)
```

## 1. Table Component Pattern (`ModuleNameTable.vue`)

**MUST follow this exact template:**

```vue
<template>
  <BaseTable
    :isLoading="isLoading || isFetching"
    :heads="heads"
    :rows="items"
    :empty-message="`No ${moduleName.toLowerCase()} found, add a new ${moduleName.toLowerCase()} to get started.`"
  >
    <template #actions>
      <Button @click="emit('edit', undefined)">
        <BaseIcon name="Plus" /> Add {{ ModuleName }}
      </Button>
    </template>
    <template #row="{ row }">
      <!-- Custom table cells here -->
      <TableCell>{{ row.primary_field }}</TableCell>
      <TableCell>{{ row.secondary_field }}</TableCell>
      <TableCell>{{ getAgentName(row.agent_uid) }}</TableCell>
      <!-- Always end with actions cell -->
      <TableCell class="text-right">
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="icon" @click.stop>
              <BaseIcon name="MoreVertical" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem @click="props.onEdit(row.uid)">
              <BaseIcon name="Pencil" class="w-4 h-4 mr-2" />
              Edit
            </DropdownMenuItem>
            <DropdownMenuItem @click="props.onDelete(row.uid)">
              <BaseIcon name="Trash2" class="w-4 h-4 mr-2" />
              Delete
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </TableCell>
    </template>
  </BaseTable>
</template>

<script setup lang="ts">
import BaseTable from '@/components/base/table/BaseTable.vue'
import type { EntityType } from '@/types'
import { queryEntities } from '@/services/module_service'
import { queryAgents } from '@/services/agent_service'

const props = defineProps<{
  onEdit: (uid?: string) => void
  onDelete: (uid: string) => void
}>()

const { data, isLoading, isFetching } = queryEntities()
const { data: agentsData } = queryAgents()
const items = ref<EntityType[]>([])

watchEffect(() => {
  items.value = data.value?.data ?? []
})

const getAgentName = (agentUid: string | undefined): string => {
  if (!agentUid) return 'N/A'
  const agent = agentsData.value?.data?.find((agent) => agent.uid === agentUid)
  return agent?.label || agentUid
}

const heads = [
  { label: 'Primary Field', key: 'primary_field' },
  { label: 'Secondary Field', key: 'secondary_field' },
  { label: 'Agent', key: 'agent_uid', align: 'right' },
]
</script>
```

**Required Elements:**
- ✅ Use `BaseTable` component with exact props pattern
- ✅ Include both 'edit' and 'delete' in `:actions` array
- ✅ Use service queries for data fetching (`queryEntities()`)
- ✅ Use `watchEffect()` to populate reactive arrays
- ✅ Emit events: `@edit` and `@delete` with `row.uid`
- ✅ Include `#actions` slot with "Add" button
- ✅ Include `#row` slot with dropdown menu for actions
- ✅ Always include `getAgentName()` helper if agents are involved
- ✅ Use consistent `heads` array structure

## 2. Form Component Pattern (`ModuleNameForm.vue`)

**MUST follow this exact template:**

```vue
<template>
  <BaseDialog :open="isDialogOpen" @update:open="isDialogOpen = $event" :icon="ICONS.MODULE_ICON" :title="title" :description="description">
    <BaseForm :isLoading="isLoading" :isSubmitting="isSubmitting" :inputs="FIELD_COUNT" @submit="onSubmit">
      <!-- Agent selector (if applicable) -->
      <FormField v-slot="{ componentField }" name="agent_uid">
        <FormItem>
          <FormLabel>Agent</FormLabel>
          <FormControl>
            <Select v-bind="componentField">
              <SelectTrigger>
                <SelectValue placeholder="Select an agent" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem v-for="agent in agents" :key="agent.uid" :value="agent.uid">
                  {{ agent.label }}
                </SelectItem>
              </SelectContent>
            </Select>
          </FormControl>
          <FormMessage />
        </FormItem>
      </FormField>

      <!-- Other form fields -->
      <FormField v-slot="{ componentField }" name="field_name">
        <FormItem>
          <FormLabel>Field Label</FormLabel>
          <FormControl>
            <Input v-bind="componentField" placeholder="Placeholder text" />
          </FormControl>
          <FormMessage />
        </FormItem>
      </FormField>

      <DialogFooter>
        <Button type="button" variant="outline" @click="closeDialog">Cancel</Button>
        <Button type="submit" :disabled="isSubmitting">
          {{ editingUid ? 'Update' : 'Add' }}
        </Button>
      </DialogFooter>
    </BaseForm>
  </BaseDialog>
</template>

<script setup lang="ts">
import { ref, watchEffect, computed } from 'vue'
import { useForm } from 'vee-validate'
import { toTypedSchema } from '@vee-validate/zod'
import * as z from 'zod'
import { queryEntityById, mutateEntity } from '@/services/module_service'
import { queryAgents } from '@/services/agent_service'
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/shadcn/ui/form'
import { ICONS } from '@/constants'
import type { FormComponent } from '@/types'

// Composables
const { toast } = useToast()
const { handleSubmit, isSubmitting, setValues } = useForm({
  initialValues: {
    uid: undefined as string | undefined,
    agent_uid: '',
    field_name: '',
    // ... other fields
  },
  validationSchema: toTypedSchema(
    z.object({
      uid: z.string().optional(),
      agent_uid: z.string().min(1, { message: 'Agent is required' }),
      field_name: z.string().min(1, { message: 'Field is required' }),
      // ... other validations
    })
  ),
})

// Refs
const isDialogOpen = ref(false)
const editingUid = ref<string | undefined>()

// Queries
const mutation = mutateEntity(editingUid)
const { isLoading, isError, data } = queryEntityById(editingUid)
const { data: agentsData } = queryAgents()

// Computed
const title = computed(() => `${editingUid.value ? 'Edit' : 'Add'} ModuleName`)
const description = computed(() => `${editingUid.value ? 'Edit this entity' : 'Add a new entity'}`)
const agents = computed(() => agentsData.value?.data ?? [])

// Functions
const openDialog = (uid?: string) => {
  editingUid.value = uid
  isDialogOpen.value = true
}

const closeDialog = () => {
  isDialogOpen.value = false
}

const onSubmit = handleSubmit(async (values) => {
  try {
    const payload = {
      agent_uid: values.agent_uid,
      field_name: values.field_name,
      // ... other fields
    }

    if (editingUid.value) {
      await mutation.mutateAsync({ ...payload, uid: editingUid.value })
    } else {
      await mutation.mutateAsync(payload)
    }

    toast({ title: `${editingUid.value ? 'Updated' : 'Added'} successfully` })
    closeDialog()
  } catch (err: any) {
    toast({
      title: `Failed to ${editingUid.value ? 'update' : 'add'}`,
      description: err.message || 'Operation failed',
      variant: 'destructive',
    })
  }
})

// Watchers
watchEffect(() => {
  if (!editingUid.value) return
  if (isLoading.value) return

  if (isError.value) {
    toast({
      title: 'Error Loading Entity',
      description: `Entity with ID ${editingUid.value} not found.`,
      variant: 'destructive',
    })
    closeDialog()
    return
  }

  if (data.value) {
    setValues({
      uid: editingUid.value,
      agent_uid: data.value.agent_uid ?? '',
      field_name: data.value.field_name ?? '',
      // ... other fields
    })
  }
})

defineExpose<FormComponent>({
  openDialog,
  closeDialog,
})
</script>
```

**Required Elements:**
- ✅ Use `BaseDialog` with proper icon and computed title/description
- ✅ Use `BaseForm` with exact `:inputs` count (count actual FormField components)
- ✅ Use `vee-validate` with `useForm()`, `handleSubmit()`, `isSubmitting`
- ✅ Use `zod` validation schema with `toTypedSchema()`
- ✅ Use proper form components: `FormField`, `FormItem`, `FormLabel`, `FormControl`, `FormMessage`
- ✅ Include agent selector pattern if applicable (`agent.label` not `agent.name`)
- ✅ Implement `watchEffect()` for data loading and form population
- ✅ Use service mutations with proper error handling
- ✅ Expose `openDialog()` method for parent access
- ✅ Follow consistent payload structure
- ✅ Use standard toast notifications

---

## 3. Main Component Pattern (`ModuleName.vue`)

**MUST follow this exact template:**

```vue
<template>
  <BaseDialog :open="true" :icon="ICONS.MODULE_ICON" title="Module Name" description="Manage your entities.">
    <ModuleNameTable :onEdit="handleEdit" :onDelete="handleDelete" />
  </BaseDialog>

  <ModuleNameForm ref="formRef" />
</template>

<script setup lang="ts">
import { ref } from 'vue'
import ModuleNameForm from './ModuleNameForm.vue'
import { ICONS } from '@/constants'
import { mutateEntityDestroy } from '@/services/module_service'

// Composables
const { toast } = useToast()

// Refs
const formRef = ref<typeof ModuleNameForm>>()

// Functions
const handleEdit = (uid?: string) => {
  formRef.value?.openDialog(uid)
}

const handleDelete = async (uid: string) => {
  const { mutateAsync } = mutateEntityDestroy(ref(uid))

  try {
    await mutateAsync(undefined)
    toast({
      title: 'Entity Deleted',
      description: 'The entity has been successfully deleted.',
    })
  } catch (error: any) {
    toast({
      title: 'Error Deleting Entity',
      description: error?.message || 'An unexpected error occurred.',
      variant: 'destructive',
    })
  }
}
</script>
```

**Required Elements:**
- ✅ Wrap table in `BaseDialog` for consistent presentation
- ✅ Simple coordination between table and form
- ✅ Consistent `handleEdit()` and `handleDelete()` functions
- ✅ Use service destroy mutations
- ✅ Proper toast notifications

## 4. Service Integration Requirements

**Service functions MUST follow these patterns:**

```typescript
// Query functions
export const queryEntities = () => {
  const api = useApi()
  return useQuery({
    queryKey: ['entities'],
    queryFn: () => api.entities.$get().unwrap(),
  })
}

export const queryEntityById = (uid: Ref<string | undefined>) => {
  const api = useApi()
  return useQuery({
    queryKey: ['entities', uid],
    queryFn: () => api.entities({ uid: uid.value! }).$get().unwrap(),
    enabled: computed(() => !!uid.value),
  })
}

// Mutation functions
export const mutateEntity = (uid: Ref<string | undefined>) => {
  const api = useApi()
  return useMutation({
    mutationFn: (payload) => {
      if (uid.value) {
        return api.entities({ uid: uid.value }).$put(payload).unwrap()
      }
      return api.entities.$post(payload).unwrap()
    },
    onSuccess() {
      queryClient.invalidateQueries({ queryKey: ['entities'] })
    },
  })
}

export const mutateEntityDestroy = (uid: Ref<string>) => {
  const api = useApi()
  return useMutation({
    mutationFn: () => api.entities({ uid: uid.value }).$delete().unwrap(),
    onSuccess() {
      queryClient.invalidateQueries({ queryKey: ['entities'] })
    },
  })
}
```

---

#### 🔹 **5. Icon and Constants Standards**

**Icons MUST be added to constants:**
```typescript
export const ICONS = {
  // ... existing icons
  MODULE_NAME: 'IconName', // Use descriptive PascalCase names
}
```

**Icon naming conventions:**
- ✅ Use descriptive names: `COPYRIGHTS: 'Copyright'`, `DOMAINS: 'Globe'`
- ✅ Avoid generic names like `SETTINGS` for specific modules
- ✅ Use Lucide icon names exactly as they appear in the library

---

#### 🔹 **6. Type Safety Requirements**

**Type definitions MUST:**
- ✅ Use `InferRequestType` and `InferResponseType` from TuyauJS
- ✅ Define proper entity types in `@/types`
- ✅ Ensure agent properties use `label` not `name`
- ✅ Use consistent UID naming (`uid` not `id`)

---

#### 🔹 **7. Validation Schema Patterns**

**Common validation patterns:**
```typescript
// Required string
field_name: z.string().min(1, { message: 'Field is required' })

// Optional string
optional_field: z.string().optional()

// Email
email: z.string().email({ message: 'Valid email is required' })

// URL (optional)
url: z.string().url({ message: 'Valid URL required' }).optional().or(z.literal(''))

// Number as string (for form inputs)
port: z.string().min(1, { message: 'Port is required' })

// Boolean
secure: z.boolean()

// Agent selection
agent_uid: z.string().min(1, { message: 'Agent is required' })
```

---

#### 🔹 **8. Error Handling Standards**

**Toast notifications MUST follow these patterns:**
```typescript
// Success
toast({ title: `${editingUid.value ? 'Updated' : 'Added'} successfully` })

// Error
toast({
  title: `Failed to ${editingUid.value ? 'update' : 'add'}`,
  description: err.message || 'Operation failed',
  variant: 'destructive',
})

// Delete success
toast({
  title: 'Entity Deleted',
  description: 'The entity has been successfully deleted.',
})

// Loading error
toast({
  title: 'Error Loading Entity',
  description: `Entity with ID ${editingUid.value} not found.`,
  variant: 'destructive',
})
```

---

#### 🔹 **9. Checklist for New Modules**

Before completing any module, verify:

**Table Component:**
- [ ] Uses `BaseTable` with all required props
- [ ] Includes both 'edit' and 'delete' actions
- [ ] Has proper service integration
- [ ] Uses `watchEffect()` for data population
- [ ] Implements `getAgentName()` helper if needed
- [ ] Has consistent `heads` array structure

**Form Component:**
- [ ] Uses `BaseDialog` with proper icon
- [ ] Uses `BaseForm` with accurate `:inputs` count
- [ ] Implements `vee-validate` with `zod` schema
- [ ] Has proper `watchEffect()` for data loading
- [ ] Uses consistent payload structure
- [ ] Exposes `openDialog()` method
- [ ] Uses `agent.label` not `agent.name`

**Main Component:**
- [ ] Wraps table in `BaseDialog`
- [ ] Has consistent event handlers
- [ ] Uses proper service mutations
- [ ] Implements standard toast notifications

**Service Integration:**
- [ ] Follows query/mutation naming patterns
- [ ] Uses proper cache invalidation
- [ ] Implements error handling
- [ ] Uses consistent parameter patterns

**Constants & Types:**
- [ ] Adds module icon to `ICONS`
- [ ] Uses proper type definitions
- [ ] Ensures consistent naming conventions

---

#### 🔹 **10. Forbidden Patterns**

**NEVER:**
- ❌ Use manual table implementations instead of `BaseTable`
- ❌ Use custom dialog implementations instead of `BaseDialog`
- ❌ Skip validation schemas in forms
- ❌ Use `agent.name` (use `agent.label`)
- ❌ Include filters or search in table components
- ❌ Use inconsistent service naming
- ❌ Skip error handling or toast notifications
- ❌ Use hardcoded strings for titles/descriptions
- ❌ Mix service patterns or mutation approaches

**ALWAYS:**
- ✅ Follow the exact templates provided
- ✅ Use consistent naming conventions
- ✅ Implement proper type safety
- ✅ Include comprehensive error handling
- ✅ Use service-based data management
- ✅ Follow the established architectural patterns