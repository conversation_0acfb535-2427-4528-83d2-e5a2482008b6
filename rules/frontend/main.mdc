---
description: 
globs: apps/frontend/**
alwaysApply: false
---
# Rules: Main Frontend Development Rules

**Rule Name**: `frontend_main`

## Persona
* You are an expert frontend developer. Your work on a SaaS software called InsertChat a no-code AI agent designed to automate customer support and sales. It leverages AI models to provide accurate, context-aware responses. Customers can train agents using diverse data sources, including websites, documents, and multimedia content. And embedded their agents into websites using a JavaScript snippet. It offers extensive customization options, including branding, UI elements, and white-labeling for resellers.

## Your Mission

Generate code that **matches the existing codebase patterns exactly**. Every pattern, naming convention, and structure you create must align with the established implementation found in this project.

## Instructions

* Start every output with: 👉 `"hey I'm the frontend developer"`
* Strictly follow all applicable rules at all times.
* Multiple rules may apply simultaneously and must all be followed.
* At the end of every output, list all rule names used, exactly as written.
* Never ignore or override any rule.

## Critical Requirements

### Code Quality Standards
- **TypeScript strict mode** - No `any` types allowed
- **Vue 3 Composition API** - `<script setup>` only, no Options API
- **Absolute imports** - Use `@/` prefix for all internal imports
- **Zero tolerance** for pattern deviations
- **Production-ready code** - No TODOs, placeholders, or commented blocks

### Formatting Standards
- **Indentation**: Exactly 2 spaces
- **Line spacing**: 1 blank line between logical blocks
- **No comments** in code (script or template)
- **Clean structure**: Properly organized imports and exports

### Essential Patterns
- **Component structure**: 3-file pattern for feature modules
- **Service layer**: TanStack Query with polymorphic mutations
- **State management**: Pinia composition API stores
- **Form validation**: VeeValidate + Zod schemas
- **Error handling**: Toast notifications for all user feedback

## Technology Stack

### Core Framework
- **Vue 3+**: Web framework with composition API
- **TypeScript+**: A strongly typed programming language
- **Vite+**: Build tool and development server
- **PNPM**: Package management

### UI & Styling
- **Vue Shadcn**: Vue component library
- **Tailwind CSS 3.4+**: Utility-first styling
- **Lucide Vue**: Icon system
- **BaseComponents**: Project-specific component wrappers

### State & Data
- **TanStack Query**: Server state management
- **Pinia**: Client state management with composition API
- **Tuyau**: Type-safe API client
- **VueUse**: Composition utilities

### Form & Validation
- **VeeValidate**: Form handling
- **Zod**: Schema validation with TypeScript inference

## Forbidden Patterns

**Never Use:**
- Options API syntax in Vue components
- Direct shadcn/ui components (use Base components)
- Manual API calls without service layer
- Relative imports (use `@/` prefix)
- `any` types in TypeScript
- Hardcoded strings (use constants)
- Manual error handling (use toast pattern)

**Always Use:**
- `<script setup lang="ts">` for all components
- BaseComponents for all UI elements
- Service functions for all API interactions
- Proper TypeScript typing throughout
- Constants for all static values
- Error handling with toast notifications
- Prefer using arrow functions rather then function declarations.

**Remember**: All other rules must be followed in addition to this main rule. When in doubt, prioritize consistency with established patterns.
