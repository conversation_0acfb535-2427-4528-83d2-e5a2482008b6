---
description: 
globs: apps/frontend/**
alwaysApply: false
---
# Rule: Frontend API Endpoint Patterns

**Rule Name**: `frontend_api_endpoints`

## API Source of Truth

All API endpoints and type definitions are auto-generated in: [api.ts](mdc:apps/backend/.adonisjs/api.ts)

This file contains:
- Complete type definitions for all request/response payloads
- Full endpoint structure with parameters
- Validation schemas integration
- TuyauJS-compatible types

**ALWAYS** reference this file before implementing any API calls.

## API Client Usage

### Required API Composables

Use these composables for all API interactions:

```ts
import { useApi } from '@/plugins/api'        // Authenticated requests (already include the app_uid)
import { useApiAnon } from '@/plugins/api'    // Anonymous requests
```

### Service Layer Pattern

Every API interaction MUST go through service functions (see `frontend_services` rule):

```ts
// ✅ Correct - Using service functions
import { queryAgents, mutateAgent } from '@/services/agent_service'

const { data, isLoading } = queryAgents()
const mutation = mutateAgent(agentUid)

// ❌ Wrong - Direct API calls in components
const api = useApi()
const data = await api.v2[appUid.value].agents.$get().unwrap()
```

## Endpoint Structure Reference

### Authentication Endpoints (Public)
```ts
// Login/Register (no app_uid required)
apiAnon.auth.login.$post(payload)
apiAnon.auth.register.$post(payload)
apiAnon.auth.me.$get()
apiAnon.auth.logout.$get()
```

### App-Scoped Endpoints (Authenticated + App Uid already included)
```ts
// Pattern: api.{module}.{action}
api.agents.$get()                   // List agents
api.agents({ uid }).$get()          // Get single agent
api.agents.$post(payload)           // Create agent
api.agents({ uid }).$put(payload)   // Update agent
api.agents({ uid }).$delete()       // Delete agent
```

### Embed Endpoints (Public)
```ts
// Chat embeds (no authentication)
apiAnon.embeds.agents({ agent_uid }).$get()
apiAnon.embeds.chats.$post(payload)
apiAnon.embeds.messages.$post(payload)
```

## Type Inference Patterns

### Using TuyauJS Type Inference

```ts
import type { InferRequestType, InferResponseType } from '@tuyau/utils/types'

// Infer request payload types
type LoginPayload = InferRequestType<
  ReturnType<typeof useApiAnon>['v2']['auth']['login']['$post']
>

// Infer response types
type UserResponse = InferResponseType<
  ReturnType<typeof useApi>['v2']['auth']['me']['$get']
>
```

### When to Use Explicit Types

Use explicit types when:
- Payload has complex nested structures
- Shared types are already defined (e.g., `AgentCreatePayload`)
- FormData uploads are involved
- Type inference is too complex

```ts
// ✅ Acceptable when type inference is complex
import type { AgentCreatePayload } from '@/types/agent'

export const mutateAgent = (uid: Ref<string | undefined>) => {
  return useMutation({
    mutationFn: (payload: AgentCreatePayload) => {
      // Implementation
    }
  })
}
```

## Common Endpoint Patterns

### CRUD Operations

Standard resource endpoints follow this pattern:

```ts
// List with filtering
api.{resource}.$get({ 
  query: { page: 1, search: 'term' } 
})

// Show single
api.{resource}({ uid }).$get()

// Create
api.{resource}.$post(payload)

// Update
api.{resource}({ uid }).$put(payload)

// Delete
api.{resource}({ uid }).$delete()
```

### In Components

```ts
const onSubmit = handleSubmit(async (values) => {
  try {
    await mutation.mutateAsync(values)
    toast({ title: 'Success' })
  } catch (err: any) {
    toast({
      title: 'Error',
      description: err.message || 'Operation failed',
      variant: 'destructive',
    })
  }
})
```

## Parameter Patterns

### Path Parameters

```ts
// Single parameter
api.agents({ uid: 'agent-123' }).$get()

// Multiple parameters
api.subscriptions['get-embedded-checkout-session']({
  stripe_price_id: 'price_123',
  quantity: '1'
}).$get()
```

### Query Parameters

```ts
// With query parameters
api.agents.$get({
  query: { 
    page: 1, 
    limit: 10, 
    search: 'term' 
  }
})

// Filter validation (using requestFilterValidator)
api.chats.$get({
  query: {
    page: 1,
    limit: 25,
    search: '',
    sort: 'created_at',
    order: 'desc'
  }
})
```

## File Upload Patterns

### Using FormData

```ts
// File uploads always use FormData
export const mutateAgentImage = (uid: Ref<string | undefined>) => {
    const api = useApi()
    return useMutation({
        mutationFn: (file: File) => {
            const formData = new FormData()
            formData.append('image', file)
            return api.agents['update-image'].$put(formData).unwrap()
        }
    })
}
```

## Required Patterns

1. **Always use** service layer functions, never direct API calls in components
2. **Always use** `.unwrap()` on all endpoint calls
3. **Always use** proper type inference with TuyauJS
4. **Always use** `useApi()` for authenticated routes, `useApiAnon()` for public routes
5. **Always reference** [api.ts](mdc:apps/backend/.adonisjs/api.ts) for endpoint structure
6. **Always validate** parameters match the expected endpoint signature
7. **Never hardcode** endpoint URLs - use the TuyauJS client structure