---
description: State Management Patterns with Pinia
globs: apps/frontend/**
alwaysApply: false
---
# Rule: Frontend State Management Patterns

**Rule Name**: `frontend_state`

## Pinia Store Structure

Every store uses composition API pattern:

```ts
// Pattern: use{Domain}Store
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { useLocalStorage } from '@vueuse/core'

export const useDomainStore = defineStore('domain', () => {
  // 1. State
  const localState = ref('')
  const persistedState = useLocalStorage('key', defaultValue)
  
  // 2. Getters
  const derivedState = computed(() => localState.value.toUpperCase())
  
  // 3. Actions
  function updateState(newValue: string) {
    localState.value = newValue
  }
  
  // 4. Return everything
  return { localState, persistedState, derivedState, updateState }
})
```

## Persistence Patterns

```ts
// Persistent state (survives page refresh)
const userPreferences = useLocalStorage('preferences', { theme: 'system' })

// Session state (cleared on page refresh)  
const temporaryFilters = useSessionStorage('temp_filters', {})

// Component state (no persistence)
const isLoading = ref(false)
```

## Usage in Components

```ts
import { useAppStore } from '@/stores/app'
import { storeToRefs } from 'pinia'

const appStore = useAppStore()
const { currentAppUid, authToken } = storeToRefs(appStore)
const { setCurrentAppUid, reset } = appStore
```

## Required Patterns

1. **Always use** composition API with `defineStore('name', () => {})`
2. **Always use** `useLocalStorage` for persistent state
3. **Always use** `storeToRefs` when destructuring reactive state
4. **Never mutate** state directly from components - use actions

## Store Naming Conventions

- **Store files**: `{domain}.ts` (app.ts, dialog.ts, theme.ts)
- **Store names**: `{domain}` (app, dialog, theme)  
- **Composable names**: `use{Domain}Store` (useAppStore, useDialogStore)
- **Action functions**: Descriptive verbs (setCurrentAppUid, openDialog, reset)