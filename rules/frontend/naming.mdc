---
description: 
globs: apps/frontend/**
alwaysApply: false
---
# Rule: Naming Conventions

**Rule Name**: `frontend_naming_conventions`

## Naming Conventions

### Component Names
- **File names**: PascalCase for components (`EntitySettings.vue`, `FeatureManagement.vue`)
- **Component exports**: Must match filename exactly
- **Component usage**: PascalCase in templates (`<EntitySettings />`)

### Variable Names
- **Variables & functions**: camelCase (`currentUser`, `handleSubmit`)
- **Constants**: UPPER_SNAKE_CASE (`ICONS`, `API_ENDPOINTS`)
- **Ref variables**: Descriptive camelCase (`isLoading`, `selectedEntity`)

### File Organization
```
components/
├── base/                    # Base components in PascalCase
│   ├── BaseButton.vue
│   ├── BaseTable.vue
│   └── BaseDialog.vue
├── features/               # Domain modules in camelCase
│   ├── management/         # Feature modules in camelCase
│   │   ├── FeatureManagement.vue
│   │   ├── FeatureManagementTable.vue
│   │   └── FeatureManagementForm.vue
services/
├── entity_service.ts       # Service files in snake_case
├── feature_service.ts
stores/
├── app.ts                  # Store files in camelCase
├── dialog.ts
```

### Variable Naming Patterns
```ts
// ✅ Correct naming
const isLoading = ref(false)
const currentUser = ref(null)
const selectedItems = ref([])
const entityData = ref(null)

// ✅ Function naming
function handleSubmit() {}
function openDialog() {}
function queryEntities() {}
function mutateEntity() {}

// ✅ Computed naming
const filteredItems = computed(() => {})
const hasSelection = computed(() => {})

// ❌ Wrong naming
const loading = ref(false)          // Too generic
const user = ref(null)             // Too short
const sel = ref([])                // Abbreviated
const entityInfo = ref(null)       // Use 'data' not 'info'
```