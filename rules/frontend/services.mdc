---
description: Service Layer Patterns with TanStack Query
globs: apps/frontend/**
alwaysApply: false
---
# Rule: Frontend Service Layer Patterns

**Rule Name**: `frontend_services`

## Service File Structure

Every service file exports these functions:

```ts
// Pattern: {entity}_service.ts
import { queryClient } from '@/plugins/tanstack-query'
import { useMutation, useQuery } from '@tanstack/vue-query'
import { useApi } from '@/plugins/api'

export const queryEntities = () => {}                          // List query
export const queryEntityById = (uid: Ref<string | undefined>) => {}  // Single entity
export const mutateEntity = (uid: Ref<string | undefined>) => {}     // Polymorphic mutation
export const mutateEntityDestroy = (uid: Ref<string | undefined>) => {}  // Delete
```

## Service Implementation

```ts
// List entities
export const queryEntities = () => {
  const api = useApi()
  return useQuery({
    queryKey: ['module', 'entities'],
    queryFn: () => api.module.entities.$get().unwrap(),
  })
}

// Single entity
export const queryEntityById = (uid: Ref<string | undefined>) => {
  const api = useApi()
  return useQuery({
    queryKey: ['module', 'entities', uid],
    queryFn: () => api.module.entities({ uid: uid.value! }).$get().unwrap(),
    enabled: computed(() => !!uid.value),
  })
}

// Polymorphic mutation (create/update)
export const mutateEntity = (uid: Ref<string | undefined>) => {
  const api = useApi()
  return useMutation({
    mutationFn: (payload) => {
      if (uid.value) {
        return api.module.entities({ uid: uid.value! }).$put(payload).unwrap()
      }
      return api.module.entities.$post(payload).unwrap()
    },
    onSuccess: () => {
      if (uid.value) {
        queryClient.invalidateQueries({ queryKey: ['module', 'entities', uid.value] })
      }
      queryClient.invalidateQueries({ queryKey: ['module', 'entities'] })
    },
  })
}

// Delete mutation
export const mutateEntityDestroy = (uid: Ref<string | undefined>) => {
  const api = useApi()
  return useMutation({
    mutationFn: () => api.module.entities({ uid: uid.value! }).$delete().unwrap(),
    onSuccess() {
      queryClient.invalidateQueries({ queryKey: ['module', 'entities'] })
    },
  })
}
```

## Query Key Pattern

```ts
['module', 'entities']           // List all
['module', 'entities', uid]      // Single entity
['module', 'entities', uid, 'details']  // With relations
```

## Usage in Components

```ts
// Basic usage
const { data, isLoading } = queryEntities()
const entityUid = ref<string | undefined>()
const { data: entity } = queryEntityById(entityUid)

// Mutations in forms
const mutation = mutateEntity(editingUid)
const onSubmit = handleSubmit(async (values) => {
  await mutation.mutateAsync(values)
  toast({ title: 'Success' })
})
```

## Type Inference & Payloads

* Always infer payloads and responses with `TuyauJS` helpers where possible:

```ts
type RequestPayload = InferRequestType<ReturnType<typeof useApi>['users']['login']['$post']>
type ResponseData = InferResponseType<ReturnType<typeof useApiAnon>['auth']['me']['$get']>
```
* If `InferRequestType` is not suitable due to complex payload structures (e.g., nested objects, `FormData`) or if a shared payload type is already defined elsewhere, explicitly imported types (e.g., `AgentCreatePayload`) are acceptable.
* Ensure payload types correctly match the expected structure of the API endpoint.

## Naming Rules

* Function names:
  * `query<Entity>()`
  * `mutate<Entity>Action()`
  * `prefetch<Entity>()`
  * `ensure<Entity>()`
  * `fetch<Entity>()`
* Must use **camelCase**, no prefixes like `get`, `fetch`, etc.

## Required Patterns

1. Ony accept parameters as `Ref<T>` (e.g., `Ref<string | undefined>`)
2. **Always use**  `useApi()` for authenticated routes, `useApiAnon()` for unauthenticated ones
3. **Always use** `.unwrap()` on all endpoint calls
4. **Always use** hierarchical query keys: `['module', 'resource', 'id']`
5. **Always use** polymorphic mutations for create/update
6. **Always use** `enabled: computed(() => !!uid.value)` for conditional queries
7. **Always use** `onSuccess` where relevant (for cache invalidation, store updates, local cache mutations, logout, etc.)