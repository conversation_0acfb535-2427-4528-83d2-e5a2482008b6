---
description: 
globs: apps/frontend/**
alwaysApply: false
---
# Rule: Loading State

**Rule Name**: `frontend_loading_state`

## Loading States & Skeletons

###  1. Prioritize Base Components (If Applicable)

- Always check if an existing base skeleton component (e.g., `<BaseSkeleton type="card" />`, `<BaseSkeleton type="table" />`) is suitable for the UI pattern you are building.
- Using pre-defined base skeletons promotes consistency and reduces boilerplate if the generic structure is a good fit.

### 2. Craft Custom Skeletons for Specificity

- If a base skeleton component is not representative enough of the actual UI, **create a custom skeleton structure**.
- The primary goal of a custom skeleton is to **closely mimic the layout, structure, and key visual elements** of the content it is replacing.

### 3. How to Build Custom Skeletons

- **Replicate HTML Structure**: Your skeleton's HTML (divs, flex, grid containers) should mirror the real component's structure.
- **Use Primitive `Skeleton` Components**: Employ the basic `<Skeleton />` component (from Shadcn or a similar UI library) to represent individual pieces of content:
  - Lines of text (e.g., titles, paragraphs, labels).
  - Buttons.
  - Images or avatars (use appropriate border-radius).
  - Blocks of content or containers.
- **Match Dimensions and Spacing**:
  - Strive to match the approximate `width`, `height`, `margin`, `padding`, and `border-radius` of the actual UI elements.
  - Use TailwindCSS classes for sizing and spacing, just as you would in the real component.
- **Key Element Placeholders**: Ensure distinct placeholders for critical UI elements such as:
  - Page or section titles.
  - Primary action buttons.
  - Price displays.
  - Lists or groups of items (e.g., feature lists within a pricing card).
- **Mimic Hierarchy and Flow**: The visual hierarchy of the skeleton should guide the user's eye similarly to how the loaded content will.

### 4. Conditional Rendering

- Skeletons should always be conditionally rendered using a loading state variable (e.g., `v-if="isLoading"`, `v-if="isFetching"`).
- Once the data is available and the loading state becomes false, the skeleton should be replaced by the actual content.

### 5. Skeleton Implementation
```vue
<template>
  <!-- Table skeleton -->
  <BaseSkeleton 
    v-if="isLoading"
    type="table" 
    :rows="3" 
    :headers="4" 
    :cols="4" 
  />
  
  <!-- Custom skeleton -->
  <div v-if="isLoading" class="space-y-4">
    <Skeleton class="w-1/3 h-6" />
    <Skeleton class="w-full h-4" />
    <Skeleton class="w-2/3 h-4" />
    <Skeleton class="w-full h-10" />
  </div>
</template>
```

### Loading States in Components
```vue
<template>
  <!-- Component with loading -->
  <BaseTable
    :isLoading="isLoading || isFetching"
    :heads="heads"
    :rows="data"
    :actions="['edit', 'delete']"
    empty-message="No items found"
  />
  
  <!-- Form with loading -->
  <BaseForm
    :isLoading="isLoading"
    :isSubmitting="isSubmitting"
    :inputs="3"
    @submit="onSubmit"
  />
  
  <!-- Button with loading -->
  <BaseButton :isSubmitting="isSubmitting" :isDisabled="false">
    Submit
  </BaseButton>
</template>
```