---
description: 
globs: apps/frontend/**
alwaysApply: false
---
# Rule: Frontend Constants Convention

**Rule Name**: `frontend_constants`

## Global Constants Organization (Real Pattern)

All global constants MUST be defined in `/src/constants.ts` exactly as implemented in the codebase:

```ts
// /src/constants.ts
export const ICONS = {
  UPGRADE: 'Sparkles',
  ...
} as const

export const STORAGE_KEYS = {
  AUTH_TOKEN: 'auth_token',
  ...
} as const
```

## ICONS Constant Usage

### Required Import Pattern
```ts
import { ICONS } from '@/constants'
```

### Component Usage
```vue
<template>
  <!-- Dialog with icon -->
  <BaseDialog :open="true" :icon="ICONS.SETTINGS" title="Settings">
    <slot />
  </BaseDialog>
</template>

<script setup lang="ts">
import { ICONS } from '@/constants'
</script>
```

## Constant Naming Conventions

### Category-Based Organization
```ts
// ✅ Correct - Semantic, category-based names
export const ICONS = {
  // Features
  AGENTS: 'Bot',
  USERS: 'Users',
  
  // Actions
  UPGRADE: 'Sparkles',
  LOGOUT: 'LogOut',
  
  // UI Elements
  SUPPORT: 'HelpCircle',
  ACCOUNT: 'BadgeCheck',
}

// ❌ Wrong - Generic or unclear names
export const ICONS = {
  ICON1: 'Bot',
  USER_ICON: 'Users',    // Redundant suffix
  BTN_UPGRADE: 'Sparkles', // Abbreviated prefix
}
```

### Naming Rules

- **Global constants**: `UPPER_SNAKE_CASE`
- **Always use** `as const` for arrays and objects
- **Always type** JSON-loaded constants with explicit types or `Infer<typeof validator>`
- **Always use** spread syntax for extending arrays (`...PARENT_ARRAY`)
- **Semantic names**: Describe the feature/action
- **No redundant suffixes**: Don't use `_ICON`, `_BTN`, etc.
- **Group logically**: Related constants together

## Required Patterns

1. **Always import** from `@/constants` using named imports
2. **Always use** `UPPER_SNAKE_CASE` naming
3. **Always use** `as const` for type literals
4. **Always use** `Infer<typeof validator>` for JSON with validation
5. **Always use** semantic names that describe purpose, not implementation
6. **Always group** related constants together
7. **Always use** spread syntax for array inheritance
8. **Always type** JSON-loaded constants explicitly
9. **Always use** constants for icons in BaseDialog, BaseButton, and navigation

## Forbidden Patterns

- ❌ Hardcoding icon names: `<BaseIcon name="Bot" />`
- ❌ Inline magic strings: `localStorage.getItem('auth_token')`
- ❌ Component-level constants for shared values
- ❌ Unclear abbreviations: `USR`, `BTN`, `ICN`
- ❌ Missing `as const` on constant objects
