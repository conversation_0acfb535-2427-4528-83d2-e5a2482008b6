---
description: 
globs: apps/frontend/**
alwaysApply: false
---
# Rule: Forms and Validation Patterns

**Rule Name**: `frontend_forms_validation`

## Required Form Stack

### Core Libraries
- **VeeValidate**: Form handling and state management
- **Zod**: Schema validation with TypeScript inference
- **@vee-validate/zod**: Bridge between VeeValidate and Zod
- **shadcn/ui form components**: FormField, FormItem, FormLabel, FormControl, FormMessage

### Forbidden Patterns
- ❌ Native HTML form elements (`<input>`, `<textarea>`, `<select>`)
- ❌ Manual form validation
- ❌ Options API form handling
- ❌ Direct shadcn/ui components without FormField wrapper

### Required Import Patterns
```ts
// Form components (explicit imports required)
import { 
  FormControl, 
  FormField, 
  FormItem, 
  FormLabel, 
  FormMessage,
  FormDescription 
} from '@/shadcn/ui/form'
```

## Form Component Structure

Every form component MUST follow this exact pattern:

```vue
<template>
  <BaseDialog :open="true" :icon="ICONS.FEATURE_ICON" :title="title">
    <BaseForm :isLoading="isLoading" :isSubmitting="isSubmitting" @submit="onSubmit">
      <!-- Text input field -->
      <FormField v-slot="{ componentField }" name="name">
        <FormItem>
          <FormLabel>Name</FormLabel>
          <FormControl>
            <Input v-bind="componentField" placeholder="Enter name" />
          </FormControl>
          <FormMessage />
        </FormItem>
      </FormField>

      <DialogFooter>
        <Button type="button" variant="outline" @click="closeDialog">Cancel</Button>
        <Button type="submit" :disabled="isSubmitting">
          {{ editingUid ? 'Update' : 'Add' }}
        </Button>
      </DialogFooter>
    </BaseForm>
  </BaseDialog>
</template>

<script setup lang="ts">
// Form setup with validation
const { handleSubmit, isSubmitting, isFetching, setValues } = useForm({
  initialValues: {
    name: '',
  },
  validationSchema: toTypedSchema(
    z.object({
      name: z.string().min(1, { message: 'Name is required' }),
    })
  ),
})

// Refs and state
const editingUid = ref<string | undefined>()

// Queries and mutations
const mutation = mutateEntity(editingUid)
const { isLoading, data } = queryEntityById(editingUid)

// Computed properties
const title = computed(() => `${editingUid.value ? 'Edit' : 'Add'} Entity`)

// Functions
function openDialog(uid?: string) {
  editingUid.value = uid
}

function closeDialog() {
  // BaseDialog handles closing via store
}

// Form submission
const onSubmit = handleSubmit(async (values) => {
  try {
    await mutation.mutateAsync(values)
    toast({ title: `${editingUid.value ? 'Updated' : 'Added'} successfully` })
    closeDialog()
  } catch (err: any) {
    toast({
      title: 'Operation failed',
      description: err.message || 'Operation failed',
      variant: 'destructive',
    })
  }
})

// Data loading watcher
watchEffect(() => {
  if (data.value && editingUid.value) {
    setValues({
      name: data.value.name ?? '',
    })
  }
})

defineExpose({ openDialog })
</script>
```

### Field Component Structure
Every form field MUST use this exact structure:

```vue
<FormField v-slot="{ componentField }" name="field_name">
  <FormItem>
    <FormLabel>Field Label</FormLabel>
    <FormControl>
      <Input v-bind="componentField" placeholder="Placeholder" />
    </FormControl>
    <FormMessage />
  </FormItem>
</FormField>
```

## Error Handling

### Form Error Display
```ts
const onSubmit = handleSubmit(async (values) => {
  try {
    await mutation.mutateAsync(values)
    toast({ title: 'Success' })
  } catch (err: any) {
    toast({
      title: 'Error',
      description: err.message || 'Operation failed',
      variant: 'destructive',
    })
  }
})
```

## Required Patterns

1. **Always use** VeeValidate + Zod for form validation
2. **Always use** `FormField` wrapper for all form inputs
3. **Always use** `v-bind="componentField"` on form controls
4. **Always use** `toTypedSchema` for Zod validation schemas
5. **Always use** `handleSubmit` for form submission
6. **Always use** toast notifications for user feedback
7. **Never use** native HTML form elements directly
8. **Always use** `setValues` to populate forms with existing data
